import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function migrateToCredits() {
  console.log('开始迁移用户配额到积分系统...');

  try {
    // 获取所有用户的当前配额
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        standardQuota: true,
        usedStandardQuota: true,
        professionalQuota: true,
        usedProfessionalQuota: true,
        textGenerationQuota: true,
        usedTextGenerationQuota: true,
      },
    });

    console.log(`找到 ${users.length} 个用户需要迁移`);

    for (const user of users) {
      // 计算总剩余字符数
      const remainingStandard = Math.max(0, user.standardQuota - user.usedStandardQuota);
      const remainingProfessional = Math.max(0, user.professionalQuota - user.usedProfessionalQuota);
      const remainingTextGeneration = Math.max(0, user.textGenerationQuota - user.usedTextGenerationQuota);

      // 转换为积分：
      // 标准配额：10字符 = 1积分
      // 专业配额：5字符 = 1积分 (更高价值)
      // 文本生成：100 tokens = 1积分
      const creditsFromStandard = Math.floor(remainingStandard / 10);
      const creditsFromProfessional = Math.floor(remainingProfessional / 5);
      const creditsFromTextGeneration = Math.floor(remainingTextGeneration / 100);

      // 总积分 = 各种配额转换的积分之和，最少给1000积分
      const totalCredits = Math.max(1000, creditsFromStandard + creditsFromProfessional + creditsFromTextGeneration);

      console.log(`用户 ${user.email}:`);
      console.log(`  标准配额剩余: ${remainingStandard} 字符 -> ${creditsFromStandard} 积分`);
      console.log(`  专业配额剩余: ${remainingProfessional} 字符 -> ${creditsFromProfessional} 积分`);
      console.log(`  文本生成剩余: ${remainingTextGeneration} tokens -> ${creditsFromTextGeneration} 积分`);
      console.log(`  总积分: ${totalCredits}`);

      // 这里暂时只打印，不实际更新数据库
      // 等确认迁移逻辑正确后再执行实际更新
    }

    console.log('\n迁移预览完成。如果确认无误，请取消注释下面的更新代码。');

    // 执行实际迁移
    console.log('\n开始执行实际迁移...');

    for (const user of users) {
      const remainingStandard = Math.max(0, user.standardQuota - user.usedStandardQuota);
      const remainingProfessional = Math.max(0, user.professionalQuota - user.usedProfessionalQuota);
      const remainingTextGeneration = Math.max(0, user.textGenerationQuota - user.usedTextGenerationQuota);

      const creditsFromStandard = Math.floor(remainingStandard / 10);
      const creditsFromProfessional = Math.floor(remainingProfessional / 5);
      const creditsFromTextGeneration = Math.floor(remainingTextGeneration / 100);

      const totalCredits = Math.max(1000, creditsFromStandard + creditsFromProfessional + creditsFromTextGeneration);

      // 先添加新字段，暂时不删除旧字段
      await prisma.$executeRaw`
        UPDATE "User"
        SET "credits" = ${totalCredits}, "usedCredits" = 0
        WHERE "id" = ${user.id}
      `;

      console.log(`✅ 用户 ${user.email} 迁移完成，积分: ${totalCredits}`);
    }

  } catch (error) {
    console.error('迁移过程中出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行迁移
migrateToCredits();
