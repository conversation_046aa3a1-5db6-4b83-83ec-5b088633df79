/**
 * 重新创建VoiceDemo数据
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// 获取默认试听文本的辅助函数
function getDefaultDemoText(languageCode: string): string {
  const demoTexts: Record<string, string> = {
    'en-US': 'Hello, this is a voice demonstration. How are you today?',
    'zh-CN': '您好！我是 Voctana 的 AI 语音助手。您想听我说些什么呢？',
    'ja-JP': 'こんにちは、これは音声デモンストレーションです。今日はいかがですか？',
    'ko-KR': '안녕하세요, 이것은 음성 데모입니다. 오늘 어떠세요?',
    'fr-FR': 'Bonjour, ceci est une démonstration vocale. Comment allez-vous aujourd\'hui?',
    'de-DE': '<PERSON><PERSON>, das ist eine Sprachdemonstration. Wie geht es Ihnen heute?',
    'es-US': 'Hola, esta es una demostración de voz. ¿Cómo estás hoy?',
    'it-IT': '<PERSON><PERSON><PERSON>, questa è una dimostrazione vocale. Come stai oggi?',
    'pt-BR': '<PERSON><PERSON><PERSON>, esta é uma demonstração de voz. Como você está hoje?',
    'ru-RU': 'Привет, это голосовая демонстрация. Как дела сегодня?',
    'ar-EG': 'مرحبا، هذا عرض صوتي. كيف حالك اليوم؟',
    'hi-IN': 'नमस्ते, यह एक आवाज़ प्रदर्शन है। आज आप कैसे हैं?',
    'th-TH': 'สวัสดี นี่คือการสาธิตเสียง วันนี้คุณเป็นอย่างไรบ้าง?',
    'vi-VN': 'Xin chào, đây là bản demo giọng nói. Hôm nay bạn thế nào?',
    'id-ID': 'Halo, ini adalah demonstrasi suara. Apa kabar hari ini?',
    'km-KH': 'សួស្តី នេះជាការបង្ហាញសំឡេង។ តើថ្ងៃនេះអ្នកមានសុខភាពយ៉ាងណា?',
    'nl-NL': 'Hallo, dit is een spraakdemonstratie. Hoe gaat het vandaag?',
    'pl-PL': 'Cześć, to jest demonstracja głosu. Jak się masz dzisiaj?',
    'tr-TR': 'Merhaba, bu bir ses gösterimi. Bugün nasılsın?',
    'uk-UA': 'Привіт, це голосова демонстрація. Як справи сьогодні?',
    'ro-RO': 'Salut, aceasta este o demonstrație vocală. Cum te simți astăzi?',
    'bn-BD': 'হ্যালো, এটি একটি ভয়েস ডেমো। আজ আপনি কেমন আছেন?',
    'en-IN': 'Hello, this is a voice demonstration. How are you today?',
    'mr-IN': 'नमस्कार, हे आवाज प्रदर्शन आहे। आज तुम्ही कसे आहात?',
    'ta-IN': 'வணக்கம், இது ஒரு குரல் ஆர்ப்பாட்டம். இன்று நீங்கள் எப்படி இருக்கிறீர்கள்?',
    'te-IN': 'హలో, ఇది వాయిస్ డెమోన్స్ట్రేషన్. ఈరోజు మీరు ఎలా ఉన్నారు?',
  };

  return demoTexts[languageCode] || 'Hello, this is a voice demonstration. How are you today?';
}

async function recreateVoiceDemos() {
  console.log('🔄 重新创建VoiceDemo数据...\n');

  try {
    // 1. 删除所有现有的VoiceDemo记录
    console.log('1. 删除现有VoiceDemo记录...');
    const deleteResult = await prisma.voiceDemo.deleteMany({});
    console.log(`   删除了 ${deleteResult.count} 个记录`);

    // 2. 获取所有语言和API模板
    console.log('\n2. 获取语言和API模板...');
    const languages = await prisma.language.findMany({
      where: { isActive: true },
      orderBy: { sortOrder: 'asc' },
    });

    const templates = await prisma.apiCharacterTemplate.findMany({
      where: { isActive: true },
      orderBy: { sortOrder: 'asc' },
    });

    console.log(`   找到 ${languages.length} 种语言`);
    console.log(`   找到 ${templates.length} 个API模板`);

    // 3. 为每个语言和每个模板创建VoiceDemo记录
    console.log('\n3. 创建新的VoiceDemo记录...');
    let createdCount = 0;

    for (const language of languages) {
      console.log(`   处理语言: ${language.name} (${language.code})`);
      
      for (const template of templates) {
        try {
          const demoText = getDefaultDemoText(language.code);
          
          const voiceDemo = await prisma.voiceDemo.create({
            data: {
              templateId: template.id,
              languageId: language.id,
              languageCode: language.code,
              demoText: demoText,
              audioUrl: '', // 暂时为空，后续可以生成
              audioKey: `demo_${template.apiVoiceName}_${language.code}`,
              quality: 'standard',
            },
          });

          createdCount++;
          
          if (createdCount % 50 === 0) {
            console.log(`     已创建 ${createdCount} 个记录...`);
          }
        } catch (error) {
          console.error(`     ❌ 创建 ${template.originalName} - ${language.code} 失败:`, error);
        }
      }
    }

    console.log(`\n✅ VoiceDemo重新创建完成:`);
    console.log(`   成功创建: ${createdCount} 个记录`);
    console.log(`   预期数量: ${languages.length * templates.length} 个记录`);

    // 4. 验证创建结果
    console.log('\n4. 验证创建结果...');
    const verifyDemos = await prisma.voiceDemo.findMany({
      include: {
        template: {
          select: {
            originalName: true,
            apiVoiceName: true,
          },
        },
        language: {
          select: {
            name: true,
            code: true,
          },
        },
      },
      take: 5,
    });

    console.log(`\n🔍 验证结果 (前5个):`);
    verifyDemos.forEach(demo => {
      console.log(`   - ${demo.template?.originalName} (${demo.template?.apiVoiceName}) - ${demo.language?.name}: ${demo.demoText.substring(0, 30)}...`);
    });

    // 5. 统计各语言的Demo数量
    console.log('\n📊 各语言Demo统计:');
    for (const language of languages.slice(0, 5)) { // 只显示前5个语言
      const count = await prisma.voiceDemo.count({
        where: { languageId: language.id },
      });
      console.log(`   ${language.flag} ${language.name}: ${count} 个Demo`);
    }

  } catch (error) {
    console.error('❌ 重新创建过程中出错:', error);
    throw error;
  }
}

async function main() {
  try {
    await recreateVoiceDemos();
  } catch (error) {
    console.error('重新创建失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 直接运行main函数
main();
