/**
 * 更新VoiceDemo数据以使用新的templateId关联
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function updateVoiceDemos() {
  console.log('🔄 更新VoiceDemo数据...\n');

  try {
    // 1. 获取所有需要更新的VoiceDemo记录
    const voiceDemos = await prisma.$queryRaw`
      SELECT * FROM "voice_demos" WHERE "templateId" IS NULL
    ` as any[];

    console.log(`找到 ${voiceDemos.length} 个需要更新的VoiceDemo记录`);

    if (voiceDemos.length === 0) {
      console.log('✅ 所有VoiceDemo记录都已更新');
      return;
    }

    // 2. 获取旧的VoiceCharacter到新的ApiCharacterTemplate的映射
    const characterToTemplateMap = new Map<string, string>();
    
    // 从备份数据中获取映射关系
    const backupFiles = await import('fs').then(fs => 
      fs.readdirSync('./backups').filter(file => file.startsWith('backup-'))
    );

    if (backupFiles.length === 0) {
      console.log('❌ 找不到备份文件，无法建立映射关系');
      return;
    }

    // 使用最新的备份文件
    const latestBackup = backupFiles.sort().pop();
    const backupPath = `./backups/${latestBackup}`;
    
    console.log(`使用备份文件: ${backupPath}`);
    
    const backupData = await import('fs').then(fs => 
      JSON.parse(fs.readFileSync(backupPath, 'utf8'))
    );

    // 建立映射关系
    for (const oldChar of backupData.voiceCharacters) {
      if (!oldChar.isCustom) {
        const templateId = `template_${oldChar.id}`;
        characterToTemplateMap.set(oldChar.id, templateId);
      }
    }

    console.log(`建立了 ${characterToTemplateMap.size} 个角色到模板的映射关系`);

    // 3. 更新VoiceDemo记录
    let updatedCount = 0;
    let skippedCount = 0;

    for (const demo of voiceDemos) {
      const templateId = characterToTemplateMap.get(demo.voiceCharacterId);
      
      if (templateId) {
        try {
          await prisma.$executeRaw`
            UPDATE "voice_demos" 
            SET "templateId" = ${templateId}
            WHERE "id" = ${demo.id}
          `;
          updatedCount++;
          
          if (updatedCount % 100 === 0) {
            console.log(`   已更新 ${updatedCount} 个记录...`);
          }
        } catch (error) {
          console.error(`   ❌ 更新记录 ${demo.id} 失败:`, error);
          skippedCount++;
        }
      } else {
        console.warn(`   ⚠️  找不到角色 ${demo.voiceCharacterId} 对应的模板`);
        skippedCount++;
      }
    }

    console.log(`\n✅ VoiceDemo更新完成:`);
    console.log(`   成功更新: ${updatedCount} 个记录`);
    console.log(`   跳过: ${skippedCount} 个记录`);

    // 4. 验证更新结果
    const updatedDemos = await prisma.voiceDemo.findMany({
      where: { templateId: { not: null } },
      include: {
        template: {
          select: {
            originalName: true,
            apiVoiceName: true,
          },
        },
      },
      take: 5,
    });

    console.log(`\n🔍 验证更新结果 (前5个):`);
    updatedDemos.forEach(demo => {
      console.log(`   - ${demo.template?.originalName} (${demo.template?.apiVoiceName}): ${demo.demoText.substring(0, 30)}...`);
    });

  } catch (error) {
    console.error('❌ 更新过程中出错:', error);
    throw error;
  }
}

async function main() {
  try {
    await updateVoiceDemos();
  } catch (error) {
    console.error('更新失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 直接运行main函数
main();
