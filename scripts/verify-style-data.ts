/**
 * 验证语音风格数据的完整性和正确性
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verifyStyleData() {
  console.log('🔍 验证语音风格数据...\n');

  try {
    // 获取所有风格
    const styles = await prisma.voiceStyle.findMany({
      where: { isActive: true },
      include: {
        category: true,
        creator: {
          select: { name: true, role: true },
        },
      },
      orderBy: { createdAt: 'asc' },
    });

    console.log(`📊 总计风格数量: ${styles.length}\n`);

    // 按类型分组统计
    const singleSpeakerStyles = styles.filter(s => s.type === 'SINGLE_SPEAKER');
    const multiSpeakerStyles = styles.filter(s => s.type === 'MULTI_SPEAKER');

    console.log(`📈 风格类型统计:`);
    console.log(`   单人语音: ${singleSpeakerStyles.length} 个`);
    console.log(`   多人对话: ${multiSpeakerStyles.length} 个\n`);

    // 按分类统计
    const categories = await prisma.styleCategory.findMany({
      where: { isActive: true },
      include: {
        _count: {
          select: { styles: true },
        },
      },
    });

    console.log(`📂 分类统计:`);
    categories.forEach(category => {
      console.log(`   ${category.name}: ${category._count.styles} 个风格`);
    });
    console.log('');

    // 验证风格提示格式
    console.log(`🎭 风格提示格式验证:`);
    let validPrompts = 0;
    let invalidPrompts = 0;

    const validPatterns = [
      /^Say /,
      /^Make /,
      /^in a /,
      /^with a /,
      /^excited and happy$/,
      /^tired and bored$/,
    ];

    styles.forEach(style => {
      const isValid = validPatterns.some(pattern => pattern.test(style.prompt));
      if (isValid) {
        validPrompts++;
      } else {
        invalidPrompts++;
        console.log(`   ❌ ${style.name}: "${style.prompt}"`);
      }
    });

    console.log(`   ✅ 符合格式: ${validPrompts} 个`);
    console.log(`   ❌ 需要调整: ${invalidPrompts} 个`);
    console.log(`   符合率: ${Math.round((validPrompts / styles.length) * 100)}%\n`);

    // 显示新增的风格
    console.log(`🆕 新增风格展示:`);
    
    // 情感类风格
    const emotionStyles = singleSpeakerStyles.filter(s => 
      s.tags.some(tag => ['温暖', '激情', '平静', '神秘', '幽默', '浪漫'].includes(tag))
    );
    console.log(`\n💝 情感类风格 (${emotionStyles.length}个):`);
    emotionStyles.forEach(style => {
      console.log(`   ${style.icon} ${style.name}: ${style.description}`);
    });

    // 角色类风格
    const roleStyles = singleSpeakerStyles.filter(s => 
      s.tags.some(tag => ['AI', '主播', '老师', '医生', '客服', '销售', '导游', '教练'].includes(tag))
    );
    console.log(`\n👥 角色类风格 (${roleStyles.length}个):`);
    roleStyles.forEach(style => {
      console.log(`   ${style.icon} ${style.name}: ${style.description}`);
    });

    // 场景类风格
    const sceneStyles = singleSpeakerStyles.filter(s => 
      s.tags.some(tag => ['睡前', '冥想', '游戏', '烹饪', '科技'].includes(tag))
    );
    console.log(`\n🎬 场景类风格 (${sceneStyles.length}个):`);
    sceneStyles.forEach(style => {
      console.log(`   ${style.icon} ${style.name}: ${style.description}`);
    });

    // 多人对话风格
    console.log(`\n👫 多人对话风格 (${multiSpeakerStyles.length}个):`);
    multiSpeakerStyles.forEach(style => {
      const speakerConfig = style.speakerConfig as any;
      const speakers = speakerConfig?.speakers || [];
      console.log(`   ${style.icon} ${style.name}: ${speakers.map((s: any) => s.name).join(' vs ')}`);
    });

    // 验证参数设置
    console.log(`\n⚙️ 参数设置验证:`);
    let validParams = 0;
    let invalidParams = 0;

    singleSpeakerStyles.forEach(style => {
      const params = style.parameters as any;
      if (params && 
          typeof params.speed === 'number' && 
          typeof params.pitch === 'number' && 
          typeof params.volume === 'number') {
        validParams++;
      } else {
        invalidParams++;
        console.log(`   ❌ ${style.name}: 参数格式错误`);
      }
    });

    console.log(`   ✅ 参数正确: ${validParams} 个`);
    console.log(`   ❌ 参数错误: ${invalidParams} 个\n`);

    // 显示标签统计
    const allTags = styles.flatMap(s => s.tags);
    const tagCounts = allTags.reduce((acc, tag) => {
      acc[tag] = (acc[tag] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const sortedTags = Object.entries(tagCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10);

    console.log(`🏷️ 热门标签 (前10):`);
    sortedTags.forEach(([tag, count]) => {
      console.log(`   ${tag}: ${count} 次`);
    });

    console.log(`\n🎉 验证完成！语音风格数据库已成功扩展到 ${styles.length} 个风格！`);

  } catch (error) {
    console.error('❌ 验证过程中出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行验证
verifyStyleData().catch(console.error);
