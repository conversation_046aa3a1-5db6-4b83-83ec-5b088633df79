/**
 * 数据迁移脚本：为现有角色添加多语言支持
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// 风格名称映射表
const styleTranslations: Record<string, { 'zh-CN': string; 'en-US': string }> = {
  '明亮': { 'zh-CN': '明亮', 'en-US': 'Bright' },
  '青春': { 'zh-CN': '青春', 'en-US': 'Youthful' },
  'Breezy': { 'zh-CN': '轻快', 'en-US': 'Breezy' },
  '轻松': { 'zh-CN': '轻松', 'en-US': 'Relaxed' },
  '友好': { 'zh-CN': '友好', 'en-US': 'Friendly' },
  '平滑': { 'zh-CN': '平滑', 'en-US': 'Smooth' },
  '清除': { 'zh-CN': '清除', 'en-US': 'Clear' },
  '欢快': { 'zh-CN': '欢快', 'en-US': 'Cheerful' },
  '直率': { 'zh-CN': '直率', 'en-US': 'Straightforward' },
  '暖色': { 'zh-CN': '暖色', 'en-US': 'Warm' },
  '温和': { 'zh-CN': '温和', 'en-US': 'Gentle' },
  '均匀': { 'zh-CN': '均匀', 'en-US': 'Even' },
  '活泼': { 'zh-CN': '活泼', 'en-US': 'Lively' },
  '气声': { 'zh-CN': '气声', 'en-US': 'Breathy' },
  '信息丰富': { 'zh-CN': '信息丰富', 'en-US': 'Informative' },
  '坚定': { 'zh-CN': '坚定', 'en-US': 'Firm' },
  'Excitable': { 'zh-CN': '兴奋', 'en-US': 'Excitable' },
  '公司': { 'zh-CN': '公司', 'en-US': 'Corporate' },
  '清晰': { 'zh-CN': '清晰', 'en-US': 'Clear' },
  '随和': { 'zh-CN': '随和', 'en-US': 'Easygoing' },
  'Gravelly': { 'zh-CN': '沙哑', 'en-US': 'Gravelly' },
  '软': { 'zh-CN': '软', 'en-US': 'Soft' },
  'Firm': { 'zh-CN': '坚定', 'en-US': 'Firm' },
  '成人': { 'zh-CN': '成人', 'en-US': 'Mature' },
  '随意': { 'zh-CN': '随意', 'en-US': 'Casual' },
  '知识渊博': { 'zh-CN': '知识渊博', 'en-US': 'Knowledgeable' },
};

// 个性描述映射表
const personalityTranslations: Record<string, { 'zh-CN': string; 'en-US': string }> = {
  '温暖友好': { 'zh-CN': '温暖友好', 'en-US': 'Warm and friendly' },
  '专业可靠': { 'zh-CN': '专业可靠', 'en-US': 'Professional and reliable' },
  '活泼开朗': { 'zh-CN': '活泼开朗', 'en-US': 'Lively and cheerful' },
  '沉稳大气': { 'zh-CN': '沉稳大气', 'en-US': 'Calm and dignified' },
  '知性优雅': { 'zh-CN': '知性优雅', 'en-US': 'Intelligent and elegant' },
  '亲切自然': { 'zh-CN': '亲切自然', 'en-US': 'Kind and natural' },
};

// 描述映射表
const descriptionTranslations: Record<string, { 'zh-CN': string; 'en-US': string }> = {
  '适合新闻播报和正式场合': { 'zh-CN': '适合新闻播报和正式场合', 'en-US': 'Suitable for news broadcasting and formal occasions' },
  '适合教育内容和儿童故事': { 'zh-CN': '适合教育内容和儿童故事', 'en-US': 'Suitable for educational content and children\'s stories' },
  '适合广告和营销内容': { 'zh-CN': '适合广告和营销内容', 'en-US': 'Suitable for advertising and marketing content' },
  '适合企业培训和商务场合': { 'zh-CN': '适合企业培训和商务场合', 'en-US': 'Suitable for corporate training and business occasions' },
  '适合有声读物和文学作品': { 'zh-CN': '适合有声读物和文学作品', 'en-US': 'Suitable for audiobooks and literary works' },
  '适合客服和助手应用': { 'zh-CN': '适合客服和助手应用', 'en-US': 'Suitable for customer service and assistant applications' },
};

// 最适合用途映射表
const bestForTranslations: Record<string, { 'zh-CN': string; 'en-US': string }> = {
  '新闻播报': { 'zh-CN': '新闻播报', 'en-US': 'News broadcasting' },
  '教育内容': { 'zh-CN': '教育内容', 'en-US': 'Educational content' },
  '广告营销': { 'zh-CN': '广告营销', 'en-US': 'Advertising and marketing' },
  '企业培训': { 'zh-CN': '企业培训', 'en-US': 'Corporate training' },
  '有声读物': { 'zh-CN': '有声读物', 'en-US': 'Audiobooks' },
  '客服助手': { 'zh-CN': '客服助手', 'en-US': 'Customer service' },
  '儿童故事': { 'zh-CN': '儿童故事', 'en-US': 'Children\'s stories' },
  '商务场合': { 'zh-CN': '商务场合', 'en-US': 'Business occasions' },
};

async function populateMultilingualData() {
  console.log('🚀 开始填充多语言数据...');

  try {
    // 获取所有角色
    const characters = await prisma.voiceCharacter.findMany();
    console.log(`📊 找到 ${characters.length} 个角色需要更新`);

    let updatedCount = 0;

    for (const character of characters) {
      const updates: any = {};

      // 处理风格多语言
      if (character.style && styleTranslations[character.style]) {
        updates.multilingualStyles = JSON.stringify(styleTranslations[character.style]);
      }

      // 处理个性多语言（如果有的话）
      if (character.personality && personalityTranslations[character.personality]) {
        updates.multilingualPersonalities = JSON.stringify(personalityTranslations[character.personality]);
      }

      // 处理描述多语言（如果有的话）
      if (character.description && descriptionTranslations[character.description]) {
        updates.multilingualDescriptions = JSON.stringify(descriptionTranslations[character.description]);
      }

      // 处理最适合用途多语言（如果有的话）
      if (character.bestFor && bestForTranslations[character.bestFor]) {
        updates.multilingualBestFor = JSON.stringify(bestForTranslations[character.bestFor]);
      }

      // 如果有需要更新的字段，执行更新
      if (Object.keys(updates).length > 0) {
        await prisma.voiceCharacter.update({
          where: { id: character.id },
          data: updates,
        });
        updatedCount++;
        console.log(`✅ 更新角色: ${character.characterName}`);
      }
    }

    console.log(`🎉 成功更新了 ${updatedCount} 个角色的多语言数据`);
  } catch (error) {
    console.error('❌ 填充多语言数据时出错:', error);
    throw error;
  }
}

async function main() {
  try {
    await populateMultilingualData();
  } catch (error) {
    console.error('脚本执行失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 直接运行脚本
main();

export { populateMultilingualData };
