/**
 * 检查缺失的风格多语言数据
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkMissingStyles() {
  console.log('🔍 检查缺失的风格多语言数据...');

  try {
    // 获取所有角色的风格
    const characters = await prisma.voiceCharacter.findMany({
      select: {
        id: true,
        characterName: true,
        style: true,
        multilingualStyles: true,
      },
    });

    console.log(`📊 找到 ${characters.length} 个角色`);

    // 统计风格
    const styleStats: Record<string, { count: number; hasMultilingual: number; characters: string[] }> = {};

    for (const character of characters) {
      if (character.style) {
        if (!styleStats[character.style]) {
          styleStats[character.style] = { count: 0, hasMultilingual: 0, characters: [] };
        }
        styleStats[character.style].count++;
        styleStats[character.style].characters.push(character.characterName);
        
        if (character.multilingualStyles) {
          styleStats[character.style].hasMultilingual++;
        }
      }
    }

    console.log('\n📈 风格统计:');
    for (const [style, stats] of Object.entries(styleStats)) {
      const hasMultilingualPercent = Math.round((stats.hasMultilingual / stats.count) * 100);
      console.log(`   ${style}: ${stats.count} 个角色, ${stats.hasMultilingual} 个有多语言数据 (${hasMultilingualPercent}%)`);
      
      if (stats.hasMultilingual === 0) {
        console.log(`     ❌ 缺失多语言数据的角色: ${stats.characters.join(', ')}`);
      }
    }

    // 检查具体的多语言数据
    console.log('\n🔍 检查多语言数据内容:');
    const stylesWithMultilingual = characters.filter(c => c.multilingualStyles);
    
    for (const character of stylesWithMultilingual.slice(0, 5)) {
      console.log(`\n   ${character.characterName} (${character.style}):`);
      console.log(`     多语言数据: ${character.multilingualStyles}`);
      
      try {
        const parsed = JSON.parse(character.multilingualStyles!);
        console.log(`     解析后: 中文="${parsed['zh-CN']}", 英文="${parsed['en-US']}"`);
      } catch (error) {
        console.log(`     ❌ 解析失败: ${error}`);
      }
    }

  } catch (error) {
    console.error('❌ 检查风格数据时出错:', error);
    throw error;
  }
}

async function main() {
  try {
    await checkMissingStyles();
  } catch (error) {
    console.error('脚本执行失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
