#!/bin/bash

# Voctana 生产环境种子数据执行脚本
# 使用方法：
# ./scripts/seed-production.sh            # 执行所有模块
# ./scripts/seed-production.sh basic      # 仅执行基础模块
# ./scripts/seed-production.sh content    # 仅执行内容模块
# ./scripts/seed-production.sh optional   # 仅执行可选模块

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查执行环境..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    # 检查 npx
    if ! command -v npx &> /dev/null; then
        log_error "npx 未安装"
        exit 1
    fi
    
    # 检查数据库连接
    if [ -z "$DATABASE_URL" ]; then
        log_error "DATABASE_URL 环境变量未设置"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 执行种子数据
run_seeds() {
    local mode=${1:-"all"}
    
    log_info "开始执行种子数据 (模式: $mode)"
    
    case $mode in
        "basic")
            log_info "执行基础模块..."
            npx tsx -e "
                import { runBasicSeeds } from './prisma/seeds/index.ts';
                runBasicSeeds()
                    .then(() => { console.log('✅ 基础模块执行完成'); process.exit(0); })
                    .catch((e) => { console.error('❌ 基础模块执行失败:', e); process.exit(1); });
            "
            ;;
        "content")
            log_info "执行内容模块..."
            npx tsx -e "
                import { runContentSeeds } from './prisma/seeds/index.ts';
                runContentSeeds()
                    .then(() => { console.log('✅ 内容模块执行完成'); process.exit(0); })
                    .catch((e) => { console.error('❌ 内容模块执行失败:', e); process.exit(1); });
            "
            ;;
        "optional")
            log_info "执行可选模块..."
            npx tsx -e "
                import { runOptionalSeeds } from './prisma/seeds/index.ts';
                runOptionalSeeds()
                    .then(() => { console.log('✅ 可选模块执行完成'); process.exit(0); })
                    .catch((e) => { console.error('❌ 可选模块执行失败:', e); process.exit(1); });
            "
            ;;
        "all"|*)
            log_info "执行所有模块..."
            npx tsx prisma/seeds/index.ts
            ;;
    esac
}

# 主函数
main() {
    local mode=${1:-"all"}
    
    echo "🚀 Voctana 生产环境种子数据执行器"
    echo "================================================"
    
    check_environment
    echo ""
    
    run_seeds "$mode"
    
    echo ""
    log_success "种子数据执行完成！"
    echo ""
    echo "📋 下一步操作："
    echo "1. 通过管理界面生成语音试听音频"
    echo "2. 验证所有功能是否正常工作"
    echo "3. 进行系统测试"
    echo ""
}

# 帮助信息
show_help() {
    echo "Voctana 生产环境种子数据执行脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [模式]"
    echo ""
    echo "模式:"
    echo "  all      执行所有模块 (默认)"
    echo "  basic    仅执行基础模块 (用户 + 语言 + API配置)"
    echo "  content  仅执行内容模块 (角色 + 套餐 + 模板)"
    echo "  optional 仅执行可选模块 (本地化名称)"
    echo "  help     显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0              # 执行所有模块"
    echo "  $0 basic        # 仅执行基础模块"
    echo "  $0 content      # 仅执行内容模块"
    echo ""
}

# 参数处理
case "${1:-}" in
    "help"|"-h"|"--help")
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac