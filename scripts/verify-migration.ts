/**
 * 验证数据迁移结果
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verifyMigration() {
  console.log('🔍 验证数据迁移结果...\n');

  try {
    // 1. 检查API角色模板
    console.log('📋 API角色模板统计:');
    const templateCount = await prisma.apiCharacterTemplate.count();
    console.log(`   总数: ${templateCount}`);

    const templatesByProvider = await prisma.apiCharacterTemplate.groupBy({
      by: ['apiProvider'],
      _count: { id: true },
    });

    templatesByProvider.forEach(group => {
      console.log(`   ${group.apiProvider}: ${group._count.id} 个模板`);
    });

    // 2. 检查语言角色
    console.log('\n🌍 语言角色统计:');
    const languageCharacterCount = await prisma.languageCharacter.count();
    console.log(`   总数: ${languageCharacterCount}`);

    const charactersByLanguage = await prisma.languageCharacter.groupBy({
      by: ['languageId'],
      _count: { id: true },
    });

    console.log(`   分布在 ${charactersByLanguage.length} 种语言中`);

    // 3. 获取语言详细信息
    console.log('\n📊 各语言角色分布:');
    const languages = await prisma.language.findMany({
      include: {
        _count: {
          select: {
            characters: true,
          },
        },
      },
      orderBy: { sortOrder: 'asc' },
    });

    languages.forEach(language => {
      console.log(`   ${language.flag} ${language.name} (${language.code}): ${language._count.characters} 个角色`);
    });

    // 4. 检查一些示例数据
    console.log('\n🔍 示例数据检查:');
    
    // 检查中文语言下的角色
    const chineseLanguage = languages.find(lang => lang.code === 'zh-CN');
    if (chineseLanguage) {
      const chineseCharacters = await prisma.languageCharacter.findMany({
        where: { languageId: chineseLanguage.id },
        include: {
          template: {
            select: {
              apiProvider: true,
              apiVoiceName: true,
              originalName: true,
            },
          },
        },
        take: 5,
      });

      console.log(`\n   中文角色示例 (前5个):`);
      chineseCharacters.forEach(char => {
        console.log(`   - ${char.name} (${char.template?.originalName}) - ${char.template?.apiProvider}:${char.template?.apiVoiceName}`);
      });
    }

    // 检查高棉语角色
    const khmerLanguage = languages.find(lang => lang.code === 'km-KH');
    if (khmerLanguage) {
      const khmerCharacters = await prisma.languageCharacter.findMany({
        where: { languageId: khmerLanguage.id },
        include: {
          template: {
            select: {
              apiProvider: true,
              apiVoiceName: true,
              originalName: true,
            },
          },
        },
        take: 5,
      });

      console.log(`\n   高棉语角色示例 (前5个):`);
      khmerCharacters.forEach(char => {
        console.log(`   - ${char.name} (${char.template?.originalName}) - ${char.template?.apiProvider}:${char.template?.apiVoiceName}`);
      });
    }

    // 5. 检查数据完整性
    console.log('\n✅ 数据完整性检查:');
    
    // 检查是否有孤立的语言角色（没有对应模板的）
    const orphanedCharacters = await prisma.languageCharacter.count({
      where: {
        templateId: { not: null },
        template: null,
      },
    });
    console.log(`   孤立的语言角色: ${orphanedCharacters} 个`);

    // 检查是否有重复的语言-模板组合
    const duplicateCheck = await prisma.$queryRaw`
      SELECT "languageId", "templateId", COUNT(*) as count
      FROM "language_characters"
      WHERE "templateId" IS NOT NULL
      GROUP BY "languageId", "templateId"
      HAVING COUNT(*) > 1
    ` as any[];
    
    console.log(`   重复的语言-模板组合: ${duplicateCheck.length} 个`);

    // 6. 性能测试
    console.log('\n⚡ 性能测试:');
    
    const start = Date.now();
    const testQuery = await prisma.languageCharacter.findMany({
      where: {
        language: { code: 'zh-CN' },
        isActive: true,
      },
      include: {
        template: true,
        language: true,
      },
    });
    const end = Date.now();
    
    console.log(`   查询中文活跃角色 (${testQuery.length} 个): ${end - start}ms`);

    console.log('\n🎉 数据迁移验证完成！');
    console.log('\n📋 总结:');
    console.log(`   - API模板: ${templateCount} 个`);
    console.log(`   - 语言角色: ${languageCharacterCount} 个`);
    console.log(`   - 支持语言: ${languages.length} 种`);
    console.log(`   - 数据完整性: ${orphanedCharacters === 0 && duplicateCheck.length === 0 ? '✅ 良好' : '⚠️ 需要检查'}`);

  } catch (error) {
    console.error('❌ 验证过程中出错:', error);
    throw error;
  }
}

async function main() {
  try {
    await verifyMigration();
  } catch (error) {
    console.error('验证失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 直接运行main函数
main();
