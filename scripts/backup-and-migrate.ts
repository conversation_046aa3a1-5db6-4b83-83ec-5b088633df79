/**
 * 安全的数据备份和迁移脚本
 * 分步骤进行数据迁移，确保数据安全
 */

import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

interface BackupData {
  voiceCharacters: any[];
  audioGenerations: any[];
  avatarGenerationHistory: any[];
  voiceDemos: any[];
  languageCharacterAvatars: any[];
}

async function backupExistingData(): Promise<BackupData> {
  console.log('📦 备份现有数据...');

  // 备份VoiceCharacter数据
  const voiceCharacters = await prisma.$queryRaw`
    SELECT * FROM "VoiceCharacter" ORDER BY "sortOrder", "characterName"
  `;

  // 备份AudioGeneration数据
  const audioGenerations = await prisma.$queryRaw`
    SELECT * FROM "AudioGeneration" ORDER BY "createdAt"
  `;

  // 备份AvatarGenerationHistory数据
  const avatarGenerationHistory = await prisma.$queryRaw`
    SELECT * FROM "AvatarGenerationHistory" ORDER BY "createdAt"
  `;

  // 备份VoiceDemo数据
  const voiceDemos = await prisma.$queryRaw`
    SELECT * FROM "voice_demos" ORDER BY "createdAt"
  `;

  // 备份LanguageCharacterAvatar数据
  const languageCharacterAvatars = await prisma.$queryRaw`
    SELECT * FROM "LanguageCharacterAvatar" ORDER BY "createdAt"
  `;

  const backupData: BackupData = {
    voiceCharacters: voiceCharacters as any[],
    audioGenerations: audioGenerations as any[],
    avatarGenerationHistory: avatarGenerationHistory as any[],
    voiceDemos: voiceDemos as any[],
    languageCharacterAvatars: languageCharacterAvatars as any[],
  };

  // 保存备份文件
  const backupDir = path.join(process.cwd(), 'backups');
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFile = path.join(backupDir, `backup-${timestamp}.json`);
  
  fs.writeFileSync(backupFile, JSON.stringify(backupData, null, 2));
  
  console.log(`✅ 数据备份完成: ${backupFile}`);
  console.log(`   VoiceCharacter: ${backupData.voiceCharacters.length} 条记录`);
  console.log(`   AudioGeneration: ${backupData.audioGenerations.length} 条记录`);
  console.log(`   AvatarGenerationHistory: ${backupData.avatarGenerationHistory.length} 条记录`);
  console.log(`   VoiceDemo: ${backupData.voiceDemos.length} 条记录`);
  console.log(`   LanguageCharacterAvatar: ${backupData.languageCharacterAvatars.length} 条记录`);

  return backupData;
}

async function step1_CreateNewTables() {
  console.log('\n🔧 步骤1: 创建新表结构...');

  try {
    // 创建API角色模板表
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "api_character_templates" (
        "id" TEXT NOT NULL,
        "apiProvider" "ApiProvider" NOT NULL,
        "apiVoiceName" TEXT NOT NULL,
        "originalName" TEXT NOT NULL,
        "gender" "Gender" NOT NULL,
        "defaultStyle" TEXT,
        "defaultDescription" TEXT,
        "isActive" BOOLEAN NOT NULL DEFAULT true,
        "sortOrder" INTEGER NOT NULL DEFAULT 0,
        "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

        CONSTRAINT "api_character_templates_pkey" PRIMARY KEY ("id")
      );
    `;

    await prisma.$executeRaw`
      CREATE UNIQUE INDEX IF NOT EXISTS "api_character_templates_apiVoiceName_key" 
      ON "api_character_templates"("apiVoiceName");
    `;

    await prisma.$executeRaw`
      CREATE INDEX IF NOT EXISTS "api_character_templates_apiProvider_idx" 
      ON "api_character_templates"("apiProvider");
    `;

    await prisma.$executeRaw`
      CREATE INDEX IF NOT EXISTS "api_character_templates_isActive_idx" 
      ON "api_character_templates"("isActive");
    `;

    // 创建语言角色表
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "language_characters" (
        "id" TEXT NOT NULL,
        "languageId" TEXT NOT NULL,
        "templateId" TEXT,
        "name" TEXT NOT NULL,
        "description" TEXT NOT NULL,
        "style" TEXT,
        "personality" TEXT,
        "bestFor" TEXT,
        "avatarUrl" TEXT,
        "isActive" BOOLEAN NOT NULL DEFAULT true,
        "sortOrder" INTEGER NOT NULL DEFAULT 0,
        "isCustom" BOOLEAN NOT NULL DEFAULT false,
        "createdBy" TEXT,
        "customSettings" TEXT,
        "customPrompt" TEXT,
        "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

        CONSTRAINT "language_characters_pkey" PRIMARY KEY ("id")
      );
    `;

    await prisma.$executeRaw`
      CREATE UNIQUE INDEX IF NOT EXISTS "language_characters_languageId_templateId_key" 
      ON "language_characters"("languageId", "templateId");
    `;

    await prisma.$executeRaw`
      CREATE INDEX IF NOT EXISTS "language_characters_languageId_idx" 
      ON "language_characters"("languageId");
    `;

    await prisma.$executeRaw`
      CREATE INDEX IF NOT EXISTS "language_characters_templateId_idx" 
      ON "language_characters"("templateId");
    `;

    console.log('✅ 新表结构创建完成');
  } catch (error) {
    console.error('❌ 创建新表失败:', error);
    throw error;
  }
}

async function step2_MigrateData(backupData: BackupData) {
  console.log('\n📊 步骤2: 迁移数据...');

  try {
    // 获取所有语言
    const languages = await prisma.language.findMany({
      orderBy: { sortOrder: 'asc' }
    });

    console.log(`   找到 ${languages.length} 种语言`);

    // 创建API角色模板
    const templateMap = new Map<string, string>();
    
    for (const oldChar of backupData.voiceCharacters) {
      if (!oldChar.isCustom) { // 只为系统角色创建模板
        try {
          const templateId = `template_${oldChar.id}`;

          // 检查是否已存在
          const existingTemplate = await prisma.$queryRaw`
            SELECT id FROM "api_character_templates" WHERE "apiVoiceName" = ${oldChar.apiVoiceName}
          ` as any[];

          if (existingTemplate.length === 0) {
            await prisma.$executeRaw`
              INSERT INTO "api_character_templates" (
                "id", "apiProvider", "apiVoiceName", "originalName", "gender",
                "defaultStyle", "defaultDescription", "isActive", "sortOrder"
              ) VALUES (
                ${templateId}, ${oldChar.apiProvider}::"ApiProvider", ${oldChar.apiVoiceName},
                ${oldChar.originalName}, ${oldChar.gender}::"Gender", ${oldChar.style},
                ${oldChar.description}, ${oldChar.isActive}, ${oldChar.sortOrder}
              )
            `;
          }

          templateMap.set(oldChar.id, templateId);
          console.log(`   ✅ 创建模板: ${oldChar.originalName} (${oldChar.apiVoiceName})`);
        } catch (error) {
          console.error(`   ❌ 创建模板失败: ${oldChar.originalName}`, error);
        }
      }
    }

    // 为每个语言创建角色实例
    for (const language of languages) {
      console.log(`\n   处理语言: ${language.name} (${language.code})`);
      
      for (const oldChar of backupData.voiceCharacters) {
        if (oldChar.isCustom) continue; // 跳过自定义角色

        const templateId = templateMap.get(oldChar.id);
        if (!templateId) continue;

        try {
          // 解析多语言数据
          const multilingualNames = oldChar.multilingualNames ? 
            JSON.parse(oldChar.multilingualNames) : {};
          const multilingualDescriptions = oldChar.multilingualDescriptions ? 
            JSON.parse(oldChar.multilingualDescriptions) : {};
          const multilingualStyles = oldChar.multilingualStyles ? 
            JSON.parse(oldChar.multilingualStyles) : {};
          const multilingualPersonalities = oldChar.multilingualPersonalities ? 
            JSON.parse(oldChar.multilingualPersonalities) : {};
          const multilingualBestFor = oldChar.multilingualBestFor ? 
            JSON.parse(oldChar.multilingualBestFor) : {};

          // 获取该语言的本土化信息
          const localizedName = multilingualNames[language.code] || 
                               (language.code === 'en-US' ? oldChar.characterNameEn : oldChar.characterName);
          const localizedDescription = multilingualDescriptions[language.code] || oldChar.description;
          const localizedStyle = multilingualStyles[language.code] || oldChar.style;
          const localizedPersonality = multilingualPersonalities[language.code] || oldChar.personality;
          const localizedBestFor = multilingualBestFor[language.code] || oldChar.bestFor;

          await prisma.$executeRaw`
            INSERT INTO "language_characters" (
              "id", "languageId", "templateId", "name", "description", "style",
              "personality", "bestFor", "avatarUrl", "isActive", "sortOrder",
              "isCustom", "createdBy", "customSettings", "customPrompt"
            ) VALUES (
              ${`lang_char_${oldChar.id}_${language.id}`}, ${language.id}, ${templateId},
              ${localizedName}, ${localizedDescription}, ${localizedStyle},
              ${localizedPersonality}, ${localizedBestFor}, ${oldChar.avatarUrl},
              ${oldChar.isActive}, ${oldChar.sortOrder}, false, ${oldChar.createdBy},
              ${oldChar.customSettings}, ${oldChar.customPrompt}
            )
            ON CONFLICT ("languageId", "templateId") DO NOTHING
          `;

          console.log(`     ✅ 创建角色: ${localizedName}`);
        } catch (error) {
          console.error(`     ❌ 创建角色失败: ${oldChar.characterName}`, error);
        }
      }
    }

    console.log('✅ 数据迁移完成');
  } catch (error) {
    console.error('❌ 数据迁移失败:', error);
    throw error;
  }
}

async function step3_UpdateReferences(backupData: BackupData) {
  console.log('\n🔗 步骤3: 更新外键引用...');

  try {
    // 这一步我们先跳过，因为需要先完成schema更新
    console.log('   暂时跳过外键更新，等待schema更新完成');
  } catch (error) {
    console.error('❌ 更新外键引用失败:', error);
    throw error;
  }
}

async function main() {
  try {
    console.log('🚀 开始安全数据迁移...');

    // 步骤1: 备份数据
    const backupData = await backupExistingData();

    // 步骤2: 创建新表
    await step1_CreateNewTables();

    // 步骤3: 迁移数据
    await step2_MigrateData(backupData);

    // 步骤4: 更新引用（暂时跳过）
    await step3_UpdateReferences(backupData);

    console.log('\n✅ 数据迁移完成！');
    console.log('\n📋 下一步操作:');
    console.log('1. 运行 npx prisma db push --accept-data-loss 来更新schema');
    console.log('2. 运行后续的数据清理脚本');

  } catch (error) {
    console.error('❌ 迁移失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 直接运行main函数
main();

export { main as backupAndMigrate };
