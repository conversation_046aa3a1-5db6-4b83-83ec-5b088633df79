/**
 * 数据库重构迁移脚本
 * 将现有的VoiceCharacter结构迁移到新的API模板+语言角色结构
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface OldVoiceCharacter {
  id: string;
  characterName: string;
  characterNameEn: string;
  multilingualNames?: string;
  originalName: string;
  apiProvider: string;
  apiVoiceName: string;
  gender: string;
  description: string;
  multilingualDescriptions?: string;
  style?: string;
  multilingualStyles?: string;
  personality?: string;
  multilingualPersonalities?: string;
  bestFor?: string;
  multilingualBestFor?: string;
  avatarUrl?: string;
  isActive: boolean;
  sortOrder: number;
  isCustom: boolean;
  createdBy?: string;
  customSettings?: string;
  customPrompt?: string;
  createdAt: Date;
  updatedAt: Date;
}

async function migrateToNewStructure() {
  console.log('🚀 开始数据库结构迁移...');

  try {
    // 1. 获取所有现有的语音角色
    console.log('📊 获取现有语音角色数据...');
    const oldCharacters = await prisma.$queryRaw<OldVoiceCharacter[]>`
      SELECT * FROM "VoiceCharacter" ORDER BY "sortOrder", "characterName"
    `;
    
    console.log(`   找到 ${oldCharacters.length} 个现有角色`);

    // 2. 获取所有语言
    const languages = await prisma.language.findMany({
      orderBy: { sortOrder: 'asc' }
    });
    
    console.log(`   找到 ${languages.length} 种语言`);

    // 3. 创建API角色模板
    console.log('🔧 创建API角色模板...');
    const templateMap = new Map<string, string>();
    
    for (const oldChar of oldCharacters) {
      if (!oldChar.isCustom) { // 只为系统角色创建模板
        try {
          const template = await prisma.apiCharacterTemplate.create({
            data: {
              apiProvider: oldChar.apiProvider as any,
              apiVoiceName: oldChar.apiVoiceName,
              originalName: oldChar.originalName,
              gender: oldChar.gender as any,
              defaultStyle: oldChar.style,
              defaultDescription: oldChar.description,
              isActive: oldChar.isActive,
              sortOrder: oldChar.sortOrder,
            }
          });
          
          templateMap.set(oldChar.id, template.id);
          console.log(`   ✅ 创建模板: ${oldChar.originalName} (${oldChar.apiVoiceName})`);
        } catch (error) {
          console.error(`   ❌ 创建模板失败: ${oldChar.originalName}`, error);
        }
      }
    }

    // 4. 为每个语言创建角色实例
    console.log('🌍 为每种语言创建角色实例...');
    
    for (const language of languages) {
      console.log(`\n   处理语言: ${language.name} (${language.code})`);
      
      for (const oldChar of oldCharacters) {
        if (oldChar.isCustom) continue; // 跳过自定义角色，稍后单独处理
        
        const templateId = templateMap.get(oldChar.id);
        if (!templateId) {
          console.warn(`     ⚠️  找不到模板ID: ${oldChar.characterName}`);
          continue;
        }

        try {
          // 解析多语言数据
          const multilingualNames = oldChar.multilingualNames ? 
            JSON.parse(oldChar.multilingualNames) : {};
          const multilingualDescriptions = oldChar.multilingualDescriptions ? 
            JSON.parse(oldChar.multilingualDescriptions) : {};
          const multilingualStyles = oldChar.multilingualStyles ? 
            JSON.parse(oldChar.multilingualStyles) : {};
          const multilingualPersonalities = oldChar.multilingualPersonalities ? 
            JSON.parse(oldChar.multilingualPersonalities) : {};
          const multilingualBestFor = oldChar.multilingualBestFor ? 
            JSON.parse(oldChar.multilingualBestFor) : {};

          // 获取该语言的本土化信息
          const localizedName = multilingualNames[language.code] || 
                               (language.code === 'en-US' ? oldChar.characterNameEn : oldChar.characterName);
          const localizedDescription = multilingualDescriptions[language.code] || oldChar.description;
          const localizedStyle = multilingualStyles[language.code] || oldChar.style;
          const localizedPersonality = multilingualPersonalities[language.code] || oldChar.personality;
          const localizedBestFor = multilingualBestFor[language.code] || oldChar.bestFor;

          const languageCharacter = await prisma.languageCharacter.create({
            data: {
              languageId: language.id,
              templateId: templateId,
              name: localizedName,
              description: localizedDescription,
              style: localizedStyle,
              personality: localizedPersonality,
              bestFor: localizedBestFor,
              avatarUrl: oldChar.avatarUrl,
              isActive: oldChar.isActive,
              sortOrder: oldChar.sortOrder,
              isCustom: false,
              createdBy: oldChar.createdBy,
              customSettings: oldChar.customSettings,
              customPrompt: oldChar.customPrompt,
            }
          });

          console.log(`     ✅ 创建角色: ${localizedName}`);
        } catch (error) {
          console.error(`     ❌ 创建角色失败: ${oldChar.characterName}`, error);
        }
      }
    }

    // 5. 处理自定义角色
    console.log('\n🎨 处理自定义角色...');
    const customCharacters = oldCharacters.filter(char => char.isCustom);
    
    for (const customChar of customCharacters) {
      // 自定义角色需要特殊处理，可能需要根据创建者的语言偏好来决定放在哪个语言下
      // 这里简化处理，放在中文语言下
      const chineseLanguage = languages.find(lang => lang.code === 'zh-CN');
      if (!chineseLanguage) continue;

      try {
        await prisma.languageCharacter.create({
          data: {
            languageId: chineseLanguage.id,
            templateId: null, // 自定义角色没有模板
            name: customChar.characterName,
            description: customChar.description,
            style: customChar.style,
            personality: customChar.personality,
            bestFor: customChar.bestFor,
            avatarUrl: customChar.avatarUrl,
            isActive: customChar.isActive,
            sortOrder: customChar.sortOrder,
            isCustom: true,
            createdBy: customChar.createdBy,
            customSettings: customChar.customSettings,
            customPrompt: customChar.customPrompt,
          }
        });

        console.log(`   ✅ 迁移自定义角色: ${customChar.characterName}`);
      } catch (error) {
        console.error(`   ❌ 迁移自定义角色失败: ${customChar.characterName}`, error);
      }
    }

    console.log('\n✅ 数据迁移完成！');
    console.log('\n📊 迁移统计:');
    
    const templateCount = await prisma.apiCharacterTemplate.count();
    const languageCharacterCount = await prisma.languageCharacter.count();
    
    console.log(`   API模板数量: ${templateCount}`);
    console.log(`   语言角色数量: ${languageCharacterCount}`);
    
    // 验证数据完整性
    console.log('\n🔍 验证数据完整性...');
    for (const language of languages) {
      const charCount = await prisma.languageCharacter.count({
        where: { languageId: language.id }
      });
      console.log(`   ${language.name}: ${charCount} 个角色`);
    }

  } catch (error) {
    console.error('❌ 迁移过程中出错:', error);
    throw error;
  }
}

async function main() {
  try {
    await migrateToNewStructure();
  } catch (error) {
    console.error('迁移失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

export { migrateToNewStructure };
