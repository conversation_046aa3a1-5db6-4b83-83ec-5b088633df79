import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 检查ApiCharacterTemplate中性别字段的当前状态
 * 对比原始种子数据，找出被修改的模板
 */

// 原始种子数据中的性别信息（从03-voice-characters.ts提取）
const ORIGINAL_GENDER_DATA = {
  'Zephyr': 'FEMALE',
  'Leda': 'FEMALE', 
  'Aoede': 'FEMALE',
  'Callirrhoe': 'FEMALE',
  'Dione': 'FEMALE',
  'Elara': 'FEMALE',
  'Io': 'FEMALE',
  'Europa': 'FEMALE',
  'Ganymede': 'FEMALE',
  'Hera': 'FEMALE',
  'Hestia': 'FEMALE',
  'Kalliope': 'FEMALE',
  'Melpomene': 'FEMALE',
  'Thalia': 'FEMALE',
  'Enceladus': 'FEMALE',
  
  'Puck': 'MALE',
  '<PERSON><PERSON>': 'MALE',
  '<PERSON>re': 'MALE',
  '<PERSON><PERSON><PERSON>': '<PERSON><PERSON>',
  '<PERSON><PERSON>': 'MALE',
  '<PERSON><PERSON><PERSON><PERSON>': '<PERSON><PERSON>',
  'Loki': '<PERSON>LE',
  'Odin': 'MALE',
  'Thor': 'MALE',
  'Tyr': 'MALE',
  'Ullr': 'MALE',
  'Vidar': 'MALE',
  'Balder': 'MALE',
  'Zubenelgenubi': 'MALE',
  'Sadaltager': 'MALE'
};

async function checkTemplateGenderChanges() {
  console.log('🔍 检查ApiCharacterTemplate性别字段变更...\n');

  try {
    // 获取所有API模板
    const templates = await prisma.apiCharacterTemplate.findMany({
      orderBy: { sortOrder: 'asc' }
    });

    console.log(`📊 找到 ${templates.length} 个API模板\n`);

    let changedCount = 0;
    const changedTemplates = [];

    console.log('📋 性别变更对比：');
    console.log('角色名称'.padEnd(20) + '原始性别'.padEnd(12) + '当前性别'.padEnd(12) + '状态');
    console.log('-'.repeat(60));

    for (const template of templates) {
      const originalGender = ORIGINAL_GENDER_DATA[template.originalName];
      const currentGender = template.gender;
      
      if (originalGender && originalGender !== currentGender) {
        changedCount++;
        changedTemplates.push({
          id: template.id,
          originalName: template.originalName,
          apiVoiceName: template.apiVoiceName,
          originalGender,
          currentGender,
          defaultStyle: template.defaultStyle,
          defaultDescription: template.defaultDescription
        });
        
        console.log(
          template.originalName.padEnd(20) + 
          originalGender.padEnd(12) + 
          currentGender.padEnd(12) + 
          '🔄 已变更'
        );
      } else {
        console.log(
          template.originalName.padEnd(20) + 
          (originalGender || 'N/A').padEnd(12) + 
          currentGender.padEnd(12) + 
          '✅ 未变更'
        );
      }
    }

    console.log('\n' + '='.repeat(60));
    console.log(`📈 统计结果：`);
    console.log(`   总模板数: ${templates.length}`);
    console.log(`   已变更性别: ${changedCount}`);
    console.log(`   未变更: ${templates.length - changedCount}`);

    if (changedCount > 0) {
      console.log('\n🎯 需要更新的模板详情：');
      for (const template of changedTemplates) {
        console.log(`\n📝 ${template.originalName} (${template.apiVoiceName})`);
        console.log(`   ID: ${template.id}`);
        console.log(`   性别变更: ${template.originalGender} → ${template.currentGender}`);
        console.log(`   当前风格: ${template.defaultStyle || 'N/A'}`);
        console.log(`   当前描述: ${template.defaultDescription || 'N/A'}`);
      }

      // 检查对应的语言角色数量
      console.log('\n🌍 检查关联的语言角色...');
      for (const template of changedTemplates) {
        const languageCharacterCount = await prisma.languageCharacter.count({
          where: { templateId: template.id }
        });
        console.log(`   ${template.originalName}: ${languageCharacterCount} 个语言角色需要更新`);
      }
    }

    return changedTemplates;

  } catch (error) {
    console.error('❌ 检查过程中出错:', error);
    throw error;
  }
}

async function main() {
  try {
    const changedTemplates = await checkTemplateGenderChanges();
    
    if (changedTemplates.length > 0) {
      console.log('\n💡 下一步建议：');
      console.log('1. 为变更的模板重新生成描述和风格');
      console.log('2. 更新所有关联语言角色的本土化内容');
      console.log('3. 确保性别字段在LanguageCharacter中的一致性');
    } else {
      console.log('\n✅ 没有发现性别变更，无需更新');
    }
    
  } catch (error) {
    console.error('❌ 执行失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { checkTemplateGenderChanges };
