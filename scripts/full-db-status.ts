import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('📊 数据库完整状态检查...\n');

  try {
    // 检查用户数量
    const userCount = await prisma.user.count();
    console.log(`👥 用户总数: ${userCount}`);

    // 检查语言数量
    const languageCount = await prisma.language.count();
    console.log(`🌍 语言总数: ${languageCount}`);

    // 检查语音角色数量
    const characterCount = await prisma.voiceCharacter.count();
    console.log(`🎭 语音角色总数: ${characterCount}`);

    // 检查风格分类数量
    const categoryCount = await prisma.styleCategory.count();
    console.log(`🏷️  风格分类总数: ${categoryCount}`);

    // 检查系统风格数量
    const styleCount = await prisma.userStyle.count({
      where: { isOfficial: true }
    });
    console.log(`🎨 系统官方风格总数: ${styleCount}`);

    // 检查字符包数量
    const packageCount = await prisma.characterPackage.count();
    console.log(`📦 字符包总数: ${packageCount}`);

    // 检查API配置数量
    const apiConfigCount = await prisma.apiProviderConfig.count();
    console.log(`🔧 API配置总数: ${apiConfigCount}`);

    // 检查API定价配置数量
    const apiPricingCount = await prisma.apiPricingConfig.count();
    console.log(`💰 API定价配置总数: ${apiPricingCount}`);

    // 检查对话模板数量
    const templateCount = await prisma.conversationTemplate.count();
    console.log(`💬 对话模板总数: ${templateCount}`);

    // 检查语音试听数量
    const demoCount = await prisma.voiceDemo.count();
    console.log(`🎵 语音试听总数: ${demoCount}`);

    console.log('\n✅ 数据库连接正常，所有表都已创建！');

    // 显示字符包详情
    console.log('\n📋 字符包详情:');
    const packages = await prisma.characterPackage.findMany({
      orderBy: { sortOrder: 'asc' }
    });
    
    packages.forEach(pkg => {
      const discountText = pkg.discount ? ` (${pkg.discount}% 折扣)` : '';
      console.log(`  - ${pkg.name}${discountText} - $${pkg.price} - ${pkg.characters} 字符 - ${pkg.type}`);
    });

    // 显示API配置详情
    console.log('\n🔌 API配置详情:');
    const apiConfigs = await prisma.apiProviderConfig.findMany();
    apiConfigs.forEach(config => {
      console.log(`  - ${config.displayName} (${config.provider}) - ${config.status} - 优先级: ${config.priority}`);
    });

  } catch (error) {
    console.error('❌ 数据库检查失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();