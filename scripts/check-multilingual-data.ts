/**
 * 检查多语言数据脚本
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkMultilingualData() {
  console.log('🔍 检查多语言数据...');

  try {
    // 获取前5个角色的多语言数据
    const characters = await prisma.voiceCharacter.findMany({
      take: 5,
      select: {
        id: true,
        characterName: true,
        style: true,
        multilingualStyles: true,
        personality: true,
        multilingualPersonalities: true,
        description: true,
        multilingualDescriptions: true,
        bestFor: true,
        multilingualBestFor: true,
      },
    });

    console.log(`📊 找到 ${characters.length} 个角色`);

    for (const character of characters) {
      console.log(`\n📝 角色: ${character.characterName}`);
      console.log(`   风格: ${character.style}`);
      console.log(`   多语言风格: ${character.multilingualStyles}`);
      
      if (character.multilingualStyles) {
        try {
          const parsed = JSON.parse(character.multilingualStyles);
          console.log(`   解析后的多语言风格:`, parsed);
        } catch (error) {
          console.log(`   ❌ 解析多语言风格失败: ${error}`);
        }
      }
    }

  } catch (error) {
    console.error('❌ 检查多语言数据时出错:', error);
    throw error;
  }
}

async function main() {
  try {
    await checkMultilingualData();
  } catch (error) {
    console.error('脚本执行失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
