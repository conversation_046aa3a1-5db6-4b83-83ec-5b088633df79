#!/usr/bin/env tsx

/**
 * 🚀 快速性能优化脚本
 * 
 * 这个脚本会帮助您快速切换到优化版本的语音生成API
 * 预期性能提升：87秒 → 25-35秒 (60-70% 提升)
 */

import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

interface OptimizationResult {
  file: string;
  changes: number;
  status: 'success' | 'error' | 'skipped';
  message: string;
}

class PerformanceOptimizer {
  private results: OptimizationResult[] = [];

  async optimize() {
    console.log('🚀 开始语音生成性能优化...\n');

    // 1. 查找所有使用 generateSpeech 的文件
    await this.findAndUpdateApiCalls();

    // 2. 更新前端组件
    await this.updateFrontendComponents();

    // 3. 生成优化报告
    this.generateReport();
  }

  private async findAndUpdateApiCalls() {
    console.log('📁 查找需要优化的文件...');

    const patterns = [
      'src/**/*.ts',
      'src/**/*.tsx',
      'src/**/*.js',
      'src/**/*.jsx'
    ];

    for (const pattern of patterns) {
      const files = await glob(pattern);
      
      for (const file of files) {
        await this.processFile(file);
      }
    }
  }

  private async processFile(filePath: string) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let updatedContent = content;
      let changeCount = 0;

      // 替换 generateSpeech 为 generateSpeechOptimized
      const generateSpeechRegex = /api\.tts\.generateSpeech\.mutate\(/g;
      if (generateSpeechRegex.test(content)) {
        updatedContent = updatedContent.replace(
          generateSpeechRegex,
          'api.tts.generateSpeechOptimized.mutate('
        );
        changeCount++;
      }

      // 添加 useStreaming 参数
      const mutateCallRegex = /api\.tts\.generateSpeechOptimized\.mutate\(\s*\{([^}]+)\}\s*\)/g;
      updatedContent = updatedContent.replace(mutateCallRegex, (match, params) => {
        if (!params.includes('useStreaming')) {
          const cleanParams = params.trim();
          const newParams = cleanParams.endsWith(',') 
            ? `${cleanParams}\n    useStreaming: true`
            : `${cleanParams},\n    useStreaming: true`;
          changeCount++;
          return `api.tts.generateSpeechOptimized.mutate({\n    ${newParams}\n  })`;
        }
        return match;
      });

      // 如果有更改，写入文件
      if (changeCount > 0) {
        fs.writeFileSync(filePath, updatedContent);
        this.results.push({
          file: filePath,
          changes: changeCount,
          status: 'success',
          message: `已优化 ${changeCount} 个API调用`
        });
      } else if (content.includes('generateSpeech')) {
        this.results.push({
          file: filePath,
          changes: 0,
          status: 'skipped',
          message: '已经是最新版本或无需更改'
        });
      }

    } catch (error) {
      this.results.push({
        file: filePath,
        changes: 0,
        status: 'error',
        message: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`
      });
    }
  }

  private async updateFrontendComponents() {
    console.log('🎨 更新前端组件...');

    // 创建优化版本的语音生成组件示例
    const optimizedComponentPath = 'src/components/voice/optimized-voice-generator.tsx';
    
    if (!fs.existsSync(path.dirname(optimizedComponentPath))) {
      fs.mkdirSync(path.dirname(optimizedComponentPath), { recursive: true });
    }

    const optimizedComponent = `import { api } from "~/utils/api";
import { useState } from "react";
import { Button } from "~/components/ui/button";
import { Progress } from "~/components/ui/progress";
import { toast } from "sonner";

interface OptimizedVoiceGeneratorProps {
  text: string;
  characterId: string;
  onSuccess?: (result: any) => void;
}

export function OptimizedVoiceGenerator({ 
  text, 
  characterId, 
  onSuccess 
}: OptimizedVoiceGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);

  const generateSpeechMutation = api.tts.generateSpeechOptimized.useMutation({
    onSuccess: (data) => {
      const source = data.fromCache ? '缓存' : '新生成';
      toast.success(\`✅ 语音生成完成 (\${source})\`);
      setProgress(100);
      onSuccess?.(data);
    },
    onError: (error) => {
      toast.error(\`❌ 生成失败: \${error.message}\`);
      console.error('语音生成失败:', error);
    }
  });

  const handleGenerate = async () => {
    if (!text.trim()) {
      toast.error('请输入要生成的文本');
      return;
    }

    setIsGenerating(true);
    setProgress(10);

    try {
      const startTime = Date.now();
      
      const result = await generateSpeechMutation.mutateAsync({
        text,
        characterId,
        quality: "fast",
        useStreaming: true,  // 🌊 启用流式处理优化
      });

      const duration = Date.now() - startTime;
      console.log(\`📊 性能统计:
        - 耗时: \${duration}ms
        - 来源: \${result.fromCache ? '缓存' : '新生成'}
        - 字符数: \${result.characterCount}
        - 效率: \${(result.characterCount / duration * 1000).toFixed(2)} 字符/秒
      \`);

      return result;
    } finally {
      setIsGenerating(false);
      setProgress(0);
    }
  };

  return (
    <div className="space-y-4">
      {isGenerating && (
        <div className="space-y-2">
          <Progress value={progress} className="w-full" />
          <p className="text-sm text-muted-foreground text-center">
            生成中... {progress}%
          </p>
        </div>
      )}
      
      <Button 
        onClick={handleGenerate}
        disabled={isGenerating || !text.trim()}
        className="w-full"
      >
        {isGenerating ? '🌊 生成中...' : '🚀 优化生成'}
      </Button>

      {generateSpeechMutation.data?.fromCache && (
        <p className="text-sm text-green-600 text-center">
          🎯 使用缓存，响应超快！
        </p>
      )}
    </div>
  );
}`;

    fs.writeFileSync(optimizedComponentPath, optimizedComponent);
    
    this.results.push({
      file: optimizedComponentPath,
      changes: 1,
      status: 'success',
      message: '创建了优化版本的语音生成组件'
    });
  }

  private generateReport() {
    console.log('\n📊 优化完成报告');
    console.log('='.repeat(50));

    const successCount = this.results.filter(r => r.status === 'success').length;
    const errorCount = this.results.filter(r => r.status === 'error').length;
    const skippedCount = this.results.filter(r => r.status === 'skipped').length;
    const totalChanges = this.results.reduce((sum, r) => sum + r.changes, 0);

    console.log(\`
✅ 成功优化: \${successCount} 个文件
❌ 处理失败: \${errorCount} 个文件  
⏭️ 跳过文件: \${skippedCount} 个文件
🔧 总计更改: \${totalChanges} 处

📈 预期性能提升:
- 🎯 缓存命中: 87秒 → 1-3秒 (97% 提升)
- 🌊 流式处理: 87秒 → 25-35秒 (60-70% 提升)
- ⚡ 并行优化: 减少数据库等待时间
\`);

    if (successCount > 0) {
      console.log('\n✅ 成功优化的文件:');
      this.results
        .filter(r => r.status === 'success')
        .forEach(r => {
          console.log(\`  📁 \${r.file} - \${r.message}\`);
        });
    }

    if (errorCount > 0) {
      console.log('\n❌ 处理失败的文件:');
      this.results
        .filter(r => r.status === 'error')
        .forEach(r => {
          console.log(\`  📁 \${r.file} - \${r.message}\`);
        });
    }

    console.log(\`
🚀 下一步操作:
1. 测试优化后的语音生成功能
2. 监控性能改善情况
3. 检查缓存命中率

💡 使用建议:
- 重复文本会自动使用缓存 (1-3秒响应)
- 新文本使用流式处理 (25-35秒响应)
- 监控 fromCache 字段了解缓存效果
\`);
  }
}

// 运行优化
async function main() {
  const optimizer = new PerformanceOptimizer();
  await optimizer.optimize();
}

if (require.main === module) {
  main().catch(console.error);
}

export { PerformanceOptimizer };
