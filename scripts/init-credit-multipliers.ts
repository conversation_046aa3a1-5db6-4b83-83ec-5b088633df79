import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function initCreditMultipliers() {
  console.log('🔧 初始化积分倍率配置...\n');

  try {
    // 获取或创建API定价配置
    let config = await prisma.apiPricingConfig.findFirst({
      where: { isActive: true }
    });

    if (!config) {
      console.log('📝 创建新的API定价配置...');
      config = await prisma.apiPricingConfig.create({
        data: {
          // 原始价格配置 (每1M tokens的USD价格)
          textGenerationInputPrice: 1.00,   // 文本输入 $1.00/1M tokens
          textGenerationOutputPrice: 1.80,  // 文本输出 $1.80/1M tokens
          standardInputPrice: 1.00,         // TTS标准输入 $1.00/1M tokens
          standardOutputPrice: 19.90,       // TTS标准输出 $19.90/1M tokens
          professionalInputPrice: 1.00,     // TTS高质量输入 $1.00/1M tokens
          professionalOutputPrice: 39.80,   // TTS高质量输出 $39.80/1M tokens
          
          // 加权tokens倍率配置 (基于textGenerationInputPrice作为基准)
          textInputMultiplier: 1.0,         // M_text_in = 1.00 / 1.00 = 1.0
          textOutputMultiplier: 1.8,        // M_text_out = 1.80 / 1.00 = 1.8
          ttsInputMultiplier: 1.0,          // M_tts_in = 1.00 / 1.00 = 1.0
          ttsOutputMultiplier: 19.9,        // M_tts_out = 19.90 / 1.00 = 19.9
          ttsProfessionalMultiplier: 39.8,  // M_tts_professional = 39.80 / 1.00 = 39.8
          
          isActive: true,
          description: '基于加权tokens的积分计算配置 v3.0'
        }
      });
    } else {
      console.log('📝 更新现有的API定价配置...');
      config = await prisma.apiPricingConfig.update({
        where: { id: config.id },
        data: {
          // 更新倍率配置
          textInputMultiplier: 1.0,         // M_text_in
          textOutputMultiplier: 1.8,        // M_text_out  
          ttsInputMultiplier: 1.0,          // M_tts_in
          ttsOutputMultiplier: 19.9,        // M_tts_out (标准)
          ttsProfessionalMultiplier: 39.8,  // M_tts_professional (高质量)
          
          description: '基于加权tokens的积分计算配置 v3.0'
        }
      });
    }

    console.log('✅ 积分倍率配置已更新:');
    console.log(`   文本输入倍率: ${config.textInputMultiplier}`);
    console.log(`   文本输出倍率: ${config.textOutputMultiplier}`);
    console.log(`   TTS输入倍率: ${config.ttsInputMultiplier}`);
    console.log(`   TTS标准输出倍率: ${config.ttsOutputMultiplier}`);
    console.log(`   TTS高质量输出倍率: ${config.ttsProfessionalMultiplier}`);

    console.log('\n🧮 计算示例:');
    console.log('   文本生成 (100输入+500输出tokens):');
    const textWeighted = 100 * config.textInputMultiplier + 500 * config.textOutputMultiplier;
    console.log(`     加权tokens = 100×${config.textInputMultiplier} + 500×${config.textOutputMultiplier} = ${textWeighted}`);
    console.log(`     积分需求 = ceil(${textWeighted} / 1000) = ${Math.ceil(textWeighted / 1000)}积分`);

    console.log('\n   TTS标准 (50输入+200输出tokens):');
    const ttsWeighted = 50 * config.ttsInputMultiplier + 200 * config.ttsOutputMultiplier;
    console.log(`     加权tokens = 50×${config.ttsInputMultiplier} + 200×${config.ttsOutputMultiplier} = ${ttsWeighted}`);
    console.log(`     积分需求 = ceil(${ttsWeighted} / 1000) = ${Math.ceil(ttsWeighted / 1000)}积分`);

    console.log('\n   TTS高质量 (50输入+200输出tokens):');
    const ttsProfWeighted = 50 * config.ttsInputMultiplier + 200 * config.ttsProfessionalMultiplier;
    console.log(`     加权tokens = 50×${config.ttsInputMultiplier} + 200×${config.ttsProfessionalMultiplier} = ${ttsProfWeighted}`);
    console.log(`     积分需求 = ceil(${ttsProfWeighted} / 1000) = ${Math.ceil(ttsProfWeighted / 1000)}积分`);

    console.log('\n🎯 新系统优势:');
    console.log('   - 保持"1积分=1000tokens"的外部规则');
    console.log('   - 通过加权tokens精确反映不同服务的成本差异');
    console.log('   - 倍率可灵活调整，适应API价格变化');
    console.log('   - 计算简单透明，用户易于理解');

  } catch (error) {
    console.error('❌ 初始化失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行初始化
initCreditMultipliers().catch(console.error);
