/**
 * 修复风格提示词格式，使其符合Gemini TTS官方规范
 * 
 * 官方格式：
 * - "Say in [style]: [text]"
 * - "Say [adverb]: [text]"
 * - "Make Speaker1 sound [description]: [dialogue]"
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// 风格提示词格式修复规则
const PROMPT_FIXES = [
  // 基本格式修复
  {
    pattern: /^([^,]+) tone,/,
    replacement: 'in a $1 tone,',
    description: '添加 "in a" 前缀'
  },
  {
    pattern: /^([^,]+) and ([^,]+) tone,/,
    replacement: 'with a $1 and $2 tone,',
    description: '添加 "with a" 前缀'
  },
  {
    pattern: /^(excited and happy)$/,
    replacement: 'excited and happy',
    description: '保持简短形容词'
  },
  {
    pattern: /^(tired and bored)$/,
    replacement: 'tired and bored',
    description: '保持简短形容词'
  },
  
  // 特殊情况修复
  {
    pattern: /^soft whisper tone,/,
    replacement: 'in a soft whisper,',
    description: '修复耳语格式'
  },
  {
    pattern: /^clear and bright tone,/,
    replacement: 'with a clear and bright tone,',
    description: '修复清晰明亮格式'
  },
  {
    pattern: /^deep and steady tone,/,
    replacement: 'with a deep and steady tone,',
    description: '修复深沉稳重格式'
  },
];

async function fixStylePrompts() {
  console.log('🔧 开始修复风格提示词格式...\n');

  try {
    // 获取所有风格
    const styles = await prisma.voiceStyle.findMany({
      where: {
        isActive: true,
      },
      orderBy: { createdAt: 'asc' },
    });

    console.log(`📊 找到 ${styles.length} 个风格需要检查\n`);

    let fixedCount = 0;
    let skippedCount = 0;

    for (const style of styles) {
      const originalPrompt = style.prompt;
      let newPrompt = originalPrompt;
      let wasFixed = false;

      // 检查是否已经符合格式
      if (originalPrompt.startsWith('Say ') || 
          originalPrompt.startsWith('Make ') ||
          originalPrompt.match(/^(excited and happy|tired and bored)$/)) {
        console.log(`✅ ${style.name}: 已符合格式`);
        skippedCount++;
        continue;
      }

      // 应用修复规则
      for (const fix of PROMPT_FIXES) {
        if (fix.pattern.test(newPrompt)) {
          newPrompt = newPrompt.replace(fix.pattern, fix.replacement);
          wasFixed = true;
          console.log(`🔧 ${style.name}: ${fix.description}`);
          break;
        }
      }

      // 如果没有匹配的规则，使用默认修复
      if (!wasFixed) {
        // 检查是否是简单的形容词
        if (newPrompt.match(/^[a-z\s]+$/i) && !newPrompt.includes(',')) {
          // 保持简单形容词不变
          console.log(`✅ ${style.name}: 保持简单形容词格式`);
          skippedCount++;
          continue;
        } else {
          // 添加默认前缀
          newPrompt = `in a ${newPrompt}`;
          wasFixed = true;
          console.log(`🔧 ${style.name}: 添加默认 "in a" 前缀`);
        }
      }

      if (wasFixed) {
        // 更新数据库
        await prisma.voiceStyle.update({
          where: { id: style.id },
          data: { prompt: newPrompt },
        });

        console.log(`   原始: "${originalPrompt}"`);
        console.log(`   修复: "${newPrompt}"`);
        console.log('');

        fixedCount++;
      }
    }

    console.log(`\n🎉 修复完成！`);
    console.log(`   修复数量: ${fixedCount}`);
    console.log(`   跳过数量: ${skippedCount}`);
    console.log(`   总计数量: ${styles.length}`);

    // 显示修复后的格式示例
    console.log('\n📝 修复后的格式示例:');
    const updatedStyles = await prisma.voiceStyle.findMany({
      where: { isActive: true },
      take: 5,
      orderBy: { updatedAt: 'desc' },
    });

    updatedStyles.forEach(style => {
      console.log(`   ${style.name}: "${style.prompt}"`);
    });

  } catch (error) {
    console.error('❌ 修复过程中出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 验证修复结果
async function validatePrompts() {
  console.log('\n🔍 验证修复结果...\n');

  const styles = await prisma.voiceStyle.findMany({
    where: { isActive: true },
  });

  const validFormats = [
    /^Say /,
    /^Make /,
    /^in a /,
    /^with a /,
    /^excited and happy$/,
    /^tired and bored$/,
  ];

  let validCount = 0;
  let invalidCount = 0;

  for (const style of styles) {
    const isValid = validFormats.some(pattern => pattern.test(style.prompt));
    
    if (isValid) {
      validCount++;
    } else {
      invalidCount++;
      console.log(`❌ ${style.name}: "${style.prompt}" - 格式可能需要手动调整`);
    }
  }

  console.log(`\n📊 验证结果:`);
  console.log(`   符合格式: ${validCount}`);
  console.log(`   需要调整: ${invalidCount}`);
  console.log(`   符合率: ${Math.round((validCount / styles.length) * 100)}%`);
}

async function main() {
  await fixStylePrompts();
  await validatePrompts();
}

// 运行修复
main().catch(console.error);
