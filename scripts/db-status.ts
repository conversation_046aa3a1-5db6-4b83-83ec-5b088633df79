import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('📊 数据库状态检查...\n');

  try {
    // 检查用户数量
    const userCount = await prisma.user.count();
    console.log(`👥 用户总数: ${userCount}`);

    // 检查语言数量
    const languageCount = await prisma.language.count();
    console.log(`🌍 语言总数: ${languageCount}`);

    // 检查语音角色数量
    const characterCount = await prisma.voiceCharacter.count();
    console.log(`🎭 语音角色总数: ${characterCount}`);

    // 检查风格分类数量
    const categoryCount = await prisma.styleCategory.count();
    console.log(`🏷️  风格分类总数: ${categoryCount}`);

    // 检查系统风格数量
    const styleCount = await prisma.userStyle.count({
      where: { isOfficial: true }
    });
    console.log(`🎨 系统官方风格总数: ${styleCount}`);

    // 检查字符包数量
    const packageCount = await prisma.characterPackage.count();
    console.log(`📦 字符包总数: ${packageCount}`);

    console.log('\n✅ 数据库连接正常，所有表都已创建！');
  } catch (error) {
    console.error('❌ 数据库检查失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();