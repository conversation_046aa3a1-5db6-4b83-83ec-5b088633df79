/**
 * 获取中文语言ID
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function getChineseLanguageId() {
  try {
    const chineseLanguage = await prisma.language.findFirst({
      where: { code: 'zh-CN' },
      select: { id: true, code: true, name: true }
    });

    if (chineseLanguage) {
      console.log('中文语言信息:');
      console.log(`ID: ${chineseLanguage.id}`);
      console.log(`代码: ${chineseLanguage.code}`);
      console.log(`名称: ${chineseLanguage.name}`);
    } else {
      console.log('未找到中文语言');
    }
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

getChineseLanguageId();
