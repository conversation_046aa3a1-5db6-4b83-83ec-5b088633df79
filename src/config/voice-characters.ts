// Voctana - 30个国际化语音角色配置
// 基于Google Gemini API的语音角色，重新命名为国际化友好的名称

export interface VoiceCharacter {
  id: string;
  name: string;
  originalName: string; // Google API中的原始名称
  gender: 'male' | 'female' | 'neutral';
  style: string;
  description: string;
  personality: string[];
  bestFor: string[];
}

export const VOICE_CHARACTERS: VoiceCharacter[] = [
  // 女性角色 (1-15)
  {
    id: 'aria',
    name: 'Aria',
    originalName: 'Zephyr',
    gender: 'female',
    style: 'bright',
    description: '明亮清新的女性声音，充满活力',
    personality: ['活泼', '明亮', '清新'],
    bestFor: ['广告', '教育', '儿童内容']
  },
  {
    id: 'luna',
    name: 'Luna',
    originalName: 'Leda',
    gender: 'female',
    style: 'youthful',
    description: '年轻甜美的女性声音，温柔可亲',
    personality: ['年轻', '甜美', '温柔'],
    bestFor: ['故事讲述', '客服', '引导']
  },
  {
    id: 'nova',
    name: 'Nova',
    originalName: 'Aoede',
    gender: 'female',
    style: 'breezy',
    description: '轻松自然的女性声音，如微风般舒适',
    personality: ['轻松', '自然', '舒适'],
    bestFor: ['冥想', '放松', '生活方式']
  },
  {
    id: 'iris',
    name: 'Iris',
    originalName: 'Callirrhoe',
    gender: 'female',
    style: 'casual',
    description: '随意友好的女性声音，平易近人',
    personality: ['友好', '随意', '亲切'],
    bestFor: ['聊天', '社交', '日常对话']
  },
  {
    id: 'stella',
    name: 'Stella',
    originalName: 'Autonoe',
    gender: 'female',
    style: 'bright',
    description: '明亮专业的女性声音，充满自信',
    personality: ['专业', '自信', '明亮'],
    bestFor: ['商务', '演示', '新闻']
  },
  {
    id: 'maya',
    name: 'Maya',
    originalName: 'Despina',
    gender: 'female',
    style: 'smooth',
    description: '流畅优雅的女性声音，温和动听',
    personality: ['优雅', '流畅', '温和'],
    bestFor: ['有声书', '纪录片', '艺术']
  },
  {
    id: 'zara',
    name: 'Zara',
    originalName: 'Erinome',
    gender: 'female',
    style: 'clear',
    description: '清晰准确的女性声音，条理分明',
    personality: ['清晰', '准确', '条理'],
    bestFor: ['教学', '指导', '技术说明']
  },
  {
    id: 'elena',
    name: 'Elena',
    originalName: 'Laomedeia',
    gender: 'female',
    style: 'cheerful',
    description: '欢快愉悦的女性声音，充满正能量',
    personality: ['欢快', '愉悦', '积极'],
    bestFor: ['娱乐', '游戏', '儿童节目']
  },
  {
    id: 'sophia',
    name: 'Sophia',
    originalName: 'Achernar',
    gender: 'female',
    style: 'soft',
    description: '柔和温暖的女性声音，令人安心',
    personality: ['柔和', '温暖', '安心'],
    bestFor: ['治疗', '安慰', '睡前故事']
  },
  {
    id: 'chloe',
    name: 'Chloe',
    originalName: 'Schedar',
    gender: 'female',
    style: 'even',
    description: '平稳均匀的女性声音，稳重可靠',
    personality: ['平稳', '均匀', '可靠'],
    bestFor: ['新闻', '报告', '正式场合']
  },
  {
    id: 'ruby',
    name: 'Ruby',
    originalName: 'Achird',
    gender: 'female',
    style: 'friendly',
    description: '友善亲和的女性声音，容易亲近',
    personality: ['友善', '亲和', '温馨'],
    bestFor: ['客服', '接待', '社区']
  },
  {
    id: 'ivy',
    name: 'Ivy',
    originalName: 'Vindemiatrix',
    gender: 'female',
    style: 'gentle',
    description: '温和细腻的女性声音，体贴入微',
    personality: ['温和', '细腻', '体贴'],
    bestFor: ['心理咨询', '护理', '关怀']
  },
  {
    id: 'grace',
    name: 'Grace',
    originalName: 'Sadachbia',
    gender: 'female',
    style: 'lively',
    description: '生动活泼的女性声音，富有表现力',
    personality: ['生动', '活泼', '表现力'],
    bestFor: ['表演', '戏剧', '动画']
  },
  {
    id: 'aurora',
    name: 'Aurora',
    originalName: 'Pulcherrima',
    gender: 'female',
    style: 'direct',
    description: '直接坦率的女性声音，简洁有力',
    personality: ['直接', '坦率', '有力'],
    bestFor: ['指令', '警告', '紧急通知']
  },
  {
    id: 'vera',
    name: 'Vera',
    originalName: 'Sulafat',
    gender: 'female',
    style: 'warm',
    description: '温暖亲切的女性声音，如春风般和煦',
    personality: ['温暖', '亲切', '和煦'],
    bestFor: ['家庭', '生活', '情感内容']
  },

  // 男性角色 (16-30)
  {
    id: 'alex',
    name: 'Alex',
    originalName: 'Puck',
    gender: 'male',
    style: 'cheerful',
    description: '欢快活泼的男性声音，充满活力',
    personality: ['欢快', '活泼', '有活力'],
    bestFor: ['娱乐', '体育', '青年内容']
  },
  {
    id: 'marcus',
    name: 'Marcus',
    originalName: 'Charon',
    gender: 'male',
    style: 'informative',
    description: '信息丰富的男性声音，知识渊博',
    personality: ['博学', '权威', '专业'],
    bestFor: ['教育', '科学', '学术']
  },
  {
    id: 'victor',
    name: 'Victor',
    originalName: 'Kore',
    gender: 'male',
    style: 'firm',
    description: '坚定有力的男性声音，充满决心',
    personality: ['坚定', '有力', '决断'],
    bestFor: ['领导', '激励', '商务']
  },
  {
    id: 'leo',
    name: 'Leo',
    originalName: 'Fenrir',
    gender: 'male',
    style: 'excitable',
    description: '兴奋激动的男性声音，富有激情',
    personality: ['激情', '兴奋', '动感'],
    bestFor: ['体育', '游戏', '竞技']
  },
  {
    id: 'ethan',
    name: 'Ethan',
    originalName: 'Orus',
    gender: 'male',
    style: 'corporate',
    description: '企业级的男性声音，专业可信',
    personality: ['专业', '可信', '稳重'],
    bestFor: ['企业', '金融', '正式']
  },
  {
    id: 'noah',
    name: 'Noah',
    originalName: 'Enceladus',
    gender: 'male',
    style: 'breathy',
    description: '气声轻柔的男性声音，富有磁性',
    personality: ['磁性', '轻柔', '魅力'],
    bestFor: ['浪漫', '艺术', '情感']
  },
  {
    id: 'daniel',
    name: 'Daniel',
    originalName: 'Iapetus',
    gender: 'male',
    style: 'clear',
    description: '清晰明确的男性声音，条理清楚',
    personality: ['清晰', '明确', '条理'],
    bestFor: ['指导', '教学', '说明']
  },
  {
    id: 'ryan',
    name: 'Ryan',
    originalName: 'Umbriel',
    gender: 'male',
    style: 'easygoing',
    description: '随和轻松的男性声音，平易近人',
    personality: ['随和', '轻松', '友好'],
    bestFor: ['聊天', '休闲', '生活']
  },
  {
    id: 'james',
    name: 'James',
    originalName: 'Algieba',
    gender: 'male',
    style: 'smooth',
    description: '流畅平滑的男性声音，优雅动听',
    personality: ['流畅', '优雅', '动听'],
    bestFor: ['广播', '主持', '媒体']
  },
  {
    id: 'owen',
    name: 'Owen',
    originalName: 'Algenib',
    gender: 'male',
    style: 'gravelly',
    description: '沙哑深沉的男性声音，富有质感',
    personality: ['深沉', '质感', '成熟'],
    bestFor: ['纪录片', '历史', '严肃']
  },
  {
    id: 'lucas',
    name: 'Lucas',
    originalName: 'Rasalgethi',
    gender: 'male',
    style: 'informative',
    description: '信息性强的男性声音，准确可靠',
    personality: ['准确', '可靠', '专业'],
    bestFor: ['新闻', '报告', '分析']
  },
  {
    id: 'mason',
    name: 'Mason',
    originalName: 'Alnilam',
    gender: 'male',
    style: 'firm',
    description: '坚实稳固的男性声音，值得信赖',
    personality: ['坚实', '稳固', '信赖'],
    bestFor: ['安全', '保障', '承诺']
  },
  {
    id: 'blake',
    name: 'Blake',
    originalName: 'Gacrux',
    gender: 'male',
    style: 'mature',
    description: '成熟稳重的男性声音，经验丰富',
    personality: ['成熟', '稳重', '经验'],
    bestFor: ['咨询', '顾问', '指导']
  },
  {
    id: 'kai',
    name: 'Kai',
    originalName: 'Zubenelgenubi',
    gender: 'male',
    style: 'casual',
    description: '随意自然的男性声音，轻松自在',
    personality: ['随意', '自然', '轻松'],
    bestFor: ['日常', '朋友', '非正式']
  },
  {
    id: 'sage',
    name: 'Sage',
    originalName: 'Sadaltager',
    gender: 'male',
    style: 'knowledgeable',
    description: '知识渊博的男性声音，智慧深邃',
    personality: ['智慧', '博学', '深邃'],
    bestFor: ['哲学', '文学', '思考']
  }
];

// 支持的24种语言配置
export interface Language {
  code: string;
  name: string;
  nativeName: string;
  region: string;
  flag: string;
}

export const SUPPORTED_LANGUAGES: Language[] = [
  { code: 'ar-EG', name: 'Arabic (Egypt)', nativeName: 'العربية (مصر)', region: 'Egypt', flag: '🇪🇬' },
  { code: 'de-DE', name: 'German (Germany)', nativeName: 'Deutsch (Deutschland)', region: 'Germany', flag: '🇩🇪' },
  { code: 'en-US', name: 'English (US)', nativeName: 'English (United States)', region: 'United States', flag: '🇺🇸' },
  { code: 'es-US', name: 'Spanish (US)', nativeName: 'Español (Estados Unidos)', region: 'United States', flag: '🇺🇸' },
  { code: 'fr-FR', name: 'French (France)', nativeName: 'Français (France)', region: 'France', flag: '🇫🇷' },
  { code: 'hi-IN', name: 'Hindi (India)', nativeName: 'हिन्दी (भारत)', region: 'India', flag: '🇮🇳' },
  { code: 'id-ID', name: 'Indonesian', nativeName: 'Bahasa Indonesia', region: 'Indonesia', flag: '🇮🇩' },
  { code: 'it-IT', name: 'Italian (Italy)', nativeName: 'Italiano (Italia)', region: 'Italy', flag: '🇮🇹' },
  { code: 'ja-JP', name: 'Japanese (Japan)', nativeName: '日本語 (日本)', region: 'Japan', flag: '🇯🇵' },
  { code: 'ko-KR', name: 'Korean (Korea)', nativeName: '한국어 (대한민국)', region: 'South Korea', flag: '🇰🇷' },
  { code: 'pt-BR', name: 'Portuguese (Brazil)', nativeName: 'Português (Brasil)', region: 'Brazil', flag: '🇧🇷' },
  { code: 'ru-RU', name: 'Russian (Russia)', nativeName: 'Русский (Россия)', region: 'Russia', flag: '🇷🇺' },
  { code: 'nl-NL', name: 'Dutch (Netherlands)', nativeName: 'Nederlands (Nederland)', region: 'Netherlands', flag: '🇳🇱' },
  { code: 'pl-PL', name: 'Polish (Poland)', nativeName: 'Polski (Polska)', region: 'Poland', flag: '🇵🇱' },
  { code: 'th-TH', name: 'Thai (Thailand)', nativeName: 'ไทย (ประเทศไทย)', region: 'Thailand', flag: '🇹🇭' },
  { code: 'tr-TR', name: 'Turkish (Turkey)', nativeName: 'Türkçe (Türkiye)', region: 'Turkey', flag: '🇹🇷' },
  { code: 'vi-VN', name: 'Vietnamese (Vietnam)', nativeName: 'Tiếng Việt (Việt Nam)', region: 'Vietnam', flag: '🇻🇳' },
  { code: 'ro-RO', name: 'Romanian (Romania)', nativeName: 'Română (România)', region: 'Romania', flag: '🇷🇴' },
  { code: 'uk-UA', name: 'Ukrainian (Ukraine)', nativeName: 'Українська (Україна)', region: 'Ukraine', flag: '🇺🇦' },
  { code: 'bn-BD', name: 'Bengali (Bangladesh)', nativeName: 'বাংলা (বাংলাদেশ)', region: 'Bangladesh', flag: '🇧🇩' },
  { code: 'en-IN', name: 'English (India)', nativeName: 'English (India)', region: 'India', flag: '🇮🇳' },
  { code: 'mr-IN', name: 'Marathi (India)', nativeName: 'मराठी (भारत)', region: 'India', flag: '🇮🇳' },
  { code: 'ta-IN', name: 'Tamil (India)', nativeName: 'தமிழ் (இந்தியா)', region: 'India', flag: '🇮🇳' },
  { code: 'te-IN', name: 'Telugu (India)', nativeName: 'తెలుగు (భారతదేశం)', region: 'India', flag: '🇮🇳' },
  { code: 'km-KH', name: 'Khmer (Cambodia)', nativeName: 'ខ្មែរ (កម្ពុជា)', region: 'Cambodia', flag: '🇰🇭' }
];

// 试听文本（每种语言的示例文本）
export const DEMO_TEXTS: Record<string, string> = {
  'ar-EG': 'مرحباً، أنا صوت ذكي يمكنني التحدث بلغات متعددة.',
  'de-DE': 'Hallo, ich bin eine KI-Stimme, die mehrere Sprachen sprechen kann.',
  'en-US': 'Hello, I am an AI voice that can speak multiple languages.',
  'es-US': 'Hola, soy una voz de IA que puede hablar varios idiomas.',
  'fr-FR': 'Bonjour, je suis une voix IA qui peut parler plusieurs langues.',
  'hi-IN': 'नमस्ते, मैं एक AI आवाज हूं जो कई भाषाएं बोल सकती हूं।',
  'id-ID': 'Halo, saya adalah suara AI yang dapat berbicara dalam berbagai bahasa.',
  'it-IT': 'Ciao, sono una voce AI che può parlare più lingue.',
  'ja-JP': 'こんにちは、私は複数の言語を話すことができるAI音声です。',
  'ko-KR': '안녕하세요, 저는 여러 언어를 구사할 수 있는 AI 음성입니다.',
  'pt-BR': 'Olá, eu sou uma voz de IA que pode falar várias línguas.',
  'ru-RU': 'Привет, я голос ИИ, который может говорить на нескольких языках.',
  'nl-NL': 'Hallo, ik ben een AI-stem die meerdere talen kan spreken.',
  'pl-PL': 'Cześć, jestem głosem AI, który może mówić w wielu językach.',
  'th-TH': 'สวัสดี ฉันเป็นเสียง AI ที่สามารถพูดได้หลายภาษา',
  'tr-TR': 'Merhaba, ben birden fazla dil konuşabilen bir AI sesiyim.',
  'vi-VN': 'Xin chào, tôi là giọng nói AI có thể nói nhiều ngôn ngữ.',
  'ro-RO': 'Salut, sunt o voce AI care poate vorbi mai multe limbi.',
  'uk-UA': 'Привіт, я голос ШІ, який може говорити кількома мовами.',
  'bn-BD': 'হ্যালো, আমি একটি AI ভয়েস যা একাধিক ভাষায় কথা বলতে পারে।',
  'en-IN': 'Hello, I am an AI voice that can speak multiple languages.',
  'mr-IN': 'नमस्कार, मी एक AI आवाज आहे जो अनेक भाषा बोलू शकते.',
  'ta-IN': 'வணக்கம், நான் பல மொழிகளில் பேசக்கூடிய AI குரல்.',
  'te-IN': 'హలో, నేను అనేక భాషలలో మాట్లాడగల AI వాయిస్.',
  'km-KH': 'សួស្តី ខ្ញុំជាសំឡេងបញ្ញាសិប្បនិម្មិតដែលអាចនិយាយភាសាជាច្រើន។'
};
