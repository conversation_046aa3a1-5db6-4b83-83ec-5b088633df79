"use client";

import { api } from "~/trpc/react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Progress } from "~/components/ui/progress";
import { Badge } from "~/components/ui/badge";
import { <PERSON><PERSON> } from "~/components/ui/button";
import {
  CreditCard,
  FileAudio,
  Calendar,
  TrendingUp,
  Zap,
  RefreshCw,
  BarChart3
} from "lucide-react";
import { useEffect } from "react";

interface VoiceGenerationStatsProps {
  className?: string;
  showRefresh?: boolean;
  compact?: boolean;
}

export function VoiceGenerationStats({ 
  className = "", 
  showRefresh = false,
  compact = false 
}: VoiceGenerationStatsProps) {
  const { data: stats, isLoading, refetch } = api.tts.getUsageStats.useQuery();

  // 自动刷新统计数据（当有新的语音生成时）
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'voice-generation-update') {
        refetch();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [refetch]);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="animate-pulse">
            <div className="h-4 bg-muted rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-muted rounded w-3/4"></div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            <div className="h-3 bg-muted rounded"></div>
            <div className="h-3 bg-muted rounded w-5/6"></div>
            <div className="h-3 bg-muted rounded w-3/4"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return null;
  }

  const { credits, usagePercentage, monthlyStats } = stats;

  // 确保 monthlyStats 有默认值
  const safeMonthlyStats = monthlyStats || {
    audiosGenerated: 0,
    charactersUsed: 0,
  };
  
  // 计算使用状态
  const getUsageStatus = (percentage: number) => {
    if (percentage >= 90) return { 
      color: 'destructive', 
      label: '积分紧张',
      icon: '🔴'
    };
    if (percentage >= 70) return { 
      color: 'warning', 
      label: '使用较多',
      icon: '🟡'
    };
    if (percentage >= 50) return { 
      color: 'default', 
      label: '使用正常',
      icon: '🟢'
    };
    return { 
      color: 'secondary', 
      label: '使用较少',
      icon: '🟢'
    };
  };

  const usageStatus = getUsageStatus(usagePercentage);

  if (compact) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="space-y-3">
            {/* 积分余额 - 紧凑版 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <CreditCard className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">积分</span>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold">
                  {credits.remaining.toLocaleString()}
                </div>
                <div className="text-xs text-muted-foreground">
                  剩余
                </div>
              </div>
            </div>
            
            <Progress value={usagePercentage} className="h-1.5" />
            
            {/* 本月统计 - 紧凑版 */}
            <div className="grid grid-cols-2 gap-2 text-center">
              <div className="p-2 bg-muted rounded">
                <div className="text-sm font-bold">
                  {safeMonthlyStats.audiosGenerated}
                </div>
                <div className="text-xs text-muted-foreground">音频</div>
              </div>
              <div className="p-2 bg-muted rounded">
                <div className="text-sm font-bold">
                  {(safeMonthlyStats.charactersUsed / 1000).toFixed(1)}K
                </div>
                <div className="text-xs text-muted-foreground">字符</div>
              </div>
            </div>

            {showRefresh && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => refetch()}
                className="w-full"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                刷新
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>语音统计</span>
          </div>
          {showRefresh && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => refetch()}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          )}
        </CardTitle>
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            实时使用情况
          </p>
          <Badge variant={usageStatus.color as any}>
            {usageStatus.icon} {usageStatus.label}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 积分余额 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CreditCard className="h-4 w-4 text-blue-500" />
              <span className="text-sm font-medium">积分余额</span>
            </div>
            <div className="text-right">
              <div className="text-lg font-bold">
                {credits.remaining.toLocaleString()}
              </div>
              <div className="text-xs text-muted-foreground">
                / {credits.total.toLocaleString()}
              </div>
            </div>
          </div>
          
          <Progress value={usagePercentage} className="h-2" />
          
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>已用 {usagePercentage.toFixed(1)}%</span>
            <span>剩余 {credits.remaining.toLocaleString()}</span>
          </div>
        </div>

        {/* 本月统计 */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium">本月统计</span>
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="flex items-center justify-center space-x-1 mb-1">
                <FileAudio className="h-3 w-3 text-blue-500" />
                <span className="text-xs text-muted-foreground">音频</span>
              </div>
              <div className="text-lg font-bold">
                {safeMonthlyStats.audiosGenerated.toLocaleString()}
              </div>
            </div>

            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="flex items-center justify-center space-x-1 mb-1">
                <Zap className="h-3 w-3 text-orange-500" />
                <span className="text-xs text-muted-foreground">字符</span>
              </div>
              <div className="text-lg font-bold">
                {(safeMonthlyStats.charactersUsed / 1000).toFixed(1)}K
              </div>
            </div>
          </div>
        </div>


      </CardContent>
    </Card>
  );
}

// 辅助函数：触发统计更新
export function triggerStatsUpdate() {
  // 使用localStorage事件来通知其他组件更新统计
  localStorage.setItem('voice-generation-update', Date.now().toString());
  localStorage.removeItem('voice-generation-update');
}
