"use client";

import Link from "next/link";
import { useSession, signOut } from "next-auth/react";
import { api } from "~/trpc/react";
import { ThemeToggle } from "~/components/theme-toggle";
import { But<PERSON> } from "~/components/ui/button";
import { Mic } from "lucide-react";
import { UpgradeButtonCompact } from "~/components/credits/upgrade-button";
import { CreditStatusCompact } from "~/components/credits/credit-status";
import { SmartCreditsLink } from "~/components/credits/smart-credits-link";

export function Navbar() {
  const { data: session, status } = useSession();

  return (
    <nav className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 shadow-sm border-b border-border sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <Mic className="w-5 h-5 text-primary-foreground" />
            </div>
            <span className="text-xl font-bold text-foreground">Voctana</span>
          </Link>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-muted-foreground hover:text-foreground transition-colors">
              Home
            </Link>
            <Link href="/demo" className="text-muted-foreground hover:text-foreground transition-colors">
              Demo
            </Link>
            {session && (
              <>
                <Link href="/" className="text-primary hover:text-primary/80 font-medium transition-colors">
                  🎤 语音生成
                </Link>
                {/* 管理员入口 - 仅管理员可见 */}
                <AdminLink session={session} />
              </>
            )}
            <Link href="/pricing" className="text-muted-foreground hover:text-foreground transition-colors">
              Pricing
            </Link>
            <SmartCreditsLink>
              💎 充值
            </SmartCreditsLink>
          </div>

          {/* Auth Section */}
          <div className="flex items-center space-x-4">
            {session && <CreditStatusCompact />}
            {session && <UpgradeButtonCompact />}
            <ThemeToggle />
            {status === "loading" ? (
              <div className="w-8 h-8 animate-spin rounded-full border-2 border-muted border-t-primary"></div>
            ) : session ? (
              <div className="flex items-center space-x-4">
                <div className="hidden md:block text-sm text-muted-foreground">
                  Welcome, {session.user?.name || session.user?.email}
                </div>
                <Button
                  variant="outline"
                  onClick={() => signOut()}
                >
                  Sign Out
                </Button>
              </div>
            ) : (
              <Button asChild>
                <Link href="/auth/signin">
                  Sign In
                </Link>
              </Button>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Button variant="ghost" size="icon">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
}

// 管理员链接组件 - 只有管理员可以看到
function AdminLink({ session }: { session: any }) {
  // 获取当前用户信息
  const { data: userInfo } = api.admin.getUserRole.useQuery(undefined, {
    enabled: !!session?.user?.id,
    retry: false,
  });

  // 只有超级管理员可以看到管理入口
  if (!session?.user || !userInfo || userInfo.role !== 'SUPER_ADMIN') {
    return null;
  }

  return (
    <Link href="/admin" className="text-orange-600 dark:text-orange-400 hover:text-orange-700 dark:hover:text-orange-300 font-medium transition-colors">
      🛠️ 管理员
    </Link>
  );
}
