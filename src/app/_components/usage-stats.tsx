"use client";

import { api } from "~/trpc/react";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Progress } from "~/components/ui/progress";
import { Badge } from "~/components/ui/badge";
import {
  CreditCard,
  FileAudio,
  Calendar,
  TrendingUp,
  Zap
} from "lucide-react";

export function UsageStats() {
  const { data: stats, isLoading, refetch } = api.tts.getUsageStats.useQuery();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
            <div className="h-3 bg-gray-200 rounded w-3/4"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return null;
  }

  // 获取积分和使用统计数据
  const { credits, usagePercentage, monthlyStats } = stats;

  // 确保 monthlyStats 有默认值
  const safeMonthlyStats = monthlyStats || {
    audiosGenerated: 0,
    charactersUsed: 0,
  };

  // 计算使用状态
  const getUsageStatus = (percentage: number) => {
    if (percentage >= 90) return {
      color: 'destructive',
      label: '积分紧张',
      icon: '🔴'
    };
    if (percentage >= 70) return {
      color: 'warning',
      label: '使用较多',
      icon: '🟡'
    };
    if (percentage >= 50) return {
      color: 'default',
      label: '使用正常',
      icon: '🟢'
    };
    return {
      color: 'secondary',
      label: '使用较少',
      icon: '🟢'
    };
  };

  const usageStatus = getUsageStatus(usagePercentage);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <TrendingUp className="h-5 w-5" />
          <span>使用统计</span>
        </CardTitle>
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            您的语音生成使用情况
          </p>
          <Badge variant={usageStatus.color as any}>
            {usageStatus.icon} {usageStatus.label}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">

        {/* 积分余额 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CreditCard className="h-4 w-4 text-blue-500" />
              <span className="text-sm font-medium">积分余额</span>
            </div>
            <div className="text-right">
              <div className="text-lg font-bold">
                {credits.remaining.toLocaleString()}
              </div>
              <div className="text-xs text-muted-foreground">
                / {credits.total.toLocaleString()} 积分
              </div>
            </div>
          </div>

          <Progress
            value={usagePercentage}
            className="h-2"
          />

          <div className="flex justify-between text-xs text-muted-foreground">
            <span>已使用 {usagePercentage.toFixed(1)}%</span>
            <span>剩余 {credits.remaining.toLocaleString()} 积分</span>
          </div>
        </div>

        {/* 本月统计 */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium">本月统计</span>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="flex items-center justify-center space-x-1 mb-1">
                <FileAudio className="h-4 w-4 text-blue-500" />
                <span className="text-xs text-muted-foreground">音频数</span>
              </div>
              <div className="text-xl font-bold">
                {safeMonthlyStats.audiosGenerated.toLocaleString()}
              </div>
            </div>

            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="flex items-center justify-center space-x-1 mb-1">
                <Zap className="h-4 w-4 text-orange-500" />
                <span className="text-xs text-muted-foreground">字符数</span>
              </div>
              <div className="text-xl font-bold">
                {safeMonthlyStats.charactersUsed.toLocaleString()}
              </div>
            </div>
          </div>
        </div>


      </CardContent>
    </Card>
  );
}
