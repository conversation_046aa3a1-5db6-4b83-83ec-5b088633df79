"use client";

import { useState } from "react";
import { api } from "~/trpc/react";
import { useSession } from "next-auth/react";

interface CharacterSelectorProps {
  selectedCharacterId: string;
  onCharacterSelect: (characterId: string) => void;
}

export function CharacterSelector({ selectedCharacterId, onCharacterSelect }: CharacterSelectorProps) {
  const { data: session } = useSession();
  const [selectedProvider, setSelectedProvider] = useState<"ALL" | "GEMINI" | "OPENAI">("ALL");

  // Get user's subscription plan to filter available characters
  const userPlan = session?.user ? "FREE" : "FREE"; // Default to FREE, would get from user data

  const { data: characters, isLoading } = api.tts.getCharacters.useQuery({
    tier: userPlan as "FREE" | "BASIC" | "PRO",
    apiProvider: selectedProvider === "ALL" ? undefined : selectedProvider,
  });

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="h-10 bg-gray-200 rounded mb-4"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-20 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  const geminiCharacters = characters?.filter(c => c.apiProvider === "GEMINI") || [];
  const openaiCharacters = characters?.filter(c => c.apiProvider === "OPENAI") || [];

  return (
    <div className="space-y-4">
      {/* Provider Filter */}
      <div className="flex space-x-2">
        <button
          onClick={() => setSelectedProvider("ALL")}
          className={`px-3 py-1 rounded-full text-sm font-medium ${
            selectedProvider === "ALL"
              ? "bg-blue-600 text-white"
              : "bg-gray-200 text-gray-700 hover:bg-gray-300"
          }`}
        >
          All
        </button>
        <button
          onClick={() => setSelectedProvider("GEMINI")}
          className={`px-3 py-1 rounded-full text-sm font-medium ${
            selectedProvider === "GEMINI"
              ? "bg-blue-600 text-white"
              : "bg-gray-200 text-gray-700 hover:bg-gray-300"
          }`}
        >
          Gemini
        </button>
        <button
          onClick={() => setSelectedProvider("OPENAI")}
          className={`px-3 py-1 rounded-full text-sm font-medium ${
            selectedProvider === "OPENAI"
              ? "bg-blue-600 text-white"
              : "bg-gray-200 text-gray-700 hover:bg-gray-300"
          }`}
        >
          OpenAI
        </button>
      </div>

      {/* Character Grid */}
      <div className="space-y-6">
        {/* Gemini Characters */}
        {(selectedProvider === "ALL" || selectedProvider === "GEMINI") && geminiCharacters.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs mr-2">
                Gemini
              </span>
              Google Gemini Voices
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {geminiCharacters.map((character) => (
                <CharacterCard
                  key={character.id}
                  character={character}
                  isSelected={selectedCharacterId === character.id}
                  onSelect={() => onCharacterSelect(character.id)}
                />
              ))}
            </div>
          </div>
        )}

        {/* OpenAI Characters */}
        {(selectedProvider === "ALL" || selectedProvider === "OPENAI") && openaiCharacters.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs mr-2">
                OpenAI
              </span>
              OpenAI Voices
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {openaiCharacters.map((character) => (
                <CharacterCard
                  key={character.id}
                  character={character}
                  isSelected={selectedCharacterId === character.id}
                  onSelect={() => onCharacterSelect(character.id)}
                />
              ))}
            </div>
          </div>
        )}
      </div>

      {characters?.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          No characters available for your subscription plan.
        </div>
      )}
    </div>
  );
}

interface CharacterCardProps {
  character: {
    id: string;
    characterName: string;
    characterNameEn: string;
    apiProvider: string;
    gender: string;
    description: string;
    tier: string;
  };
  isSelected: boolean;
  onSelect: () => void;
}

function CharacterCard({ character, isSelected, onSelect }: CharacterCardProps) {
  const tierColors = {
    FREE: "bg-gray-100 text-gray-800",
    BASIC: "bg-blue-100 text-blue-800",
    PRO: "bg-purple-100 text-purple-800",
  };

  const genderIcons = {
    MALE: "👨",
    FEMALE: "👩",
  };

  return (
    <div
      onClick={onSelect}
      className={`p-4 rounded-lg border-2 text-left transition-all hover:shadow-md cursor-pointer ${
        isSelected
          ? "border-blue-500 bg-blue-50"
          : "border-gray-200 hover:border-gray-300"
      }`}
    >
      <div className="flex items-start justify-between mb-2">
        <div>
          <h4 className="font-medium text-gray-900">
            {character.characterName}
          </h4>
          <p className="text-sm text-gray-600">
            {character.characterNameEn}
          </p>
        </div>
        <div className="flex items-center space-x-1">
          <span className="text-lg">
            {genderIcons[character.gender as keyof typeof genderIcons]}
          </span>
          <span
            className={`px-2 py-1 rounded text-xs font-medium ${
              tierColors[character.tier as keyof typeof tierColors]
            }`}
          >
            {character.tier}
          </span>
        </div>
      </div>
      
      <p className="text-sm text-gray-600 mb-2">
        {character.description}
      </p>
      
      <div className="flex items-center justify-between">
        <span className={`text-xs px-2 py-1 rounded ${
          character.apiProvider === "GEMINI" 
            ? "bg-blue-100 text-blue-700" 
            : "bg-green-100 text-green-700"
        }`}>
          {character.apiProvider}
        </span>
        
        {/* Preview button - would implement audio preview */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            // TODO: Implement preview functionality
            alert("Preview feature coming soon!");
          }}
          className="text-xs text-blue-600 hover:text-blue-800"
        >
          Preview
        </button>
      </div>
    </div>
  );
}
