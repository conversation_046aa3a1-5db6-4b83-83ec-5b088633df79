import { NextRequest, NextResponse } from "next/server";
import { auth } from "~/server/auth";
import { db } from "~/server/db";
import { TTSManager } from "~/lib/tts/tts-manager";
import { r2Storage } from "~/lib/r2-storage";
import { GeminiTTSService } from "~/lib/tts/gemini-tts";

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      dialogue, // 对话数组 [{ speaker, text, characterId }]
      stylePrompt = "",
      speed = 1.0,
      pitch = 0,
      volume = 1.0,
      quality = 'fast',
    } = body;

    if (!dialogue || !Array.isArray(dialogue) || dialogue.length === 0) {
      return NextResponse.json(
        { error: "对话内容不能为空" },
        { status: 400 }
      );
    }

    console.log('多人对话生成请求:', { 
      dialogueCount: dialogue.length, 
      stylePrompt, 
      speed, 
      pitch, 
      volume, 
      quality 
    });

    // 检查是否所有角色都使用Gemini API
    const characters = await Promise.all(
      dialogue.map(async (segment) => {
        const character = await db.voiceCharacter.findUnique({
          where: { id: segment.characterId },
        });
        if (!character) {
          throw new Error(`找不到角色: ${segment.characterId}`);
        }
        return { ...segment, character };
      })
    );

    const allGemini = characters.every(c => c.character.apiProvider === 'GEMINI');

    // 声明变量
    let totalDuration = 0;
    let totalCost = 0;
    let totalCharacterCount = 0;

    if (allGemini && dialogue.length <= 10) {
      // 使用Gemini原生多人对话API
      console.log('使用Gemini原生多人对话API生成...');
      const result = await generateGeminiMultiSpeaker(characters, stylePrompt, speed, quality);

      totalCharacterCount = result.characterCount;
      totalCost = result.cost;
      totalDuration = result.duration;

      // 上传音频到R2存储
      let audioUrl = '';
      if (r2Storage.isConfigured()) {
        const fileName = `conversation-${Date.now()}.wav`;
        audioUrl = await r2Storage.uploadAudio(result.audioData, fileName, 'audio/wav');
        console.log(`✅ 多人对话已上传到R2: ${audioUrl}`);
      } else {
        audioUrl = `data:audio/wav;base64,${result.audioData.toString('base64')}`;
        console.log(`💾 R2未配置，使用base64数据URL`);
      }

      return NextResponse.json({
        success: true,
        audioUrl,
        duration: totalDuration,
        cost: totalCost,
        characterCount: totalCharacterCount,
        segmentCount: dialogue.length,
        generatedAt: new Date().toISOString(),
      });
    } else {
      // 回退到逐个生成方式
      console.log('使用逐个生成方式...');
      const ttsManager = new TTSManager();
      const audioBuffers: Buffer[] = [];

      // 逐个生成每个对话片段
      for (let i = 0; i < characters.length; i++) {
        const { speaker, text, character } = characters[i];

        // 只使用对话内容，不包含说话者名字
        const dialogueText = text;

        console.log(`生成片段 ${i + 1}/${characters.length}: ${speaker} - ${text.substring(0, 50)}...`);
        console.log(`角色信息: ${character.characterName} (${character.apiVoiceName}) - ${character.apiProvider}`);

        try {
          // 生成语音
          const ttsResult = await ttsManager.generateSpeech({
            text: dialogueText,
            characterId: character.id,
            apiProvider: character.apiProvider,
            voiceName: character.apiVoiceName,
            speed: speed,
            pitch: pitch,
            volumeGainDb: (volume - 1.0) * 10,
            format: 'WAV',
            quality: quality,
            stylePrompt: stylePrompt,
          });

          audioBuffers.push(ttsResult.audioData);
          totalDuration += ttsResult.duration || 3;
          totalCost += ttsResult.cost || 0;
          totalCharacterCount += dialogueText.length;

          console.log(`✅ 片段 ${i + 1} 生成成功`);
        } catch (error) {
          console.error(`❌ 片段 ${i + 1} 生成失败:`, error);
          console.error(`失败的文本: "${dialogueText}"`);
          console.error(`角色: ${character.characterName} (${character.apiVoiceName})`);
          throw error;
        }



        // 短暂延迟，避免API限制
        if (i < characters.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // 合并音频文件
      console.log(`合并 ${audioBuffers.length} 个音频片段...`);
      const mergedAudio = await mergeWavFiles(audioBuffers);

      // 上传合成的音频到R2存储
      let audioUrl = '';
      if (r2Storage.isConfigured()) {
        const fileName = `conversation-${Date.now()}.wav`;
        audioUrl = await r2Storage.uploadAudio(mergedAudio, fileName, 'audio/wav');
        console.log(`✅ 合成对话已上传到R2: ${audioUrl}`);
      } else {
        // 如果R2未配置，使用base64数据URL
        audioUrl = `data:audio/wav;base64,${mergedAudio.toString('base64')}`;
        console.log(`💾 R2未配置，使用base64数据URL`);
      }

      return NextResponse.json({
        success: true,
        audioUrl,
        duration: totalDuration,
        cost: totalCost,
        characterCount: totalCharacterCount,
        segmentCount: dialogue.length,
        generatedAt: new Date().toISOString(),
      });
    }

  } catch (error) {
    console.error('多人对话生成失败:', error);

    return NextResponse.json(
      {
        error: "生成失败，请稍后重试"
      },
      { status: 500 }
    );
  }
}

// 合并WAV文件的函数
async function mergeWavFiles(audioBuffers: Buffer[]): Promise<Buffer> {
  if (audioBuffers.length === 0) {
    throw new Error('没有音频数据可合并');
  }

  if (audioBuffers.length === 1) {
    return audioBuffers[0]!;
  }

  // 简单的WAV文件合并
  // 这里使用基本的二进制拼接，假设所有WAV文件具有相同的格式
  const firstWav = audioBuffers[0]!;
  const wavHeader = firstWav.subarray(0, 44); // WAV头部
  
  // 收集所有音频数据（跳过每个文件的头部）
  const audioDataChunks: Buffer[] = [];
  let totalDataSize = 0;

  for (const buffer of audioBuffers) {
    const audioData = buffer.subarray(44); // 跳过WAV头部
    audioDataChunks.push(audioData);
    totalDataSize += audioData.length;
  }

  // 更新WAV头部中的文件大小信息
  const newHeader = Buffer.from(wavHeader);
  const newFileSize = 36 + totalDataSize;
  newHeader.writeUInt32LE(newFileSize, 4); // 文件大小
  newHeader.writeUInt32LE(totalDataSize, 40); // 数据块大小

  // 合并所有数据
  const mergedBuffer = Buffer.concat([newHeader, ...audioDataChunks]);
  
  console.log(`✅ 音频合并完成: ${audioBuffers.length} 个片段, 总大小: ${mergedBuffer.length} 字节`);
  
  return mergedBuffer;
}

// Gemini原生多人对话生成函数
async function generateGeminiMultiSpeaker(
  characters: Array<{ speaker: string; text: string; character: any }>,
  stylePrompt: string,
  speed: number,
  quality: string
) {
  const geminiService = new GeminiTTSService();

  // 构建对话文本 - 使用官方示例的格式
  const conversationText = `TTS the following conversation:\n${characters
    .map(({ speaker, text }) => `${speaker}: ${text}`)
    .join('\n')}`;

  console.log('🎭 多人对话文本:', conversationText);

  // 构建多人对话配置 - 只包含唯一的说话者
  const uniqueSpeakers = Array.from(
    new Map(characters.map(({ speaker, character }) => [
      speaker,
      { speaker, voiceName: character.apiVoiceName }
    ])).values()
  );

  const multiSpeaker = {
    speakers: uniqueSpeakers
  };

  console.log('🎭 多人对话配置:', JSON.stringify(multiSpeaker, null, 2));

  const result = await geminiService.generateSpeech({
    text: conversationText,
    quality: quality as 'fast' | 'high',
    speed,
    format: 'wav',
    prompt: stylePrompt,
    multiSpeaker
  });

  // 估算费用
  const characterCount = conversationText.length;
  const cost = characterCount * (quality === 'high' ? 0.0002 : 0.0001);

  return {
    audioData: result.audioData,
    characterCount,
    cost,
    duration: Math.max(3, characterCount * 0.1), // 估算时长
  };
}

export const runtime = 'nodejs';
