import { NextRequest, NextResponse } from "next/server";
import { auth } from "~/server/auth";
import { db } from "~/server/db";
import { TTSManager } from "~/lib/tts/tts-manager";
import { r2Storage } from "~/lib/r2-storage";

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      text,
      characterId,
      styleId,
      speaker,
      stylePrompt,
      speed = 1.0,
      pitch = 0,
      volume = 1.0,
      quality = 'fast',
    } = body;

    // 验证输入
    if (!text || !speaker) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    if (text.length > 200) {
      return NextResponse.json(
        { error: "Text too long for segment. Maximum 200 characters." },
        { status: 400 }
      );
    }

    // 获取角色信息
    let character = null;
    if (characterId) {
      // 尝试获取自定义角色
      character = await db.customVoiceCharacter.findFirst({
        where: {
          OR: [
            { id: characterId },
            { baseCharacterId: characterId }
          ]
        },
        include: {
          baseCharacter: true,
          defaultStyle: true,
        },
      });

      // 如果没找到自定义角色，尝试获取基础角色
      if (!character) {
        const baseCharacter = await db.voiceCharacter.findUnique({
          where: { id: characterId },
        });
        
        if (baseCharacter) {
          character = {
            baseCharacter,
            speed: 1.0,
            pitch: 0.0,
            volume: 0.0,
          };
        }
      }
    }

    // 获取风格信息
    let style = null;
    if (styleId) {
      style = await db.userStyle.findUnique({
        where: { id: styleId },
      });
    }

    // 构建语音生成参数
    const voiceParams = {
      text,
      speaker,
      character: character?.baseCharacter?.characterName || character?.name || "Aria",
      apiVoiceName: character?.baseCharacter?.apiVoiceName || "aria",
      speed: character?.speed || 1.0,
      pitch: character?.pitch || 0.0,
      volume: character?.volume || 0.0,
      stylePrompt: stylePrompt || style?.prompt || "",
      provider: character?.baseCharacter?.apiProvider || "azure",
    };

    console.log('多人语音片段生成请求:', voiceParams);

    // 使用真实的TTS服务生成语音
    const ttsManager = new TTSManager();

    // 不要将stylePrompt合并到文本中，它应该作为单独的指令传递
    const ttsResult = await ttsManager.generateSpeech({
      text: text, // 只传递用户输入的文本
      characterId: character?.baseCharacter?.id || character?.id || '',
      apiProvider: character?.baseCharacter?.apiProvider || 'GEMINI',
      voiceName: character?.baseCharacter?.apiVoiceName || character?.apiVoiceName || 'Puck',
      speed: speed,
      pitch: pitch,
      volumeGainDb: (volume - 1.0) * 10, // 转换为dB
      format: 'WAV',
      quality: quality,
      stylePrompt: stylePrompt, // stylePrompt作为单独的指令传递
    });

    // 上传音频到R2存储
    let audioUrl = '';
    if (r2Storage.isConfigured()) {
      const fileName = `multi-speaker-${Date.now()}-${speaker.replace(/\s+/g, '-')}.wav`;
      audioUrl = await r2Storage.uploadAudio(ttsResult.audioData, fileName, 'audio/wav');
      console.log(`✅ 多人语音片段已上传到R2: ${audioUrl}`);
    } else {
      // 如果R2未配置，使用base64数据URL
      audioUrl = `data:audio/wav;base64,${ttsResult.audioData.toString('base64')}`;
      console.log(`💾 R2未配置，使用base64数据URL`);
    }

    const result = {
      audioUrl,
      duration: ttsResult.duration || Math.max(1, fullText.length / 3), // 估算时长
    };

    // 记录使用统计
    if (characterId) {
      await db.customVoiceCharacter.updateMany({
        where: { id: characterId },
        data: {
          usageCount: { increment: 1 }
        },
      });
    }

    if (styleId) {
      await db.userStyle.updateMany({
        where: { id: styleId },
        data: {
          usageCount: { increment: 1 }
        },
      });
    }

    return NextResponse.json({
      success: true,
      audioUrl: result.audioUrl,
      duration: result.duration,
      speaker,
      text,
      characterName: character?.baseCharacter?.characterName || character?.name,
      cost: ttsResult.cost,
      characterCount: ttsResult.characterCount,
      generatedAt: new Date().toISOString(),
    });

  } catch (error) {
    console.error('多人语音生成失败:', error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Voice generation failed",
        speaker,
        text
      },
      { status: 500 }
    );
  }
}

export const runtime = 'nodejs';
