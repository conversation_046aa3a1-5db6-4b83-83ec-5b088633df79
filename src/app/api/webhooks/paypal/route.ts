import { NextRequest, NextResponse } from 'next/server';
import { db } from '~/server/db';
import PayPalService from '~/lib/paypal';
import NotificationService from '~/lib/notifications';
import InvoiceService from '~/lib/invoice';

// PayPal Webhook处理
export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const headers = Object.fromEntries(request.headers.entries());

    // 验证webhook签名
    const webhookId = process.env.PAYPAL_WEBHOOK_ID;
    if (!webhookId) {
      console.error('PayPal webhook ID not configured');
      return NextResponse.json({ error: 'Webhook not configured' }, { status: 500 });
    }

    const isValid = PayPalService.verifyWebhook(headers, body, webhookId);
    if (!isValid) {
      console.error('Invalid PayPal webhook signature');
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    }

    const event = JSON.parse(body);
    console.log('PayPal webhook event:', event.event_type);

    // 处理不同类型的webhook事件
    switch (event.event_type) {
      case 'PAYMENT.CAPTURE.COMPLETED':
        await handlePaymentCaptureCompleted(event);
        break;
      
      case 'PAYMENT.CAPTURE.DENIED':
        await handlePaymentCaptureDenied(event);
        break;
      
      case 'PAYMENT.CAPTURE.PENDING':
        await handlePaymentCapturePending(event);
        break;
      
      case 'CHECKOUT.ORDER.APPROVED':
        await handleOrderApproved(event);
        break;
      
      case 'CHECKOUT.ORDER.COMPLETED':
        await handleOrderCompleted(event);
        break;
      
      default:
        console.log(`Unhandled PayPal webhook event: ${event.event_type}`);
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('PayPal webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

// 处理支付捕获完成
async function handlePaymentCaptureCompleted(event: any) {
  const capture = event.resource;
  const paypalOrderId = capture.supplementary_data?.related_ids?.order_id;

  if (!paypalOrderId) {
    console.error('No PayPal order ID found in capture event');
    return;
  }

  try {
    // 查找对应的支付记录
    const payment = await db.payment.findFirst({
      where: {
        paypalOrderId: paypalOrderId,
        status: { in: ['PENDING'] },
      },
      include: {
        creditPurchase: true,
        user: true,
      },
    });

    if (!payment) {
      console.error(`Payment not found for PayPal order: ${paypalOrderId}`);
      return;
    }

    // 更新支付状态
    await db.payment.update({
      where: { id: payment.id },
      data: {
        status: 'COMPLETED',
        completedAt: new Date(),
        metadata: {
          ...(payment.metadata as object || {}),
          paypalCaptureId: capture.id,
          paypalCaptureStatus: capture.status,
          webhookProcessedAt: new Date().toISOString(),
          webhookEvent: event,
        },
      },
    });

    // 处理积分购买
    if (payment.creditPurchase) {
      // 更新积分购买状态
      await db.creditPurchase.update({
        where: { id: payment.creditPurchase.id },
        data: {
          status: 'COMPLETED',
          completedAt: new Date(),
        },
      });

      // 给用户增加积分
      await db.user.update({
        where: { id: payment.userId },
        data: {
          credits: { increment: payment.creditPurchase.credits },
        },
      });

      // 记录积分赠送历史
      await db.creditGift.create({
        data: {
          fromUserId: null, // 系统赠送
          toUserId: payment.userId,
          amount: payment.creditPurchase.credits,
          reason: `购买积分包：${payment.creditPurchase.packageName}`,
          type: 'PROMOTION',
        },
      });
    }

    console.log(`Payment ${payment.id} completed via webhook`);

    // 创建发票
    await InvoiceService.create(payment.id);

    // 发送支付成功通知
    await NotificationService.sendPaymentSuccess(payment);

  } catch (error) {
    console.error('Error processing payment capture completed:', error);
  }
}

// 处理支付捕获被拒绝
async function handlePaymentCaptureDenied(event: any) {
  const capture = event.resource;
  const paypalOrderId = capture.supplementary_data?.related_ids?.order_id;

  if (!paypalOrderId) {
    console.error('No PayPal order ID found in denied capture event');
    return;
  }

  try {
    const payment = await db.payment.findFirst({
      where: {
        paypalOrderId: paypalOrderId,
      },
      include: {
        user: true,
      },
    });

    if (!payment) {
      console.error(`Payment not found for PayPal order: ${paypalOrderId}`);
      return;
    }

    // 更新支付状态为失败
    await db.payment.update({
      where: { id: payment.id },
      data: {
        status: 'FAILED',
        metadata: {
          ...(payment.metadata as object || {}),
          paypalCaptureId: capture.id,
          paypalCaptureStatus: capture.status,
          failureReason: capture.status_details?.reason || 'Payment denied',
          webhookProcessedAt: new Date().toISOString(),
          webhookEvent: event,
        },
      },
    });

    console.log(`Payment ${payment.id} denied via webhook`);

    // 发送支付失败通知
    await NotificationService.sendPaymentFailure(payment, capture.status_details?.reason || 'Payment denied');

  } catch (error) {
    console.error('Error processing payment capture denied:', error);
  }
}

// 处理支付捕获待处理
async function handlePaymentCapturePending(event: any) {
  const capture = event.resource;
  const paypalOrderId = capture.supplementary_data?.related_ids?.order_id;

  if (!paypalOrderId) {
    return;
  }

  try {
    const payment = await db.payment.findFirst({
      where: {
        paypalOrderId: paypalOrderId,
      },
    });

    if (!payment) {
      return;
    }

    // 更新支付状态为处理中
    await db.payment.update({
      where: { id: payment.id },
      data: {
        status: 'PENDING',
        metadata: {
          ...(payment.metadata as object || {}),
          paypalCaptureId: capture.id,
          paypalCaptureStatus: capture.status,
          pendingReason: capture.status_details?.reason || 'Payment pending',
          webhookProcessedAt: new Date().toISOString(),
        },
      },
    });

    console.log(`Payment ${payment.id} pending via webhook`);

  } catch (error) {
    console.error('Error processing payment capture pending:', error);
  }
}

// 处理订单批准
async function handleOrderApproved(event: any) {
  const order = event.resource;
  
  try {
    const payment = await db.payment.findFirst({
      where: {
        paypalOrderId: order.id,
      },
    });

    if (!payment) {
      return;
    }

    // 更新支付元数据
    await db.payment.update({
      where: { id: payment.id },
      data: {
        metadata: {
          ...(payment.metadata as object || {}),
          paypalOrderStatus: order.status,
          approvedAt: new Date().toISOString(),
          webhookProcessedAt: new Date().toISOString(),
        },
      },
    });

    console.log(`Order ${order.id} approved via webhook`);

  } catch (error) {
    console.error('Error processing order approved:', error);
  }
}

// 处理订单完成
async function handleOrderCompleted(event: any) {
  const order = event.resource;
  
  try {
    const payment = await db.payment.findFirst({
      where: {
        paypalOrderId: order.id,
      },
    });

    if (!payment) {
      return;
    }

    // 更新支付元数据
    await db.payment.update({
      where: { id: payment.id },
      data: {
        metadata: {
          ...(payment.metadata as object || {}),
          paypalOrderStatus: order.status,
          orderCompletedAt: new Date().toISOString(),
          webhookProcessedAt: new Date().toISOString(),
        },
      },
    });

    console.log(`Order ${order.id} completed via webhook`);

  } catch (error) {
    console.error('Error processing order completed:', error);
  }
}


