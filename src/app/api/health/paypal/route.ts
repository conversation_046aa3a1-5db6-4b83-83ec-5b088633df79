import { NextResponse } from 'next/server';

/**
 * PayPal健康检查端点
 * 验证PayPal配置是否正确
 */
export async function GET() {
  try {
    const clientId = process.env.PAYPAL_CLIENT_ID;
    const clientSecret = process.env.PAYPAL_CLIENT_SECRET;
    const webhookId = process.env.PAYPAL_WEBHOOK_ID;
    const environment = process.env.NODE_ENV === 'production' ? 'live' : 'sandbox';
    
    // 检查基本配置
    const isConfigured = !!(clientId && clientSecret);
    
    // 检查PayPal SDK连接（不暴露敏感信息）
    let sdkStatus = 'unknown';
    try {
      // 这里可以添加简单的PayPal API连接测试
      // 但不要暴露实际的API密钥
      sdkStatus = isConfigured ? 'configured' : 'missing_credentials';
    } catch (error) {
      sdkStatus = 'connection_error';
    }
    
    return NextResponse.json({
      environment,
      configured: isConfigured,
      clientId: !!clientId,
      clientSecret: !!clientSecret,
      webhookId: !!webhookId,
      sdkStatus,
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error('PayPal health check error:', error);
    return NextResponse.json(
      { 
        error: 'Configuration check failed',
        timestamp: new Date().toISOString(),
      }, 
      { status: 500 }
    );
  }
}
