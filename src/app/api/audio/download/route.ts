import { NextRequest, NextResponse } from 'next/server';
import { auth } from '~/server/auth';

export async function GET(request: NextRequest) {
  try {
    // Get the audio URL from query parameters
    const { searchParams } = new URL(request.url);
    const audioUrl = searchParams.get('url');

    if (!audioUrl) {
      return NextResponse.json(
        { error: 'Audio URL is required' },
        { status: 400 }
      );
    }

    // Validate that the URL is from our R2 storage or is a data URL
    const isDataUrl = audioUrl.startsWith('data:audio/');
    const isR2Url = process.env.R2_PUBLIC_URL && audioUrl.startsWith(process.env.R2_PUBLIC_URL);
    
    if (!isDataUrl && !isR2Url) {
      return NextResponse.json(
        { error: 'Invalid audio URL' },
        { status: 400 }
      );
    }

    // For data URLs, we can't proxy them, return an error
    if (isDataUrl) {
      return NextResponse.json(
        { error: 'Data URLs cannot be proxied for download' },
        { status: 400 }
      );
    }

    // Check authentication for protected content
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Fetch the audio file from R2
    const response = await fetch(audioUrl, {
      headers: {
        'User-Agent': 'Voctana-Audio-Proxy/1.0',
      },
    });

    if (!response.ok) {
      console.error('Failed to fetch audio from R2:', response.status, response.statusText);
      return NextResponse.json(
        { error: 'Failed to fetch audio file' },
        { status: response.status }
      );
    }

    // Get the audio data
    const audioBuffer = await response.arrayBuffer();
    
    // Determine content type
    const contentType = response.headers.get('content-type') || 'audio/wav';
    
    // Generate filename from URL or use default
    const urlParts = audioUrl.split('/');
    const filename = urlParts[urlParts.length - 1] || `audio-${Date.now()}.wav`;

    // Return the audio file with proper headers
    return new NextResponse(audioBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': audioBuffer.byteLength.toString(),
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        // CORS headers
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('Audio download proxy error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
