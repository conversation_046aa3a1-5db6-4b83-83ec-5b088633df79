import { NextRequest, NextResponse } from "next/server";
import { auth } from "~/server/auth";
import { db } from "~/server/db";
import { aiAvatarGenerator, type AvatarGenerationOptions } from "~/lib/ai-avatar-generator";
import { r2Storage, R2StorageService } from "~/lib/r2-storage";
import { z } from "zod";

// 请求验证模式
const GenerateAvatarSchema = z.object({
  characterId: z.string().min(1),
  languageCode: z.string().optional(),
  culturalContext: z.string().optional(),
  regenerate: z.boolean().default(false),
});

export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // 检查是否是管理员
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (!user || !['ADMIN', 'SUPER_ADMIN'].includes(user.role)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // 验证请求数据
    const body = await request.json();
    const { characterId, languageCode, culturalContext, regenerate } = GenerateAvatarSchema.parse(body);

    // 获取角色信息 - 使用新的languageCharacter表
    const character = await db.languageCharacter.findUnique({
      where: { id: characterId },
      include: {
        template: true,
        language: true,
      },
    });

    if (!character) {
      return NextResponse.json(
        { error: "Character not found" },
        { status: 404 }
      );
    }

    // 检查AI头像生成服务是否可用
    if (!aiAvatarGenerator.isAvailable()) {
      return NextResponse.json(
        { error: "AI avatar generation service is not available" },
        { status: 503 }
      );
    }

    // 检查R2存储是否配置
    if (!r2Storage.isConfigured()) {
      return NextResponse.json(
        { error: "R2 storage is not configured" },
        { status: 503 }
      );
    }

    console.log(`🎨 开始为角色 ${character.name} 生成AI头像...`);

    // 构建生成选项 - 使用新的数据结构
    const options: AvatarGenerationOptions = {
      characterName: character.name,
      gender: character.template?.gender || 'NEUTRAL',
      style: character.style || undefined,
      personality: character.personality || undefined,
      description: character.description || undefined,
      languageCode: languageCode || character.language?.code,
      culturalContext: culturalContext || character.language?.nativeName,
    };

    // 生成头像
    const result = await aiAvatarGenerator.generateAvatar(options);

    // 生成文件名
    const fileName = R2StorageService.generateAvatarFileName(
      characterId,
      languageCode,
      'webp'
    );

    // 上传到R2
    const avatarUrl = await r2Storage.uploadAvatar(
      result.imageData,
      fileName,
      'image/webp'
    );

    console.log(`✅ 头像已上传到R2: ${avatarUrl}`);

    // 更新角色头像URL - 使用新的数据结构
    await db.languageCharacter.update({
      where: { id: characterId },
      data: {
        avatarUrl: avatarUrl,
      },
    });

    // 记录生成历史（可选）
    await db.avatarGenerationHistory.create({
      data: {
        characterId,
        languageCode,
        avatarUrl,
        prompt: result.prompt,
        metadata: JSON.stringify(result.metadata),
        generatedBy: session.user.id,
      },
    }).catch((error) => {
      // 如果表不存在，忽略错误
      console.warn('⚠️ 无法记录头像生成历史:', error.message);
    });

    return NextResponse.json({
      success: true,
      avatarUrl,
      metadata: result.metadata,
      message: `角色 ${character.characterName} 的头像生成成功`,
    });

  } catch (error) {
    console.error("❌ AI头像生成失败:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: "Avatar generation failed", 
        message: error instanceof Error ? error.message : "Unknown error" 
      },
      { status: 500 }
    );
  }
}

// 获取角色头像生成历史
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const characterId = searchParams.get('characterId');

    if (!characterId) {
      return NextResponse.json(
        { error: "Character ID is required" },
        { status: 400 }
      );
    }

    // 获取生成历史
    const history = await db.avatarGenerationHistory.findMany({
      where: { characterId },
      orderBy: { createdAt: 'desc' },
      take: 10,
      include: {
        generatedByUser: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    }).catch(() => {
      // 如果表不存在，返回空数组
      return [];
    });

    return NextResponse.json({
      success: true,
      history,
    });

  } catch (error) {
    console.error("❌ 获取头像历史失败:", error);
    return NextResponse.json(
      { error: "Failed to fetch avatar history" },
      { status: 500 }
    );
  }
}

export const runtime = 'nodejs';
