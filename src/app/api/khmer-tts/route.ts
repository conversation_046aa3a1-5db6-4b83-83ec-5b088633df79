import { NextRequest, NextResponse } from 'next/server';
import { KhmerTTSService } from '~/lib/tts/khmer-tts';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { text, voice = 'female', speed = 1.0 } = body;

    if (!text || typeof text !== 'string') {
      return NextResponse.json(
        { error: 'Text is required and must be a string' },
        { status: 400 }
      );
    }

    if (text.length > 1000) {
      return NextResponse.json(
        { error: 'Text is too long (max 1000 characters)' },
        { status: 400 }
      );
    }

    console.log('🎤 Generating Khmer TTS:', {
      text: text.substring(0, 50) + '...',
      voice,
      speed,
      length: text.length
    });

    // 使用Gemini TTS服务生成高棉语语音
    const { GeminiTTSService } = await import('~/lib/tts/gemini-tts');
    const geminiTTS = new GeminiTTSService();

    // 生成语音
    const result = await geminiTTS.generateSpeech({
      text,
      voiceName: voice === 'male' ? 'Charon' : 'Puck', // 使用Gemini的语音
      speed,
      format: 'wav'
    });

    console.log('✅ Khmer TTS generation successful, size:', result.audioData.length, 'bytes');

    // 返回音频数据
    return new NextResponse(result.audioData, {
      status: 200,
      headers: {
        'Content-Type': 'audio/wav',
        'Content-Length': result.audioData.length.toString(),
        'Cache-Control': 'public, max-age=3600',
        'Content-Disposition': 'inline; filename="voctana-speech.wav"',
      },
    });

  } catch (error) {
    console.error('❌ Khmer TTS API Error:', error);
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        details: 'Failed to generate Khmer speech'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Voctana TTS API',
    usage: 'POST with { text, voice?, speed? }',
    voices: ['female', 'male'],
    maxLength: 1000
  });
}
