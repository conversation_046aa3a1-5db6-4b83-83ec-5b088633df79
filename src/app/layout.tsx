import "~/styles/globals.css";

import { type Metadata } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";

import { TRPCReactProvider } from "~/trpc/react";
import { ClientSessionProvider } from "~/app/_components/session-provider";
import { ThemeProvider } from "~/components/theme-provider";
import { LanguageProvider } from "~/contexts/LanguageContext";
import { AudioPlayerProvider } from "~/contexts/AudioPlayerContext";
import { Toaster } from "sonner";

export const metadata: Metadata = {
  title: "Voctana - AI多语言语音生成平台",
  description: "使用AI技术生成高质量的多语言语音，支持24种语言、30个语音角色和自然语调。",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className={`${geist.variable}`} suppressHydrationWarning>
      <body>
        <TRPCReactProvider>
          <ClientSessionProvider>
            <LanguageProvider>
              <AudioPlayerProvider>
                <ThemeProvider
                  attribute="class"
                  defaultTheme="system"
                  enableSystem
                  disableTransitionOnChange
                >
                  {children}
                  <Toaster
                    position="top-right"
                    richColors
                    closeButton
                    expand={false}
                    visibleToasts={3}
                  />
                </ThemeProvider>
              </AudioPlayerProvider>
            </LanguageProvider>
          </ClientSessionProvider>
        </TRPCReactProvider>
      </body>
    </html>
  );
}
