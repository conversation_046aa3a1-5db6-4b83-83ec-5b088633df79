"use client";

import React, { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { api } from "~/trpc/react";
import { AppLayout } from "~/components/app-layout";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Separator } from "~/components/ui/separator";
import {
  Package,
  DollarSign,
  Zap,
  Crown,
  TrendingUp,
  Users,
  ShoppingCart,
  Loader2,
  Save,
  RefreshCw,
  Sparkles,
  Calculator,
} from "lucide-react";
import { toast } from "sonner";

export default function PackageAdminPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'packages' | 'api-pricing' | 'multipliers'>('packages');

  // API定价表单状态
  const [apiPricingForm, setApiPricingForm] = useState({
    standardInputPrice: 0.50,
    standardOutputPrice: 10.00,
    professionalInputPrice: 1.00,
    professionalOutputPrice: 20.00,
    textGenerationInputPrice: 0.50,
    textGenerationOutputPrice: 1.50,
  });

  // 积分包定价表单状态
  const [packageForms, setPackageForms] = useState({
    basic: { credits: 1000, salePrice: 9.99, promoPrice: 0 },
    standard: { credits: 3000, salePrice: 24.99, promoPrice: 0 },
    premium: { credits: 10000, salePrice: 79.99, promoPrice: 0 },
  });

  // 免费包配置表单状态
  const [freePackageForm, setFreePackageForm] = useState({
    enabled: true,
    dailyCredits: 100,
    monthlyCredits: 0,
    giftType: 'daily' as 'daily' | 'monthly',
  });

  // 积分消耗比配置表单状态
  const [consumptionRatioForm, setConsumptionRatioForm] = useState({
    textGenerationRatio: 1000,    // 1000 tokens = 1 credit
    standardVoiceRatio: 100,      // 100 tokens = 1 credit
    professionalVoiceRatio: 50,   // 50 tokens = 1 credit
  });

  // 获取用户信息
  const { data: userInfo, isLoading: userLoading } = api.admin.getUserRole.useQuery(
    undefined,
    { 
      enabled: status === 'authenticated' && !!session?.user?.id,
      retry: false,
    }
  );

  // 获取API定价配置
  const { data: apiPricing, refetch: refetchApiPricing } = api.packageAdmin.getApiPricing.useQuery(
    undefined,
    {
      enabled: status === 'authenticated' && !!session?.user?.id && userInfo?.role === 'SUPER_ADMIN',
      retry: false,
    }
  );

  // 获取积分消耗比配置
  const { data: consumptionRatios, refetch: refetchConsumptionRatios } = api.packageAdmin.getCreditConsumptionRatios.useQuery(
    undefined,
    {
      enabled: status === 'authenticated' && !!session?.user?.id && userInfo?.role === 'SUPER_ADMIN',
      retry: false,
    }
  );

  // 获取积分包定价
  const { data: packagePricing, refetch: refetchPackagePricing } = api.packageAdmin.getPackagePricing.useQuery(
    undefined,
    { 
      enabled: status === 'authenticated' && !!session?.user?.id && userInfo?.role === 'SUPER_ADMIN',
      retry: false,
    }
  );

  // 获取套餐统计
  const { data: stats } = api.packageAdmin.getPackageStats.useQuery(
    undefined,
    {
      enabled: status === 'authenticated' && !!session?.user?.id && userInfo?.role === 'SUPER_ADMIN',
      retry: false,
    }
  );

  // 获取免费包配置
  const { data: freePackageConfig, refetch: refetchFreePackageConfig } = api.packageAdmin.getFreePackageConfig.useQuery(
    undefined,
    {
      enabled: status === 'authenticated' && !!session?.user?.id && userInfo?.role === 'SUPER_ADMIN',
      retry: false,
    }
  );

  // 更新API定价
  const updateApiPricing = api.packageAdmin.updateApiPricing.useMutation({
    onSuccess: () => {
      toast.success("API定价更新成功");
      refetchApiPricing();
    },
    onError: (error) => {
      toast.error(error.message || "更新失败");
    },
  });

  // 更新积分包定价
  const updatePackagePricing = api.packageAdmin.updatePackagePricing.useMutation({
    onSuccess: () => {
      toast.success("积分包定价更新成功");
      refetchPackagePricing();
    },
    onError: (error) => {
      toast.error(error.message || "更新失败");
    },
  });

  // 更新免费包配置
  const updateFreePackageConfig = api.packageAdmin.updateFreePackageConfig.useMutation({
    onSuccess: () => {
      toast.success("免费包配置更新成功");
      refetchFreePackageConfig();
    },
    onError: (error) => {
      toast.error(error.message || "更新失败");
    },
  });

  // 更新积分消耗比配置
  const updateConsumptionRatios = api.packageAdmin.updateCreditConsumptionRatios.useMutation({
    onSuccess: () => {
      toast.success('积分消耗比配置已更新');
      refetchConsumptionRatios();
    },
    onError: (error: any) => {
      toast.error(`更新失败: ${error.message}`);
    },
  });

  // 计算利润信息
  const { data: profitInfo, refetch: refetchProfitInfo } = api.packageAdmin.calculatePackageProfit.useQuery(
    {
      credits: packageForms.standard.credits,
      salePrice: packageForms.standard.salePrice,
      promoPrice: packageForms.standard.promoPrice || undefined,
    },
    { enabled: false }
  );

  // 初始化表单数据
  useEffect(() => {
    if (apiPricing) {
      setApiPricingForm({
        standardInputPrice: apiPricing.standardInputPrice,
        standardOutputPrice: apiPricing.standardOutputPrice,
        professionalInputPrice: apiPricing.professionalInputPrice,
        professionalOutputPrice: apiPricing.professionalOutputPrice,
        textGenerationInputPrice: (apiPricing as any).textGenerationInputPrice || 1.00,
        textGenerationOutputPrice: (apiPricing as any).textGenerationOutputPrice || 1.80,
      });
    }
  }, [apiPricing]);

  useEffect(() => {
    if (packagePricing && packagePricing.length > 0) {
      const newForms = { ...packageForms };
      packagePricing.forEach(pkg => {
        if (pkg.packageType in newForms) {
          newForms[pkg.packageType as keyof typeof newForms] = {
            credits: pkg.credits,
            salePrice: pkg.salePrice,
            promoPrice: pkg.promoPrice || 0,
          };
        }
      });
      setPackageForms(newForms);
    }
  }, [packagePricing]);

  // 初始化免费包配置表单
  useEffect(() => {
    if (freePackageConfig) {
      setFreePackageForm({
        enabled: freePackageConfig.enabled,
        giftType: freePackageConfig.giftType as 'daily' | 'monthly',
        dailyCredits: freePackageConfig.dailyCredits,
        monthlyCredits: freePackageConfig.monthlyCredits,
      });
    }
  }, [freePackageConfig]);

  // 初始化消耗比配置表单
  useEffect(() => {
    if (consumptionRatios) {
      setConsumptionRatioForm({
        textGenerationRatio: consumptionRatios.textGenerationRatio,
        standardVoiceRatio: consumptionRatios.standardVoiceRatio,
        professionalVoiceRatio: consumptionRatios.professionalVoiceRatio,
      });
    }
  }, [consumptionRatios]);

  const handleSaveApiPricing = () => {
    updateApiPricing.mutate(apiPricingForm);
  };

  const handleSaveFreePackageConfig = () => {
    updateFreePackageConfig.mutate(freePackageForm);
  };

  const handleSaveConsumptionRatios = () => {
    updateConsumptionRatios.mutate(consumptionRatioForm);
  };

  // 计算成本价和利润的函数
  const calculateCostAndProfit = (credits: number, salePrice: number, promoPrice?: number) => {
    if (!apiPricing) return { costPrice: 0, saleProfit: 0, promoProfit: 0, saleProfitMargin: 0, promoProfitMargin: 0 };

    // 计算成本价
    const tokensPerCredit = 1000;
    const totalTokens = credits * tokensPerCredit;
    const avgTokenCost = (apiPricing.standardInputPrice + apiPricing.standardOutputPrice) / 2;
    const costPrice = (totalTokens / 1000000) * avgTokenCost;

    // 计算利润
    const saleProfit = salePrice - costPrice;
    const saleProfitMargin = costPrice > 0 ? (saleProfit / costPrice) * 100 : 0;

    let promoProfit = 0;
    let promoProfitMargin = 0;
    if (promoPrice && promoPrice > 0) {
      promoProfit = promoPrice - costPrice;
      promoProfitMargin = costPrice > 0 ? (promoProfit / costPrice) * 100 : 0;
    }

    return {
      costPrice: Math.round(costPrice * 100) / 100,
      saleProfit: Math.round(saleProfit * 100) / 100,
      promoProfit: Math.round(promoProfit * 100) / 100,
      saleProfitMargin: Math.round(saleProfitMargin * 100) / 100,
      promoProfitMargin: Math.round(promoProfitMargin * 100) / 100,
    };
  };

  const handleSavePackage = (packageType: 'basic' | 'standard' | 'premium') => {
    const form = packageForms[packageType];
    updatePackagePricing.mutate({
      packageType,
      credits: form.credits,
      salePrice: form.salePrice,
      promoPrice: form.promoPrice > 0 ? form.promoPrice : undefined,
    });
  };

  // 加载状态
  if (status === 'loading' || userLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">加载中...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  // 未登录检查
  if (status === 'unauthenticated' || !session?.user) {
    router.push('/auth/signin');
    return null;
  }

  // 权限检查
  if (!userInfo || userInfo.role !== 'SUPER_ADMIN') {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="text-6xl mb-4">🚫</div>
              <CardTitle>访问被拒绝</CardTitle>
              <CardDescription>
                只有超级管理员才能访问套餐管理页面
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-center text-sm text-muted-foreground">
                <p>当前角色: {userInfo?.role || 'USER'}</p>
                <p>需要角色: SUPER_ADMIN</p>
              </div>
              <Button asChild className="w-full mt-4">
                <a href="/admin">返回管理中心</a>
              </Button>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">积分套餐管理</h1>
            <p className="text-muted-foreground">
              简单直接的定价管理：API成本 → 销售价格 → 优惠价格
            </p>
          </div>
          <Button onClick={() => { refetchApiPricing(); refetchPackagePricing(); refetchConsumptionRatios(); }} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新数据
          </Button>
        </div>

        {/* 统计卡片 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总套餐数</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalPackages}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总购买次数</CardTitle>
                <ShoppingCart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalPurchases}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总收入</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${stats.totalRevenue}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">活跃购买</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.activePurchases}</div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 主要内容 */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'packages' | 'api-pricing' | 'multipliers')}>
          <TabsList className="grid w-full grid-cols-3 max-w-lg">
            <TabsTrigger value="packages">积分包定价</TabsTrigger>
            <TabsTrigger value="api-pricing">API成本配置</TabsTrigger>
            <TabsTrigger value="multipliers">消耗比配置</TabsTrigger>
          </TabsList>

          <TabsContent value="packages" className="space-y-6">
            {/* 免费包配置 */}
            <Card className="border-emerald-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-emerald-600">
                  <Sparkles className="h-5 w-5" />
                  免费包配置
                </CardTitle>
                <CardDescription>配置每日或每月免费积分赠送</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="freePackageEnabled"
                      checked={freePackageForm.enabled}
                      onChange={(e) => setFreePackageForm({
                        ...freePackageForm,
                        enabled: e.target.checked
                      })}
                      className="rounded"
                    />
                    <Label htmlFor="freePackageEnabled">启用免费积分赠送</Label>
                  </div>

                  {freePackageForm.enabled && (
                    <>
                      <div>
                        <Label>赠送类型</Label>
                        <select
                          value={freePackageForm.giftType}
                          onChange={(e) => setFreePackageForm({
                            ...freePackageForm,
                            giftType: e.target.value as 'daily' | 'monthly'
                          })}
                          className="w-full p-2 border rounded-md bg-background"
                        >
                          <option value="daily">每日赠送</option>
                          <option value="monthly">每月赠送</option>
                        </select>
                      </div>

                      {freePackageForm.giftType === 'daily' && (
                        <div>
                          <Label>每日赠送积分</Label>
                          <Input
                            type="number"
                            value={freePackageForm.dailyCredits}
                            onChange={(e) => setFreePackageForm({
                              ...freePackageForm,
                              dailyCredits: parseInt(e.target.value) || 0
                            })}
                            placeholder="例如: 100"
                          />
                        </div>
                      )}

                      {freePackageForm.giftType === 'monthly' && (
                        <div>
                          <Label>每月赠送积分</Label>
                          <Input
                            type="number"
                            value={freePackageForm.monthlyCredits}
                            onChange={(e) => setFreePackageForm({
                              ...freePackageForm,
                              monthlyCredits: parseInt(e.target.value) || 0
                            })}
                            placeholder="例如: 3000"
                          />
                        </div>
                      )}
                    </>
                  )}
                </div>

                {/* 免费包预览 */}
                {freePackageForm.enabled && (
                  <div className="bg-muted/50 p-3 rounded-lg space-y-2 border">
                    <div className="text-sm font-medium text-foreground">免费包预览</div>
                    <div className="text-xs text-muted-foreground">
                      {freePackageForm.giftType === 'daily'
                        ? `用户每天可获得 ${freePackageForm.dailyCredits} 积分`
                        : `用户每月可获得 ${freePackageForm.monthlyCredits} 积分`
                      }
                    </div>
                    <div className="text-xs text-emerald-600 dark:text-emerald-400">
                      💝 完全免费，无需购买
                    </div>
                  </div>
                )}

                <Button
                  onClick={handleSaveFreePackageConfig}
                  disabled={updateFreePackageConfig.isPending}
                  className="w-full"
                >
                  {updateFreePackageConfig.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  保存免费包配置
                </Button>
              </CardContent>
            </Card>

            {/* 积分包定价管理 */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* 基础包 */}
              <Card className="border-blue-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-blue-600">
                    <Sparkles className="h-5 w-5" />
                    基础包
                  </CardTitle>
                  <CardDescription>适合轻度使用</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <Label>积分数量</Label>
                      <Input
                        type="number"
                        value={packageForms.basic.credits}
                        onChange={(e) => setPackageForms({
                          ...packageForms,
                          basic: { ...packageForms.basic, credits: parseInt(e.target.value) || 0 }
                        })}
                      />
                    </div>
                    <div>
                      <Label>销售价格 ($)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={packageForms.basic.salePrice}
                        onChange={(e) => setPackageForms({
                          ...packageForms,
                          basic: { ...packageForms.basic, salePrice: parseFloat(e.target.value) || 0 }
                        })}
                      />
                    </div>
                    <div>
                      <Label>优惠价格 ($) - 可选</Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={packageForms.basic.promoPrice}
                        onChange={(e) => setPackageForms({
                          ...packageForms,
                          basic: { ...packageForms.basic, promoPrice: parseFloat(e.target.value) || 0 }
                        })}
                        placeholder="留空表示无优惠"
                      />
                    </div>
                  </div>

                  {/* 成本价和利润显示 */}
                  {(() => {
                    const calculation = calculateCostAndProfit(
                      packageForms.basic.credits,
                      packageForms.basic.salePrice,
                      packageForms.basic.promoPrice > 0 ? packageForms.basic.promoPrice : undefined
                    );
                    return (
                      <div className="bg-muted/50 p-3 rounded-lg space-y-2 border">
                        <div className="text-sm font-medium text-foreground">财务分析</div>
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div>
                            <span className="text-muted-foreground">成本价:</span>
                            <span className="font-medium ml-1 text-foreground">${calculation.costPrice}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">销售利润:</span>
                            <span className={`font-medium ml-1 ${calculation.saleProfit >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                              ${calculation.saleProfit}
                            </span>
                          </div>
                          {packageForms.basic.promoPrice > 0 && (
                            <>
                              <div>
                                <span className="text-muted-foreground">优惠利润:</span>
                                <span className={`font-medium ml-1 ${calculation.promoProfit >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                                  ${calculation.promoProfit}
                                </span>
                              </div>
                              <div>
                                <span className="text-muted-foreground">优惠利润率:</span>
                                <span className={`font-medium ml-1 ${calculation.promoProfitMargin >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                                  {calculation.promoProfitMargin}%
                                </span>
                              </div>
                            </>
                          )}
                          <div>
                            <span className="text-muted-foreground">销售利润率:</span>
                            <span className={`font-medium ml-1 ${calculation.saleProfitMargin >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                              {calculation.saleProfitMargin}%
                            </span>
                          </div>
                        </div>
                      </div>
                    );
                  })()}
                  <Button
                    onClick={() => handleSavePackage('basic')}
                    disabled={updatePackagePricing.isPending}
                    className="w-full"
                  >
                    {updatePackagePricing.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    保存基础包
                  </Button>
                </CardContent>
              </Card>

              {/* 标准包 */}
              <Card className="border-green-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-600">
                    <Zap className="h-5 w-5" />
                    标准包
                  </CardTitle>
                  <CardDescription>最受欢迎的选择</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <Label>积分数量</Label>
                      <Input
                        type="number"
                        value={packageForms.standard.credits}
                        onChange={(e) => setPackageForms({
                          ...packageForms,
                          standard: { ...packageForms.standard, credits: parseInt(e.target.value) || 0 }
                        })}
                      />
                    </div>
                    <div>
                      <Label>销售价格 ($)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={packageForms.standard.salePrice}
                        onChange={(e) => setPackageForms({
                          ...packageForms,
                          standard: { ...packageForms.standard, salePrice: parseFloat(e.target.value) || 0 }
                        })}
                      />
                    </div>
                    <div>
                      <Label>优惠价格 ($) - 可选</Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={packageForms.standard.promoPrice}
                        onChange={(e) => setPackageForms({
                          ...packageForms,
                          standard: { ...packageForms.standard, promoPrice: parseFloat(e.target.value) || 0 }
                        })}
                        placeholder="留空表示无优惠"
                      />
                    </div>
                  </div>

                  {/* 成本价和利润显示 */}
                  {(() => {
                    const calculation = calculateCostAndProfit(
                      packageForms.standard.credits,
                      packageForms.standard.salePrice,
                      packageForms.standard.promoPrice > 0 ? packageForms.standard.promoPrice : undefined
                    );
                    return (
                      <div className="bg-muted/50 p-3 rounded-lg space-y-2 border">
                        <div className="text-sm font-medium text-foreground">财务分析</div>
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div>
                            <span className="text-muted-foreground">成本价:</span>
                            <span className="font-medium ml-1 text-foreground">${calculation.costPrice}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">销售利润:</span>
                            <span className={`font-medium ml-1 ${calculation.saleProfit >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                              ${calculation.saleProfit}
                            </span>
                          </div>
                          {packageForms.standard.promoPrice > 0 && (
                            <>
                              <div>
                                <span className="text-muted-foreground">优惠利润:</span>
                                <span className={`font-medium ml-1 ${calculation.promoProfit >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                                  ${calculation.promoProfit}
                                </span>
                              </div>
                              <div>
                                <span className="text-muted-foreground">优惠利润率:</span>
                                <span className={`font-medium ml-1 ${calculation.promoProfitMargin >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                                  {calculation.promoProfitMargin}%
                                </span>
                              </div>
                            </>
                          )}
                          <div>
                            <span className="text-muted-foreground">销售利润率:</span>
                            <span className={`font-medium ml-1 ${calculation.saleProfitMargin >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                              {calculation.saleProfitMargin}%
                            </span>
                          </div>
                        </div>
                      </div>
                    );
                  })()}
                  <Button
                    onClick={() => handleSavePackage('standard')}
                    disabled={updatePackagePricing.isPending}
                    className="w-full"
                  >
                    {updatePackagePricing.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    保存标准包
                  </Button>
                </CardContent>
              </Card>

              {/* 高级包 */}
              <Card className="border-purple-200">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-purple-600">
                    <Crown className="h-5 w-5" />
                    高级包
                  </CardTitle>
                  <CardDescription>大容量，更优惠</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <Label>积分数量</Label>
                      <Input
                        type="number"
                        value={packageForms.premium.credits}
                        onChange={(e) => setPackageForms({
                          ...packageForms,
                          premium: { ...packageForms.premium, credits: parseInt(e.target.value) || 0 }
                        })}
                      />
                    </div>
                    <div>
                      <Label>销售价格 ($)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={packageForms.premium.salePrice}
                        onChange={(e) => setPackageForms({
                          ...packageForms,
                          premium: { ...packageForms.premium, salePrice: parseFloat(e.target.value) || 0 }
                        })}
                      />
                    </div>
                    <div>
                      <Label>优惠价格 ($) - 可选</Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={packageForms.premium.promoPrice}
                        onChange={(e) => setPackageForms({
                          ...packageForms,
                          premium: { ...packageForms.premium, promoPrice: parseFloat(e.target.value) || 0 }
                        })}
                        placeholder="留空表示无优惠"
                      />
                    </div>
                  </div>

                  {/* 成本价和利润显示 */}
                  {(() => {
                    const calculation = calculateCostAndProfit(
                      packageForms.premium.credits,
                      packageForms.premium.salePrice,
                      packageForms.premium.promoPrice > 0 ? packageForms.premium.promoPrice : undefined
                    );
                    return (
                      <div className="bg-muted/50 p-3 rounded-lg space-y-2 border">
                        <div className="text-sm font-medium text-foreground">财务分析</div>
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div>
                            <span className="text-muted-foreground">成本价:</span>
                            <span className="font-medium ml-1 text-foreground">${calculation.costPrice}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">销售利润:</span>
                            <span className={`font-medium ml-1 ${calculation.saleProfit >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                              ${calculation.saleProfit}
                            </span>
                          </div>
                          {packageForms.premium.promoPrice > 0 && (
                            <>
                              <div>
                                <span className="text-muted-foreground">优惠利润:</span>
                                <span className={`font-medium ml-1 ${calculation.promoProfit >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                                  ${calculation.promoProfit}
                                </span>
                              </div>
                              <div>
                                <span className="text-muted-foreground">优惠利润率:</span>
                                <span className={`font-medium ml-1 ${calculation.promoProfitMargin >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                                  {calculation.promoProfitMargin}%
                                </span>
                              </div>
                            </>
                          )}
                          <div>
                            <span className="text-muted-foreground">销售利润率:</span>
                            <span className={`font-medium ml-1 ${calculation.saleProfitMargin >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                              {calculation.saleProfitMargin}%
                            </span>
                          </div>
                        </div>
                      </div>
                    );
                  })()}
                  <Button
                    onClick={() => handleSavePackage('premium')}
                    disabled={updatePackagePricing.isPending}
                    className="w-full"
                  >
                    {updatePackagePricing.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    保存高级包
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="api-pricing" className="space-y-6">
            {/* API成本配置 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5 text-orange-500" />
                  API Token成本配置
                </CardTitle>
                <CardDescription>
                  配置API的Token价格，用于计算积分包的成本价
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* 标准语音 */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-blue-600">标准语音</h4>
                    <div className="space-y-3">
                      <div>
                        <Label>输入价格 ($/100万token)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={apiPricingForm.standardInputPrice}
                          onChange={(e) => setApiPricingForm({
                            ...apiPricingForm,
                            standardInputPrice: parseFloat(e.target.value) || 0
                          })}
                        />
                      </div>
                      <div>
                        <Label>输出价格 ($/100万token)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={apiPricingForm.standardOutputPrice}
                          onChange={(e) => setApiPricingForm({
                            ...apiPricingForm,
                            standardOutputPrice: parseFloat(e.target.value) || 0
                          })}
                        />
                      </div>
                    </div>
                  </div>

                  {/* 专业语音 */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-purple-600">专业语音</h4>
                    <div className="space-y-3">
                      <div>
                        <Label>输入价格 ($/100万token)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={apiPricingForm.professionalInputPrice}
                          onChange={(e) => setApiPricingForm({
                            ...apiPricingForm,
                            professionalInputPrice: parseFloat(e.target.value) || 0
                          })}
                        />
                      </div>
                      <div>
                        <Label>输出价格 ($/100万token)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={apiPricingForm.professionalOutputPrice}
                          onChange={(e) => setApiPricingForm({
                            ...apiPricingForm,
                            professionalOutputPrice: parseFloat(e.target.value) || 0
                          })}
                        />
                      </div>
                    </div>
                  </div>

                  {/* AI文本生成 */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-green-600">AI文本生成</h4>
                    <div className="space-y-3">
                      <div>
                        <Label>输入价格 ($/100万token)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={apiPricingForm.textGenerationInputPrice}
                          onChange={(e) => setApiPricingForm({
                            ...apiPricingForm,
                            textGenerationInputPrice: parseFloat(e.target.value) || 0
                          })}
                        />
                      </div>
                      <div>
                        <Label>输出价格 ($/100万token)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={apiPricingForm.textGenerationOutputPrice}
                          onChange={(e) => setApiPricingForm({
                            ...apiPricingForm,
                            textGenerationOutputPrice: parseFloat(e.target.value) || 0
                          })}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <Separator className="my-6" />

                <div className="flex justify-end">
                  <Button
                    onClick={handleSaveApiPricing}
                    disabled={updateApiPricing.isPending}
                    className="min-w-[120px]"
                  >
                    {updateApiPricing.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    <Save className="h-4 w-4 mr-2" />
                    保存API配置
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="multipliers" className="space-y-6">
            {/* 积分消耗比配置 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5 text-purple-500" />
                  积分消耗比配置
                </CardTitle>
                <CardDescription>
                  配置不同服务类型的tokens消耗比。积分 = ceil(tokens / 消耗比)，1积分 = $0.01
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* 文本生成消耗比 */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-blue-600">文本生成</h4>
                    <div className="space-y-3">
                      <div>
                        <Label>消耗比 (tokens/积分)</Label>
                        <Input
                          type="number"
                          step="1"
                          min="1"
                          value={consumptionRatioForm.textGenerationRatio}
                          onChange={(e) => setConsumptionRatioForm({
                            ...consumptionRatioForm,
                            textGenerationRatio: parseInt(e.target.value) || 1
                          })}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          {consumptionRatioForm.textGenerationRatio} tokens = 1积分
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* TTS标准语音消耗比 */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-green-600">TTS标准语音</h4>
                    <div className="space-y-3">
                      <div>
                        <Label>消耗比 (tokens/积分)</Label>
                        <Input
                          type="number"
                          step="1"
                          min="1"
                          value={consumptionRatioForm.standardVoiceRatio}
                          onChange={(e) => setConsumptionRatioForm({
                            ...consumptionRatioForm,
                            standardVoiceRatio: parseInt(e.target.value) || 1
                          })}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          {consumptionRatioForm.standardVoiceRatio} tokens = 1积分
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* TTS高质量语音消耗比 */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-purple-600">TTS高质量语音</h4>
                    <div className="space-y-3">
                      <div>
                        <Label>消耗比 (tokens/积分)</Label>
                        <Input
                          type="number"
                          step="1"
                          min="1"
                          value={consumptionRatioForm.professionalVoiceRatio}
                          onChange={(e) => setConsumptionRatioForm({
                            ...consumptionRatioForm,
                            professionalVoiceRatio: parseInt(e.target.value) || 1
                          })}
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          {consumptionRatioForm.professionalVoiceRatio} tokens = 1积分
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 计算示例 */}
                <div className="mt-6 p-4 bg-muted rounded-lg">
                  <h5 className="font-medium mb-3">计算示例</h5>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="font-medium text-blue-600">文本生成 (1000 tokens)</p>
                      <p>积分需求 = ceil(1000 ÷ {consumptionRatioForm.textGenerationRatio}) = {Math.ceil(1000 / consumptionRatioForm.textGenerationRatio)}积分</p>
                      <p>成本 = {Math.ceil(1000 / consumptionRatioForm.textGenerationRatio)} × $0.01 = ${(Math.ceil(1000 / consumptionRatioForm.textGenerationRatio) * 0.01).toFixed(2)}</p>
                    </div>
                    <div>
                      <p className="font-medium text-green-600">TTS标准 (200 tokens)</p>
                      <p>积分需求 = ceil(200 ÷ {consumptionRatioForm.standardVoiceRatio}) = {Math.ceil(200 / consumptionRatioForm.standardVoiceRatio)}积分</p>
                      <p>成本 = {Math.ceil(200 / consumptionRatioForm.standardVoiceRatio)} × $0.01 = ${(Math.ceil(200 / consumptionRatioForm.standardVoiceRatio) * 0.01).toFixed(2)}</p>
                    </div>
                    <div>
                      <p className="font-medium text-purple-600">TTS高质量 (200 tokens)</p>
                      <p>积分需求 = ceil(200 ÷ {consumptionRatioForm.professionalVoiceRatio}) = {Math.ceil(200 / consumptionRatioForm.professionalVoiceRatio)}积分</p>
                      <p>成本 = {Math.ceil(200 / consumptionRatioForm.professionalVoiceRatio)} × $0.01 = ${(Math.ceil(200 / consumptionRatioForm.professionalVoiceRatio) * 0.01).toFixed(2)}</p>
                    </div>
                  </div>
                </div>

                {/* 保存按钮 */}
                <div className="flex justify-end mt-6">
                  <Button
                    onClick={handleSaveConsumptionRatios}
                    disabled={updateConsumptionRatios.isPending}
                    className="min-w-[120px]"
                  >
                    {updateConsumptionRatios.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    <Save className="h-4 w-4 mr-2" />
                    保存消耗比配置
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}
