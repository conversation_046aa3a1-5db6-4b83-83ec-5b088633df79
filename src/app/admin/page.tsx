"use client";

import { useState, useEffect } from "react";
import { api } from "~/trpc/react";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { AppLayout } from "~/components/app-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { useTranslation } from "~/contexts/LanguageContext";
import { RouteGuard } from "~/components/auth/route-guard";
import {
  Users,
  Volume2, 
  TrendingUp, 
  Activity, 
  DollarSign, 
  Clock,
  Shield,
  Settings,
  BarChart3,
  UserCheck,
  Database,
  ArrowRight
} from "lucide-react";

export default function AdminPage() {
  return (
    <RouteGuard requireAuth={true} requireSuperAdmin={true}>
      <AdminPageContent />
    </RouteGuard>
  );
}

function AdminPageContent() {
  const { t } = useTranslation();
  const { data: session, status } = useSession();
  const router = useRouter();

  // 获取用户信息
  const { data: userInfo, isLoading: userLoading, error: userError } = api.auth.getCurrentUser.useQuery(
    undefined,
    { enabled: !!session?.user }
  );

  // 获取统计数据
  const { data: stats, isLoading: statsLoading, error: statsError } = api.admin.getStats.useQuery(
    undefined,
    { enabled: !!session?.user && userInfo?.role === 'SUPER_ADMIN' }
  );

  // 加载状态
  if (status === 'loading' || userLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-muted-foreground">验证权限中...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  // 权限不足 - 只有超级管理员可以访问
  if (!session?.user || !userInfo || userInfo.role !== 'SUPER_ADMIN') {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="text-6xl mb-4">🚫</div>
              <CardTitle>{t('admin.title')}</CardTitle>
              <CardDescription>
                {t('admin.subtitle')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-center text-sm text-muted-foreground">
                <p>{t('admin.role')}: {userInfo?.role || t('admin.user_user')}</p>
                <p>{t('admin.role')}: SUPER_ADMIN</p>
              </div>
              <Button asChild className="w-full mt-4">
                <Link href="/dashboard">{t('navigation.dashboard')}</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">🛠️ {t('admin.title')}</h1>
          <p className="text-muted-foreground">{t('admin.subtitle')}</p>
        </div>

        {/* System Stats */}
        {statsLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-muted-foreground mt-2">{t('common.loading')}</p>
          </div>
        ) : stats ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('admin.total_users')}</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalUsers}</div>
                <p className="text-xs text-muted-foreground">
                  {t('admin.total_users')}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('admin.active_users')}</CardTitle>
                <UserCheck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.activeUsers}</div>
                <p className="text-xs text-muted-foreground">
                  {t('admin.active_users')}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('admin.total_generations')}</CardTitle>
                <Volume2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalGenerations}</div>
                <p className="text-xs text-muted-foreground">
                  {t('admin.total_generations')}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('admin.new_users_today')}</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.todayGenerations}</div>
                <p className="text-xs text-muted-foreground">
                  {t('admin.new_users_today')}
                </p>
              </CardContent>
            </Card>
          </div>
        ) : (
          <Card>
            <CardContent className="pt-6">
              <p className="text-muted-foreground">{t('common.error')}</p>
            </CardContent>
          </Card>
        )}

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>{t('common.get_started')}</CardTitle>
            <CardDescription>
              {t('admin.subtitle')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Button asChild variant="outline" className="h-auto p-4 justify-start">
                <Link href="/admin/users">
                  <div className="flex items-center space-x-3">
                    <Users className="h-8 w-8 text-blue-600" />
                    <div className="text-left">
                      <p className="font-medium">{t('admin.users')}</p>
                      <p className="text-sm text-muted-foreground">{t('admin.user_management')}</p>
                    </div>
                  </div>
                </Link>
              </Button>

              <Button asChild variant="outline" className="h-auto p-4 justify-start">
                <Link href="/admin/languages">
                  <div className="flex items-center space-x-3">
                    <Database className="h-8 w-8 text-cyan-600" />
                    <div className="text-left">
                      <p className="font-medium">语言管理</p>
                      <p className="text-sm text-muted-foreground">管理支持的语言和地区</p>
                    </div>
                  </div>
                </Link>
              </Button>

              <Button asChild variant="outline" className="h-auto p-4 justify-start">
                <Link href="/admin/language-characters">
                  <div className="flex items-center space-x-3">
                    <Volume2 className="h-8 w-8 text-purple-600" />
                    <div className="text-left">
                      <p className="font-medium">语言角色管理</p>
                      <p className="text-sm text-muted-foreground">管理各语言下的语音角色</p>
                    </div>
                  </div>
                </Link>
              </Button>

              <Button asChild variant="outline" className="h-auto p-4 justify-start">
                <Link href="/admin/voice-stats">
                  <div className="flex items-center space-x-3">
                    <BarChart3 className="h-8 w-8 text-green-600" />
                    <div className="text-left">
                      <p className="font-medium">语音统计</p>
                      <p className="text-sm text-muted-foreground">查看语音生成统计数据</p>
                    </div>
                  </div>
                </Link>
              </Button>

              <Button asChild variant="outline" className="h-auto p-4 justify-start">
                <Link href="/admin/settings">
                  <div className="flex items-center space-x-3">
                    <Settings className="h-8 w-8 text-orange-600" />
                    <div className="text-left">
                      <p className="font-medium">系统设置</p>
                      <p className="text-sm text-muted-foreground">管理系统配置</p>
                    </div>
                  </div>
                </Link>
              </Button>

              <Button asChild variant="outline" className="h-auto p-4 justify-start">
                <Link href="/admin/debug">
                  <div className="flex items-center space-x-3">
                    <Database className="h-8 w-8 text-red-600" />
                    <div className="text-left">
                      <p className="font-medium">调试工具</p>
                      <p className="text-sm text-muted-foreground">系统调试和诊断</p>
                    </div>
                  </div>
                </Link>
              </Button>

              <Button asChild variant="outline" className="h-auto p-4 justify-start">
                <Link href="/admin/packages">
                  <div className="flex items-center space-x-3">
                    <DollarSign className="h-8 w-8 text-green-600" />
                    <div className="text-left">
                      <p className="font-medium">套餐管理</p>
                      <p className="text-sm text-muted-foreground">管理字符包和定价</p>
                    </div>
                  </div>
                </Link>
              </Button>

              <Button asChild variant="outline" className="h-auto p-4 justify-start">
                <Link href="/admin/security">
                  <div className="flex items-center space-x-3">
                    <Shield className="h-8 w-8 text-indigo-600" />
                    <div className="text-left">
                      <p className="font-medium">{t('settings.security')}</p>
                      <p className="text-sm text-muted-foreground">{t('settings.security')}</p>
                    </div>
                  </div>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>最近活动</CardTitle>
            <CardDescription>
              系统最近的重要活动
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">系统正常运行</p>
                  <p className="text-xs text-muted-foreground">所有服务运行正常</p>
                </div>
                <Badge variant="secondary">刚刚</Badge>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">新用户注册</p>
                  <p className="text-xs text-muted-foreground">今日新增 {stats?.todayNewUsers || 0} 名用户</p>
                </div>
                <Badge variant="secondary">今天</Badge>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">语音生成活跃</p>
                  <p className="text-xs text-muted-foreground">今日生成 {stats?.todayGenerations || 0} 次语音</p>
                </div>
                <Badge variant="secondary">今天</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
