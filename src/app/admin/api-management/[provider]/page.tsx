'use client';

import { useState } from 'react';
import { useParams } from 'next/navigation';
import { AppLayout } from '~/components/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { But<PERSON> } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Separator } from '~/components/ui/separator';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { Progress } from '~/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { 
  ArrowLeft,
  Activity, 
  DollarSign, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Clock,
  TrendingUp,
  Settings,
  BarChart3,
  Users,
  Zap
} from 'lucide-react';
import { api } from '~/trpc/react';
import Link from 'next/link';

export default function ApiProviderDetailPage() {
  const params = useParams();
  const provider = (params.provider as string)?.toUpperCase() as 'GEMINI' | 'OPENAI' | 'OPENROUTER';

  // 获取配置详情
  const { data: config, isLoading: configLoading } = api.apiManagement.getConfig.useQuery({
    provider,
  });

  // 获取使用统计
  const { data: stats, isLoading: statsLoading } = api.apiManagement.getUsageStats.useQuery({
    provider,
  });

  // 获取使用日志
  const { data: logs, isLoading: logsLoading } = api.apiManagement.getUsageLogs.useQuery({
    provider,
    limit: 20,
  });

  if (configLoading) {
    return (
      <AppLayout>
        <div className="container mx-auto p-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-muted-foreground">加载中...</p>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (!config) {
    return (
      <AppLayout>
        <div className="container mx-auto p-6">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              未找到 API 配置信息。
            </AlertDescription>
          </Alert>
        </div>
      </AppLayout>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'text-green-600';
      case 'INACTIVE': return 'text-gray-600';
      case 'MAINTENANCE': return 'text-yellow-600';
      case 'ERROR': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE': return <CheckCircle className="h-4 w-4" />;
      case 'INACTIVE': return <XCircle className="h-4 w-4" />;
      case 'MAINTENANCE': return <AlertTriangle className="h-4 w-4" />;
      case 'ERROR': return <XCircle className="h-4 w-4" />;
      default: return <XCircle className="h-4 w-4" />;
    }
  };

  return (
    <AppLayout>
      <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题和导航 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/admin/api-management">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">{config.displayName}</h1>
            <p className="text-muted-foreground">{config.description}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className={`flex items-center gap-2 ${getStatusColor(config.status)}`}>
            {getStatusIcon(config.status)}
            <span className="font-medium">
              {config.status === 'ACTIVE' ? '正常运行' : 
               config.status === 'INACTIVE' ? '已停用' :
               config.status === 'MAINTENANCE' ? '维护中' : '错误'}
            </span>
          </div>
          <Badge variant={config.isEnabled ? 'default' : 'secondary'}>
            {config.isEnabled ? '已启用' : '已禁用'}
          </Badge>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总请求数</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{config.totalRequests}</div>
            <p className="text-xs text-muted-foreground">
              成功率: {config.totalRequests > 0 ? ((config.successRequests / config.totalRequests) * 100).toFixed(1) : 0}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总成本</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${config.totalCost.toFixed(4)}</div>
            <p className="text-xs text-muted-foreground">
              累计费用
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">成功请求</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{config.successRequests}</div>
            <p className="text-xs text-muted-foreground">
              成功的请求数
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">失败请求</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{config.failedRequests}</div>
            <p className="text-xs text-muted-foreground">
              失败的请求数
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 详细信息标签页 */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="limits">限额管理</TabsTrigger>
          <TabsTrigger value="logs">使用日志</TabsTrigger>
          <TabsTrigger value="config">配置信息</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>基本信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">提供商:</span>
                  <span>{config.provider}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">优先级:</span>
                  <span>{config.priority}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">最后使用:</span>
                  <span>{config.lastUsedAt ? new Date(config.lastUsedAt).toLocaleString() : '从未使用'}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>性能统计</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {statsLoading ? (
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
                  </div>
                ) : stats ? (
                  <>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">平均响应时间:</span>
                      <span>{stats.avgResponseTime?.toFixed(0) || 0}ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">成功率:</span>
                      <span>{stats.successRate?.toFixed(1) || 0}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">总成本:</span>
                      <span>${stats.totalCost?.toFixed(4) || 0}</span>
                    </div>
                  </>
                ) : (
                  <p className="text-muted-foreground">暂无统计数据</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="limits" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>限额设置</CardTitle>
              <CardDescription>
                当前配置的使用限额和使用情况
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {config.dailyLimit && (
                <div>
                  <div className="flex justify-between mb-2">
                    <span>每日请求限额</span>
                    <span>0 / {config.dailyLimit}</span>
                  </div>
                  <Progress value={0} className="h-2" />
                </div>
              )}
              
              {config.monthlyLimit && (
                <div>
                  <div className="flex justify-between mb-2">
                    <span>每月请求限额</span>
                    <span>0 / {config.monthlyLimit}</span>
                  </div>
                  <Progress value={0} className="h-2" />
                </div>
              )}
              
              {config.costLimit && (
                <div>
                  <div className="flex justify-between mb-2">
                    <span>成本限额</span>
                    <span>$0 / ${config.costLimit}</span>
                  </div>
                  <Progress value={0} className="h-2" />
                </div>
              )}

              {!config.dailyLimit && !config.monthlyLimit && !config.costLimit && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    未设置任何限额。建议设置适当的限额以控制成本。
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>最近使用日志</CardTitle>
              <CardDescription>
                最近 20 条 API 调用记录
              </CardDescription>
            </CardHeader>
            <CardContent>
              {logsLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-2 text-muted-foreground">加载日志中...</p>
                </div>
              ) : logs?.logs?.length ? (
                <div className="space-y-2">
                  {logs.logs.map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex items-center space-x-3">
                        {log.success ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-500" />
                        )}
                        <div>
                          <div className="font-medium">{log.requestType}</div>
                          <div className="text-sm text-muted-foreground">
                            {log.inputSize} 字符 • {log.responseTime}ms
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm">{new Date(log.createdAt).toLocaleString()}</div>
                        {log.cost && (
                          <div className="text-sm text-muted-foreground">${log.cost.toFixed(4)}</div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    暂无使用日志记录。
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="config" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>配置信息</CardTitle>
              <CardDescription>
                当前 API 配置参数（敏感信息已隐藏）
              </CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted p-4 rounded text-sm overflow-auto">
                {JSON.stringify(
                  {
                    ...config.config,
                    apiKey: config.config.apiKey ? '***' : undefined,
                  },
                  null,
                  2
                )}
              </pre>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 操作按钮 */}
      <div className="flex justify-end space-x-2">
        <Link href={`/admin/api-management/${provider.toLowerCase()}/edit`}>
          <Button>
            <Settings className="h-4 w-4 mr-2" />
            编辑配置
          </Button>
        </Link>
      </div>
      </div>
    </AppLayout>
  );
}
