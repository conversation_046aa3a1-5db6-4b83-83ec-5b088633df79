'use client';

import { useState } from 'react';
import { AppLayout } from '~/components/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { <PERSON><PERSON> } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Separator } from '~/components/ui/separator';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { Switch } from '~/components/ui/switch';
import { Progress } from '~/components/ui/progress';
import {
  Settings,
  Activity,
  DollarSign,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Zap,
  TrendingUp,
  Server,
  Eye,
  Edit,
  TestTube,
  BarChart3
} from 'lucide-react';
import { api } from '~/trpc/react';
import Link from 'next/link';

export default function ApiManagementPage() {
  const [selectedProvider, setSelectedProvider] = useState<string | null>(null);

  // 获取API概览数据
  const { data: overview, isLoading, refetch } = api.apiManagement.getOverview.useQuery();

  // 更新配置的mutation
  const updateConfigMutation = api.apiManagement.updateConfig.useMutation({
    onSuccess: () => {
      refetch();
    },
  });

  // 测试连接的mutation
  const testConnectionMutation = api.apiManagement.testConnection.useMutation();

  // 初始化默认配置的mutation
  const initializeDefaultsMutation = api.apiManagement.initializeDefaults.useMutation({
    onSuccess: () => {
      refetch();
    },
  });

  const handleToggleProvider = async (provider: string, enabled: boolean) => {
    await updateConfigMutation.mutateAsync({
      provider: provider as any,
      isEnabled: enabled,
    });
  };

  const handleTestConnection = async (provider: string) => {
    const result = await testConnectionMutation.mutateAsync({
      provider: provider as any,
    });
    
    alert(result.success ? '连接测试成功！' : `连接测试失败：${result.errorMessage}`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-500';
      case 'INACTIVE': return 'bg-gray-500';
      case 'MAINTENANCE': return 'bg-yellow-500';
      case 'ERROR': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'ACTIVE': return '正常';
      case 'INACTIVE': return '停用';
      case 'MAINTENANCE': return '维护';
      case 'ERROR': return '错误';
      default: return '未知';
    }
  };

  if (isLoading) {
    return (
      <AppLayout>
        <div className="container mx-auto p-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-muted-foreground">加载中...</p>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Server className="h-6 w-6" />
          <h1 className="text-2xl font-bold">接口管理</h1>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => initializeDefaultsMutation.mutate()}
            disabled={initializeDefaultsMutation.isPending}
          >
            <Settings className="h-4 w-4 mr-2" />
            初始化默认配置
          </Button>
          <Link href="/admin/api-management/logs">
            <Button variant="outline">
              <BarChart3 className="h-4 w-4 mr-2" />
              查看日志
            </Button>
          </Link>
        </div>
      </div>

      {/* 统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日请求</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview?.totalTodayRequests || 0}</div>
            <p className="text-xs text-muted-foreground">
              总请求数
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日成本</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${(overview?.totalTodayCost || 0).toFixed(4)}</div>
            <p className="text-xs text-muted-foreground">
              美元
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">本月请求</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview?.totalMonthRequests || 0}</div>
            <p className="text-xs text-muted-foreground">
              总请求数
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">本月成本</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${(overview?.totalMonthCost || 0).toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              美元
            </p>
          </CardContent>
        </Card>
      </div>

      {/* API 提供商列表 */}
      <Card>
        <CardHeader>
          <CardTitle>API 提供商</CardTitle>
          <CardDescription>
            管理和监控所有 API 服务提供商
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!overview?.configs?.length ? (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                没有找到 API 配置。请点击"初始化默认配置"来创建默认设置。
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              {overview.configs.map((config) => (
                <div key={config.provider} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(config.status)}`} />
                      <div>
                        <h3 className="font-medium">{config.displayName}</h3>
                        <p className="text-sm text-muted-foreground">{config.description}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <Badge variant={config.status === 'ACTIVE' ? 'default' : 'secondary'}>
                        {getStatusText(config.status)}
                      </Badge>
                      <Badge variant="outline">
                        优先级 {config.priority}
                      </Badge>
                      <Switch
                        checked={config.isEnabled}
                        onCheckedChange={(enabled) => handleToggleProvider(config.provider, enabled)}
                      />
                    </div>
                  </div>

                  <Separator className="my-3" />

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <div className="text-muted-foreground">今日请求</div>
                      <div className="font-medium">{config.todayRequests || 0}</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">今日成本</div>
                      <div className="font-medium">${(config.todayCost || 0).toFixed(4)}</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">本月请求</div>
                      <div className="font-medium">{config.monthRequests || 0}</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">本月成本</div>
                      <div className="font-medium">${(config.monthCost || 0).toFixed(2)}</div>
                    </div>
                  </div>

                  {/* 限额进度条 */}
                  {(config.dailyLimit || config.monthlyLimit || config.costLimit) && (
                    <>
                      <Separator className="my-3" />
                      <div className="space-y-2">
                        {config.dailyLimit && (
                          <div>
                            <div className="flex justify-between text-xs text-muted-foreground mb-1">
                              <span>每日限额</span>
                              <span>{config.todayRequests || 0} / {config.dailyLimit}</span>
                            </div>
                            <Progress 
                              value={((config.todayRequests || 0) / config.dailyLimit) * 100} 
                              className="h-2"
                            />
                          </div>
                        )}
                        {config.monthlyLimit && (
                          <div>
                            <div className="flex justify-between text-xs text-muted-foreground mb-1">
                              <span>每月限额</span>
                              <span>{config.monthRequests || 0} / {config.monthlyLimit}</span>
                            </div>
                            <Progress 
                              value={((config.monthRequests || 0) / config.monthlyLimit) * 100} 
                              className="h-2"
                            />
                          </div>
                        )}
                        {config.costLimit && (
                          <div>
                            <div className="flex justify-between text-xs text-muted-foreground mb-1">
                              <span>成本限额</span>
                              <span>${(config.monthCost || 0).toFixed(2)} / ${config.costLimit}</span>
                            </div>
                            <Progress 
                              value={((config.monthCost || 0) / config.costLimit) * 100} 
                              className="h-2"
                            />
                          </div>
                        )}
                      </div>
                    </>
                  )}

                  <Separator className="my-3" />

                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleTestConnection(config.provider)}
                      disabled={testConnectionMutation.isPending}
                    >
                      <TestTube className="h-4 w-4 mr-1" />
                      测试连接
                    </Button>
                    <Link href={`/admin/api-management/${config.provider.toLowerCase()}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        查看详情
                      </Button>
                    </Link>
                    <Link href={`/admin/api-management/${config.provider.toLowerCase()}/edit`}>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4 mr-1" />
                        编辑配置
                      </Button>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
      </div>
    </AppLayout>
  );
}
