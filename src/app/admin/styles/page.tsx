"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "~/trpc/react";
import { AppLayout } from "~/components/app-layout";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Switch } from "~/components/ui/switch";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { RouteGuard } from "~/components/auth/route-guard";
import {
  Palette,
  Plus,
  Search,
  Edit,
  Trash2,
  Filter,
  RefreshCw,
  Users,
  User,
  Star,
  Eye,
  Settings
} from "lucide-react";
import { toast } from "sonner";

export default function AdminStylesPage() {
  return (
    <RouteGuard requireSuperAdmin={true}>
      <AdminStylesPageContent />
    </RouteGuard>
  );
}

function AdminStylesPageContent() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingStyle, setEditingStyle] = useState<any>(null);

  // API queries
  const { data: categories } = api.styleManagement.getCategories.useQuery();
  const { data: stylesData, refetch: refetchStyles } = api.admin.getAllStyles.useQuery({
    search: searchQuery,
    categoryId: selectedCategory === "all" ? undefined : selectedCategory,
    status: statusFilter === "all" ? undefined : statusFilter,
    type: typeFilter === "all" ? undefined : (typeFilter as "SINGLE_SPEAKER" | "MULTI_SPEAKER"),
    includeUserStyles: true,
  });

  // API mutations
  const createStyleMutation = api.admin.createSystemStyle.useMutation({
    onSuccess: () => {
      toast.success("官方风格创建成功！");
      setIsCreateDialogOpen(false);
      refetchStyles();
    },
    onError: (error) => {
      toast.error(`创建失败：${error.message}`);
    },
  });

  const updateStyleMutation = api.admin.updateStyle.useMutation({
    onSuccess: () => {
      toast.success("风格更新成功！");
      setEditingStyle(null);
      refetchStyles();
    },
    onError: (error) => {
      toast.error(`更新失败：${error.message}`);
    },
  });

  const deleteStyleMutation = api.admin.deleteStyle.useMutation({
    onSuccess: () => {
      toast.success("风格删除成功！");
      refetchStyles();
    },
    onError: (error) => {
      toast.error(`删除失败：${error.message}`);
    },
  });

  const toggleStyleStatusMutation = api.admin.toggleStyleStatus.useMutation({
    onSuccess: () => {
      refetchStyles();
    },
  });

  const handleCreateStyle = (formData: FormData) => {
    const data = {
      name: formData.get("name") as string,
      nameEn: formData.get("nameEn") as string,
      description: formData.get("description") as string,
      prompt: formData.get("prompt") as string,
      categoryId: formData.get("categoryId") as string,
      type: formData.get("type") as "SINGLE_SPEAKER" | "MULTI_SPEAKER",
      tags: (formData.get("tags") as string).split(",").map(tag => tag.trim()).filter(Boolean),
      isOfficial: true,
      isPublic: true,
    };

    createStyleMutation.mutate(data);
  };

  const handleUpdateStyle = (formData: FormData) => {
    if (!editingStyle) return;

    const data = {
      id: editingStyle.id,
      name: formData.get("name") as string,
      nameEn: formData.get("nameEn") as string,
      description: formData.get("description") as string,
      prompt: formData.get("prompt") as string,
      categoryId: formData.get("categoryId") as string,
      type: formData.get("type") as "SINGLE_SPEAKER" | "MULTI_SPEAKER",
      tags: (formData.get("tags") as string).split(",").map(tag => tag.trim()).filter(Boolean),
      isPublic: formData.get("isPublic") === "on",
    };

    updateStyleMutation.mutate(data);
  };

  const handleDeleteStyle = (styleId: string) => {
    if (confirm("确定要删除这个风格吗？删除后无法恢复。")) {
      deleteStyleMutation.mutate({ id: styleId });
    }
  };

  const handleToggleStatus = (styleId: string, currentStatus: boolean) => {
    toggleStyleStatusMutation.mutate({
      id: styleId,
      isActive: !currentStatus,
    });
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'MULTI_SPEAKER':
        return <Users className="h-4 w-4" />;
      case 'SINGLE_SPEAKER':
        return <User className="h-4 w-4" />;
      default:
        return <Palette className="h-4 w-4" />;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'MULTI_SPEAKER':
        return '多人语音';
      case 'SINGLE_SPEAKER':
        return '单人语音';
      default:
        return '未知类型';
    }
  };

  // 筛选后的风格数据
  const filteredStyles = stylesData?.styles || [];

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Palette className="h-8 w-8 text-purple-600" />
              风格管理
            </h1>
            <p className="text-muted-foreground">管理系统官方风格和用户自定义风格</p>
          </div>
          <div className="flex items-center space-x-2">
            <Button onClick={() => refetchStyles()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新
            </Button>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              创建官方风格
            </Button>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总风格数</CardTitle>
              <Palette className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{filteredStyles.length}</div>
              <p className="text-xs text-muted-foreground">包含官方和用户风格</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">官方风格</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {filteredStyles.filter(s => s.isOfficial).length}
              </div>
              <p className="text-xs text-muted-foreground">系统官方风格</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">用户风格</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {filteredStyles.filter(s => !s.isOfficial).length}
              </div>
              <p className="text-xs text-muted-foreground">用户创建风格</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">启用风格</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {filteredStyles.filter(s => s.isActive).length}
              </div>
              <p className="text-xs text-muted-foreground">当前启用的风格</p>
            </CardContent>
          </Card>
        </div>

        {/* 筛选和搜索 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              筛选和搜索
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label>搜索风格</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索风格名称..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>分类筛选</Label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择分类" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部分类</SelectItem>
                    {categories?.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.icon} {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>状态筛选</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="active">启用</SelectItem>
                    <SelectItem value="inactive">禁用</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>类型筛选</Label>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    <SelectItem value="SINGLE_SPEAKER">单人语音</SelectItem>
                    <SelectItem value="MULTI_SPEAKER">多人语音</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 风格列表 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>风格列表</CardTitle>
                <CardDescription>
                  管理所有系统风格 ({filteredStyles.length} 个风格)
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>风格信息</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>分类</TableHead>
                  <TableHead>创建者</TableHead>
                  <TableHead>使用统计</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredStyles.map((style) => (
                  <TableRow key={style.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium flex items-center gap-2">
                          {style.name}
                          {style.isOfficial && (
                            <Badge variant="secondary">官方</Badge>
                          )}
                          {style.isPublic ? (
                            <Badge variant="default">公开</Badge>
                          ) : (
                            <Badge variant="outline">私有</Badge>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {style.description || "暂无描述"}
                        </div>
                        {style.tags && style.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {style.tags.slice(0, 3).map((tag, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                            {style.tags.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{style.tags.length - 3}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getTypeIcon(style.type)}
                        <span className="text-sm">{getTypeText(style.type)}</span>
                      </div>
                    </TableCell>

                    <TableCell>
                      {style.category ? (
                        <Badge variant="secondary">
                          {style.category.icon} {style.category.name}
                        </Badge>
                      ) : (
                        <span className="text-muted-foreground text-sm">未分类</span>
                      )}
                    </TableCell>

                    <TableCell>
                      {style.creator ? (
                        <div className="text-sm">
                          <div className="font-medium">{style.creator.name}</div>
                          <div className="text-muted-foreground">{style.creator.email}</div>
                        </div>
                      ) : (
                        <span className="text-muted-foreground text-sm">系统</span>
                      )}
                    </TableCell>

                    <TableCell>
                      <div className="space-y-1 text-sm">
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3" />
                          收藏: {style.favoriteCount || 0}
                        </div>
                        <div className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          使用: {style.usageCount || 0}
                        </div>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={style.isActive}
                          onCheckedChange={() => handleToggleStatus(style.id, style.isActive)}
                          disabled={toggleStyleStatusMutation.isPending}
                        />
                        <span className="text-sm">
                          {style.isActive ? "启用" : "禁用"}
                        </span>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEditingStyle(style)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteStyle(style.id)}
                          className="text-red-500 hover:text-red-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {filteredStyles.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                没有找到匹配的风格
              </div>
            )}
          </CardContent>
        </Card>

        {/* 创建/编辑风格对话框 */}
        <CreateEditStyleDialog
          style={editingStyle}
          isOpen={isCreateDialogOpen || !!editingStyle}
          onClose={() => {
            setIsCreateDialogOpen(false);
            setEditingStyle(null);
          }}
          categories={categories || []}
          onSubmit={editingStyle ? handleUpdateStyle : handleCreateStyle}
          isLoading={createStyleMutation.isPending || updateStyleMutation.isPending}
        />
      </div>
    </AppLayout>
  );
}

// 创建/编辑风格对话框组件
function CreateEditStyleDialog({
  style,
  isOpen,
  onClose,
  categories,
  onSubmit,
  isLoading,
}: {
  style?: any;
  isOpen: boolean;
  onClose: () => void;
  categories: any[];
  onSubmit: (formData: FormData) => void;
  isLoading: boolean;
}) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{style ? "编辑风格" : "创建官方风格"}</DialogTitle>
          <DialogDescription>
            {style ? "修改风格信息和参数" : "创建一个新的系统官方风格"}
          </DialogDescription>
        </DialogHeader>

        <form action={onSubmit} className="space-y-4">
          {/* 基本信息 */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">风格名称 *</Label>
              <Input
                id="name"
                name="name"
                defaultValue={style?.name || ""}
                placeholder="输入风格名称"
                required
              />
            </div>
            <div>
              <Label htmlFor="nameEn">英文名称</Label>
              <Input
                id="nameEn"
                name="nameEn"
                defaultValue={style?.nameEn || ""}
                placeholder="English name"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="description">描述</Label>
            <Textarea
              id="description"
              name="description"
              defaultValue={style?.description || ""}
              placeholder="描述这个风格的特点和适用场景"
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="prompt">提示词 *</Label>
            <Textarea
              id="prompt"
              name="prompt"
              defaultValue={style?.prompt || ""}
              placeholder="输入语音风格的提示词，例如：in a warm, friendly tone"
              rows={3}
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="type">类型 *</Label>
              <Select name="type" defaultValue={style?.type || "SINGLE_SPEAKER"}>
                <SelectTrigger>
                  <SelectValue placeholder="选择类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="SINGLE_SPEAKER">单人语音</SelectItem>
                  <SelectItem value="MULTI_SPEAKER">多人语音</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="categoryId">分类</Label>
              <Select name="categoryId" defaultValue={style?.categoryId || ""}>
                <SelectTrigger>
                  <SelectValue placeholder="选择分类" />
                </SelectTrigger>
                <SelectContent>
                  {categories?.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="tags">标签</Label>
            <Input
              id="tags"
              name="tags"
              defaultValue={style?.tags?.join(", ") || ""}
              placeholder="用逗号分隔，例如：专业, 友好, 正式"
            />
          </div>

          {style && (
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="isPublic"
                name="isPublic"
                defaultChecked={style?.isPublic || false}
                className="rounded"
              />
              <Label htmlFor="isPublic">公开显示（用户可以看到和使用）</Label>
            </div>
          )}

          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button type="submit" disabled={isLoading}>
              {style ? "更新" : "创建"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}