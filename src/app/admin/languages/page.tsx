"use client";

import { useState } from "react";
import { api } from "~/trpc/react";
import { AppLayout } from "~/components/app-layout";
import { RouteGuard } from "~/components/auth/route-guard";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Switch } from "~/components/ui/switch";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "~/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { toast } from "~/hooks/use-toast";
import { 
  Languages, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff, 
  Globe,
  Users,
  Volume2,
  Search,
  Filter,
  MoreHorizontal
} from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "~/components/ui/dropdown-menu";

export default function LanguagesAdminPage() {
  return (
    <RouteGuard requireAuth={true} requireSuperAdmin={true}>
      <LanguagesAdminContent />
    </RouteGuard>
  );
}

function LanguagesAdminContent() {
  const [searchTerm, setSearchTerm] = useState("");
  const [showInactive, setShowInactive] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingLanguage, setEditingLanguage] = useState<any>(null);

  // 获取语言列表
  const { data: languages, isLoading, refetch } = api.languageAdmin.getAllLanguages.useQuery();

  // 获取语言统计
  const { data: stats } = api.languageAdmin.getLanguageStats.useQuery();

  // 更新语言状态
  const updateLanguageStatus = api.languageAdmin.updateLanguageStatus.useMutation({
    onSuccess: () => {
      toast({
        title: "成功",
        description: "语言状态更新成功",
      });
      refetch();
    },
    onError: (error) => {
      toast({
        title: "错误",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // 删除语言
  const deleteLanguage = api.languageAdmin.deleteLanguage.useMutation({
    onSuccess: () => {
      toast({
        title: "成功",
        description: "语言删除成功",
      });
      refetch();
    },
    onError: (error) => {
      toast({
        title: "错误",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // 过滤语言
  const filteredLanguages = languages?.filter(lang => {
    const matchesSearch = lang.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lang.nativeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lang.code.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = showInactive || lang.isActive;
    return matchesSearch && matchesStatus;
  }) || [];

  const handleStatusToggle = (languageId: string, isActive: boolean) => {
    updateLanguageStatus.mutate({ id: languageId, isActive });
  };

  const handleDelete = (languageId: string) => {
    if (confirm("确定要删除这个语言吗？这将删除所有相关的语音试听。")) {
      deleteLanguage.mutate({ id: languageId });
    }
  };

  if (isLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-muted-foreground">加载中...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Languages className="h-8 w-8" />
              语言管理
            </h1>
            <p className="text-muted-foreground">管理系统支持的语言和地区设置</p>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                添加语言
              </Button>
            </DialogTrigger>
            <DialogContent>
              <CreateLanguageForm 
                onSuccess={() => {
                  setIsCreateDialogOpen(false);
                  refetch();
                }}
              />
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总语言数</CardTitle>
                <Globe className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalLanguages}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">活跃语言</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.activeLanguages}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">有试听的语言</CardTitle>
                <Volume2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.languagesWithDemos}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总试听数</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalDemos}</div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>筛选和搜索</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索语言名称、本地名称或代码..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="show-inactive"
                  checked={showInactive}
                  onCheckedChange={setShowInactive}
                />
                <Label htmlFor="show-inactive">显示已禁用</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Languages Table */}
        <Card>
          <CardHeader>
            <CardTitle>语言列表 ({filteredLanguages.length})</CardTitle>
            <CardDescription>
              管理系统支持的所有语言
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>语言</TableHead>
                  <TableHead>代码</TableHead>
                  <TableHead>地区</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>试听数</TableHead>
                  <TableHead>排序</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredLanguages.map((language) => (
                  <TableRow key={language.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{language.flag}</span>
                        <div>
                          <div className="font-medium">{language.name}</div>
                          <div className="text-sm text-muted-foreground">{language.nativeName}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{language.code}</Badge>
                    </TableCell>
                    <TableCell>{language.region}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={language.isActive}
                          onCheckedChange={(checked) => handleStatusToggle(language.id, checked)}
                          disabled={updateLanguageStatus.isLoading}
                        />
                        <Badge variant={language.isActive ? "default" : "secondary"}>
                          {language.isActive ? "启用" : "禁用"}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{language._count?.voiceDemos || 0}</Badge>
                    </TableCell>
                    <TableCell>{language.sortOrder}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => setEditingLanguage(language)}>
                            <Edit className="h-4 w-4 mr-2" />
                            编辑
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDelete(language.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            删除
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Edit Dialog */}
        {editingLanguage && (
          <Dialog open={!!editingLanguage} onOpenChange={() => setEditingLanguage(null)}>
            <DialogContent>
              <EditLanguageForm 
                language={editingLanguage}
                onSuccess={() => {
                  setEditingLanguage(null);
                  refetch();
                }}
                onCancel={() => setEditingLanguage(null)}
              />
            </DialogContent>
          </Dialog>
        )}
      </div>
    </AppLayout>
  );
}

// 创建语言表单组件
function CreateLanguageForm({ onSuccess }: { onSuccess: () => void }) {
  const [formData, setFormData] = useState({
    code: "",
    name: "",
    nativeName: "",
    region: "",
    flag: "",
    isActive: true,
    sortOrder: 0,
  });

  const createLanguage = api.languageAdmin.createLanguage.useMutation({
    onSuccess: () => {
      toast({
        title: "成功",
        description: "语言创建成功",
      });
      onSuccess();
    },
    onError: (error) => {
      toast({
        title: "错误",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createLanguage.mutate(formData);
  };

  return (
    <form onSubmit={handleSubmit}>
      <DialogHeader>
        <DialogTitle>添加新语言</DialogTitle>
        <DialogDescription>
          添加系统支持的新语言
        </DialogDescription>
      </DialogHeader>
      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="code" className="text-right">语言代码</Label>
          <Input
            id="code"
            value={formData.code}
            onChange={(e) => setFormData({ ...formData, code: e.target.value })}
            placeholder="如: zh-CN"
            className="col-span-3"
            required
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="name" className="text-right">英文名称</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            placeholder="如: Chinese (China)"
            className="col-span-3"
            required
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="nativeName" className="text-right">本地名称</Label>
          <Input
            id="nativeName"
            value={formData.nativeName}
            onChange={(e) => setFormData({ ...formData, nativeName: e.target.value })}
            placeholder="如: 中文 (中国)"
            className="col-span-3"
            required
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="region" className="text-right">地区</Label>
          <Input
            id="region"
            value={formData.region}
            onChange={(e) => setFormData({ ...formData, region: e.target.value })}
            placeholder="如: 中国"
            className="col-span-3"
            required
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="flag" className="text-right">旗帜</Label>
          <Input
            id="flag"
            value={formData.flag}
            onChange={(e) => setFormData({ ...formData, flag: e.target.value })}
            placeholder="如: 🇨🇳"
            className="col-span-3"
            required
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="sortOrder" className="text-right">排序</Label>
          <Input
            id="sortOrder"
            type="number"
            value={formData.sortOrder}
            onChange={(e) => setFormData({ ...formData, sortOrder: parseInt(e.target.value) || 0 })}
            className="col-span-3"
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="isActive" className="text-right">启用</Label>
          <Switch
            id="isActive"
            checked={formData.isActive}
            onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
          />
        </div>
      </div>
      <DialogFooter>
        <Button type="submit" disabled={createLanguage.isLoading}>
          {createLanguage.isLoading ? "创建中..." : "创建语言"}
        </Button>
      </DialogFooter>
    </form>
  );
}

// 编辑语言表单组件
function EditLanguageForm({ 
  language, 
  onSuccess, 
  onCancel 
}: { 
  language: any; 
  onSuccess: () => void; 
  onCancel: () => void; 
}) {
  const [formData, setFormData] = useState({
    id: language.id,
    code: language.code,
    name: language.name,
    nativeName: language.nativeName,
    region: language.region,
    flag: language.flag,
    isActive: language.isActive,
    sortOrder: language.sortOrder,
  });

  const updateLanguage = api.languageAdmin.updateLanguage.useMutation({
    onSuccess: () => {
      toast({
        title: "成功",
        description: "语言更新成功",
      });
      onSuccess();
    },
    onError: (error) => {
      toast({
        title: "错误",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateLanguage.mutate(formData);
  };

  return (
    <form onSubmit={handleSubmit}>
      <DialogHeader>
        <DialogTitle>编辑语言</DialogTitle>
        <DialogDescription>
          修改语言信息
        </DialogDescription>
      </DialogHeader>
      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="edit-code" className="text-right">语言代码</Label>
          <Input
            id="edit-code"
            value={formData.code}
            onChange={(e) => setFormData({ ...formData, code: e.target.value })}
            className="col-span-3"
            required
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="edit-name" className="text-right">英文名称</Label>
          <Input
            id="edit-name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            className="col-span-3"
            required
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="edit-nativeName" className="text-right">本地名称</Label>
          <Input
            id="edit-nativeName"
            value={formData.nativeName}
            onChange={(e) => setFormData({ ...formData, nativeName: e.target.value })}
            className="col-span-3"
            required
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="edit-region" className="text-right">地区</Label>
          <Input
            id="edit-region"
            value={formData.region}
            onChange={(e) => setFormData({ ...formData, region: e.target.value })}
            className="col-span-3"
            required
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="edit-flag" className="text-right">旗帜</Label>
          <Input
            id="edit-flag"
            value={formData.flag}
            onChange={(e) => setFormData({ ...formData, flag: e.target.value })}
            className="col-span-3"
            required
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="edit-sortOrder" className="text-right">排序</Label>
          <Input
            id="edit-sortOrder"
            type="number"
            value={formData.sortOrder}
            onChange={(e) => setFormData({ ...formData, sortOrder: parseInt(e.target.value) || 0 })}
            className="col-span-3"
          />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="edit-isActive" className="text-right">启用</Label>
          <Switch
            id="edit-isActive"
            checked={formData.isActive}
            onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
          />
        </div>
      </div>
      <DialogFooter>
        <Button type="button" variant="outline" onClick={onCancel}>
          取消
        </Button>
        <Button type="submit" disabled={updateLanguage.isLoading}>
          {updateLanguage.isLoading ? "更新中..." : "更新语言"}
        </Button>
      </DialogFooter>
    </form>
  );
}
