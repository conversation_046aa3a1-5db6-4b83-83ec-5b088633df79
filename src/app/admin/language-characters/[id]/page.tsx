"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { api } from "~/trpc/react";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Switch } from "~/components/ui/switch";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { useToast } from "~/hooks/use-toast";
import { AppLayout } from "~/components/app-layout";
import { AdminOnlyRoute } from "~/components/auth/admin-only-route";
import {
  <PERSON>L<PERSON><PERSON>,
  Edit,
  Save,
  X,
  Image as ImageIcon,
  Volume2,
  Play,
  Pause,
  RefreshCw,
  Loader2
} from "lucide-react";

export default function LanguageCharacterDetailPage() {
  return (
    <AdminOnlyRoute>
      <AppLayout>
        <LanguageCharacterDetailPageContent />
      </AppLayout>
    </AdminOnlyRoute>
  );
}

function LanguageCharacterDetailPageContent() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const characterId = params.id as string;

  const [isEditing, setIsEditing] = useState(false);
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null);

  // 获取角色详情
  const { data: character, isLoading, refetch } = api.languageCharacterAdmin.getLanguageCharacterById.useQuery({
    id: characterId,
  });

  // 获取语音试听
  const { data: voiceDemo, refetch: refetchDemo } = api.voiceDemo.getByCharacter.useQuery({
    characterId,
  }, {
    enabled: !!characterId,
  });

  // 更新角色信息
  const updateCharacterMutation = api.languageCharacterAdmin.updateLanguageCharacter.useMutation({
    onSuccess: () => {
      toast({
        title: "更新成功",
        description: "角色信息已更新",
      });
      setIsEditing(false);
      refetch();
    },
    onError: (error) => {
      toast({
        title: "更新失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // 生成头像
  const generateAvatarMutation = api.aiGeneration.generateCharacterAvatar.useMutation({
    onSuccess: () => {
      toast({
        title: "头像生成成功",
        description: "AI头像已生成并保存",
      });
      refetch();
    },
    onError: (error) => {
      toast({
        title: "头像生成失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // 生成语音试听
  const generateVoiceMutation = api.aiGeneration.generateVoiceDemo.useMutation({
    onSuccess: () => {
      toast({
        title: "语音试听生成成功",
        description: "AI语音试听已生成并保存",
      });
      refetchDemo();
    },
    onError: (error) => {
      toast({
        title: "语音试听生成失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    style: "",
    personality: "",
    bestFor: "",
    avatarUrl: "",
    isActive: true,
    sortOrder: 0,
  });

  // 当角色数据加载完成时，初始化表单数据
  useState(() => {
    if (character) {
      setFormData({
        name: character.name || "",
        description: character.description || "",
        style: character.style || "",
        personality: character.personality || "",
        bestFor: character.bestFor || "",
        avatarUrl: character.avatarUrl || "",
        isActive: character.isActive,
        sortOrder: character.sortOrder || 0,
      });
    }
  }, [character]);

  const handleSave = () => {
    updateCharacterMutation.mutate({
      id: characterId,
      ...formData,
    });
  };

  const handleGenerateAvatar = () => {
    if (!character) return;
    
    generateAvatarMutation.mutate({
      characterId: character.id,
      characterName: character.name,
      gender: character.template?.gender || 'NEUTRAL',
      style: 'professional',
    });
  };

  const handleGenerateVoice = () => {
    if (!character) return;
    
    generateVoiceMutation.mutate({
      characterId: character.id,
      regenerate: true,
    });
  };

  const handlePlayAudio = () => {
    if (!voiceDemo?.audioUrl) return;

    if (isPlayingAudio && audioElement) {
      audioElement.pause();
      setIsPlayingAudio(false);
      return;
    }

    const audio = new Audio(voiceDemo.audioUrl);
    audio.onended = () => setIsPlayingAudio(false);
    audio.play();
    setAudioElement(audio);
    setIsPlayingAudio(true);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!character) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">角色不存在</h2>
          <p className="text-muted-foreground mb-4">请检查角色ID是否正确</p>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{character.name}</h1>
            <p className="text-muted-foreground">
              {character.language.name} • {character.template?.originalName || '自定义角色'}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={character.isActive ? "default" : "secondary"}>
            {character.isActive ? "活跃" : "禁用"}
          </Badge>
          <Badge variant={character.isCustom ? "outline" : "default"}>
            {character.isCustom ? "自定义" : "系统"}
          </Badge>
        </div>
      </div>

      <Tabs defaultValue="info" className="space-y-4">
        <TabsList>
          <TabsTrigger value="info">基本信息</TabsTrigger>
          <TabsTrigger value="avatar">头像管理</TabsTrigger>
          <TabsTrigger value="voice">语音试听</TabsTrigger>
        </TabsList>

        {/* 基本信息标签页 */}
        <TabsContent value="info">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>基本信息</CardTitle>
                  <CardDescription>管理角色的基本属性和设置</CardDescription>
                </div>
                <Button
                  variant={isEditing ? "default" : "outline"}
                  onClick={isEditing ? handleSave : () => setIsEditing(true)}
                  disabled={updateCharacterMutation.isPending}
                >
                  {updateCharacterMutation.isPending ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : isEditing ? (
                    <Save className="h-4 w-4 mr-2" />
                  ) : (
                    <Edit className="h-4 w-4 mr-2" />
                  )}
                  {isEditing ? "保存" : "编辑"}
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="name">角色名称</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    disabled={!isEditing}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="style">风格</Label>
                  <Input
                    id="style"
                    value={formData.style}
                    onChange={(e) => setFormData({ ...formData, style: e.target.value })}
                    disabled={!isEditing}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">描述</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  disabled={!isEditing}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="personality">个性特点</Label>
                  <Input
                    id="personality"
                    value={formData.personality}
                    onChange={(e) => setFormData({ ...formData, personality: e.target.value })}
                    disabled={!isEditing}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bestFor">适用场景</Label>
                  <Input
                    id="bestFor"
                    value={formData.bestFor}
                    onChange={(e) => setFormData({ ...formData, bestFor: e.target.value })}
                    disabled={!isEditing}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                  disabled={!isEditing}
                />
                <Label htmlFor="isActive">启用角色</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 头像管理标签页 */}
        <TabsContent value="avatar">
          <Card>
            <CardHeader>
              <CardTitle>头像管理</CardTitle>
              <CardDescription>管理角色头像，支持AI生成</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center space-x-6">
                <div className="flex flex-col items-center space-y-4">
                  <Avatar className="h-32 w-32">
                    <AvatarImage src={character.avatarUrl || ""} alt={character.name} />
                    <AvatarFallback className="text-2xl">
                      {character.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-center">
                    <p className="text-sm text-muted-foreground">当前头像</p>
                    {character.template?.gender && (
                      <Badge variant="outline" className="mt-1">
                        {character.template.gender === 'MALE' ? '男性' :
                         character.template.gender === 'FEMALE' ? '女性' : '中性'}
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="flex-1 space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="avatarUrl">头像URL</Label>
                    <Input
                      id="avatarUrl"
                      value={formData.avatarUrl}
                      onChange={(e) => setFormData({ ...formData, avatarUrl: e.target.value })}
                      placeholder="https://example.com/avatar.jpg"
                    />
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      onClick={handleGenerateAvatar}
                      disabled={generateAvatarMutation.isPending}
                      className="flex-1"
                    >
                      {generateAvatarMutation.isPending ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <ImageIcon className="h-4 w-4 mr-2" />
                      )}
                      AI生成头像
                    </Button>

                    <Button
                      variant="outline"
                      onClick={() => setFormData({ ...formData, avatarUrl: "" })}
                    >
                      <X className="h-4 w-4 mr-2" />
                      清除
                    </Button>
                  </div>

                  {generateAvatarMutation.isPending && (
                    <div className="text-sm text-muted-foreground">
                      正在生成AI头像，请稍候...（约需要30秒）
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 语音试听标签页 */}
        <TabsContent value="voice">
          <Card>
            <CardHeader>
              <CardTitle>语音试听</CardTitle>
              <CardDescription>管理角色的语音试听文件</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {voiceDemo ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium">试听文本</h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        {voiceDemo.demoText}
                      </p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-muted-foreground">
                        <span>时长: {voiceDemo.duration}秒</span>
                        <span>大小: {Math.round(voiceDemo.fileSize / 1024)}KB</span>
                        <span>质量: {voiceDemo.quality}</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handlePlayAudio}
                        disabled={!voiceDemo.audioUrl}
                      >
                        {isPlayingAudio ? (
                          <Pause className="h-4 w-4" />
                        ) : (
                          <Play className="h-4 w-4" />
                        )}
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleGenerateVoice}
                        disabled={generateVoiceMutation.isPending}
                      >
                        {generateVoiceMutation.isPending ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <RefreshCw className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Volume2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">暂无语音试听</h3>
                  <p className="text-muted-foreground mb-4">
                    点击下方按钮生成AI语音试听
                  </p>
                  <Button
                    onClick={handleGenerateVoice}
                    disabled={generateVoiceMutation.isPending}
                  >
                    {generateVoiceMutation.isPending ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Volume2 className="h-4 w-4 mr-2" />
                    )}
                    生成语音试听
                  </Button>
                </div>
              )}

              {character.template && (
                <div className="mt-6 p-4 bg-muted rounded-lg">
                  <h4 className="font-medium mb-2">API模板信息</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">提供商:</span>
                      <span className="ml-2">{character.template.apiProvider}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">语音名称:</span>
                      <span className="ml-2">{character.template.apiVoiceName}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">性别:</span>
                      <span className="ml-2">
                        {character.template.gender === 'MALE' ? '男性' :
                         character.template.gender === 'FEMALE' ? '女性' : '中性'}
                      </span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">语言:</span>
                      <span className="ml-2">{character.language.name}</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
