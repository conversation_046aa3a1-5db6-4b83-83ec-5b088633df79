"use client";

import { useState, useEffect } from "react";
import { api } from "~/trpc/react";
import { AppLayout } from "~/components/app-layout";
import { RouteGuard } from "~/components/auth/route-guard";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import { Switch } from "~/components/ui/switch";
import { Label } from "~/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter
} from "~/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "~/components/ui/table";
import { toast } from "~/hooks/use-toast";
import { Loader2, Plus, Edit, Trash2, Eye, Settings, Wand2, Volume2, Image, Play, Download, Save, X, Pause, RefreshCw, FileText, Check } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { LanguageCharacterEditDialog } from "~/components/admin/language-character-edit-dialog";
import { VoiceDemoGenerationDialog } from "~/components/admin/voice-demo-generation-dialog";
import { AvatarGenerationDialog } from "~/components/admin/avatar-generation-dialog";
import { ApiTemplateEditDialog } from "~/components/admin/api-template-edit-dialog";

export default function LanguageCharactersPage() {
  return (
    <RouteGuard requireAuth={true} requireSuperAdmin={true}>
      <AppLayout>
        <LanguageCharactersPageContent />
      </AppLayout>
    </RouteGuard>
  );
}

function LanguageCharactersPageContent() {
  const [selectedLanguageId, setSelectedLanguageId] = useState<string>("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingCharacter, setEditingCharacter] = useState<any>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // 新增：弹窗状态管理
  const [voiceDemoDialogOpen, setVoiceDemoDialogOpen] = useState(false);
  const [avatarDialogOpen, setAvatarDialogOpen] = useState(false);
  const [selectedCharacterForDialog, setSelectedCharacterForDialog] = useState<any>(null);

  // API模板编辑状态
  const [apiTemplateDialogOpen, setApiTemplateDialogOpen] = useState(false);
  const [editingApiTemplate, setEditingApiTemplate] = useState<any>(null);

  // 获取语言列表
  const { data: languages } = api.languageAdmin.getAllLanguages.useQuery({
    includeInactive: false,
  });

  // 当语言列表加载完成后，自动选择中文
  useEffect(() => {
    if (languages && selectedLanguageId === "all") {
      const chineseLanguage = languages.find(lang => lang.code === 'zh-CN');
      if (chineseLanguage) {
        setSelectedLanguageId(chineseLanguage.id);
      }
    }
  }, [languages, selectedLanguageId]);

  // 获取语言角色列表
  const { data: languageCharacters, isLoading, refetch } = api.languageCharacterAdmin.getLanguageCharacters.useQuery(
    { 
      languageId: selectedLanguageId,
      includeInactive: true,
    },
    {
      enabled: selectedLanguageId !== "all",
    }
  );

  // 获取API模板列表
  const { data: apiTemplates } = api.apiCharacterTemplateAdmin.getAllTemplates.useQuery({
    includeInactive: false,
  });

  // 获取统计信息
  const { data: stats } = api.languageCharacterAdmin.getLanguageCharacterStats.useQuery(
    { languageId: selectedLanguageId !== "all" ? selectedLanguageId : undefined }
  );

  // 创建语言角色
  const createCharacterMutation = api.languageCharacterAdmin.createLanguageCharacter.useMutation({
    onSuccess: (data) => {
      toast({
        title: "创建成功",
        description: data.message,
      });
      setIsCreateDialogOpen(false);
      refetch();
    },
    onError: (error) => {
      toast({
        title: "创建失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // 更新语言角色
  const updateCharacterMutation = api.languageCharacterAdmin.updateLanguageCharacter.useMutation({
    onSuccess: (data) => {
      toast({
        title: "更新成功",
        description: data.message,
      });
      setEditingCharacter(null);
      refetch();
    },
    onError: (error) => {
      toast({
        title: "更新失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // 删除语言角色
  const deleteCharacterMutation = api.languageCharacterAdmin.deleteLanguageCharacter.useMutation({
    onSuccess: (data) => {
      toast({
        title: "删除成功",
        description: data.message,
      });
      refetch();
    },
    onError: (error) => {
      toast({
        title: "删除失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // 从模板创建角色
  const createFromTemplateMutation = api.languageCharacterAdmin.createFromTemplate.useMutation({
    onSuccess: (data) => {
      toast({
        title: "创建成功",
        description: data.message,
      });
      refetch();
    },
    onError: (error) => {
      toast({
        title: "创建失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // AI生成头像
  const generateAvatarMutation = api.aiGeneration.generateCharacterAvatar.useMutation({
    onSuccess: (data) => {
      toast({
        title: "头像生成成功",
        description: data.message,
      });
      refetch();
    },
    onError: (error) => {
      toast({
        title: "头像生成失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // AI生成语音试听
  const generateVoiceDemoMutation = api.aiGeneration.generateVoiceDemo.useMutation({
    onSuccess: (data) => {
      toast({
        title: "语音试听生成成功",
        description: data.message,
      });
      refetch();
    },
    onError: (error) => {
      toast({
        title: "语音试听生成失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // 批量生成语音试听
  const batchGenerateVoiceDemo = api.aiGeneration.batchGenerateVoiceDemos.useMutation({
    onSuccess: (data) => {
      toast({
        title: "批量生成完成",
        description: data.message,
      });
      refetch();
    },
    onError: (error) => {
      toast({
        title: "批量生成失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleCreateCharacter = (formData: any) => {
    createCharacterMutation.mutate({
      ...formData,
      languageId: selectedLanguageId,
      templateId: formData.templateId === "custom" ? null : formData.templateId,
    });
  };

  const handleUpdateCharacter = (formData: any) => {
    if (!editingCharacter) return;
    updateCharacterMutation.mutate({
      id: editingCharacter.id,
      ...formData,
      templateId: formData.templateId === "custom" ? null : formData.templateId,
    });
  };

  const handleDeleteCharacter = (characterId: string) => {
    if (confirm("确定要删除这个语言角色吗？")) {
      deleteCharacterMutation.mutate({ id: characterId });
    }
  };

  const handleCreateFromTemplate = (templateId: string) => {
    if (selectedLanguageId === "all") {
      toast({
        title: "请先选择语言",
        description: "需要先选择一个语言才能创建角色",
        variant: "destructive",
      });
      return;
    }

    createFromTemplateMutation.mutate({
      languageId: selectedLanguageId,
      templateId,
    });
  };

  // 处理AI生成头像 - 打开弹窗
  const handleGenerateAvatar = (character: any) => {
    setSelectedCharacterForDialog(character);
    setAvatarDialogOpen(true);
  };

  // 处理AI生成语音试听 - 打开弹窗
  const handleGenerateVoiceDemo = (character: any, quality: 'standard' | 'professional' = 'standard', regenerate = true) => {
    setSelectedCharacterForDialog(character);
    setVoiceDemoDialogOpen(true);
  };

  // 处理API模板编辑
  const handleEditApiTemplate = (template: any) => {
    setEditingApiTemplate(template);
    setApiTemplateDialogOpen(true);
  };

  // 处理创建新API模板
  const handleCreateApiTemplate = () => {
    setEditingApiTemplate(null);
    setApiTemplateDialogOpen(true);
  };

  // 处理批量生成语音试听
  const handleBatchGenerateVoiceDemo = () => {
    if (selectedLanguageId === "all") {
      toast({
        title: "请先选择语言",
        description: "需要先选择一个语言才能批量生成",
        variant: "destructive",
      });
      return;
    }

    batchGenerateVoiceDemo.mutate({
      languageId: selectedLanguageId,
      regenerateExisting: false,
    });
  };

  const selectedLanguage = languages?.find(lang => lang.id === selectedLanguageId);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">语言角色管理</h1>
          <p className="text-muted-foreground">
            管理每个语言下的语音角色，基于API模板进行本土化
          </p>
        </div>
      </div>

      {/* 语言选择器 */}
      <Card>
        <CardHeader>
          <CardTitle>选择语言</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Select value={selectedLanguageId} onValueChange={setSelectedLanguageId}>
              <SelectTrigger className="w-64">
                <SelectValue placeholder="选择语言" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部语言</SelectItem>
                {languages?.map((language) => (
                  <SelectItem key={language.id} value={language.id}>
                    {language.flag} {language.name} ({language.code})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {selectedLanguage && (
              <div className="flex items-center gap-4">
                <Badge variant="outline">
                  {selectedLanguage.flag} {selectedLanguage.name}
                </Badge>
                {stats && (
                  <div className="text-sm text-muted-foreground">
                    共 {stats.totalCharacters} 个角色
                    （活跃: {stats.activeCharacters}, 自定义: {stats.customCharacters}）
                  </div>
                )}

                {/* 批量操作按钮 */}
                <div className="flex items-center gap-2 ml-auto">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBatchGenerateVoiceDemo}
                    disabled={batchGenerateVoiceDemo.isPending || selectedLanguageId === "all"}
                  >
                    <Wand2 className="h-4 w-4 mr-2" />
                    {batchGenerateVoiceDemo.isPending ? "生成中..." : "批量生成试听"}
                  </Button>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {selectedLanguageId === "all" ? (
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <h3 className="text-lg font-medium mb-2">请选择一个语言</h3>
              <p className="text-muted-foreground">
                选择一个语言来查看和管理该语言下的角色
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Tabs defaultValue="characters" className="space-y-4">
          <TabsList>
            <TabsTrigger value="characters">语言角色</TabsTrigger>
            <TabsTrigger value="templates">API模板</TabsTrigger>
          </TabsList>

          <TabsContent value="characters">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>
                    {selectedLanguage?.name} 语言角色
                  </CardTitle>
                  <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                    <DialogTrigger asChild>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        创建角色
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                      <DialogHeader>
                        <DialogTitle>创建语言角色</DialogTitle>
                      </DialogHeader>
                      <CharacterForm
                        onSubmit={handleCreateCharacter}
                        apiTemplates={apiTemplates}
                        isLoading={createCharacterMutation.isPending}
                      />
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : languageCharacters?.length === 0 ? (
                  <div className="text-center py-12">
                    <h3 className="text-lg font-medium mb-2">暂无角色</h3>
                    <p className="text-muted-foreground mb-4">
                      该语言下还没有角色，可以从API模板创建或手动创建
                    </p>
                    <Button onClick={() => setIsCreateDialogOpen(true)}>
                      创建第一个角色
                    </Button>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>角色名称</TableHead>
                        <TableHead>描述</TableHead>
                        <TableHead>风格</TableHead>
                        <TableHead>性别</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {languageCharacters?.map((character) => (
                        <TableRow key={character.id}>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {character.avatarUrl && (
                                <img 
                                  src={character.avatarUrl} 
                                  alt={character.name}
                                  className="w-8 h-8 rounded-full"
                                />
                              )}
                              <div>
                                <div className="font-medium">{character.name}</div>
                                {character.template && (
                                  <div className="text-sm text-muted-foreground">
                                    {character.template.apiProvider}: {character.template.apiVoiceName}
                                  </div>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="max-w-xs truncate">
                            {character.description}
                          </TableCell>
                          <TableCell>{character.style || "-"}</TableCell>
                          <TableCell>
                            {(() => {
                              // 优先显示角色自己的性别设置，如果没有则显示模板的性别
                              const gender = character.gender || character.template?.gender;
                              const isOverridden = character.gender && character.gender !== character.template?.gender;

                              if (gender) {
                                return (
                                  <div className="flex items-center gap-1">
                                    <Badge
                                      variant="outline"
                                      className={
                                        gender === 'MALE' ? 'border-blue-200 text-blue-700 bg-blue-50' :
                                        gender === 'FEMALE' ? 'border-pink-200 text-pink-700 bg-pink-50' :
                                        'border-gray-200 text-gray-700 bg-gray-50'
                                      }
                                    >
                                      {gender === 'MALE' ? '♂️ 男性' :
                                       gender === 'FEMALE' ? '♀️ 女性' :
                                       '⚧️ 中性'}
                                    </Badge>
                                    {isOverridden && (
                                      <Badge variant="secondary" className="text-xs">
                                        自定义
                                      </Badge>
                                    )}
                                  </div>
                                );
                              }
                              return <span className="text-muted-foreground">未设置</span>;
                            })()}
                          </TableCell>
                          <TableCell>
                            <Badge variant={character.isCustom ? "secondary" : "default"}>
                              {character.isCustom ? "自定义" : "系统"}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={character.isActive ? "default" : "secondary"}>
                              {character.isActive ? "活跃" : "禁用"}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              {/* 编辑角色按钮 */}
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setEditingCharacter(character);
                                  setIsEditDialogOpen(true);
                                }}
                                title="编辑角色"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>

                              {/* AI生成头像按钮 */}
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleGenerateAvatar(character)}
                                disabled={generateAvatarMutation.isPending}
                                title="AI生成头像"
                              >
                                <Image className="h-4 w-4" />
                              </Button>

                              {/* AI生成语音试听按钮 */}
                              {character.template && (
                                <div className="flex gap-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleGenerateVoiceDemo(character, 'standard')}
                                    disabled={generateVoiceDemoMutation.isPending}
                                    title="生成标准质量语音试听"
                                  >
                                    <Volume2 className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleGenerateVoiceDemo(character, 'professional')}
                                    disabled={generateVoiceDemoMutation.isPending}
                                    title="生成专业质量语音试听"
                                  >
                                    <Wand2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              )}

                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteCharacter(character.id)}
                                title="删除角色"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="templates">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>API角色模板</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      从这些API模板创建语言特定的角色
                    </p>
                  </div>
                  <Button onClick={handleCreateApiTemplate}>
                    <Plus className="h-4 w-4 mr-2" />
                    创建模板
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {apiTemplates?.map((template) => (
                    <Card key={template.id} className="relative">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{template.originalName}</h4>
                          <div className="flex items-center gap-1">
                            <Badge variant="outline">{template.apiProvider}</Badge>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditApiTemplate(template)}
                              className="h-6 w-6 p-0"
                              title="编辑模板"
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          {template.apiVoiceName}
                        </p>
                        {template.defaultDescription && (
                          <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                            {template.defaultDescription}
                          </p>
                        )}
                        <div className="flex items-center justify-between">
                          <Badge variant="secondary">
                            {template.gender === 'MALE' ? '♂ 男性' :
                             template.gender === 'FEMALE' ? '♀ 女性' : '⚪ 中性'}
                          </Badge>
                          <Button
                            size="sm"
                            onClick={() => handleCreateFromTemplate(template.id)}
                            disabled={createFromTemplateMutation.isPending || selectedLanguageId === "all"}
                            title={selectedLanguageId === "all" ? "请先选择语言" : "基于此模板创建角色"}
                          >
                            {createFromTemplateMutation.isPending ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              "创建角色"
                            )}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {/* 语言角色编辑对话框 */}
      <LanguageCharacterEditDialog
        character={editingCharacter}
        open={isEditDialogOpen}
        onOpenChange={(open) => {
          setIsEditDialogOpen(open);
          if (!open) {
            setEditingCharacter(null);
          }
        }}
        onSuccess={() => {
          refetch();
        }}
      />

      {/* 语音试听生成弹窗 */}
      {selectedCharacterForDialog && (
        <VoiceDemoGenerationDialog
          character={selectedCharacterForDialog}
          open={voiceDemoDialogOpen}
          onOpenChange={setVoiceDemoDialogOpen}
          onSuccess={() => {
            refetch();
          }}
        />
      )}

      {/* 头像生成弹窗 */}
      {selectedCharacterForDialog && (
        <AvatarGenerationDialog
          character={selectedCharacterForDialog}
          open={avatarDialogOpen}
          onOpenChange={setAvatarDialogOpen}
          onSuccess={() => {
            refetch();
          }}
        />
      )}

      {/* API模板编辑弹窗 */}
      <ApiTemplateEditDialog
        template={editingApiTemplate}
        open={apiTemplateDialogOpen}
        onOpenChange={(open) => {
          setApiTemplateDialogOpen(open);
          if (!open) {
            setEditingApiTemplate(null);
          }
        }}
        onSuccess={() => {
          // 刷新API模板列表
          // 注意：这里需要重新获取API模板数据
          window.location.reload(); // 临时解决方案，后续可以优化为局部刷新
        }}
      />
    </div>
  );
}

// 角色表单组件
function CharacterForm({ 
  initialData, 
  onSubmit, 
  apiTemplates, 
  isLoading 
}: {
  initialData?: any;
  onSubmit: (data: any) => void;
  apiTemplates?: any[];
  isLoading: boolean;
}) {
  const [formData, setFormData] = useState({
    templateId: initialData?.templateId ? initialData.templateId : "custom",
    name: initialData?.name || "",
    description: initialData?.description || "",
    style: initialData?.style || "",
    personality: initialData?.personality || "",
    bestFor: initialData?.bestFor || "",
    avatarUrl: initialData?.avatarUrl || "",
    isActive: initialData?.isActive ?? true,
    isCustom: initialData?.isCustom ?? false,
    sortOrder: initialData?.sortOrder || 0,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="templateId">API模板</Label>
          <Select 
            value={formData.templateId} 
            onValueChange={(value) => setFormData(prev => ({ ...prev, templateId: value }))}
          >
            <SelectTrigger>
              <SelectValue placeholder="选择API模板（可选）" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="custom">无模板（自定义角色）</SelectItem>
              {apiTemplates?.map((template) => (
                <SelectItem key={template.id} value={template.id}>
                  {template.originalName} ({template.apiProvider})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="name">角色名称</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            required
          />
        </div>
      </div>

      <div>
        <Label htmlFor="description">描述</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          required
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="style">风格</Label>
          <Input
            id="style"
            value={formData.style}
            onChange={(e) => setFormData(prev => ({ ...prev, style: e.target.value }))}
          />
        </div>

        <div>
          <Label htmlFor="avatarUrl">头像URL</Label>
          <Input
            id="avatarUrl"
            value={formData.avatarUrl}
            onChange={(e) => setFormData(prev => ({ ...prev, avatarUrl: e.target.value }))}
          />
        </div>
      </div>

      <div>
        <Label htmlFor="personality">个性描述</Label>
        <Textarea
          id="personality"
          value={formData.personality}
          onChange={(e) => setFormData(prev => ({ ...prev, personality: e.target.value }))}
        />
      </div>

      <div>
        <Label htmlFor="bestFor">适用场景</Label>
        <Textarea
          id="bestFor"
          value={formData.bestFor}
          onChange={(e) => setFormData(prev => ({ ...prev, bestFor: e.target.value }))}
        />
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="isActive"
          checked={formData.isActive}
          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
        />
        <Label htmlFor="isActive">启用角色</Label>
      </div>

      <DialogFooter>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              保存中...
            </>
          ) : (
            "保存"
          )}
        </Button>
      </DialogFooter>
    </form>
  );
}

// 增强版角色表单组件 - 包含所有编辑功能
function EnhancedCharacterForm({
  character,
  onClose,
  onSuccess,
  apiTemplates
}: {
  character: any;
  onClose: () => void;
  onSuccess: () => void;
  apiTemplates?: any[];
}) {
  const [activeTab, setActiveTab] = useState("basic");
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null);
  const [demoText, setDemoText] = useState("你好，这是一个语音试听测试。Hello, this is a voice demo test.");
  const [isEditingText, setIsEditingText] = useState(false);

  // 表单数据状态
  const [formData, setFormData] = useState({
    templateId: character?.templateId || "",
    name: character?.name || "",
    description: character?.description || "",
    style: character?.style || "",
    personality: character?.personality || "",
    bestFor: character?.bestFor || "",
    avatarUrl: character?.avatarUrl || "",
    isActive: character?.isActive ?? true,
    isCustom: character?.isCustom ?? false,
    sortOrder: character?.sortOrder || 0,
  });

  // 获取语音试听数据
  const { data: voiceDemo, refetch: refetchDemo } = api.voiceDemo.getByCharacter.useQuery({
    characterId: character.id,
  }, {
    enabled: !!character.id,
  });

  // 更新角色信息
  const updateCharacterMutation = api.languageCharacterAdmin.updateLanguageCharacter.useMutation({
    onSuccess: () => {
      toast({
        title: "更新成功",
        description: "角色信息已更新",
      });
      // 保存后不关闭弹窗，只刷新数据
      onSuccess();
    },
    onError: (error) => {
      toast({
        title: "更新失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // 生成头像
  const generateAvatarMutation = api.aiGeneration.generateCharacterAvatar.useMutation({
    onSuccess: (data) => {
      toast({
        title: "头像生成成功",
        description: "AI头像已生成并保存",
      });
      setFormData(prev => ({ ...prev, avatarUrl: data.avatarUrl }));
    },
    onError: (error) => {
      toast({
        title: "头像生成失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // 生成语音试听
  const generateVoiceMutation = api.aiGeneration.generateVoiceDemo.useMutation({
    onSuccess: () => {
      toast({
        title: "语音试听生成成功",
        description: "AI语音试听已生成并保存",
      });
      refetchDemo();
    },
    onError: (error) => {
      toast({
        title: "语音试听生成失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSave = () => {
    updateCharacterMutation.mutate({
      id: character.id,
      ...formData,
    });
  };

  const handleGenerateAvatar = () => {
    generateAvatarMutation.mutate({
      characterId: character.id,
      characterName: character.name,
      gender: character.template?.gender || 'NEUTRAL',
      style: 'professional',
    });
  };

  const handleGenerateVoice = (quality: 'standard' | 'professional' = 'standard') => {
    generateVoiceMutation.mutate({
      characterId: character.id,
      quality,
      demoText: demoText.trim() || undefined, // 使用自定义文本，如果为空则使用默认
      regenerate: true,
    });
  };

  const handlePlayAudio = (demo?: any) => {
    const audioUrl = demo?.audioUrl || voiceDemo?.standard?.audioUrl || voiceDemo?.professional?.audioUrl;
    console.log('🎵 尝试播放音频:', audioUrl);

    if (!audioUrl) {
      console.error('❌ 音频URL为空');
      toast({
        title: "播放失败",
        description: "音频文件不存在",
        variant: "destructive",
      });
      return;
    }

    if (isPlayingAudio && audioElement) {
      audioElement.pause();
      setIsPlayingAudio(false);
      return;
    }

    const audio = new Audio(audioUrl);

    // 添加错误处理
    audio.onerror = (error) => {
      console.error('❌ 音频播放错误:', error);
      console.error('❌ 音频URL:', audioUrl);
      setIsPlayingAudio(false);
      toast({
        title: "播放失败",
        description: "音频文件无法播放，可能是格式不支持或文件损坏",
        variant: "destructive",
      });
    };

    audio.onloadstart = () => {
      console.log('🔄 开始加载音频...');
    };

    audio.oncanplay = () => {
      console.log('✅ 音频可以播放');
    };

    audio.onended = () => {
      console.log('🔚 音频播放结束');
      setIsPlayingAudio(false);
    };

    // 尝试播放
    audio.play().then(() => {
      console.log('▶️ 音频开始播放');
      setAudioElement(audio);
      setIsPlayingAudio(true);
    }).catch((error) => {
      console.error('❌ 播放失败:', error);
      setIsPlayingAudio(false);
      toast({
        title: "播放失败",
        description: "无法播放音频文件",
        variant: "destructive",
      });
    });
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic">基本信息</TabsTrigger>
          <TabsTrigger value="avatar">头像管理</TabsTrigger>
          <TabsTrigger value="voice">语音试听</TabsTrigger>
        </TabsList>

        {/* 基本信息标签页 */}
        <TabsContent value="basic" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">角色名称</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="输入角色名称"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="style">风格</Label>
              <Input
                id="style"
                value={formData.style}
                onChange={(e) => setFormData({ ...formData, style: e.target.value })}
                placeholder="输入角色风格"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">描述</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="输入角色描述"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="personality">个性特点</Label>
              <Input
                id="personality"
                value={formData.personality}
                onChange={(e) => setFormData({ ...formData, personality: e.target.value })}
                placeholder="输入个性特点"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="bestFor">适用场景</Label>
              <Input
                id="bestFor"
                value={formData.bestFor}
                onChange={(e) => setFormData({ ...formData, bestFor: e.target.value })}
                placeholder="输入适用场景"
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
            />
            <Label htmlFor="isActive">启用角色</Label>
          </div>

          {character.template && (
            <div className="p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">API模板信息</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">提供商:</span>
                  <span className="ml-2">{character.template.apiProvider}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">语音名称:</span>
                  <span className="ml-2">{character.template.apiVoiceName}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">性别:</span>
                  <span className="ml-2">
                    {character.template.gender === 'MALE' ? '男性' :
                     character.template.gender === 'FEMALE' ? '女性' : '中性'}
                  </span>
                </div>
                <div>
                  <span className="text-muted-foreground">语言:</span>
                  <span className="ml-2">{character.template.languageCode}</span>
                </div>
              </div>
            </div>
          )}
        </TabsContent>

        {/* 头像管理标签页 */}
        <TabsContent value="avatar" className="space-y-4">
          <div className="flex items-center space-x-6">
            <div className="flex flex-col items-center space-y-4">
              <Avatar className="h-32 w-32">
                <AvatarImage src={formData.avatarUrl || ""} alt={character.name} />
                <AvatarFallback className="text-2xl">
                  {character.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="text-center">
                <p className="text-sm text-muted-foreground">当前头像</p>
                {character.template?.gender && (
                  <Badge variant="outline" className="mt-1">
                    {character.template.gender === 'MALE' ? '男性' :
                     character.template.gender === 'FEMALE' ? '女性' : '中性'}
                  </Badge>
                )}
              </div>
            </div>

            <div className="flex-1 space-y-4">
              <div className="space-y-2">
                <Label htmlFor="avatarUrl">头像URL</Label>
                <Input
                  id="avatarUrl"
                  value={formData.avatarUrl}
                  onChange={(e) => setFormData({ ...formData, avatarUrl: e.target.value })}
                  placeholder="https://example.com/avatar.jpg"
                />
              </div>

              <div className="flex space-x-2">
                <Button
                  onClick={handleGenerateAvatar}
                  disabled={generateAvatarMutation.isPending}
                  className="flex-1"
                >
                  {generateAvatarMutation.isPending ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Image className="h-4 w-4 mr-2" />
                  )}
                  AI生成头像
                </Button>

                <Button
                  variant="outline"
                  onClick={() => setFormData({ ...formData, avatarUrl: "" })}
                >
                  <X className="h-4 w-4 mr-2" />
                  清除
                </Button>
              </div>

              {generateAvatarMutation.isPending && (
                <div className="text-sm text-muted-foreground">
                  正在生成AI头像，请稍候...（约需要30秒）
                </div>
              )}
            </div>
          </div>
        </TabsContent>

        {/* 语音试听标签页 */}
        <TabsContent value="voice" className="space-y-4">
          {/* 试听文本编辑区域 */}
          <div className="border rounded-lg p-4 bg-muted/50">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium flex items-center gap-2">
                <FileText className="h-4 w-4" />
                试听文本
              </h4>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditingText(!isEditingText)}
              >
                {isEditingText ? (
                  <>
                    <Check className="h-4 w-4 mr-1" />
                    完成
                  </>
                ) : (
                  <>
                    <Edit className="h-4 w-4 mr-1" />
                    编辑
                  </>
                )}
              </Button>
            </div>

            {isEditingText ? (
              <div className="space-y-2">
                <Textarea
                  value={demoText}
                  onChange={(e) => setDemoText(e.target.value)}
                  placeholder="输入要生成语音的文本..."
                  className="min-h-[80px]"
                />
                <p className="text-xs text-muted-foreground">
                  字符数: {demoText.length} | 建议长度: 50-200字符
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {demoText}
                </p>
                <p className="text-xs text-muted-foreground">
                  字符数: {demoText.length} | 点击"编辑"修改试听文本
                </p>
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 标准质量试听 */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Volume2 className="h-4 w-4" />
                  标准质量
                </h4>
                <Badge variant="secondary">Standard</Badge>
              </div>

              {voiceDemo?.standard ? (
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-muted-foreground">
                      {voiceDemo.standard.demoText}
                    </p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-muted-foreground">
                      <span>时长: {voiceDemo.standard.duration}秒</span>
                      <span>大小: {Math.round(voiceDemo.standard.fileSize / 1024)}KB</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePlayAudio(voiceDemo.standard)}
                      disabled={!voiceDemo.standard.audioUrl}
                    >
                      {isPlayingAudio ? (
                        <Pause className="h-4 w-4" />
                      ) : (
                        <Play className="h-4 w-4" />
                      )}
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleGenerateVoice('standard')}
                      disabled={generateVoiceMutation.isPending}
                    >
                      {generateVoiceMutation.isPending ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <RefreshCw className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-muted-foreground mb-3">暂无标准质量试听</p>
                  <Button
                    size="sm"
                    onClick={() => handleGenerateVoice('standard')}
                    disabled={generateVoiceMutation.isPending}
                  >
                    生成标准试听
                  </Button>
                </div>
              )}
            </div>

            {/* 专业质量试听 */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Wand2 className="h-4 w-4" />
                  专业质量
                </h4>
                <Badge variant="default">Professional</Badge>
              </div>

              {voiceDemo?.professional ? (
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-muted-foreground">
                      {voiceDemo.professional.demoText}
                    </p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-muted-foreground">
                      <span>时长: {voiceDemo.professional.duration}秒</span>
                      <span>大小: {Math.round(voiceDemo.professional.fileSize / 1024)}KB</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePlayAudio(voiceDemo.professional)}
                      disabled={!voiceDemo.professional.audioUrl}
                    >
                      {isPlayingAudio ? (
                        <Pause className="h-4 w-4" />
                      ) : (
                        <Play className="h-4 w-4" />
                      )}
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleGenerateVoice('professional')}
                      disabled={generateVoiceMutation.isPending}
                    >
                      {generateVoiceMutation.isPending ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <RefreshCw className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-muted-foreground mb-3">暂无专业质量试听</p>
                  <Button
                    size="sm"
                    onClick={() => handleGenerateVoice('professional')}
                    disabled={generateVoiceMutation.isPending}
                  >
                    生成专业试听
                  </Button>
                </div>
              )}
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* 底部操作按钮 */}
      <DialogFooter className="flex justify-between">
        <Button variant="outline" onClick={onClose}>
          取消
        </Button>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={handleSave}
            disabled={updateCharacterMutation.isPending}
          >
            {updateCharacterMutation.isPending ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            保存更改
          </Button>
          <Button
            onClick={() => {
              handleSave();
              // 延迟关闭，确保保存完成
              setTimeout(() => onClose(), 500);
            }}
            disabled={updateCharacterMutation.isPending}
          >
            {updateCharacterMutation.isPending ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            保存并关闭
          </Button>
        </div>
      </DialogFooter>
    </div>
  );
}
