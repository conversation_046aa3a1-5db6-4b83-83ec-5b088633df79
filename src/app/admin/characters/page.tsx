"use client";

import { useState } from "react";
import { api } from "~/trpc/react";
import { AppLayout } from "~/components/app-layout";
import { AdminOnlyRoute } from "~/components/auth/admin-only-route";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { Slider } from "~/components/ui/slider";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Heart, Plus, Search, Edit, Trash2, Star, Volume2, Zap, Music } from "lucide-react";
import { toast } from "~/hooks/use-toast";
import { CharacterParameterConfig } from "~/components/character-parameter-config";
import { AvatarUpload } from "~/components/avatar-upload";
import { VoicePreview } from "~/components/voice-preview";
import { CharacterShare } from "~/components/character-share";

export default function CharactersPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedBaseCharacter, setSelectedBaseCharacter] = useState<string>("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingCharacter, setEditingCharacter] = useState<any>(null);

  // API queries
  const { data: baseCharacters } = api.characterManagement.getBaseCharacters.useQuery();
  const { data: charactersData, refetch: refetchCharacters } = api.characterManagement.getCustomCharacters.useQuery({
    search: searchQuery,
    baseCharacterId: selectedBaseCharacter === "all" ? undefined : selectedBaseCharacter,
    limit: 20,
  });
  const { data: myCharactersData, refetch: refetchMyCharacters } = api.characterManagement.getMyCustomCharacters.useQuery({});
  const { data: favoriteCharactersData, refetch: refetchFavorites } = api.characterManagement.getFavoriteCharacters.useQuery({});
  const { data: userStyles } = api.styleManagement.getMyStyles.useQuery({});

  // API mutations
  const createCharacterMutation = api.characterManagement.createCustomCharacter.useMutation({
    onSuccess: () => {
      toast({ title: "角色创建成功！" });
      setIsCreateDialogOpen(false);
      refetchCharacters();
      refetchMyCharacters();
    },
    onError: (error) => {
      toast({ title: "创建失败", description: error.message, variant: "destructive" });
    },
  });

  const updateCharacterMutation = api.characterManagement.updateCustomCharacter.useMutation({
    onSuccess: () => {
      toast({ title: "角色更新成功！" });
      setEditingCharacter(null);
      refetchCharacters();
      refetchMyCharacters();
    },
    onError: (error) => {
      toast({ title: "更新失败", description: error.message, variant: "destructive" });
    },
  });

  const deleteCharacterMutation = api.characterManagement.deleteCustomCharacter.useMutation({
    onSuccess: () => {
      toast({ title: "角色删除成功！" });
      refetchCharacters();
      refetchMyCharacters();
    },
    onError: (error) => {
      toast({ title: "删除失败", description: error.message, variant: "destructive" });
    },
  });

  const toggleFavoriteMutation = api.characterManagement.toggleCharacterFavorite.useMutation({
    onSuccess: () => {
      refetchCharacters();
      refetchFavorites();
    },
  });

  const handleCreateCharacter = (formData: FormData) => {
    const data = {
      name: formData.get("name") as string,
      nameEn: formData.get("nameEn") as string,
      description: formData.get("description") as string,
      baseCharacterId: formData.get("baseCharacterId") as string,
      speed: parseFloat(formData.get("speed") as string),
      pitch: parseFloat(formData.get("pitch") as string),
      volume: parseFloat(formData.get("volume") as string),
      defaultStyleId: formData.get("defaultStyleId") as string || undefined,
      personality: (formData.get("personality") as string).split(",").map(p => p.trim()).filter(Boolean),
      bestFor: (formData.get("bestFor") as string).split(",").map(b => b.trim()).filter(Boolean),
      tags: (formData.get("tags") as string).split(",").map(tag => tag.trim()).filter(Boolean),
      isPublic: formData.get("isPublic") === "on",
    };

    createCharacterMutation.mutate(data);
  };

  const handleUpdateCharacter = (formData: FormData) => {
    if (!editingCharacter) return;

    const data = {
      id: editingCharacter.id,
      name: formData.get("name") as string,
      nameEn: formData.get("nameEn") as string,
      description: formData.get("description") as string,
      baseCharacterId: formData.get("baseCharacterId") as string,
      speed: parseFloat(formData.get("speed") as string),
      pitch: parseFloat(formData.get("pitch") as string),
      volume: parseFloat(formData.get("volume") as string),
      defaultStyleId: formData.get("defaultStyleId") as string || undefined,
      personality: (formData.get("personality") as string).split(",").map(p => p.trim()).filter(Boolean),
      bestFor: (formData.get("bestFor") as string).split(",").map(b => b.trim()).filter(Boolean),
      tags: (formData.get("tags") as string).split(",").map(tag => tag.trim()).filter(Boolean),
      isPublic: formData.get("isPublic") === "on",
    };

    updateCharacterMutation.mutate(data);
  };

  const handleDeleteCharacter = (characterId: string) => {
    if (confirm("确定要删除这个角色吗？")) {
      deleteCharacterMutation.mutate({ id: characterId });
    }
  };

  const handleToggleFavorite = (characterId: string) => {
    toggleFavoriteMutation.mutate({ characterId });
  };

  const CharacterCard = ({ character, showActions = false }: { character: any; showActions?: boolean }) => {
    const [showPreview, setShowPreview] = useState(false);

    return (
      <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={character.avatarUrl || character.baseCharacter?.avatarUrl} />
                    <AvatarFallback>{character.name[0]}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <CardTitle className="text-lg">{character.name}</CardTitle>
                    <CardDescription className="mt-1">
                      基于 {character.baseCharacter?.characterName}
                    </CardDescription>
                    {character.description && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {character.description}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowPreview(!showPreview)}
                    className="text-blue-500 hover:text-blue-600"
                    title="试听角色"
                  >
                    <Volume2 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleToggleFavorite(character.id)}
                    className="text-red-500 hover:text-red-600"
                  >
                    <Heart className="h-4 w-4" />
                  </Button>
                  {showActions && (
                    <>
                      <CharacterShare
                        character={character}
                        onShareCreated={(shareData) => {
                          console.log('角色分享创建成功:', shareData);
                        }}
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setEditingCharacter(character)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteCharacter(character.id)}
                  className="text-red-500 hover:text-red-600"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Badge variant={character.isPublic ? "default" : "outline"}>
              {character.isPublic ? "公开" : "私有"}
            </Badge>
            {character.defaultStyle && (
              <Badge variant="secondary">{character.defaultStyle.name}</Badge>
            )}
          </div>

          <div className="grid grid-cols-3 gap-2 text-sm">
            <div className="flex items-center gap-1">
              <Zap className="h-3 w-3" />
              <span>速度: {character.speed}x</span>
            </div>
            <div className="flex items-center gap-1">
              <Music className="h-3 w-3" />
              <span>音调: {character.pitch > 0 ? '+' : ''}{character.pitch}</span>
            </div>
            <div className="flex items-center gap-1">
              <Volume2 className="h-3 w-3" />
              <span>音量: {character.volume > 0 ? '+' : ''}{character.volume}</span>
            </div>
          </div>

          {character.personality && character.personality.length > 0 && (
            <div>
              <p className="text-sm font-medium mb-1">性格特点:</p>
              <div className="flex flex-wrap gap-1">
                {character.personality.map((trait: string, index: number) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {trait}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {character.bestFor && character.bestFor.length > 0 && (
            <div>
              <p className="text-sm font-medium mb-1">适用场景:</p>
              <div className="flex flex-wrap gap-1">
                {character.bestFor.map((scenario: string, index: number) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {scenario}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          <div className="text-sm text-muted-foreground">
            <div className="flex items-center gap-4">
              <span className="flex items-center gap-1">
                <Heart className="h-3 w-3" />
                {character.favoriteCount || 0}
              </span>
              <span className="flex items-center gap-1">
                <Star className="h-3 w-3" />
                {character.usageCount || 0}
              </span>
            </div>
          </div>
        </div>

        {/* 语音预览 */}
        {showPreview && (
          <div className="mt-4 pt-4 border-t">
            <VoicePreview
              character={character}
              style={character.defaultStyle}
              parameters={{
                speed: character.speed,
                pitch: character.pitch,
                volume: character.volume,
              }}
              onGenerate={async (text, params) => {
                // TODO: 集成实际的语音生成API
                console.log('生成语音:', { text, params });
                // 模拟生成延迟
                await new Promise(resolve => setTimeout(resolve, 2000));
                return "/audio/sample-voice.mp3";
              }}
            />
          </div>
        )}
      </CardContent>
    </Card>
    );
  };

  return (
    <AdminOnlyRoute>
      <AppLayout>
        <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">角色管理</h1>
            <p className="text-muted-foreground">创建和管理您的自定义语音角色</p>
          </div>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            创建角色
          </Button>
        </div>

      <div className="flex gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索角色..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <Select value={selectedBaseCharacter} onValueChange={setSelectedBaseCharacter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="选择基础角色" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部角色</SelectItem>
            {baseCharacters?.map((character) => (
              <SelectItem key={character.id} value={character.id}>
                {character.characterName}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">全部角色</TabsTrigger>
          <TabsTrigger value="my">我的角色</TabsTrigger>
          <TabsTrigger value="favorites">收藏夹</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {charactersData?.characters?.map((character) => (
              <CharacterCard key={character.id} character={character} />
            ))}
          </div>
          {charactersData?.characters?.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              没有找到匹配的角色
            </div>
          )}
        </TabsContent>

        <TabsContent value="my" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {myCharactersData?.characters?.map((character) => (
              <CharacterCard key={character.id} character={character} showActions />
            ))}
          </div>
          {myCharactersData?.characters?.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              您还没有创建任何角色
            </div>
          )}
        </TabsContent>

        <TabsContent value="favorites" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {favoriteCharactersData?.characters?.map((character) => (
              <CharacterCard key={character.id} character={character} />
            ))}
          </div>
          {favoriteCharactersData?.characters?.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              您还没有收藏任何角色
            </div>
          )}
        </TabsContent>
      </Tabs>

        {/* Create/Edit Character Dialog */}
        <CreateEditCharacterDialog
          character={editingCharacter}
          isOpen={isCreateDialogOpen || !!editingCharacter}
          onClose={() => {
            setIsCreateDialogOpen(false);
            setEditingCharacter(null);
          }}
          baseCharacters={baseCharacters || []}
          userStyles={userStyles?.styles || []}
          onSubmit={editingCharacter ? handleUpdateCharacter : handleCreateCharacter}
          isLoading={createCharacterMutation.isPending || updateCharacterMutation.isPending}
        />
        </div>
      </AppLayout>
    </AdminOnlyRoute>
  );
}

// Create/Edit Character Dialog Component
function CreateEditCharacterDialog({
  character,
  isOpen,
  onClose,
  baseCharacters,
  userStyles,
  onSubmit,
  isLoading,
}: {
  character?: any;
  isOpen: boolean;
  onClose: () => void;
  baseCharacters: any[];
  userStyles: any[];
  onSubmit: (formData: FormData) => void;
  isLoading: boolean;
}) {
  const [selectedBaseCharacter, setSelectedBaseCharacter] = useState(character?.baseCharacter || null);
  const [parameters, setParameters] = useState({
    speed: character?.speed || 1.0,
    pitch: character?.pitch || 0.0,
    volume: character?.volume || 0.0,
  });
  const [avatarUrl, setAvatarUrl] = useState<string | null>(character?.avatarUrl || null);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{character ? "编辑角色" : "创建新角色"}</DialogTitle>
          <DialogDescription>
            {character ? "修改角色信息和参数" : "基于现有角色创建您的专属语音角色"}
          </DialogDescription>
        </DialogHeader>

        <form
          action={(formData) => {
            // 添加参数到表单数据
            formData.set('speed', parameters.speed.toString());
            formData.set('pitch', parameters.pitch.toString());
            formData.set('volume', parameters.volume.toString());
            if (avatarUrl) {
              formData.set('avatarUrl', avatarUrl);
            }
            onSubmit(formData);
          }}
          className="space-y-6"
        >
          {/* Avatar Upload */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">角色头像</h3>
            <AvatarUpload
              currentAvatarUrl={character?.avatarUrl}
              characterName={character?.name || "新角色"}
              onAvatarChange={setAvatarUrl}
            />
          </div>

          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">基本信息</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">角色名称 *</Label>
                <Input
                  id="name"
                  name="name"
                  defaultValue={character?.name || ""}
                  placeholder="输入角色名称"
                  required
                />
              </div>
              <div>
                <Label htmlFor="nameEn">英文名称</Label>
                <Input
                  id="nameEn"
                  name="nameEn"
                  defaultValue={character?.nameEn || ""}
                  placeholder="English name"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">描述</Label>
              <Textarea
                id="description"
                name="description"
                defaultValue={character?.description || ""}
                placeholder="描述这个角色的特点和用途"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="baseCharacterId">基础角色 *</Label>
                <Select
                  name="baseCharacterId"
                  defaultValue={character?.baseCharacterId || ""}
                  onValueChange={(value) => {
                    const baseChar = baseCharacters.find(c => c.id === value);
                    setSelectedBaseCharacter(baseChar);
                  }}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择基础角色" />
                  </SelectTrigger>
                  <SelectContent>
                    {baseCharacters.map((baseChar) => (
                      <SelectItem key={baseChar.id} value={baseChar.id}>
                        {baseChar.characterName} ({baseChar.gender})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="defaultStyleId">默认风格</Label>
                <Select name="defaultStyleId" defaultValue={character?.defaultStyleId || ""}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择默认风格" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">无</SelectItem>
                    {userStyles.map((style) => (
                      <SelectItem key={style.id} value={style.id}>
                        {style.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Enhanced Voice Parameters */}
          <CharacterParameterConfig
            baseCharacter={selectedBaseCharacter}
            initialSpeed={parameters.speed}
            initialPitch={parameters.pitch}
            initialVolume={parameters.volume}
            onParametersChange={setParameters}
            onPreview={(params) => {
              // TODO: 实现语音预览功能
              console.log('预览参数:', params);
            }}
          />

          {/* Character Traits */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">角色特征</h3>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="personality">性格特点</Label>
                <Input
                  id="personality"
                  name="personality"
                  defaultValue={character?.personality?.join(", ") || ""}
                  placeholder="用逗号分隔，例如：友好, 专业, 温暖"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  描述角色的性格特征，帮助用户了解角色风格
                </p>
              </div>
              <div>
                <Label htmlFor="bestFor">适用场景</Label>
                <Input
                  id="bestFor"
                  name="bestFor"
                  defaultValue={character?.bestFor?.join(", ") || ""}
                  placeholder="用逗号分隔，例如：客服对话, 教育内容"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  推荐的使用场景，帮助用户选择合适的角色
                </p>
              </div>
            </div>

            <div>
              <Label htmlFor="tags">标签</Label>
              <Input
                id="tags"
                name="tags"
                defaultValue={character?.tags?.join(", ") || ""}
                placeholder="用逗号分隔，例如：自定义, 专业, 友好"
              />
              <p className="text-xs text-muted-foreground mt-1">
                便于搜索和分类的标签
              </p>
            </div>
          </div>

          {/* Sharing Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">分享设置</h3>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="isPublic"
                name="isPublic"
                defaultChecked={character?.isPublic || false}
                className="rounded"
              />
              <Label htmlFor="isPublic">公开分享（其他用户可以看到和使用）</Label>
            </div>
            <p className="text-xs text-muted-foreground">
              公开后，其他用户可以在角色库中找到并使用您的角色
            </p>
          </div>

          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button type="submit" disabled={isLoading}>
              {character ? "更新" : "创建"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
