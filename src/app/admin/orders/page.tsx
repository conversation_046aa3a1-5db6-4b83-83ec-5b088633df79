"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { api } from "~/trpc/react";
import { AppLayout } from "~/components/app-layout";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "~/components/ui/dialog";
import { Textarea } from "~/components/ui/textarea";
import { 
  CreditCard, 
  QrCode, 
  Smartphone, 
  CheckCircle, 
  XCircle, 
  Clock,
  Search,
  Filter,
  Eye,
  RefreshCw,
  Edit,
  DollarSign,
  Users,
  TrendingUp
} from "lucide-react";
import { toast } from "sonner";

const paymentIcons = {
  PAYPAL: CreditCard,
  KHQR: QrCode,
  WECHAT: Smartphone,
  STRIPE: CreditCard,
};

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  COMPLETED: "bg-green-100 text-green-800",
  FAILED: "bg-red-100 text-red-800",
  CANCELLED: "bg-gray-100 text-gray-800",
};

const statusIcons = {
  PENDING: Clock,
  COMPLETED: CheckCircle,
  FAILED: XCircle,
  CANCELLED: XCircle,
};

export default function AdminOrdersPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [page, setPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [providerFilter, setProviderFilter] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [updateDialogOpen, setUpdateDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState<string>('');
  const [adminNotes, setAdminNotes] = useState('');

  // 权限检查
  const { data: userInfo } = api.auth.getCurrentUser.useQuery(
    undefined,
    { enabled: !!session?.user }
  );

  // 获取所有订单
  const { data: orderHistory, isLoading, refetch } = api.order.getAllOrders.useQuery(
    {
      page,
      limit: 20,
      ...(statusFilter && { status: statusFilter as any }),
      ...(providerFilter && { provider: providerFilter as any }),
    },
    { enabled: !!session?.user && userInfo?.role === 'SUPER_ADMIN' }
  );

  // 更新订单状态
  const updateOrderStatus = api.order.adminUpdateOrder.useMutation({
    onSuccess: () => {
      toast.success("订单状态更新成功");
      setUpdateDialogOpen(false);
      setSelectedOrder(null);
      setNewStatus('');
      setAdminNotes('');
      refetch();
    },
    onError: (error) => {
      toast.error(error.message || "更新订单状态失败");
    },
  });

  const handleUpdateOrder = () => {
    if (!selectedOrder || !newStatus) return;

    updateOrderStatus.mutate({
      paymentId: selectedOrder.id,
      status: newStatus as any,
      notes: adminNotes,
    });
  };

  const handleRefresh = () => {
    refetch();
    toast.success("订单列表已刷新");
  };

  // 权限检查
  if (!session?.user) {
    router.push('/auth/signin');
    return null;
  }

  if (userInfo && userInfo.role !== 'SUPER_ADMIN') {
    return (
      <AppLayout>
        <div className="container mx-auto px-4 py-8">
          <Card>
            <CardContent className="text-center py-12">
              <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">访问被拒绝</h2>
              <p className="text-muted-foreground">只有超级管理员才能访问订单管理页面</p>
              <div className="mt-4 text-sm text-muted-foreground">
                <p>当前角色: {userInfo.role}</p>
                <p>需要角色: SUPER_ADMIN</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">订单管理</h1>
            <p className="text-muted-foreground">管理所有用户的订阅订单</p>
          </div>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">总收入</p>
                  <p className="text-2xl font-bold">
                    ${orderHistory?.payments
                      .filter(p => p.status === 'COMPLETED')
                      .reduce((sum, p) => sum + p.amount, 0)
                      .toLocaleString() || '0'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">成功订单</p>
                  <p className="text-2xl font-bold">
                    {orderHistory?.payments.filter(p => p.status === 'COMPLETED').length || 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">待处理</p>
                  <p className="text-2xl font-bold">
                    {orderHistory?.payments.filter(p => p.status === 'PENDING').length || 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">总订单</p>
                  <p className="text-2xl font-bold">{orderHistory?.total || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 筛选和搜索 */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              筛选订单
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex space-x-4">
              <div className="flex-1">
                <Input
                  placeholder="搜索用户邮箱、订单号..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <Select value={statusFilter || "ALL"} onValueChange={(value) => setStatusFilter(value === "ALL" ? "" : value)}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">全部状态</SelectItem>
                  <SelectItem value="PENDING">待支付</SelectItem>
                  <SelectItem value="COMPLETED">已完成</SelectItem>
                  <SelectItem value="FAILED">失败</SelectItem>
                  <SelectItem value="CANCELLED">已取消</SelectItem>
                </SelectContent>
              </Select>
              <Select value={providerFilter || "ALL"} onValueChange={(value) => setProviderFilter(value === "ALL" ? "" : value)}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="支付方式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">全部方式</SelectItem>
                  <SelectItem value="PAYPAL">PayPal</SelectItem>
                  <SelectItem value="KHQR">KHQR</SelectItem>
                  <SelectItem value="WECHAT">微信支付</SelectItem>
                  <SelectItem value="STRIPE">Stripe</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline">
                <Search className="h-4 w-4 mr-2" />
                搜索
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 订单列表 */}
        <Card>
          <CardHeader>
            <CardTitle>订单列表</CardTitle>
            <CardDescription>
              {orderHistory?.total ? `共 ${orderHistory.total} 个订单` : '暂无订单'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : orderHistory?.payments.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-muted-foreground">
                  <CreditCard className="h-16 w-16 mx-auto mb-4 opacity-50" />
                  <p>暂无订单数据</p>
                </div>
              </div>
            ) : (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>用户信息</TableHead>
                      <TableHead>订单信息</TableHead>
                      <TableHead>订阅计划</TableHead>
                      <TableHead>金额</TableHead>
                      <TableHead>支付方式</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {orderHistory?.payments.map((payment) => {
                      const PaymentIcon = paymentIcons[payment.provider];
                      const StatusIcon = statusIcons[payment.status];
                      const metadata = payment.metadata as any;

                      return (
                        <TableRow key={payment.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{payment.user.name || '未知用户'}</div>
                              <div className="text-sm text-muted-foreground">
                                {payment.user.email}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {metadata?.orderNumber || payment.id.slice(0, 8)}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {payment.description}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {payment.subscription?.plan?.displayName || '未知计划'}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {payment.subscription?.billingCycle === 'yearly' ? '年付' : '月付'}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">
                              {payment.currency === 'KHR' ? '៛' : '$'}
                              {payment.amount.toLocaleString()}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <PaymentIcon className="h-4 w-4 mr-2" />
                              {payment.provider}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={statusColors[payment.status]}>
                              <StatusIcon className="h-3 w-3 mr-1" />
                              {payment.status === 'PENDING' && '待支付'}
                              {payment.status === 'COMPLETED' && '已完成'}
                              {payment.status === 'FAILED' && '失败'}
                              {payment.status === 'CANCELLED' && '已取消'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {new Date(payment.createdAt).toLocaleDateString('zh-CN')}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {new Date(payment.createdAt).toLocaleTimeString('zh-CN')}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => router.push(`/payment/${payment.id}`)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Dialog open={updateDialogOpen} onOpenChange={setUpdateDialogOpen}>
                                <DialogTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      setSelectedOrder(payment);
                                      setNewStatus(payment.status);
                                      setAdminNotes('');
                                    }}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                </DialogTrigger>
                                <DialogContent>
                                  <DialogHeader>
                                    <DialogTitle>更新订单状态</DialogTitle>
                                    <DialogDescription>
                                      修改订单 {metadata?.orderNumber || payment.id.slice(0, 8)} 的状态
                                    </DialogDescription>
                                  </DialogHeader>
                                  <div className="space-y-4">
                                    <div>
                                      <label className="text-sm font-medium">新状态</label>
                                      <Select value={newStatus} onValueChange={setNewStatus}>
                                        <SelectTrigger>
                                          <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                          <SelectItem value="PENDING">待支付</SelectItem>
                                          <SelectItem value="COMPLETED">已完成</SelectItem>
                                          <SelectItem value="FAILED">失败</SelectItem>
                                          <SelectItem value="CANCELLED">已取消</SelectItem>
                                        </SelectContent>
                                      </Select>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">管理员备注</label>
                                      <Textarea
                                        placeholder="添加备注..."
                                        value={adminNotes}
                                        onChange={(e) => setAdminNotes(e.target.value)}
                                      />
                                    </div>
                                  </div>
                                  <DialogFooter>
                                    <Button
                                      variant="outline"
                                      onClick={() => setUpdateDialogOpen(false)}
                                    >
                                      取消
                                    </Button>
                                    <Button
                                      onClick={handleUpdateOrder}
                                      disabled={updateOrderStatus.isPending}
                                    >
                                      更新
                                    </Button>
                                  </DialogFooter>
                                </DialogContent>
                              </Dialog>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>

                {/* 分页 */}
                {orderHistory && orderHistory.totalPages > 1 && (
                  <div className="flex items-center justify-between mt-6">
                    <div className="text-sm text-muted-foreground">
                      第 {orderHistory.page} 页，共 {orderHistory.totalPages} 页
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={orderHistory.page <= 1}
                        onClick={() => setPage(page - 1)}
                      >
                        上一页
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={orderHistory.page >= orderHistory.totalPages}
                        onClick={() => setPage(page + 1)}
                      >
                        下一页
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
