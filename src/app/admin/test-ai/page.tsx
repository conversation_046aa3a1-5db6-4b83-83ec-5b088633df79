"use client";

import { useState } from "react";
import { api } from "~/trpc/react";
import { AppLayout } from "~/components/app-layout";
import { RouteGuard } from "~/components/auth/route-guard";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { toast } from "~/hooks/use-toast";
import { Wand2, Image, Volume2, Loader2 } from "lucide-react";

export default function TestAIPage() {
  return (
    <RouteGuard requireAuth={true} requireSuperAdmin={true}>
      <AppLayout>
        <TestAIContent />
      </AppLayout>
    </RouteGuard>
  );
}

function TestAIContent() {
  const [selectedCharacterId, setSelectedCharacterId] = useState("");
  const [customPrompt, setCustomPrompt] = useState("");
  const [demoText, setDemoText] = useState("");

  // 获取语言角色列表
  const { data: characters } = api.languageCharacterAdmin.getLanguageCharacters.useQuery({
    languageId: "cmf4vq91k0003rdp17yiww1ov", // 中文
    includeInactive: true,
  });

  // AI生成头像
  const generateAvatarMutation = api.aiGeneration.generateCharacterAvatar.useMutation({
    onSuccess: (data) => {
      toast({
        title: "头像生成成功",
        description: data.message,
      });
    },
    onError: (error) => {
      toast({
        title: "头像生成失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // AI生成语音试听
  const generateVoiceDemoMutation = api.aiGeneration.generateVoiceDemo.useMutation({
    onSuccess: (data) => {
      toast({
        title: "语音试听生成成功",
        description: data.message,
      });
    },
    onError: (error) => {
      toast({
        title: "语音试听生成失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleGenerateAvatar = () => {
    if (!selectedCharacterId) {
      toast({
        title: "请选择角色",
        description: "需要先选择一个角色才能生成头像",
        variant: "destructive",
      });
      return;
    }

    const character = characters?.find(c => c.id === selectedCharacterId);
    if (!character) return;

    generateAvatarMutation.mutate({
      characterId: character.id,
      characterName: character.name,
      gender: character.template?.gender || 'NEUTRAL',
      customPrompt: customPrompt || undefined,
    });
  };

  const handleGenerateVoiceDemo = () => {
    if (!selectedCharacterId) {
      toast({
        title: "请选择角色",
        description: "需要先选择一个角色才能生成语音试听",
        variant: "destructive",
      });
      return;
    }

    generateVoiceDemoMutation.mutate({
      characterId: selectedCharacterId,
      demoText: demoText || undefined,
      regenerate: true,
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">AI生成功能测试</h1>
        <p className="text-muted-foreground">测试AI生成头像和语音试听功能</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 头像生成测试 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Image className="h-5 w-5" />
              AI头像生成测试
            </CardTitle>
            <CardDescription>
              测试为语言角色生成AI头像
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="character-select">选择角色</Label>
              <Select value={selectedCharacterId} onValueChange={setSelectedCharacterId}>
                <SelectTrigger>
                  <SelectValue placeholder="选择一个角色" />
                </SelectTrigger>
                <SelectContent>
                  {characters?.map((character) => (
                    <SelectItem key={character.id} value={character.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{character.name} ({character.template?.apiVoiceName || '自定义'})</span>
                        {character.template?.gender && (
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            character.template.gender === 'MALE' ? 'bg-blue-100 text-blue-700' :
                            character.template.gender === 'FEMALE' ? 'bg-pink-100 text-pink-700' :
                            'bg-gray-100 text-gray-700'
                          }`}>
                            {character.template.gender === 'MALE' ? '男' :
                             character.template.gender === 'FEMALE' ? '女' :
                             '中'}
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="custom-prompt">自定义提示词（可选）</Label>
              <Textarea
                id="custom-prompt"
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                placeholder="例如: professional business person, smiling, modern office background"
                rows={3}
              />
            </div>

            <Button 
              onClick={handleGenerateAvatar}
              disabled={generateAvatarMutation.isPending || !selectedCharacterId}
              className="w-full"
            >
              {generateAvatarMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  生成中...
                </>
              ) : (
                <>
                  <Wand2 className="h-4 w-4 mr-2" />
                  生成AI头像
                </>
              )}
            </Button>

            {generateAvatarMutation.isSuccess && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-green-800 text-sm">
                  ✅ 头像生成成功！请刷新页面查看结果。
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 语音试听生成测试 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Volume2 className="h-5 w-5" />
              AI语音试听生成测试
            </CardTitle>
            <CardDescription>
              测试为语言角色生成AI语音试听
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="character-select-voice">选择角色</Label>
              <Select value={selectedCharacterId} onValueChange={setSelectedCharacterId}>
                <SelectTrigger>
                  <SelectValue placeholder="选择一个角色" />
                </SelectTrigger>
                <SelectContent>
                  {characters?.filter(c => c.template).map((character) => (
                    <SelectItem key={character.id} value={character.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{character.name} ({character.template?.apiVoiceName})</span>
                        {character.template?.gender && (
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            character.template.gender === 'MALE' ? 'bg-blue-100 text-blue-700' :
                            character.template.gender === 'FEMALE' ? 'bg-pink-100 text-pink-700' :
                            'bg-gray-100 text-gray-700'
                          }`}>
                            {character.template.gender === 'MALE' ? '男' :
                             character.template.gender === 'FEMALE' ? '女' :
                             '中'}
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="demo-text">试听文本（可选）</Label>
              <Textarea
                id="demo-text"
                value={demoText}
                onChange={(e) => setDemoText(e.target.value)}
                placeholder="留空将使用默认试听文本"
                rows={3}
              />
            </div>

            <Button 
              onClick={handleGenerateVoiceDemo}
              disabled={generateVoiceDemoMutation.isPending || !selectedCharacterId}
              className="w-full"
            >
              {generateVoiceDemoMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  生成中...
                </>
              ) : (
                <>
                  <Wand2 className="h-4 w-4 mr-2" />
                  生成AI语音试听
                </>
              )}
            </Button>

            {generateVoiceDemoMutation.isSuccess && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-green-800 text-sm">
                  ✅ 语音试听生成成功！请刷新页面查看结果。
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 角色列表 */}
      <Card>
        <CardHeader>
          <CardTitle>当前角色列表</CardTitle>
          <CardDescription>
            显示当前中文语言下的所有角色
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {characters?.map((character) => (
              <div key={character.id} className="p-4 border rounded-lg">
                <div className="flex items-center gap-3 mb-2">
                  {character.avatarUrl && (
                    <img 
                      src={character.avatarUrl} 
                      alt={character.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                  )}
                  <div>
                    <h4 className="font-medium">{character.name}</h4>
                    <p className="text-sm text-muted-foreground">
                      {character.template?.apiVoiceName || '自定义角色'}
                    </p>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  {character.description}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
