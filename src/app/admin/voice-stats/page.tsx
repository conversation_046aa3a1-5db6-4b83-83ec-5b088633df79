"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Play, 
  Clock,
  Globe,
  RefreshCw,
  Download,
  Calendar
} from "lucide-react";
import { api } from "~/trpc/react";
import { toast } from "sonner";

export default function VoiceStatsPage() {
  const [selectedCharacter, setSelectedCharacter] = useState<string>("all");
  const [selectedLanguage, setSelectedLanguage] = useState<string>("all");
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({
    start: "",
    end: "",
  });

  // 获取角色列表
  const { data: characters } = api.voiceAdmin.getCharacters.useQuery();
  
  // 获取语言列表
  const { data: languages } = api.languageAdmin.getLanguages.useQuery();

  // 获取播放统计
  const { data: playStats, refetch: refetchPlayStats } = api.voiceAdmin.getPlayStats.useQuery({
    characterId: selectedCharacter === "all" ? undefined : selectedCharacter,
    languageCode: selectedLanguage === "all" ? undefined : selectedLanguage,
    startDate: dateRange.start ? new Date(dateRange.start) : undefined,
    endDate: dateRange.end ? new Date(dateRange.end) : undefined,
    limit: 50,
  });

  // 获取聚合统计
  const { data: aggregateStats, refetch: refetchAggregateStats } = api.voiceAdmin.getAggregateStats.useQuery({
    characterId: selectedCharacter === "all" ? undefined : selectedCharacter,
    languageCode: selectedLanguage === "all" ? undefined : selectedLanguage,
  });

  const handleRefresh = () => {
    void refetchPlayStats();
    void refetchAggregateStats();
    toast.success('统计数据已刷新');
  };

  // 计算总体统计
  const totalStats = aggregateStats ? {
    totalPlays: aggregateStats.reduce((sum, stat) => sum + stat.totalPlays, 0),
    totalDemos: aggregateStats.length,
    avgPlaysPerDemo: aggregateStats.length > 0 ? 
      aggregateStats.reduce((sum, stat) => sum + stat.totalPlays, 0) / aggregateStats.length : 0,
    mostPopular: aggregateStats.sort((a, b) => b.totalPlays - a.totalPlays)[0],
  } : null;

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">语音试听统计</h1>
          <p className="text-muted-foreground">查看语音试听的播放统计和使用分析</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新数据
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            导出报告
          </Button>
        </div>
      </div>

      {/* 筛选器 */}
      <Card>
        <CardHeader>
          <CardTitle>筛选条件</CardTitle>
          <CardDescription>选择要查看统计的角色、语言和时间范围</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="character">角色</Label>
              <Select value={selectedCharacter} onValueChange={setSelectedCharacter}>
                <SelectTrigger>
                  <SelectValue placeholder="选择角色" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部角色</SelectItem>
                  {characters?.map((character) => (
                    <SelectItem key={character.id} value={character.id}>
                      {character.gender === 'FEMALE' ? '👩' : '👨'} {character.characterName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="language">语言</Label>
              <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                <SelectTrigger>
                  <SelectValue placeholder="选择语言" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部语言</SelectItem>
                  {languages?.map((language) => (
                    <SelectItem key={language.id} value={language.code}>
                      {language.flag} {language.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="startDate">开始日期</Label>
              <Input
                id="startDate"
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              />
            </div>
            
            <div>
              <Label htmlFor="endDate">结束日期</Label>
              <Input
                id="endDate"
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 总体统计卡片 */}
      {totalStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总播放次数</CardTitle>
              <Play className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalStats.totalPlays.toLocaleString()}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">试听总数</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalStats.totalDemos}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平均播放次数</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalStats.avgPlaysPerDemo.toFixed(1)}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">最受欢迎</CardTitle>
              <Globe className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-sm font-bold">
                {totalStats.mostPopular ? 
                  `${totalStats.mostPopular.voiceDemo.voiceCharacter.characterName} (${totalStats.mostPopular.voiceDemo.language.code})` : 
                  '暂无数据'
                }
              </div>
              <div className="text-xs text-muted-foreground">
                {totalStats.mostPopular ? `${totalStats.mostPopular.totalPlays} 次播放` : ''}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 详细统计表格 */}
      <Tabs defaultValue="aggregate" className="space-y-4">
        <TabsList>
          <TabsTrigger value="aggregate">聚合统计</TabsTrigger>
          <TabsTrigger value="detailed">详细记录</TabsTrigger>
        </TabsList>
        
        <TabsContent value="aggregate">
          <Card>
            <CardHeader>
              <CardTitle>聚合统计</CardTitle>
              <CardDescription>按试听分组的播放统计</CardDescription>
            </CardHeader>
            <CardContent>
              {aggregateStats && aggregateStats.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>角色</TableHead>
                      <TableHead>语言</TableHead>
                      <TableHead>总播放次数</TableHead>
                      <TableHead>独立播放次数</TableHead>
                      <TableHead>完整播放次数</TableHead>
                      <TableHead>平均时长</TableHead>
                      <TableHead>最后播放</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {aggregateStats.map((stat) => (
                      <TableRow key={stat.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <span>{stat.voiceDemo.voiceCharacter.gender === 'FEMALE' ? '👩' : '👨'}</span>
                            <span>{stat.voiceDemo.voiceCharacter.characterName}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <span>{stat.voiceDemo.language.flag}</span>
                            <span>{stat.voiceDemo.language.code}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">{stat.totalPlays}</Badge>
                        </TableCell>
                        <TableCell>{stat.uniquePlays}</TableCell>
                        <TableCell>{stat.completedPlays}</TableCell>
                        <TableCell>
                          {stat.averageDuration ? `${stat.averageDuration.toFixed(1)}s` : '-'}
                        </TableCell>
                        <TableCell>
                          {stat.lastPlayedAt ? 
                            new Date(stat.lastPlayedAt).toLocaleDateString() : 
                            '-'
                          }
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8">
                  <BarChart3 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                    暂无统计数据
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    还没有播放记录，请先播放一些语音试听
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="detailed">
          <Card>
            <CardHeader>
              <CardTitle>详细播放记录</CardTitle>
              <CardDescription>最近的播放记录详情</CardDescription>
            </CardHeader>
            <CardContent>
              {playStats && playStats.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>时间</TableHead>
                      <TableHead>角色</TableHead>
                      <TableHead>语言</TableHead>
                      <TableHead>用户</TableHead>
                      <TableHead>播放时长</TableHead>
                      <TableHead>是否完整</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {playStats.map((stat) => (
                      <TableRow key={stat.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">
                              {new Date(stat.createdAt).toLocaleString()}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <span>{stat.voiceDemo.voiceCharacter.gender === 'FEMALE' ? '👩' : '👨'}</span>
                            <span>{stat.voiceDemo.voiceCharacter.characterName}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <span>{stat.voiceDemo.language.flag}</span>
                            <span>{stat.voiceDemo.language.code}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {stat.user ? (
                            <div className="flex items-center space-x-2">
                              <Users className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm">{stat.user.name || stat.user.email}</span>
                            </div>
                          ) : (
                            <Badge variant="outline">匿名用户</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span>{stat.playDuration ? `${stat.playDuration.toFixed(1)}s` : '-'}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={stat.isCompleted ? "default" : "secondary"}>
                            {stat.isCompleted ? "完整" : "部分"}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8">
                  <Play className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                    暂无播放记录
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    还没有播放记录，请先播放一些语音试听
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
