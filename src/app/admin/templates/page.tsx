"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "~/trpc/react";
import { AppLayout } from "~/components/app-layout";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Switch } from "~/components/ui/switch";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { RouteGuard } from "~/components/auth/route-guard";
import {
  MessageSquare,
  Plus,
  Search,
  Edit,
  Trash2,
  RefreshCw,
  Users,
  User,
  Star,
  Eye,
  Settings,
  Crown,
  FileText,
  Filter,
  BarChart3
} from "lucide-react";
import { toast } from "sonner";

export default function AdminTemplatesPage() {
  return (
    <RouteGuard requireSuperAdmin={true}>
      <AdminTemplatesPageContent />
    </RouteGuard>
  );
}

function AdminTemplatesPageContent() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [includeUserTemplates, setIncludeUserTemplates] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<any>(null);

  // API queries
  const { data: templatesData, refetch: refetchTemplates } = api.admin.getAllTemplates.useQuery({
    search: searchQuery,
    includeUserTemplates,
    limit: 50,
    offset: 0,
  });

  const { data: voiceCharacters } = api.tts.getCharacters.useQuery({});

  // API mutations
  const createTemplateMutation = api.admin.createSystemTemplate.useMutation({
    onSuccess: () => {
      toast.success("官方模板创建成功！");
      setIsCreateDialogOpen(false);
      refetchTemplates();
    },
    onError: (error) => {
      toast.error(`创建失败：${error.message}`);
    },
  });

  const updateTemplateMutation = api.admin.updateTemplate.useMutation({
    onSuccess: () => {
      toast.success("模板更新成功！");
      setEditingTemplate(null);
      refetchTemplates();
    },
    onError: (error) => {
      toast.error(`更新失败：${error.message}`);
    },
  });

  const deleteTemplateMutation = api.admin.deleteTemplate.useMutation({
    onSuccess: () => {
      toast.success("模板删除成功！");
      refetchTemplates();
    },
    onError: (error) => {
      toast.error(`删除失败：${error.message}`);
    },
  });

  const handleCreateTemplate = (formData: FormData) => {
    const characters = JSON.parse(formData.get("characters") as string || "[]");
    const dialogues = JSON.parse(formData.get("dialogues") as string || "[]");
    
    const data = {
      name: formData.get("name") as string,
      description: formData.get("description") as string,
      characters,
      dialogues,
      isPublic: formData.get("isPublic") === "on",
    };

    createTemplateMutation.mutate(data);
  };

  const handleUpdateTemplate = (formData: FormData) => {
    if (!editingTemplate) return;

    const characters = JSON.parse(formData.get("characters") as string || "[]");
    const dialogues = JSON.parse(formData.get("dialogues") as string || "[]");

    const data = {
      id: editingTemplate.id,
      name: formData.get("name") as string,
      description: formData.get("description") as string,
      characters,
      dialogues,
      isPublic: formData.get("isPublic") === "on",
    };

    updateTemplateMutation.mutate(data);
  };

  const handleDeleteTemplate = (templateId: string) => {
    if (confirm("确定要删除这个模板吗？删除后无法恢复。")) {
      deleteTemplateMutation.mutate({ id: templateId });
    }
  };

  // 筛选后的模板数据
  const filteredTemplates = templatesData?.templates || [];
  const totalTemplates = templatesData?.total || 0;

  const officialTemplates = filteredTemplates.filter(t => t.isOfficial);
  const userTemplates = filteredTemplates.filter(t => !t.isOfficial);

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <MessageSquare className="h-8 w-8 text-purple-600" />
              模板管理
            </h1>
            <p className="text-muted-foreground">管理系统官方模板和用户自定义模板</p>
          </div>
          <div className="flex items-center space-x-2">
            <Button onClick={() => refetchTemplates()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新
            </Button>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              创建官方模板
            </Button>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总模板数</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalTemplates}</div>
              <p className="text-xs text-muted-foreground">包含官方和用户模板</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">官方模板</CardTitle>
              <Crown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{officialTemplates.length}</div>
              <p className="text-xs text-muted-foreground">系统官方模板</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">用户模板</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{userTemplates.length}</div>
              <p className="text-xs text-muted-foreground">用户创建模板</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">公开模板</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {filteredTemplates.filter(t => t.isPublic).length}
              </div>
              <p className="text-xs text-muted-foreground">公开可用的模板</p>
            </CardContent>
          </Card>
        </div>

        {/* 筛选和搜索 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              筛选和搜索
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>搜索模板</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索模板名称或描述..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>模板类型</Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={includeUserTemplates}
                    onCheckedChange={setIncludeUserTemplates}
                  />
                  <span className="text-sm">包含用户模板</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 模板列表 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>模板列表</CardTitle>
                <CardDescription>
                  管理所有系统模板 ({filteredTemplates.length} 个模板)
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>模板信息</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>创建者</TableHead>
                  <TableHead>配置</TableHead>
                  <TableHead>使用统计</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTemplates.map((template) => (
                  <TableRow key={template.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium flex items-center gap-2">
                          {template.name}
                          {template.isOfficial && (
                            <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                              <Crown className="h-3 w-3 mr-1" />
                              官方
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {template.description || "暂无描述"}
                        </div>
                      </div>
                    </TableCell>

                    <TableCell>
                      <Badge variant={template.isOfficial ? "default" : "outline"}>
                        {template.isOfficial ? "官方模板" : "用户模板"}
                      </Badge>
                    </TableCell>

                    <TableCell>
                      {template.user ? (
                        <div className="text-sm">
                          <div className="font-medium">{template.user.name}</div>
                          <div className="text-muted-foreground">{template.user.email}</div>
                        </div>
                      ) : (
                        <span className="text-muted-foreground text-sm">系统</span>
                      )}
                    </TableCell>

                    <TableCell>
                      <div className="space-y-1 text-sm">
                        <div>角色数量: {template.characters?.length || 0}</div>
                        <div>对话数量: {template.dialogues?.length || 0}</div>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="space-y-1 text-sm">
                        <div className="flex items-center gap-1">
                          <BarChart3 className="h-3 w-3" />
                          使用: {template.usageCount || 0} 次
                        </div>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="flex flex-col gap-1">
                        {template.isPublic ? (
                          <Badge variant="default">公开</Badge>
                        ) : (
                          <Badge variant="outline">私有</Badge>
                        )}
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEditingTemplate(template)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteTemplate(template.id)}
                          className="text-red-500 hover:text-red-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {filteredTemplates.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                没有找到匹配的模板
              </div>
            )}
          </CardContent>
        </Card>

        {/* 创建/编辑模板对话框 */}
        <CreateEditTemplateDialog
          template={editingTemplate}
          isOpen={isCreateDialogOpen || !!editingTemplate}
          onClose={() => {
            setIsCreateDialogOpen(false);
            setEditingTemplate(null);
          }}
          voiceCharacters={voiceCharacters || []}
          onSubmit={editingTemplate ? handleUpdateTemplate : handleCreateTemplate}
          isLoading={createTemplateMutation.isPending || updateTemplateMutation.isPending}
        />
      </div>
    </AppLayout>
  );
}

// 创建/编辑模板对话框组件
function CreateEditTemplateDialog({
  template,
  isOpen,
  onClose,
  voiceCharacters,
  onSubmit,
  isLoading,
}: {
  template?: any;
  isOpen: boolean;
  onClose: () => void;
  voiceCharacters: any[];
  onSubmit: (formData: FormData) => void;
  isLoading: boolean;
}) {
  const [characters, setCharacters] = useState<any[]>(template?.characters || []);
  const [dialogues, setDialogues] = useState<any[]>(template?.dialogues || []);

  React.useEffect(() => {
    if (template) {
      setCharacters(template.characters || []);
      setDialogues(template.dialogues || []);
    } else {
      setCharacters([]);
      setDialogues([]);
    }
  }, [template]);

  const addCharacter = () => {
    setCharacters([...characters, {
      name: `角色 ${characters.length + 1}`,
      voiceId: '',
      speed: 1.0,
      pitch: 0,
      volume: 1.0,
      emotion: 'neutral'
    }]);
  };

  const removeCharacter = (index: number) => {
    const newCharacters = characters.filter((_, i) => i !== index);
    setCharacters(newCharacters);
    // 移除相关的对话
    setDialogues(dialogues.filter(d => parseInt(d.characterId) !== index));
  };

  const updateCharacter = (index: number, updates: any) => {
    setCharacters(characters.map((char, i) => 
      i === index ? { ...char, ...updates } : char
    ));
  };

  const addDialogue = () => {
    if (characters.length === 0) {
      toast.error("请先添加角色");
      return;
    }
    setDialogues([...dialogues, {
      characterId: "0",
      text: "",
      order: dialogues.length
    }]);
  };

  const removeDialogue = (index: number) => {
    setDialogues(dialogues.filter((_, i) => i !== index));
  };

  const updateDialogue = (index: number, updates: any) => {
    setDialogues(dialogues.map((dialogue, i) => 
      i === index ? { ...dialogue, ...updates } : dialogue
    ));
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    formData.set("characters", JSON.stringify(characters));
    formData.set("dialogues", JSON.stringify(dialogues));
    onSubmit(formData);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{template ? "编辑模板" : "创建官方模板"}</DialogTitle>
          <DialogDescription>
            {template ? "修改模板信息和对话内容" : "创建一个新的系统官方模板"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">模板名称 *</Label>
              <Input
                id="name"
                name="name"
                defaultValue={template?.name || ""}
                placeholder="输入模板名称"
                required
              />
            </div>
            <div>
              <Label htmlFor="description">描述</Label>
              <Textarea
                id="description"
                name="description"
                defaultValue={template?.description || ""}
                placeholder="描述这个模板的用途和特点"
                rows={3}
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="isPublic"
                name="isPublic"
                defaultChecked={template?.isPublic ?? true}
                className="rounded"
              />
              <Label htmlFor="isPublic">公开模板（用户可以看到和使用）</Label>
            </div>
          </div>

          {/* 角色配置 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">角色配置</h3>
              <Button type="button" onClick={addCharacter}>
                <Plus className="h-4 w-4 mr-2" />
                添加角色
              </Button>
            </div>

            {characters.map((character, index) => (
              <Card key={index}>
                <CardContent className="pt-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>角色名称</Label>
                      <Input
                        value={character.name}
                        onChange={(e) => updateCharacter(index, { name: e.target.value })}
                        placeholder="角色名称"
                      />
                    </div>
                    <div>
                      <Label>语音角色</Label>
                      <Select
                        value={character.voiceId}
                        onValueChange={(value) => updateCharacter(index, { voiceId: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择语音角色" />
                        </SelectTrigger>
                        <SelectContent>
                          {voiceCharacters.map((voice) => (
                            <SelectItem key={voice.id} value={voice.id}>
                              {voice.characterName} ({voice.description})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>语速</Label>
                      <Input
                        type="number"
                        min="0.5"
                        max="2.0"
                        step="0.1"
                        value={character.speed}
                        onChange={(e) => updateCharacter(index, { speed: parseFloat(e.target.value) })}
                      />
                    </div>
                    <div className="flex justify-end items-end">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => removeCharacter(index)}
                        className="text-red-500"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* 对话内容 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">对话内容</h3>
              <Button type="button" onClick={addDialogue} disabled={characters.length === 0}>
                <Plus className="h-4 w-4 mr-2" />
                添加对话
              </Button>
            </div>

            {dialogues.map((dialogue, index) => (
              <Card key={index}>
                <CardContent className="pt-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label>说话角色</Label>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeDialogue(index)}
                        className="text-red-500"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    <Select
                      value={dialogue.characterId}
                      onValueChange={(value) => updateDialogue(index, { characterId: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择说话角色" />
                      </SelectTrigger>
                      <SelectContent>
                        {characters.map((char, charIndex) => (
                          <SelectItem key={charIndex} value={charIndex.toString()}>
                            {char.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Label>对话内容</Label>
                    <Textarea
                      value={dialogue.text}
                      onChange={(e) => updateDialogue(index, { text: e.target.value })}
                      placeholder="输入对话内容..."
                      rows={2}
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button type="submit" disabled={isLoading}>
              {template ? "更新" : "创建"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}