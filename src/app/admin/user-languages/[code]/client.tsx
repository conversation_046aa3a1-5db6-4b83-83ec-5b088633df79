"use client";

import React, { useState, useRef } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { api } from "~/trpc/react";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Avatar, AvatarImage, AvatarFallback } from "~/components/ui/avatar";
import { Textarea } from "~/components/ui/textarea";
import { Label } from "~/components/ui/label";
import { Slider } from "~/components/ui/slider";
import { useTranslation } from "~/contexts/LanguageContext";
import { WaveformPlayer } from "~/components/waveform-player";
import { useAudioPlayer } from "~/contexts/AudioPlayerContext";
import { toast } from "sonner";
import {
  ArrowLeft,
  Play,
  Pause,
  User,
  Mi<PERSON>,
  <PERSON><PERSON><PERSON>,
  Volume2,
  <PERSON><PERSON>2,
  <PERSON>ap,
  Crown,
  Globe,
  Sparkles,
  Heart,
  Share2,
} from "lucide-react";

interface Language {
  id: string;
  code: string;
  name: string;
  nativeName: string;
  flag: string;
}

interface Character {
  id: string;
  characterName: string;
  characterNameEn: string;
  gender: 'MALE' | 'FEMALE' | 'NEUTRAL';
  description?: string;
  style?: string;
  personality?: string;
  bestFor?: string;
  avatarUrl?: string;
  template?: {
    id: string;
    apiProvider: string;
    apiVoiceName: string;
    originalName: string;
    gender: string;
  };
}

interface LanguagePageClientProps {
  language: Language;
  characters: Character[];
}

export default function LanguagePageClient({ language, characters }: LanguagePageClientProps) {
  const { t } = useTranslation();
  const { data: session } = useSession();
  const router = useRouter();
  const { stopAllOthers } = useAudioPlayer();

  const [selectedCharacterId, setSelectedCharacterId] = useState<string>(
    characters.length > 0 ? characters[0]!.id : ""
  );
  const [inputText, setInputText] = useState("");
  const [speed, setSpeed] = useState([1.0]);
  const [pitch, setPitch] = useState([0]);
  const [volume, setVolume] = useState([1.0]);
  const [quality, setQuality] = useState<'fast' | 'high'>('fast');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedAudio, setGeneratedAudio] = useState<{
    audioUrl?: string;
    character: { characterName: string; characterNameEn: string };
    characterCount: number;
    duration?: number;
    cost?: number;
  } | null>(null);

  // 试听状态
  const [audioState, setAudioState] = useState<{
    characterId: string;
    isPlaying: boolean;
    isLoading: boolean;
  } | null>(null);

  // 获取试听文本
  const demoTexts: Record<string, string> = {
    'zh-CN': '你好，我是AI语音助手，很高兴为您服务。',
    'en-US': 'Hello, I am an AI voice assistant, nice to serve you.',
    'km-KH': 'សួស្តី ខ្ញុំជាជំនួយការសំឡេងAI រីករាយដែលបានបម្រើអ្នក។',
    'th-TH': 'สวัสดี ฉันเป็นผู้ช่วยเสียง AI ยินดีที่ได้ให้บริการคุณ',
    'vi-VN': 'Xin chào, tôi là trợ lý giọng nói AI, rất vui được phục vụ bạn.',
    'ja-JP': 'こんにちは、私はAI音声アシスタントです。お役に立てて嬉しいです。',
    'ko-KR': '안녕하세요, 저는 AI 음성 어시스턴트입니다. 서비스를 제공하게 되어 기쁩니다.',
    'es-ES': 'Hola, soy un asistente de voz AI, encantado de servirte.',
    'fr-FR': 'Bonjour, je suis un assistant vocal IA, ravi de vous servir.',
    'de-DE': 'Hallo, ich bin ein KI-Sprachassistent und freue mich, Ihnen zu helfen.',
    'it-IT': 'Ciao, sono un assistente vocale AI, felice di servirti.',
  };

  const selectedCharacter = characters.find(c => c.id === selectedCharacterId);

  // 生成语音
  const generateSpeech = api.tts.generateSpeech.useMutation({
    onMutate: () => {
      setIsGenerating(true);
    },
    onSuccess: (data) => {
      setGeneratedAudio(data);
      setIsGenerating(false);
      toast.success('语音生成成功！');
    },
    onError: (error) => {
      setIsGenerating(false);
      toast.error('语音生成失败', {
        description: error.message,
      });
    },
  });

  // 试听功能
  const handlePlayDemo = async (characterId: string) => {
    if (audioState?.characterId === characterId && audioState?.isPlaying) {
      // 如果正在播放，则暂停
      setAudioState(prev => prev ? { ...prev, isPlaying: false } : null);
      return;
    }

    const character = characters.find(c => c.id === characterId);
    if (!character) return;

    const demoText = demoTexts[language.code] || demoTexts['en-US'];

    setAudioState({ characterId, isPlaying: false, isLoading: true });

    try {
      const result = await generateSpeech.mutateAsync({
        text: demoText,
        characterId: characterId,
        speed: 1.0,
        pitch: 0,
        volumeGainDb: 0,
        format: 'WAV',
        quality: 'fast',
      });

      if (result.audioUrl) {
        const audio = new Audio(result.audioUrl);
        audio.onplay = () => {
          setAudioState({ characterId, isPlaying: true, isLoading: false });
        };
        audio.onended = () => {
          setAudioState(null);
        };
        audio.onerror = () => {
          setAudioState(null);
          toast.error('音频播放失败');
        };
        
        stopAllOthers('');
        await audio.play();
      }
    } catch (error) {
      setAudioState(null);
      toast.error('试听生成失败');
    }
  };

  const handleGenerate = () => {
    if (!session) {
      toast.error('请先登录');
      router.push('/auth/signin');
      return;
    }

    if (!selectedCharacterId || !inputText.trim()) {
      toast.error('请选择角色并输入文本');
      return;
    }

    stopAllOthers('');

    generateSpeech.mutate({
      text: inputText,
      characterId: selectedCharacterId,
      speed: speed[0],
      pitch: pitch[0],
      volumeGainDb: ((volume[0] ?? 1.0) - 1.0) * 10,
      format: 'WAV',
      quality,
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* 头部导航 */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Link href="/">
                <Button variant="ghost" size="sm" className="gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  返回首页
                </Button>
              </Link>
              <div className="flex items-center gap-3">
                <span className="text-2xl">{language.flag}</span>
                <div>
                  <h1 className="text-xl font-bold">{language.nativeName}</h1>
                  <p className="text-sm text-muted-foreground">{language.name} ({language.code})</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="gap-1">
                <User className="h-3 w-3" />
                {characters.length} 个角色
              </Badge>
              {!session && (
                <Link href="/auth/signin">
                  <Button size="sm">登录体验</Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* 左侧：角色选择 */}
          <div className="lg:col-span-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  选择语音角色
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="divide-y divide-border">
                  {characters.map((character) => (
                    <div
                      key={character.id}
                      className={`p-4 cursor-pointer transition-colors hover:bg-muted/50 ${
                        selectedCharacterId === character.id ? 'bg-primary/5 border-l-4 border-l-primary' : ''
                      }`}
                      onClick={() => setSelectedCharacterId(character.id)}
                    >
                      <div className="flex items-center gap-3">
                        <Avatar className="h-12 w-12">
                          <AvatarImage src={character.avatarUrl || undefined} />
                          <AvatarFallback>
                            <User className="h-6 w-6" />
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-semibold truncate">{character.characterName}</h3>
                            <Badge variant="secondary" className="text-xs">
                              {t(`characters.${character.gender.toLowerCase()}`)}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground truncate">
                            {character.characterNameEn}
                          </p>
                          {character.style && (
                            <Badge variant="outline" className="text-xs mt-1">
                              {character.style}
                            </Badge>
                          )}
                        </div>

                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handlePlayDemo(character.id);
                          }}
                          disabled={audioState?.isLoading}
                          className="gap-1"
                        >
                          {audioState?.characterId === character.id && audioState?.isLoading ? (
                            <Loader2 className="h-3 w-3 animate-spin" />
                          ) : audioState?.characterId === character.id && audioState?.isPlaying ? (
                            <Pause className="h-3 w-3" />
                          ) : (
                            <Play className="h-3 w-3" />
                          )}
                          <span className="hidden sm:inline text-xs">试听</span>
                        </Button>
                      </div>
                      
                      {character.description && (
                        <p className="text-xs text-muted-foreground mt-2 line-clamp-2">
                          {character.description}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧：语音生成 */}
          <div className="lg:col-span-8 space-y-6">
            {/* 语音设置 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  语音设置
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 质量选择 */}
                <div className="space-y-2">
                  <Label>质量</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <div
                      className={`p-3 border rounded cursor-pointer transition-all ${
                        quality === 'fast'
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:border-primary/50'
                      }`}
                      onClick={() => setQuality('fast')}
                    >
                      <div className="flex items-center gap-2">
                        <Zap className="h-4 w-4" />
                        <span className="font-medium">标准（快速）</span>
                      </div>
                    </div>
                    <div
                      className={`p-3 border rounded cursor-pointer transition-all ${
                        quality === 'high'
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:border-primary/50'
                      }`}
                      onClick={() => setQuality('high')}
                    >
                      <div className="flex items-center gap-2">
                        <Crown className="h-4 w-4" />
                        <span className="font-medium">专业（高质量）</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 语音参数 */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>语速: {speed[0]}x</Label>
                    <Slider
                      value={speed}
                      onValueChange={setSpeed}
                      max={2.0}
                      min={0.5}
                      step={0.1}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>音调: {(pitch[0] ?? 0) > 0 ? `+${pitch[0] ?? 0}` : `${pitch[0] ?? 0}`}</Label>
                    <Slider
                      value={pitch}
                      onValueChange={setPitch}
                      max={10}
                      min={-10}
                      step={1}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>音量: {((volume[0] ?? 1.0) * 100).toFixed(0)}%</Label>
                    <Slider
                      value={volume}
                      onValueChange={setVolume}
                      max={1.5}
                      min={0.3}
                      step={0.1}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 文本输入 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mic className="h-5 w-5" />
                  输入文本
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Textarea
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  placeholder={`请输入${language.nativeName}文本...`}
                  className="min-h-[120px] resize-y"
                  maxLength={6000}
                />
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>{inputText.length}/6000 字符</span>
                  {selectedCharacter && (
                    <span>当前角色: {selectedCharacter.characterName}</span>
                  )}
                </div>
                
                <Button
                  onClick={handleGenerate}
                  disabled={!selectedCharacterId || !inputText.trim() || isGenerating}
                  className="w-full"
                  size="lg"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      生成中...
                    </>
                  ) : (
                    <>
                      <Play className="mr-2 h-4 w-4" />
                      生成语音
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* 生成结果 */}
            {generatedAudio && (
              <Card className="border-green-200 bg-gradient-to-br from-green-50 to-blue-50 dark:border-green-800 dark:from-green-900/20 dark:to-blue-900/20 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-700 dark:text-green-300">
                    <Volume2 className="h-5 w-5" />
                    生成成功
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {generatedAudio.audioUrl && (
                    <WaveformPlayer
                      audioUrl={generatedAudio.audioUrl}
                      className="w-full"
                      playerId={`language-${language.code}`}
                    />
                  )}
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">角色:</span>
                      <p className="font-medium">{generatedAudio.character.characterName}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">字符数:</span>
                      <p className="font-medium">{generatedAudio.characterCount}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">质量:</span>
                      <p className="font-medium">{quality === 'high' ? '高质量' : '标准质量'}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">格式:</span>
                      <p className="font-medium">WAV</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
