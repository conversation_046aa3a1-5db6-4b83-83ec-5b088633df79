import Link from "next/link";
import { Button } from "~/components/ui/button";
import { AdminOnlyRoute } from "~/components/auth/admin-only-route";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { ArrowLeft, Globe, Search } from "lucide-react";

export default function LanguageNotFound() {
  return (
    <AdminOnlyRoute>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
            <div className="max-w-md mx-auto px-4">
              <Card>
                <CardHeader className="text-center">
                  <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                    <Search className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <CardTitle className="text-xl">语言未找到</CardTitle>
                </CardHeader>
                <CardContent className="text-center space-y-4">
                  <p className="text-muted-foreground">
                    抱歉，我们暂时不支持您请求的语言，或者该语言已被禁用。
                  </p>
                  
                  <div className="space-y-3">
                    <Link href="/">
                      <Button className="w-full gap-2">
                        <ArrowLeft className="h-4 w-4" />
                        返回首页
                      </Button>
                    </Link>
                    
                    <Link href="/#voice-characters">
                      <Button variant="outline" className="w-full gap-2">
                        <Globe className="h-4 w-4" />
                        查看支持的语言
                      </Button>
                    </Link>
                  </div>
                  
                  <div className="pt-4 border-t">
                    <p className="text-sm text-muted-foreground">
                      我们目前支持以下语言：
                    </p>
                    <div className="flex flex-wrap justify-center gap-2 mt-2">
                      <span className="text-sm">🇰🇭 高棉语</span>
                      <span className="text-sm">🇨🇳 中文</span>
                      <span className="text-sm">🇺🇸 英语</span>
                      <span className="text-sm">🇹🇭 泰语</span>
                      <span className="text-sm">🇻🇳 越南语</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
      </div>
    </AdminOnlyRoute>
  );
}
