"use client";

import { api } from "~/trpc/react";
import { useSession } from "next-auth/react";

export default function AdminDebugPage() {
  const { data: session, status } = useSession();

  // 获取用户角色
  const { data: userInfo, isLoading: userLoading, error: userError } = api.admin.getUserRole.useQuery(undefined, {
    enabled: !!session?.user?.id,
    retry: false,
  });

  // 获取系统统计
  const { data: stats, isLoading: statsLoading, error: statsError } = api.admin.getSystemStats.useQuery(undefined, {
    enabled: !!userInfo,
    retry: false,
  });

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">管理员调试页面</h1>
        
        <div className="space-y-6">
          {/* Session Info */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">会话信息</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify({ 
                status,
                userId: session?.user?.id,
                userEmail: session?.user?.email,
                hasSession: !!session
              }, null, 2)}
            </pre>
          </div>

          {/* User Role Info */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">用户角色信息</h2>
            <div className="space-y-2">
              <p><strong>加载中:</strong> {userLoading ? '是' : '否'}</p>
              <p><strong>错误:</strong> {userError?.message || '无'}</p>
              <p><strong>用户信息:</strong></p>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(userInfo, null, 2)}
              </pre>
            </div>
          </div>

          {/* Stats Info */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">统计信息</h2>
            <div className="space-y-2">
              <p><strong>加载中:</strong> {statsLoading ? '是' : '否'}</p>
              <p><strong>错误:</strong> {statsError?.message || '无'}</p>
              <p><strong>统计数据:</strong></p>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(stats, null, 2)}
              </pre>
            </div>
          </div>

          {/* Raw API Test */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">API测试</h2>
            <div className="space-y-4">
              <button
                onClick={() => {
                  fetch('/api/trpc/admin.getUserRole')
                    .then(res => res.json())
                    .then(data => console.log('getUserRole:', data))
                    .catch(err => console.error('getUserRole error:', err));
                }}
                className="bg-blue-600 text-white px-4 py-2 rounded mr-4"
              >
                测试 getUserRole
              </button>
              
              <button
                onClick={() => {
                  fetch('/api/trpc/admin.getSystemStats')
                    .then(res => res.json())
                    .then(data => console.log('getSystemStats:', data))
                    .catch(err => console.error('getSystemStats error:', err));
                }}
                className="bg-green-600 text-white px-4 py-2 rounded"
              >
                测试 getSystemStats
              </button>
            </div>
            <p className="text-sm text-gray-600 mt-2">
              查看浏览器控制台获取结果
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
