"use client";

import { useState, useEffect } from "react";
import { api } from "~/trpc/react";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { AppLayout } from "~/components/app-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import {
  Users,
  Search,
  Filter,
  MoreHorizontal,
  UserCheck,
  UserX,
  Shield,
  Crown,
  Calendar,
  Activity,
  Mail,
  Phone,
  CreditCard,
  Zap,
  Eye,
  Edit,
  Trash2,
  Key,
  MessageSquare,
  FileText,
  Settings
} from "lucide-react";
import { toast } from "sonner";
import { QuotaRechargeDialog } from "~/components/admin/quota-recharge-dialog";

export default function AdminUsersPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const [page, setPage] = useState(1);
  const [search, setSearch] = useState("");
  const [selectedRole, setSelectedRole] = useState<'USER' | 'ADMIN' | 'SUPER_ADMIN' | 'ALL'>('ALL');

  // 获取用户角色
  const { data: userInfo, isLoading: userLoading } = api.admin.getUserRole.useQuery(undefined, {
    enabled: !!session?.user?.id,
  });

  // 权限检查
  useEffect(() => {
    if (status === 'loading' || userLoading) return;

    if (!session?.user) {
      router.push('/auth/signin');
      return;
    }

    if (userInfo && userInfo.role !== 'SUPER_ADMIN') {
      router.push('/dashboard');
      return;
    }
  }, [session, userInfo, status, userLoading, router]);

  // 获取用户列表
  const { data: usersData, isLoading, refetch } = api.admin.getUsers.useQuery({
    page,
    limit: 20,
    search: search || undefined,
    role: selectedRole === 'ALL' ? undefined : selectedRole,
  });

  // 获取用户统计
  const { data: userStats } = api.admin.getUserStats.useQuery();

  // 更新用户角色
  const updateUserRole = api.admin.updateUserRole.useMutation({
    onSuccess: () => {
      toast.success("用户角色更新成功");
      refetch();
    },
    onError: (error) => {
      toast.error(`更新失败: ${error.message}`);
    },
  });

  // 禁用/启用用户
  const toggleUserStatus = api.admin.toggleUserStatus.useMutation({
    onSuccess: () => {
      toast.success("用户状态更新成功");
      refetch();
    },
    onError: (error) => {
      toast.error(`更新失败: ${error.message}`);
    },
  });

  // 加载状态
  if (status === 'loading' || userLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-muted-foreground">验证权限中...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  // 权限不足
  if (!session?.user || !userInfo || (userInfo.role !== 'ADMIN' && userInfo.role !== 'SUPER_ADMIN')) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="text-6xl mb-4">🚫</div>
              <CardTitle>访问被拒绝</CardTitle>
              <CardDescription>
                您没有权限访问用户管理
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild className="w-full">
                <Link href="/admin">返回控制台</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  const handleRoleChange = (userId: string, newRole: 'USER' | 'ADMIN' | 'SUPER_ADMIN') => {
    if (confirm(`确定要将用户角色更改为 ${newRole} 吗？`)) {
      updateUserRole.mutate({ userId, role: newRole });
    }
  };

  const handleToggleStatus = (userId: string, currentStatus: boolean) => {
    const action = currentStatus ? '禁用' : '启用';
    if (confirm(`确定要${action}该用户吗？`)) {
      toggleUserStatus.mutate({ userId, isActive: !currentStatus });
    }
  };

  const handleResetPassword = (userId: string) => {
    if (confirm('确定要重置该用户的密码吗？重置后将发送新密码到用户邮箱。')) {
      // TODO: 实现重置密码功能
      toast.success("密码重置邮件已发送");
    }
  };

  const handleSendMessage = (userId: string) => {
    // TODO: 实现发送消息功能
    toast.info("消息发送功能开发中");
  };

  const handleDeleteUser = (userId: string) => {
    if (confirm('确定要删除该用户吗？此操作不可撤销！')) {
      if (confirm('请再次确认删除操作，用户的所有数据将被永久删除！')) {
        // TODO: 实现删除用户功能
        toast.success("用户删除成功");
      }
    }
  };

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'SUPER_ADMIN':
        return <Badge variant="destructive"><Crown className="w-3 h-3 mr-1" />超级管理员</Badge>;
      case 'ADMIN':
        return <Badge variant="secondary"><Shield className="w-3 h-3 mr-1" />管理员</Badge>;
      default:
        return <Badge variant="outline"><Users className="w-3 h-3 mr-1" />普通用户</Badge>;
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">用户管理</h1>
          <p className="text-muted-foreground">管理系统用户和权限</p>
        </div>

        {/* Stats Cards */}
        {userStats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总用户数</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{userStats.total}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
                <UserCheck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{userStats.active}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">管理员</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{userStats.admins}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">今日新增</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{userStats.todayNew}</div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>筛选和搜索</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索用户名、邮箱..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={selectedRole} onValueChange={(value: any) => setSelectedRole(value)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="选择角色" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">所有角色</SelectItem>
                  <SelectItem value="USER">普通用户</SelectItem>
                  <SelectItem value="ADMIN">管理员</SelectItem>
                  <SelectItem value="SUPER_ADMIN">超级管理员</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Users Table */}
        <Card>
          <CardHeader>
            <CardTitle>用户列表</CardTitle>
            <CardDescription>
              {usersData?.total ? `共 ${usersData.total} 个用户` : '加载中...'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="text-muted-foreground mt-2">加载用户数据...</p>
              </div>
            ) : usersData?.users && usersData.users.length > 0 ? (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>用户</TableHead>
                      <TableHead>角色</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>积分状态</TableHead>
                      <TableHead>注册时间</TableHead>
                      <TableHead>最后活跃</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {usersData.users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={user.image || undefined} />
                              <AvatarFallback>
                                {user.name?.charAt(0) || user.email?.charAt(0) || 'U'}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{user.name || '未设置'}</p>
                              <p className="text-sm text-muted-foreground">{user.email}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {getRoleBadge(user.role)}
                        </TableCell>
                        <TableCell>
                          <Badge variant={user.isActive ? "default" : "secondary"}>
                            {user.isActive ? "活跃" : "禁用"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center gap-2 text-xs">
                              <CreditCard className="h-3 w-3 text-blue-500" />
                              <span>总积分: {(user.credits || 0).toLocaleString()}</span>
                            </div>
                            <div className="flex items-center gap-2 text-xs">
                              <Zap className="h-3 w-3 text-orange-500" />
                              <span>已用: {(user.usedCredits || 0).toLocaleString()}</span>
                            </div>
                            <div className="flex items-center gap-2 text-xs">
                              <Crown className="h-3 w-3 text-green-500" />
                              <span>余额: {((user.credits || 0) - (user.usedCredits || 0)).toLocaleString()}</span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {user.createdAt ? new Date(user.createdAt).toLocaleDateString('zh-CN') : '未知'}
                        </TableCell>
                        <TableCell>
                          {user.lastActiveAt ?
                            new Date(user.lastActiveAt).toLocaleDateString('zh-CN') :
                            '从未活跃'
                          }
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              {/* 查看用户详情 */}
                              <DropdownMenuItem onClick={() => router.push(`/admin/users/${user.id}`)}>
                                <Eye className="mr-2 h-4 w-4" />
                                查看详情
                              </DropdownMenuItem>

                              {/* 编辑用户信息 */}
                              <DropdownMenuItem onClick={() => router.push(`/admin/users/${user.id}/edit`)}>
                                <Edit className="mr-2 h-4 w-4" />
                                编辑信息
                              </DropdownMenuItem>

                              <DropdownMenuSeparator />

                              {/* 用户状态管理 */}
                              <DropdownMenuItem onClick={() => handleToggleStatus(user.id, user.isActive)}>
                                {user.isActive ? (
                                  <>
                                    <UserX className="mr-2 h-4 w-4" />
                                    禁用用户
                                  </>
                                ) : (
                                  <>
                                    <UserCheck className="mr-2 h-4 w-4" />
                                    启用用户
                                  </>
                                )}
                              </DropdownMenuItem>

                              {/* 重置密码 */}
                              <DropdownMenuItem onClick={() => handleResetPassword(user.id)}>
                                <Key className="mr-2 h-4 w-4" />
                                重置密码
                              </DropdownMenuItem>

                              <DropdownMenuSeparator />

                              {/* 积分管理 */}
                              <QuotaRechargeDialog
                                user={user}
                                onSuccess={() => refetch()}
                                trigger={
                                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                    <CreditCard className="mr-2 h-4 w-4" />
                                    充值积分
                                  </DropdownMenuItem>
                                }
                              />

                              {/* 发送消息 */}
                              <DropdownMenuItem onClick={() => handleSendMessage(user.id)}>
                                <MessageSquare className="mr-2 h-4 w-4" />
                                发送消息
                              </DropdownMenuItem>

                              {/* 查看使用记录 */}
                              <DropdownMenuItem onClick={() => router.push(`/admin/users/${user.id}/usage`)}>
                                <FileText className="mr-2 h-4 w-4" />
                                使用记录
                              </DropdownMenuItem>

                              <DropdownMenuSeparator />

                              {/* 角色管理 */}
                              <DropdownMenuItem onClick={() => handleRoleChange(user.id, 'USER')}>
                                <Users className="mr-2 h-4 w-4" />
                                设为普通用户
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleRoleChange(user.id, 'ADMIN')}>
                                <Shield className="mr-2 h-4 w-4" />
                                设为管理员
                              </DropdownMenuItem>
                              {userInfo?.role === 'SUPER_ADMIN' && (
                                <DropdownMenuItem onClick={() => handleRoleChange(user.id, 'SUPER_ADMIN')}>
                                  <Crown className="mr-2 h-4 w-4" />
                                  设为超级管理员
                                </DropdownMenuItem>
                              )}

                              <DropdownMenuSeparator />

                              {/* 危险操作 */}
                              <DropdownMenuItem
                                onClick={() => handleDeleteUser(user.id)}
                                className="text-red-600 focus:text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                删除用户
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8">
                <Users className="mx-auto h-12 w-12 text-muted-foreground" />
                <p className="mt-2 text-muted-foreground">没有找到用户</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {usersData && usersData.total > 20 && (
          <div className="flex justify-center space-x-2">
            <Button
              variant="outline"
              onClick={() => setPage(page - 1)}
              disabled={page <= 1}
            >
              上一页
            </Button>
            <span className="flex items-center px-4">
              第 {page} 页，共 {Math.ceil(usersData.total / 20)} 页
            </span>
            <Button
              variant="outline"
              onClick={() => setPage(page + 1)}
              disabled={page >= Math.ceil(usersData.total / 20)}
            >
              下一页
            </Button>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
