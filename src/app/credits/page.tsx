"use client";

import { useState } from "react";
import { api } from "~/trpc/react";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Progress } from "~/components/ui/progress";
import { 
  Sparkles, 
  Star, 
  Loader2, 
  History, 
  CreditCard, 
  Zap,
  Crown,
  Gift,
  ArrowRight,
  Check
} from "lucide-react";
import { toast } from "sonner";
import { cn } from "~/lib/utils";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";

export default function CreditsPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
  const [isCreatingPurchase, setIsCreatingPurchase] = useState(false);

  // 获取用户积分余额
  const { data: balance } = api.credits.getBalance.useQuery();

  // 获取积分包列表
  const { data: creditPackages } = api.creditPurchase.getPackages.useQuery();

  // 创建购买订单
  const createPurchaseMutation = api.creditPurchase.createPurchase.useMutation({
    onSuccess: (data) => {
      toast.success(`订单创建成功`, {
        description: `订单号：${data.purchase.orderNumber}`,
      });
      // 跳转到支付页面
      window.location.href = `/payment/${data.payment.id}`;
    },
    onError: (error) => {
      toast.error('创建订单失败', {
        description: error.message,
      });
    },
    onSettled: () => {
      setIsCreatingPurchase(false);
      setSelectedPackage(null);
    },
  });

  const handlePurchase = async (packageId: string) => {
    if (!session) {
      toast.error('请先登录');
      router.push('/auth/signin');
      return;
    }

    setSelectedPackage(packageId);
    setIsCreatingPurchase(true);

    try {
      await createPurchaseMutation.mutateAsync({
        packageId,
        paymentProvider: 'PAYPAL', // 默认使用PayPal
      });
    } catch (error) {
      // Error handled in mutation
    }
  };

  // 计算使用百分比
  const usagePercentage = balance 
    ? Math.round((balance.used / balance.total) * 100)
    : 0;

  const remainingCredits = balance ? balance.total - balance.used : 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-12">
        {/* 页面标题 */}
        <div className="text-center space-y-6 mb-12">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-700 dark:text-blue-300 text-sm font-medium">
            <Sparkles className="w-4 h-4" />
            积分充值中心
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            升级您的语音生成体验
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            选择适合您的积分包，享受更多高质量语音生成服务
          </p>
        </div>

        {/* 当前积分状态 */}
        {session && balance && (
          <Card className="mb-8 border-2 border-blue-200 dark:border-blue-800 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="w-5 h-5 text-blue-600" />
                当前积分状态
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">可用积分</span>
                <span className="text-2xl font-bold text-blue-600">
                  {remainingCredits.toLocaleString()}
                </span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>使用进度</span>
                  <span>{usagePercentage}%</span>
                </div>
                <Progress value={usagePercentage} className="h-2" />
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">总积分：</span>
                  <span className="font-medium">{balance.total.toLocaleString()}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">已使用：</span>
                  <span className="font-medium">{balance.used.toLocaleString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 积分包列表 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {creditPackages?.map((pkg) => {
            const isPopular = pkg.name.includes('专业') || pkg.name.includes('标准');
            const isSelected = selectedPackage === pkg.id;
            const isLoading = isCreatingPurchase && isSelected;

            return (
              <Card 
                key={pkg.id} 
                className={cn(
                  "relative transition-all duration-300 hover:shadow-lg cursor-pointer",
                  isPopular && "border-2 border-blue-500 shadow-lg scale-105",
                  isSelected && "ring-2 ring-blue-500"
                )}
                onClick={() => !isLoading && handlePurchase(pkg.id)}
              >
                {isPopular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-3 py-1">
                      <Star className="w-3 h-3 mr-1" />
                      推荐
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-4">
                  <div className="flex items-center justify-center mb-2">
                    {pkg.name.includes('入门') && <Gift className="w-8 h-8 text-green-500" />}
                    {pkg.name.includes('基础') && <Zap className="w-8 h-8 text-blue-500" />}
                    {pkg.name.includes('标准') && <Star className="w-8 h-8 text-purple-500" />}
                    {pkg.name.includes('专业') && <Crown className="w-8 h-8 text-yellow-500" />}
                    {pkg.name.includes('企业') && <Sparkles className="w-8 h-8 text-red-500" />}
                  </div>
                  <CardTitle className="text-xl">{pkg.name}</CardTitle>
                  <div className="text-3xl font-bold text-blue-600">
                    ${pkg.price}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {pkg.credits.toLocaleString()} 积分
                  </p>
                </CardHeader>

                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-sm text-muted-foreground">
                      约可生成 {Math.floor(pkg.credits / 10).toLocaleString()} 次语音
                    </div>
                    <div className="text-xs text-green-600 mt-1">
                      每积分约 ${(pkg.price / pkg.credits).toFixed(4)}
                    </div>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <Check className="w-4 h-4 text-green-500" />
                      <span>支持所有语音角色</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Check className="w-4 h-4 text-green-500" />
                      <span>高质量音频输出</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Check className="w-4 h-4 text-green-500" />
                      <span>无使用时间限制</span>
                    </div>
                    {isPopular && (
                      <div className="flex items-center gap-2">
                        <Check className="w-4 h-4 text-green-500" />
                        <span className="text-blue-600 font-medium">优先客服支持</span>
                      </div>
                    )}
                  </div>

                  <Button 
                    className={cn(
                      "w-full transition-all duration-300",
                      isPopular 
                        ? "bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600" 
                        : "bg-blue-600 hover:bg-blue-700"
                    )}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        创建订单中...
                      </>
                    ) : (
                      <>
                        立即购买
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* 使用说明 */}
        <Card className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <History className="w-5 h-5" />
              积分使用说明
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-semibold text-blue-600">积分消耗规则</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• 标准质量：约 1 积分/100字符</li>
                  <li>• 高质量：约 2 积分/100字符</li>
                  <li>• 多人对话：按总字符数计算</li>
                  <li>• 积分永不过期</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-green-600">购买优势</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• 支持PayPal安全支付</li>
                  <li>• 即时到账，无需等待</li>
                  <li>• 7天无理由退款</li>
                  <li>• 24/7客服支持</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
