"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { 
  CheckCircle, 
  Sparkles, 
  ArrowRight, 
  Download,
  Share2,
  Gift
} from "lucide-react";
import { api } from "~/trpc/react";
import { toast } from "sonner";

export default function CreditSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [showConfetti, setShowConfetti] = useState(false);
  
  const paymentId = searchParams.get('payment_id');
  const orderNumber = searchParams.get('order');
  const credits = searchParams.get('credits');

  // 获取更新后的积分余额
  const { data: balance, refetch: refetchBalance } = api.credits.getBalance.useQuery();

  useEffect(() => {
    // 触发庆祝动画
    if (!showConfetti) {
      setShowConfetti(true);

      // 刷新积分余额
      refetchBalance();
    }
  }, [showConfetti, refetchBalance]);

  const handleStartUsing = () => {
    router.push('/');
  };

  const handleViewHistory = () => {
    router.push('/history');
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: 'Voctana 语音生成',
        text: '我刚刚在 Voctana 充值了积分，现在可以生成更多高质量语音了！',
        url: window.location.origin
      });
    } else {
      navigator.clipboard.writeText(window.location.origin);
      toast.success('链接已复制到剪贴板');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden">
      {/* 庆祝动画背景 */}
      {showConfetti && (
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-10 left-10 w-4 h-4 bg-blue-500 rounded-full animate-bounce opacity-70"></div>
          <div className="absolute top-20 right-20 w-3 h-3 bg-purple-500 rounded-full animate-pulse opacity-60"></div>
          <div className="absolute top-32 left-1/4 w-2 h-2 bg-green-500 rounded-full animate-ping opacity-50"></div>
          <div className="absolute top-16 right-1/3 w-3 h-3 bg-yellow-500 rounded-full animate-bounce opacity-70"></div>
          <div className="absolute top-40 left-1/2 w-2 h-2 bg-pink-500 rounded-full animate-pulse opacity-60"></div>
          <div className="absolute top-24 right-1/4 w-4 h-4 bg-indigo-500 rounded-full animate-ping opacity-50"></div>
        </div>
      )}

      <div className="container mx-auto px-4 py-12 relative z-10">
        <div className="max-w-2xl mx-auto">
          {/* 成功标题 */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full mb-6">
              <CheckCircle className="w-10 h-10 text-green-600 dark:text-green-400" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-4">
              充值成功！
            </h1>
            <p className="text-lg text-muted-foreground">
              感谢您的支持，积分已成功添加到您的账户
            </p>
          </div>

          {/* 充值详情卡片 */}
          <Card className="mb-8 border-2 border-green-200 dark:border-green-800 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="w-5 h-5 text-green-600" />
                充值详情
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {orderNumber && (
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">订单号</span>
                  <Badge variant="outline" className="font-mono">
                    {orderNumber}
                  </Badge>
                </div>
              )}
              
              {credits && (
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">获得积分</span>
                  <div className="flex items-center gap-2">
                    <span className="text-2xl font-bold text-green-600">
                      +{parseInt(credits).toLocaleString()}
                    </span>
                    <Sparkles className="w-5 h-5 text-green-500" />
                  </div>
                </div>
              )}

              {balance && (
                <div className="flex justify-between items-center pt-4 border-t">
                  <span className="text-muted-foreground">当前余额</span>
                  <span className="text-xl font-bold text-blue-600">
                    {(balance.total - balance.used).toLocaleString()} 积分
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 使用建议 */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Gift className="w-5 h-5 text-purple-600" />
                现在您可以
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/40 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-blue-600 font-bold">1</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-blue-900 dark:text-blue-100">
                      生成高质量语音
                    </h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                      使用专业模式获得更好的音质效果
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/40 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-purple-600 font-bold">2</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-purple-900 dark:text-purple-100">
                      创建多人对话
                    </h4>
                    <p className="text-sm text-purple-700 dark:text-purple-300 mt-1">
                      制作精彩的多角色语音内容
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="w-8 h-8 bg-green-100 dark:bg-green-900/40 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-green-600 font-bold">3</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-green-900 dark:text-green-100">
                      探索更多角色
                    </h4>
                    <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                      尝试不同的语音角色和风格
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                  <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900/40 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-orange-600 font-bold">4</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-orange-900 dark:text-orange-100">
                      分享您的作品
                    </h4>
                    <p className="text-sm text-orange-700 dark:text-orange-300 mt-1">
                      在社区中展示您的语音创作
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className="flex flex-col sm:flex-row gap-4">
            <Button 
              onClick={handleStartUsing}
              className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
              size="lg"
            >
              开始使用
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>

            <Button 
              onClick={handleViewHistory}
              variant="outline"
              size="lg"
              className="flex-1"
            >
              <Download className="w-4 h-4 mr-2" />
              查看历史
            </Button>

            <Button 
              onClick={handleShare}
              variant="outline"
              size="lg"
              className="sm:w-auto"
            >
              <Share2 className="w-4 h-4 mr-2" />
              分享
            </Button>
          </div>

          {/* 感谢信息 */}
          <div className="text-center mt-8 p-6 bg-muted/50 rounded-lg">
            <p className="text-muted-foreground">
              感谢您选择 Voctana！如有任何问题，请随时联系我们的客服团队。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
