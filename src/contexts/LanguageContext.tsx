"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';
import { t, type Locale, defaultLocale } from '~/lib/i18n';

interface LanguageContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  t: (key: string, params?: Record<string, string | number>) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [locale, setLocaleState] = useState<Locale>(defaultLocale);

  // Load saved language preference from localStorage
  useEffect(() => {
    const savedLocale = localStorage.getItem('language') as Locale;
    if (savedLocale && ['en', 'zh'].includes(savedLocale)) {
      setLocaleState(savedLocale);
    }
  }, []);

  // Save language preference to localStorage
  const setLocale = (newLocale: Locale) => {
    setLocaleState(newLocale);
    localStorage.setItem('language', newLocale);
  };

  // Translation function with current locale
  const translate = (key: string, params?: Record<string, string | number>) => {
    return t(key, locale, params);
  };

  const value: LanguageContextType = {
    locale,
    setLocale,
    t: translate,
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

// Hook for easy translation access
export function useTranslation() {
  const { t } = useLanguage();
  return { t };
}
