"use client";

import React, { createContext, useContext, useState, useCallback, ReactNode, useRef } from 'react';

interface AudioPlayerContextType {
  currentPlayingId: string | null;
  registerPlayer: (id: string, audioElement: HTMLAudioElement) => void;
  unregisterPlayer: (id: string) => void;
  playAudio: (id: string) => void;
  stopAllOthers: (currentId: string) => void;
}

const AudioPlayerContext = createContext<AudioPlayerContextType | undefined>(undefined);

export function AudioPlayerProvider({ children }: { children: ReactNode }) {
  const [currentPlayingId, setCurrentPlayingId] = useState<string | null>(null);
  const playersRef = useRef<Map<string, HTMLAudioElement>>(new Map());

  const registerPlayer = useCallback((id: string, audioElement: HTMLAudioElement) => {
    playersRef.current.set(id, audioElement);
  }, []);

  const unregisterPlayer = useCallback((id: string) => {
    playersRef.current.delete(id);
    if (currentPlayingId === id) {
      setCurrentPlayingId(null);
    }
  }, [currentPlayingId]);

  const stopAllOthers = useCallback((currentId: string) => {
    // 停止所有其他播放器
    playersRef.current.forEach((audioElement, id) => {
      if (id !== currentId && !audioElement.paused) {
        audioElement.pause();
      }
    });
    setCurrentPlayingId(currentId);
  }, []);

  const playAudio = useCallback((id: string) => {
    const audioElement = playersRef.current.get(id);
    if (audioElement) {
      // 先停止所有其他播放器
      stopAllOthers(id);
      // 然后播放当前音频
      audioElement.play().catch(console.error);
    }
  }, [stopAllOthers]);

  return (
    <AudioPlayerContext.Provider value={{
      currentPlayingId,
      registerPlayer,
      unregisterPlayer,
      playAudio,
      stopAllOthers
    }}>
      {children}
    </AudioPlayerContext.Provider>
  );
}

export function useAudioPlayer() {
  const context = useContext(AudioPlayerContext);
  if (context === undefined) {
    throw new Error('useAudioPlayer must be used within an AudioPlayerProvider');
  }
  return context;
}
