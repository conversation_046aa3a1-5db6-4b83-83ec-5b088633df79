'use client';

import { useState, useRef, useEffect } from 'react';
import { Play, Pause, Volume2, VolumeX } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { Card, CardContent } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Avatar, AvatarImage, AvatarFallback } from '~/components/ui/avatar';

interface VoiceDemo {
  id: string;
  audioUrl: string;
  demoText: string;
  duration?: number | null;
  voiceCharacter?: {
    id: string;
    characterName: string;
    gender: 'MALE' | 'FEMALE' | 'NEUTRAL';
    style?: string | null;
    multilingualNames?: string | null;
    avatarUrl?: string | null;
  } | null;
  language?: {
    code: string;
    name: string;
    flag: string;
  } | null;
}

interface VoiceDemoPlayerProps {
  demo: VoiceDemo;
  currentLanguage?: string;
  autoPlay?: boolean;
  showLanguageInfo?: boolean;
  showCharacterInfo?: boolean;
  compact?: boolean;
}

export function VoiceDemoPlayer({
  demo,
  currentLanguage = 'en-US',
  autoPlay = false,
  showLanguageInfo = true,
  showCharacterInfo = true,
  compact = false
}: VoiceDemoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(demo.duration || 0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const audioRef = useRef<HTMLAudioElement>(null);

  // 获取角色的本地化名称
  const getLocalizedCharacterName = () => {
    if (!demo.voiceCharacter?.multilingualNames) {
      return demo.voiceCharacter?.characterName || 'Unknown';
    }

    try {
      const names = JSON.parse(demo.voiceCharacter.multilingualNames);
      return names[currentLanguage] || names[demo.language?.code || ''] || demo.voiceCharacter.characterName;
    } catch {
      return demo.voiceCharacter.characterName || 'Unknown';
    }
  };

  // 性别图标
  const getGenderIcon = () => {
    switch (demo.voiceCharacter?.gender) {
      case 'FEMALE': return '👩';
      case 'MALE': return '👨';
      default: return '🧑';
    }
  };

  // 播放/暂停控制
  const togglePlayPause = async () => {
    if (!audioRef.current) return;

    try {
      setError(null);
      setIsLoading(true);

      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        await audioRef.current.play();
        setIsPlaying(true);
      }
    } catch (err) {
      console.error('Audio playback error:', err);
      setError('播放失败，请检查音频文件');
      setIsPlaying(false);
    } finally {
      setIsLoading(false);
    }
  };

  // 静音控制
  const toggleMute = () => {
    if (audioRef.current) {
      audioRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  // 音频事件处理
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleTimeUpdate = () => setCurrentTime(audio.currentTime);
    const handleDurationChange = () => setDuration(audio.duration);
    const handleEnded = () => setIsPlaying(false);
    const handleLoadStart = () => setIsLoading(true);
    const handleCanPlay = () => setIsLoading(false);
    const handleError = () => {
      setError('音频加载失败');
      setIsLoading(false);
      setIsPlaying(false);
    };

    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('durationchange', handleDurationChange);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('error', handleError);

    // 自动播放
    if (autoPlay) {
      togglePlayPause();
    }

    return () => {
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('durationchange', handleDurationChange);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('error', handleError);
    };
  }, [demo.audioUrl]);

  // 格式化时间
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // 进度百分比
  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  if (compact) {
    return (
      <div className="flex items-center space-x-2 p-2 border rounded-lg">
        <Button
          variant="outline"
          size="sm"
          onClick={togglePlayPause}
          disabled={isLoading}
          className="h-8 w-8 p-0"
        >
          {isLoading ? (
            <div className="animate-spin h-3 w-3 border border-gray-300 border-t-gray-600 rounded-full" />
          ) : isPlaying ? (
            <Pause className="h-3 w-3" />
          ) : (
            <Play className="h-3 w-3" />
          )}
        </Button>
        
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium truncate">
            {getGenderIcon()} {getLocalizedCharacterName()}
          </div>
          <div className="text-xs text-gray-500 truncate">
            {demo.language?.flag} {demo.language?.name}
          </div>
        </div>
        
        <div className="text-xs text-gray-400">
          {formatTime(currentTime)} / {formatTime(duration)}
        </div>
        
        <audio ref={audioRef} src={demo.audioUrl} preload="metadata" />
      </div>
    );
  }

  return (
    <Card className="w-full">
      <CardContent className="p-4">
        {/* 角色信息 */}
        {showCharacterInfo && (
          <div className="flex items-center space-x-3 mb-4">
            <Avatar className="h-12 w-12">
              <AvatarImage
                src={demo.voiceCharacter?.avatarUrl || ''}
                alt={demo.voiceCharacter?.characterName || ''}
              />
              <AvatarFallback>
                {getGenderIcon()}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1">
              <h3 className="font-semibold text-lg">
                {getLocalizedCharacterName()}
              </h3>
              <div className="flex items-center space-x-2">
                <Badge variant="secondary">
                  {demo.voiceCharacter?.gender === 'FEMALE' ? '女性' : '男性'}
                </Badge>
                {demo.voiceCharacter?.style && (
                  <Badge variant="outline">
                    {demo.voiceCharacter.style}
                  </Badge>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 语言信息 */}
        {showLanguageInfo && (
          <div className="flex items-center space-x-2 mb-3">
            <span className="text-2xl">{demo.language?.flag}</span>
            <span className="font-medium">{demo.language?.name}</span>
            <Badge variant="outline">{demo.language?.code}</Badge>
          </div>
        )}

        {/* 试听文本 */}
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <p className="text-sm text-gray-700 leading-relaxed">
            {demo.demoText}
          </p>
        </div>

        {/* 播放控制 */}
        <div className="space-y-3">
          {/* 进度条 */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>

          {/* 控制按钮 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={togglePlayPause}
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="animate-spin h-4 w-4 border border-gray-300 border-t-gray-600 rounded-full" />
                ) : isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
                <span className="ml-2">
                  {isLoading ? '加载中...' : isPlaying ? '暂停' : '播放'}
                </span>
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={toggleMute}
              >
                {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
              </Button>
            </div>

            <div className="text-sm text-gray-500">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
              {error}
            </div>
          )}
        </div>

        {/* 隐藏的音频元素 */}
        <audio ref={audioRef} src={demo.audioUrl} preload="metadata" />
      </CardContent>
    </Card>
  );
}
