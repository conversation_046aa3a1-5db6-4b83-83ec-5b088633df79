"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "~/components/ui/button";
import { Slider } from "~/components/ui/slider";
import { Badge } from "~/components/ui/badge";
import { 
  Play, 
  Pause, 
  Download, 
  Volume2, 
  VolumeX,
  RotateCcw,
  FastForward,
  Rewind,
  Clock
} from "lucide-react";
import { cn } from "~/lib/utils";

interface AudioPlayerProps {
  audioUrl: string;
  title?: string;
  subtitle?: string;
  className?: string;
  showDownload?: boolean;
  autoPlay?: boolean;
  onPlay?: () => void;
  onPause?: () => void;
  onEnded?: () => void;
}

export function AudioPlayer({
  audioUrl,
  title,
  subtitle,
  className,
  showDownload = true,
  autoPlay = false,
  onPlay,
  onPause,
  onEnded,
}: AudioPlayerProps) {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 格式化时间显示
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // 处理播放/暂停
  const togglePlayPause = async () => {
    if (!audioRef.current) return;

    try {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
        onPause?.();
      } else {
        await audioRef.current.play();
        setIsPlaying(true);
        onPlay?.();
      }
    } catch (error) {
      console.error('播放失败:', error);
      setError('播放失败，请重试');
    }
  };

  // 处理进度条变化
  const handleProgressChange = (value: number[]) => {
    if (!audioRef.current) return;
    const newTime = (value[0] / 100) * duration;
    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };

  // 处理音量变化
  const handleVolumeChange = (value: number[]) => {
    if (!audioRef.current) return;
    const newVolume = value[0] / 100;
    audioRef.current.volume = newVolume;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  };

  // 切换静音
  const toggleMute = () => {
    if (!audioRef.current) return;
    
    if (isMuted) {
      audioRef.current.volume = volume;
      setIsMuted(false);
    } else {
      audioRef.current.volume = 0;
      setIsMuted(true);
    }
  };

  // 快进/快退
  const skipTime = (seconds: number) => {
    if (!audioRef.current) return;
    audioRef.current.currentTime = Math.max(0, Math.min(duration, currentTime + seconds));
  };

  // 重置播放
  const resetAudio = () => {
    if (!audioRef.current) return;
    audioRef.current.currentTime = 0;
    setCurrentTime(0);
  };

  // 下载音频
  const downloadAudio = async () => {
    try {
      const response = await fetch(audioUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = title ? `${title}.wav` : 'audio.wav';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('下载失败:', error);
      setError('下载失败，请重试');
    }
  };

  // 音频事件处理
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
      setIsLoading(false);
      setError(null);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
      onEnded?.();
    };

    const handleError = () => {
      setIsLoading(false);
      setError('音频加载失败');
    };

    const handleLoadStart = () => {
      setIsLoading(true);
      setError(null);
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);
    audio.addEventListener('loadstart', handleLoadStart);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('loadstart', handleLoadStart);
    };
  }, [onEnded]);

  // 自动播放
  useEffect(() => {
    if (autoPlay && !isLoading && !error) {
      togglePlayPause();
    }
  }, [autoPlay, isLoading, error]);

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <div className={cn("bg-card border rounded-lg p-4 space-y-4", className)}>
      <audio ref={audioRef} src={audioUrl} preload="metadata" />
      
      {/* 标题信息 */}
      {(title || subtitle) && (
        <div className="space-y-1">
          {title && (
            <h4 className="font-medium text-sm truncate">{title}</h4>
          )}
          {subtitle && (
            <p className="text-xs text-muted-foreground truncate">{subtitle}</p>
          )}
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <div className="text-sm text-destructive bg-destructive/10 p-2 rounded">
          {error}
        </div>
      )}

      {/* 进度条 */}
      <div className="space-y-2">
        <Slider
          value={[progressPercentage]}
          onValueChange={handleProgressChange}
          max={100}
          step={0.1}
          className="w-full"
          disabled={isLoading || !!error}
        />
        
        {/* 时间显示 */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span className="flex items-center">
            <Clock className="w-3 h-3 mr-1" />
            {formatTime(currentTime)}
          </span>
          <span>{formatTime(duration)}</span>
        </div>
      </div>

      {/* 控制按钮 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {/* 快退 */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => skipTime(-10)}
            disabled={isLoading || !!error}
            className="h-8 w-8"
          >
            <Rewind className="w-4 h-4" />
          </Button>

          {/* 播放/暂停 */}
          <Button
            variant="default"
            size="icon"
            onClick={togglePlayPause}
            disabled={isLoading || !!error}
            className="h-10 w-10"
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground" />
            ) : isPlaying ? (
              <Pause className="w-5 h-5" />
            ) : (
              <Play className="w-5 h-5" />
            )}
          </Button>

          {/* 快进 */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => skipTime(10)}
            disabled={isLoading || !!error}
            className="h-8 w-8"
          >
            <FastForward className="w-4 h-4" />
          </Button>

          {/* 重置 */}
          <Button
            variant="ghost"
            size="icon"
            onClick={resetAudio}
            disabled={isLoading || !!error}
            className="h-8 w-8"
          >
            <RotateCcw className="w-4 h-4" />
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          {/* 音量控制 */}
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleMute}
              disabled={isLoading || !!error}
              className="h-8 w-8"
            >
              {isMuted || volume === 0 ? (
                <VolumeX className="w-4 h-4" />
              ) : (
                <Volume2 className="w-4 h-4" />
              )}
            </Button>
            
            <Slider
              value={[isMuted ? 0 : volume * 100]}
              onValueChange={handleVolumeChange}
              max={100}
              step={1}
              className="w-16"
              disabled={isLoading || !!error}
            />
          </div>

          {/* 下载按钮 */}
          {showDownload && (
            <Button
              variant="ghost"
              size="icon"
              onClick={downloadAudio}
              disabled={isLoading || !!error}
              className="h-8 w-8"
            >
              <Download className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>

      {/* 状态指示 */}
      {isLoading && (
        <div className="flex items-center justify-center py-2">
          <Badge variant="secondary" className="text-xs">
            加载中...
          </Badge>
        </div>
      )}
    </div>
  );
}
