"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { But<PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Textarea } from "~/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "~/components/ui/dialog";
import { ScrollArea } from "~/components/ui/scroll-area";
import { History, FileText, MessageSquare, Copy, Eye, Loader2, RefreshCw } from "lucide-react";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { formatDistanceToNow } from "date-fns";
import { zhCN } from "date-fns/locale";

interface TextGenerationHistoryProps {
  onUseText?: (text: string) => void;
}

export function TextGenerationHistory({ onUseText }: TextGenerationHistoryProps) {
  const [selectedText, setSelectedText] = useState<string>("");
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);

  // 获取文本生成历史
  const {
    data: history,
    isLoading,
    refetch,
  } = api.text.getUserHistory.useQuery({
    limit: 20,
    offset: 0,
  });

  // 获取用户统计
  const { data: stats } = api.text.getUserStats.useQuery();

  const handleCopyText = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('文本已复制到剪贴板');
  };

  const handleUseText = (text: string) => {
    if (onUseText) {
      onUseText(text);
      toast.success('文本已应用');
    }
  };

  const handlePreviewText = (text: string) => {
    setSelectedText(text);
    setShowPreviewDialog(true);
  };

  const getTypeLabel = (type: string) => {
    const typeMap = {
      SINGLE: '单个文本',
      CONVERSATION: '对话文本',
    };
    return typeMap[type as keyof typeof typeMap] || type;
  };

  const getTypeIcon = (type: string) => {
    return type === 'CONVERSATION' ? MessageSquare : FileText;
  };

  const getTypeColor = (type: string) => {
    return type === 'CONVERSATION' ? 'text-purple-600' : 'text-blue-600';
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-base">
              <History className="h-4 w-4" />
              文本生成历史
            </CardTitle>
            <CardDescription>
              查看和重用之前生成的文本内容
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* 统计信息 */}
        {stats && (
          <div className="grid grid-cols-2 gap-4 mb-4 p-3 bg-muted/30 rounded-lg">
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-600">
                {stats.monthlyStats.generationsCount}
              </div>
              <div className="text-xs text-muted-foreground">本月生成</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-green-600">
                {stats.monthlyStats.tokensUsed.toLocaleString()}
              </div>
              <div className="text-xs text-muted-foreground">本月tokens</div>
            </div>
          </div>
        )}

        {/* 历史记录列表 */}
        <ScrollArea className="h-[400px]">
          <div className="space-y-3">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : history && history.length > 0 ? (
              history.map((item) => {
                const TypeIcon = getTypeIcon(item.type);
                return (
                  <div
                    key={item.id}
                    className="p-3 border rounded-lg hover:shadow-sm transition-shadow"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <TypeIcon className={`h-4 w-4 ${getTypeColor(item.type)}`} />
                        <Badge variant="secondary" className="text-xs">
                          {getTypeLabel(item.type)}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {item.languageCode}
                        </span>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(item.createdAt), {
                          addSuffix: true,
                          locale: zhCN,
                        })}
                      </div>
                    </div>

                    {/* 提示词 */}
                    <div className="text-xs text-muted-foreground mb-2 line-clamp-1">
                      {item.prompt}
                    </div>

                    {/* 生成文本预览 */}
                    <div className="text-sm mb-3 line-clamp-2 bg-muted/20 p-2 rounded text-muted-foreground">
                      {item.generatedText}
                    </div>

                    {/* 统计信息 */}
                    <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
                      <span>{item.tokenCount} tokens</span>
                      <span>${item.cost ? Number(item.cost).toFixed(6) : '0.000000'}</span>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePreviewText(item.generatedText)}
                        className="text-xs h-7 px-2"
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        预览
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleCopyText(item.generatedText)}
                        className="text-xs h-7 px-2"
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        复制
                      </Button>
                      {onUseText && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleUseText(item.generatedText)}
                          className="text-xs h-7 px-2 text-blue-600 border-blue-200 hover:bg-blue-50"
                        >
                          使用
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="text-center py-8">
                <History className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-sm text-muted-foreground">
                  暂无文本生成历史
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  开始使用AI生成功能来创建您的第一个文本
                </p>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* 文本预览对话框 */}
        <Dialog open={showPreviewDialog} onOpenChange={setShowPreviewDialog}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>文本预览</DialogTitle>
              <DialogDescription>
                查看完整的生成文本内容
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <Textarea
                value={selectedText}
                readOnly
                className="min-h-[300px] resize-none"
              />
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => handleCopyText(selectedText)}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  复制文本
                </Button>
                {onUseText && (
                  <Button
                    onClick={() => {
                      handleUseText(selectedText);
                      setShowPreviewDialog(false);
                    }}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    使用此文本
                  </Button>
                )}
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
