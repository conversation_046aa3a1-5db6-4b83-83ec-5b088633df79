"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Play, Pause, Square, Volume2, Download, RotateCcw } from "lucide-react";
import { toast } from "~/hooks/use-toast";

interface VoicePreviewProps {
  character?: any;
  style?: any;
  parameters?: {
    speed: number;
    pitch: number;
    volume: number;
  };
  onGenerate?: (text: string, params: any) => Promise<string>;
}

export function VoicePreview({
  character,
  style,
  parameters = { speed: 1.0, pitch: 0.0, volume: 0.0 },
  onGenerate,
}: VoicePreviewProps) {
  const [previewText, setPreviewText] = useState("你好，我是你的专属语音角色。这是一段试听文本，用来展示我的语音效果。");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [generationTime, setGenerationTime] = useState<number | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  // 预设试听文本
  const presetTexts = [
    {
      name: "问候语",
      text: "你好，我是你的专属语音角色。很高兴为您服务！",
    },
    {
      name: "介绍",
      text: "这是一段试听文本，用来展示我的语音效果。我可以用不同的语调和风格来表达内容。",
    },
    {
      name: "新闻播报",
      text: "今天是一个美好的日子。科技的发展让我们的生活变得更加便利和有趣。",
    },
    {
      name: "故事叙述",
      text: "从前有一座山，山上有一座庙，庙里有一个老和尚在给小和尚讲故事。",
    },
    {
      name: "客服对话",
      text: "感谢您的来电，我很乐意为您解答问题。请问有什么可以帮助您的吗？",
    },
    {
      name: "教育内容",
      text: "学习是一个持续的过程，需要我们保持好奇心和耐心。让我们一起探索知识的海洋吧！",
    },
  ];

  // 生成语音
  const handleGenerate = async () => {
    if (!previewText.trim()) {
      toast({
        title: "请输入试听文本",
        variant: "destructive",
      });
      return;
    }

    if (!character) {
      toast({
        title: "请选择角色",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsGenerating(true);
      const startTime = Date.now();

      // 构建生成参数
      const generateParams = {
        characterId: character.baseCharacterId || character.id,
        text: previewText,
        speed: parameters.speed,
        pitch: parameters.pitch,
        volume: parameters.volume,
        stylePrompt: style?.prompt || "",
        ...parameters,
      };

      let audioUrl: string;
      
      if (onGenerate) {
        // 使用自定义生成函数
        audioUrl = await onGenerate(previewText, generateParams);
      } else {
        // 使用API生成
        audioUrl = await callVoiceAPI(generateParams);
      }

      const endTime = Date.now();
      setGenerationTime(endTime - startTime);
      setAudioUrl(audioUrl);

      toast({
        title: "语音生成成功",
        description: `耗时 ${((endTime - startTime) / 1000).toFixed(1)} 秒`,
      });

    } catch (error) {
      console.error('生成失败:', error);
      toast({
        title: "生成失败",
        description: error instanceof Error ? error.message : "请重试",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // 调用语音生成API
  const callVoiceAPI = async (params: any): Promise<string> => {
    const response = await fetch('/api/voice/preview', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: previewText,
        characterId: params.characterId,
        speed: params.speed,
        pitch: params.pitch,
        volume: params.volume,
        stylePrompt: params.stylePrompt,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Voice generation failed');
    }

    const result = await response.json();
    return result.audioUrl;
  };

  // 播放音频
  const handlePlay = () => {
    if (audioRef.current && audioUrl) {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        audioRef.current.play();
        setIsPlaying(true);
      }
    }
  };

  // 停止播放
  const handleStop = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(false);
    }
  };

  // 下载音频
  const handleDownload = async () => {
    if (audioUrl) {
      try {
        const response = await fetch(audioUrl);
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `voice-preview-${Date.now()}.mp3`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      } catch (error) {
        console.error('下载失败:', error);
        // 如果fetch失败，回退到直接链接方式
        const link = document.createElement('a');
        link.href = audioUrl;
        link.download = `voice-preview-${Date.now()}.mp3`;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    }
  };

  // 重置
  const handleReset = () => {
    setAudioUrl(null);
    setIsPlaying(false);
    setGenerationTime(null);
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Volume2 className="h-5 w-5" />
          语音试听
        </CardTitle>
        <CardDescription>
          输入文本并生成语音预览，体验角色效果
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 角色和风格信息 */}
        {(character || style) && (
          <div className="p-3 bg-muted/50 rounded-lg">
            <div className="text-sm space-y-1">
              {character && (
                <div>
                  <strong>角色:</strong> {character.name || character.characterName}
                  {parameters && (
                    <span className="ml-2 text-muted-foreground">
                      (速度: {parameters.speed}x, 音调: {parameters.pitch > 0 ? '+' : ''}{parameters.pitch}, 音量: {parameters.volume > 0 ? '+' : ''}{parameters.volume}dB)
                    </span>
                  )}
                </div>
              )}
              {style && (
                <div>
                  <strong>风格:</strong> {style.name}
                  <span className="ml-2 text-muted-foreground">"{style.prompt}"</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 预设文本选择 */}
        <div>
          <Label>快速选择试听文本</Label>
          <Select onValueChange={(value) => setPreviewText(value)}>
            <SelectTrigger>
              <SelectValue placeholder="选择预设文本" />
            </SelectTrigger>
            <SelectContent>
              {presetTexts.map((preset, index) => (
                <SelectItem key={index} value={preset.text}>
                  {preset.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 自定义文本输入 */}
        <div>
          <Label htmlFor="previewText">试听文本</Label>
          <Textarea
            id="previewText"
            value={previewText}
            onChange={(e) => setPreviewText(e.target.value)}
            placeholder="输入要试听的文本..."
            rows={3}
            maxLength={500}
          />
          <div className="text-xs text-muted-foreground mt-1">
            {previewText.length}/500 字符
          </div>
        </div>

        {/* 生成按钮 */}
        <div className="flex gap-2">
          <Button
            onClick={handleGenerate}
            disabled={isGenerating || !previewText.trim()}
            className="flex-1"
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                生成中...
              </>
            ) : (
              <>
                <Volume2 className="h-4 w-4 mr-2" />
                生成试听
              </>
            )}
          </Button>

          {audioUrl && (
            <Button
              variant="outline"
              onClick={handleReset}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              重置
            </Button>
          )}
        </div>

        {/* 音频播放控制 */}
        {audioUrl && (
          <Card className="bg-primary/5 border-primary/20">
            <CardContent className="pt-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePlay}
                    className="flex items-center gap-2"
                  >
                    {isPlaying ? (
                      <Pause className="h-4 w-4" />
                    ) : (
                      <Play className="h-4 w-4" />
                    )}
                    {isPlaying ? "暂停" : "播放"}
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleStop}
                    className="flex items-center gap-2"
                  >
                    <Square className="h-4 w-4" />
                    停止
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDownload}
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    下载
                  </Button>
                </div>

                {generationTime && (
                  <div className="text-sm text-muted-foreground">
                    生成耗时: {(generationTime / 1000).toFixed(1)}s
                  </div>
                )}
              </div>

              {/* 隐藏的音频元素 */}
              <audio
                ref={audioRef}
                src={audioUrl}
                onEnded={() => setIsPlaying(false)}
                onError={() => {
                  setIsPlaying(false);
                  toast({
                    title: "播放失败",
                    description: "音频文件可能已损坏",
                    variant: "destructive",
                  });
                }}
              />
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  );
}
