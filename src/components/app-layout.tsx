"use client";

import * as React from "react";
import { AppSidebar } from "~/components/app-sidebar";
import { ThemeToggle } from "~/components/theme-toggle";
import { LanguageSwitcher } from "~/components/LanguageSwitcher";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "~/components/ui/breadcrumb";
import { Separator } from "~/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "~/components/ui/sidebar";
import { usePathname } from "next/navigation";
import { useTranslation } from "~/contexts/LanguageContext";

// 路径映射函数
function getPathMap(t: (key: string) => string): Record<string, { title: string; parent?: string }> {
  return {
    "/": { title: t('navigation.home') },
    "/admin": { title: t('sidebar.admin') },
    "/admin/dashboard": { title: t('navigation.dashboard'), parent: "/admin" },
    "/admin/users": { title: t('sidebar.user_management'), parent: "/admin" },
    "/admin/orders": { title: t('sidebar.order_management'), parent: "/admin" },
    "/admin/languages": { title: "语言管理", parent: "/admin" },
    "/admin/language-characters": { title: "语言角色管理", parent: "/admin" },
    "/admin/characters": { title: "角色管理", parent: "/admin" },
    "/admin/styles": { title: "风格管理", parent: "/admin" },
    "/admin/templates": { title: "模板管理", parent: "/admin" },
    "/admin/api-management": { title: "接口管理", parent: "/admin" },
    "/admin/packages": { title: "套餐管理", parent: "/admin" },
    "/admin/voice-stats": { title: "语音统计", parent: "/admin" },
    "/admin/settings": { title: "系统设置", parent: "/admin" },
    "/admin/debug": { title: "调试工具", parent: "/admin" },
    "/admin/test-ai": { title: "AI 测试", parent: "/admin" },
    "/admin/voices": { title: t('sidebar.voice_management'), parent: "/admin" },
  };
}

function generateBreadcrumbs(pathname: string, t: (key: string) => string) {
  const segments = pathname.split("/").filter(Boolean);
  const breadcrumbs = [];
  const pathMap = getPathMap(t);

  // 添加首页
  if (pathname !== "/") {
    breadcrumbs.push({ title: t('navigation.home'), href: "/" });
  }
  
  // 构建路径
  let currentPath = "";
  for (const segment of segments) {
    currentPath += `/${segment}`;
    const pathInfo = pathMap[currentPath];
    if (pathInfo) {
      breadcrumbs.push({
        title: pathInfo.title,
        href: currentPath,
        isLast: currentPath === pathname,
      });
    }
  }
  
  return breadcrumbs;
}

export function AppLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const { t } = useTranslation();
  const breadcrumbs = generateBreadcrumbs(pathname, t);

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                {breadcrumbs.map((breadcrumb, index) => (
                  <React.Fragment key={breadcrumb.href}>
                    <BreadcrumbItem className="hidden md:block">
                      {breadcrumb.isLast ? (
                        <BreadcrumbPage>{breadcrumb.title}</BreadcrumbPage>
                      ) : (
                        <BreadcrumbLink href={breadcrumb.href}>
                          {breadcrumb.title}
                        </BreadcrumbLink>
                      )}
                    </BreadcrumbItem>
                    {index < breadcrumbs.length - 1 && (
                      <BreadcrumbSeparator className="hidden md:block" />
                    )}
                  </React.Fragment>
                ))}
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          <div className="ml-auto px-4 flex items-center gap-2">
            <LanguageSwitcher />
            <ThemeToggle />
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 pt-6">
          <div className="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
