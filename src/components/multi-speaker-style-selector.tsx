"use client";

import React, { useState, forwardRef, useImperativeHandle } from "react";
import { api } from "~/trpc/react";
import { useSession } from "next-auth/react";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Badge } from "~/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Search, Heart, Star, Users, Sparkles } from "lucide-react";

interface MultiSpeakerStyleSelectorProps {
  selectedStyleId?: string;
  onStyleSelect: (style: any) => void;
  onStyleClear: () => void;
}

export interface MultiSpeakerStyleSelectorRef {
  openDialog: () => void;
}

export const MultiSpeakerStyleSelector = forwardRef<MultiSpeakerStyleSelectorRef, MultiSpeakerStyleSelectorProps>(
  ({ selectedStyleId, onStyleSelect, onStyleClear }, ref) => {
    const { data: session } = useSession();
    const [isOpen, setIsOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState("");
    const [selectedCategory, setSelectedCategory] = useState<string>("all");

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      openDialog: () => setIsOpen(true),
    }));

    // API queries - 使用专门的多人对话风格API
    const { data: categories } = api.styleManagement.getMultiSpeakerCategories.useQuery();
    const { data: stylesData } = api.styleManagement.getMultiSpeakerStyles.useQuery({
      search: searchQuery,
      categoryId: selectedCategory === "all" ? undefined : selectedCategory,
      isPublic: true,
      limit: 100,
    });

    const { data: myStylesData } = api.styleManagement.getMultiSpeakerStyles.useQuery({
      search: searchQuery,
      categoryId: selectedCategory === "all" ? undefined : selectedCategory,
      userId: "current", // 当前用户的风格
      limit: 100,
    }, {
      enabled: !!session, // 只有登录时才查询
    });

    const { data: favoriteStylesData } = api.styleManagement.getFavoriteStyles.useQuery({
      limit: 100,
    }, {
      enabled: !!session, // 只有登录时才查询
    });

    // 过滤出多人对话风格
    const filteredStyles = stylesData?.styles;
    const filteredMyStyles = myStylesData?.styles;

    // 对收藏的风格进行客户端过滤
    const filteredFavoriteStyles = React.useMemo(() => {
      let styles = favoriteStylesData?.styles?.filter(style => style.type === 'MULTI_SPEAKER') || [];

      // 应用搜索过滤
      if (searchQuery) {
        styles = styles.filter(style =>
          style.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          style.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          style.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
        );
      }

      // 应用分类过滤
      if (selectedCategory !== "all") {
        styles = styles.filter(style => style.categoryId === selectedCategory);
      }

      return styles;
    }, [favoriteStylesData?.styles, searchQuery, selectedCategory]);

    // 获取当前选中的风格信息
    const selectedStyle = React.useMemo(() => {
      if (!selectedStyleId) return null;

      // 在所有风格列表中查找选中的风格
      const allStyles = [
        ...(filteredStyles || []),
        ...(filteredMyStyles || []),
        ...(filteredFavoriteStyles || [])
      ];

      return allStyles.find(style => style.id === selectedStyleId) || null;
    }, [selectedStyleId, filteredStyles, filteredMyStyles, filteredFavoriteStyles]);

    const handleStyleSelect = (style: any) => {
      onStyleSelect(style);
      setIsOpen(false);
    };

    const StyleListItem = ({ style }: { style: any }) => {
      const speakerConfig = style.speakerConfig as any;
      const speakers = speakerConfig?.speakers || [];
      
      return (
        <div
          className="cursor-pointer hover:bg-accent/50 transition-colors p-3 rounded-lg border border-transparent hover:border-primary/20"
          onClick={() => handleStyleSelect(style)}
        >
          <div className="flex items-start gap-3">
            {/* 风格图标 */}
            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-lg">
              {style.icon || '👥'}
            </div>
            
            {/* 风格信息 */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-medium text-sm truncate">{style.name}</h4>
                {style.category && (
                  <Badge variant="secondary" className="text-xs flex-shrink-0">
                    {style.category.name}
                  </Badge>
                )}
                {style.isOfficial && (
                  <Badge variant="default" className="text-xs flex-shrink-0">
                    <Sparkles className="h-3 w-3 mr-1" />
                    官方
                  </Badge>
                )}
              </div>
              
              <p className="text-xs text-muted-foreground mb-2 line-clamp-1">
                {style.description || "暂无描述"}
              </p>
              
              {/* 说话人信息 */}
              {speakers.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-2">
                  {speakers.map((speaker: any, index: number) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {speaker.name}
                    </Badge>
                  ))}
                </div>
              )}
              
              {/* 标签 */}
              {style.tags && style.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-2">
                  {style.tags.slice(0, 3).map((tag: string, index: number) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {style.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{style.tags.length - 3}
                    </Badge>
                  )}
                </div>
              )}
              
              {/* 统计信息 */}
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <span className="flex items-center gap-1">
                  <Heart className="h-3 w-3" />
                  {style.favoriteCount || 0}
                </span>
                <span className="flex items-center gap-1">
                  <Star className="h-3 w-3" />
                  {style.usageCount || 0}
                </span>
                <span className="flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  {speakers.length}人对话
                </span>
              </div>
            </div>
          </div>
        </div>
      );
    };

    return (
      <>
        {/* 当前选择的风格显示 */}
        <div className="space-y-2">
          {selectedStyleId && selectedStyle ? (
            <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm">
                  👥
                </div>
                <div className="flex flex-col">
                  <span className="text-sm font-medium">已选择对话风格</span>
                  <span className="text-xs text-muted-foreground">{selectedStyle.name}</span>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onStyleClear}
                className="text-xs h-7 px-2"
              >
                清除
              </Button>
            </div>
          ) : null}
        </div>

        {/* 风格选择对话框 */}
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
            <DialogHeader className="flex-shrink-0">
              <DialogTitle>选择多人对话风格</DialogTitle>
              <DialogDescription>
                选择适合多人对话生成的风格，控制对话的场景和角色特性
              </DialogDescription>
            </DialogHeader>

            <div className="flex-1 flex flex-col space-y-4 min-h-0">
              {/* 搜索和筛选 */}
              <div className="flex gap-4 flex-shrink-0">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索对话风格..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                      autoFocus={false}
                    />
                  </div>
                </div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="选择分类" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部分类</SelectItem>
                    {categories?.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.icon} {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 风格标签页 */}
              <Tabs defaultValue="public" className="flex-1 flex flex-col min-h-0">
                <TabsList className="grid w-full grid-cols-3 flex-shrink-0">
                  <TabsTrigger value="public">公开风格</TabsTrigger>
                  <TabsTrigger value="my">我的风格</TabsTrigger>
                  <TabsTrigger value="favorites">收藏夹</TabsTrigger>
                </TabsList>

                <div className="flex-1 min-h-0 mt-4">
                  <TabsContent value="public" className="h-full mt-0">
                    <div className="h-full overflow-y-auto">
                      {filteredStyles && filteredStyles.length > 0 ? (
                        <div className="space-y-2 pr-2">
                          {filteredStyles.map((style) => (
                            <StyleListItem key={style.id} style={style} />
                          ))}
                        </div>
                      ) : (
                        <div className="flex items-center justify-center h-full text-muted-foreground">
                          没有找到匹配的对话风格
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="my" className="h-full mt-0">
                    <div className="h-full overflow-y-auto">
                      {filteredMyStyles && filteredMyStyles.length > 0 ? (
                        <div className="space-y-2 pr-2">
                          {filteredMyStyles.map((style) => (
                            <StyleListItem key={style.id} style={style} />
                          ))}
                        </div>
                      ) : (
                        <div className="flex items-center justify-center h-full text-muted-foreground">
                          您还没有创建任何对话风格
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="favorites" className="h-full mt-0">
                    <div className="h-full overflow-y-auto">
                      {filteredFavoriteStyles && filteredFavoriteStyles.length > 0 ? (
                        <div className="space-y-2 pr-2">
                          {filteredFavoriteStyles.map((style) => (
                            <StyleListItem key={style.id} style={style} />
                          ))}
                        </div>
                      ) : (
                        <div className="flex items-center justify-center h-full text-muted-foreground">
                          您还没有收藏任何对话风格
                        </div>
                      )}
                    </div>
                  </TabsContent>
                </div>
              </Tabs>
            </div>
          </DialogContent>
        </Dialog>
      </>
    );
  }
);

MultiSpeakerStyleSelector.displayName = "MultiSpeakerStyleSelector";
