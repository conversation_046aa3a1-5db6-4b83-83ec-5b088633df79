"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, ReactNode } from "react";
import { api } from "~/trpc/react";

interface ProtectedRouteProps {
  children: ReactNode;
  requireAuth?: boolean;
  requireAdmin?: boolean;
  requireSuperAdmin?: boolean;
  fallbackUrl?: string;
  loadingComponent?: ReactNode;
  unauthorizedComponent?: ReactNode;
}

export function ProtectedRoute({
  children,
  requireAuth = true,
  requireAdmin = false,
  requireSuperAdmin = false,
  fallbackUrl = "/auth/signin",
  loadingComponent,
  unauthorizedComponent,
}: ProtectedRouteProps) {
  const { data: session, status } = useSession();
  const router = useRouter();

  // 获取用户角色信息（仅在需要权限检查时）
  const { data: userInfo, isLoading: userLoading } = api.admin.getUserRole.useQuery(
    undefined,
    {
      enabled: !!session?.user?.id && (requireAdmin || requireSuperAdmin),
      retry: false,
    }
  );

  useEffect(() => {
    // 等待session加载完成
    if (status === "loading") return;

    // 需要认证但未登录
    if (requireAuth && !session?.user) {
      const currentUrl = window.location.pathname + window.location.search;
      const signInUrl = `/auth/signin?callbackUrl=${encodeURIComponent(currentUrl)}`;
      router.push(signInUrl);
      return;
    }

    // 需要管理员权限但权限不足
    if (requireAdmin && session?.user) {
      if (userLoading) return; // 等待用户信息加载
      
      if (!userInfo || (userInfo.role !== 'ADMIN' && userInfo.role !== 'SUPER_ADMIN')) {
        router.push(fallbackUrl);
        return;
      }
    }

    // 需要超级管理员权限但权限不足
    if (requireSuperAdmin && session?.user) {
      if (userLoading) return; // 等待用户信息加载
      
      if (!userInfo || userInfo.role !== 'SUPER_ADMIN') {
        router.push(fallbackUrl);
        return;
      }
    }
  }, [
    session,
    status,
    userInfo,
    userLoading,
    requireAuth,
    requireAdmin,
    requireSuperAdmin,
    router,
    fallbackUrl,
  ]);

  // 显示加载状态
  if (status === "loading" || (requireAuth && !session)) {
    return loadingComponent || (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // 显示权限检查加载状态
  if ((requireAdmin || requireSuperAdmin) && session?.user && userLoading) {
    return loadingComponent || (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // 权限不足
  if (requireAuth && !session?.user) {
    return unauthorizedComponent || (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">需要登录</h1>
          <p className="text-muted-foreground">请先登录以访问此页面</p>
        </div>
      </div>
    );
  }

  if (requireAdmin && (!userInfo || (userInfo.role !== 'ADMIN' && userInfo.role !== 'SUPER_ADMIN'))) {
    return unauthorizedComponent || (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">权限不足</h1>
          <p className="text-muted-foreground">需要管理员权限才能访问此页面</p>
        </div>
      </div>
    );
  }

  if (requireSuperAdmin && (!userInfo || userInfo.role !== 'SUPER_ADMIN')) {
    return unauthorizedComponent || (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">权限不足</h1>
          <p className="text-muted-foreground">需要超级管理员权限才能访问此页面</p>
        </div>
      </div>
    );
  }

  // 权限验证通过，渲染子组件
  return <>{children}</>;
}
