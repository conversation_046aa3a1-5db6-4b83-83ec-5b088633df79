"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, ReactNode } from "react";
import { api } from "~/trpc/react";

interface AdminOnlyRouteProps {
  children: ReactNode;
  fallbackUrl?: string;
  loadingComponent?: ReactNode;
  unauthorizedComponent?: ReactNode;
}

export function AdminOnlyRoute({
  children,
  fallbackUrl = "/app",
  loadingComponent,
  unauthorizedComponent,
}: AdminOnlyRouteProps) {
  const { data: session, status } = useSession();
  const router = useRouter();

  // 获取用户角色信息
  const { data: userInfo, isLoading: userLoading, error } = api.admin.getUserRole.useQuery(
    undefined,
    {
      enabled: !!session?.user?.id,
      retry: false,
    }
  );

  useEffect(() => {
    // 等待session加载完成
    if (status === "loading") return;

    // 未登录用户重定向到登录页
    if (!session?.user) {
      const currentUrl = window.location.pathname + window.location.search;
      const signInUrl = `/auth/signin?callbackUrl=${encodeURIComponent(currentUrl)}`;
      router.push(signInUrl);
      return;
    }

    // 等待用户信息加载完成
    if (userLoading) return;

    // 如果查询出错或用户不是管理员，重定向到用户前端
    if (error || !userInfo || userInfo.role !== 'SUPER_ADMIN') {
      console.log('🚫 Non-admin user trying to access admin route, redirecting to user frontend');
      router.push(fallbackUrl);
      return;
    }
  }, [
    session,
    status,
    userInfo,
    userLoading,
    error,
    router,
    fallbackUrl,
  ]);

  // 显示加载状态
  if (status === "loading" || !session) {
    return loadingComponent || (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">验证登录状态...</p>
        </div>
      </div>
    );
  }

  // 显示权限检查加载状态
  if (userLoading) {
    return loadingComponent || (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">验证管理员权限...</p>
        </div>
      </div>
    );
  }

  // 权限不足或查询出错
  if (error || !userInfo || userInfo.role !== 'SUPER_ADMIN') {
    return unauthorizedComponent || (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">重定向到用户界面...</p>
        </div>
      </div>
    );
  }

  // 权限验证通过，显示管理员内容
  return <>{children}</>;
}
