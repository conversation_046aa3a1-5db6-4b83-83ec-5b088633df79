"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, ReactNode } from "react";
import { api } from "~/trpc/react";

interface RouteGuardProps {
  children: ReactNode;
  requireAuth?: boolean;
  requireAdmin?: boolean;
  requireSuperAdmin?: boolean;
  redirectTo?: string;
}

export function RouteGuard({
  children,
  requireAuth = true,
  requireAdmin = false,
  requireSuperAdmin = false,
  redirectTo = "/auth/signin",
}: RouteGuardProps) {
  const { data: session, status } = useSession();
  const router = useRouter();

  // 获取用户角色信息（仅在需要权限检查时）
  const { data: userInfo, isLoading: userLoading } = api.admin.getUserRole.useQuery(
    undefined,
    {
      enabled: !!session?.user?.id && (requireAdmin || requireSuperAdmin),
      retry: false,
    }
  );

  useEffect(() => {
    // 等待session加载完成
    if (status === "loading") return;

    // 需要认证但未登录 - 重定向到登录页
    if (requireAuth && !session?.user) {
      const currentUrl = window.location.pathname + window.location.search;
      const signInUrl = `${redirectTo}?callbackUrl=${encodeURIComponent(currentUrl)}`;
      router.push(signInUrl);
      return;
    }

    // 需要管理员权限但权限不足
    if (requireAdmin && session?.user) {
      if (userLoading) return; // 等待用户信息加载
      
      if (!userInfo || (userInfo.role !== 'ADMIN' && userInfo.role !== 'SUPER_ADMIN')) {
        router.push('/');
        return;
      }
    }

    // 需要超级管理员权限但权限不足
    if (requireSuperAdmin && session?.user) {
      if (userLoading) return; // 等待用户信息加载
      
      if (!userInfo || userInfo.role !== 'SUPER_ADMIN') {
        router.push('/');
        return;
      }
    }
  }, [
    session,
    status,
    userInfo,
    userLoading,
    requireAuth,
    requireAdmin,
    requireSuperAdmin,
    router,
    redirectTo,
  ]);

  // 显示加载状态
  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // 需要认证但未登录 - 显示加载状态（重定向进行中）
  if (requireAuth && !session?.user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  // 显示权限检查加载状态
  if ((requireAdmin || requireSuperAdmin) && session?.user && userLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Checking permissions...</p>
        </div>
      </div>
    );
  }

  // 权限不足 - 显示加载状态（重定向进行中）
  if (requireAdmin && session?.user && (!userInfo || (userInfo.role !== 'ADMIN' && userInfo.role !== 'SUPER_ADMIN'))) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Access denied. Redirecting...</p>
        </div>
      </div>
    );
  }

  if (requireSuperAdmin && session?.user && (!userInfo || userInfo.role !== 'SUPER_ADMIN')) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Access denied. Redirecting...</p>
        </div>
      </div>
    );
  }

  // 权限验证通过，渲染子组件
  return <>{children}</>;
}
