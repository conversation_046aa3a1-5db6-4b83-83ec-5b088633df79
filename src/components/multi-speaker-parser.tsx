"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Badge } from "~/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Users, Play, Settings, Trash2, Plus, Wand2 } from "lucide-react";
import { toast } from "~/hooks/use-toast";

interface Speaker {
  id: string;
  name: string;
  characterId?: string;
  styleId?: string;
  color: string;
  avatar?: string;
}

interface DialogueLine {
  speaker: string;
  text: string;
  lineNumber: number;
}

interface MultiSpeakerParserProps {
  onParsedDialogue: (dialogue: DialogueLine[], speakers: Speaker[]) => void;
  availableCharacters?: any[];
  availableStyles?: any[];
}

export function MultiSpeakerParser({
  onParsedDialogue,
  availableCharacters = [],
  availableStyles = [],
}: MultiSpeakerParserProps) {
  const [inputText, setInputText] = useState("");
  const [parsedDialogue, setParsedDialogue] = useState<DialogueLine[]>([]);
  const [speakers, setSpeakers] = useState<Speaker[]>([]);
  const [isAutoDetecting, setIsAutoDetecting] = useState(false);

  // 预设颜色
  const speakerColors = [
    "#3B82F6", "#EF4444", "#10B981", "#F59E0B",
    "#8B5CF6", "#EC4899", "#06B6D4", "#84CC16"
  ];

  // 示例对话文本
  const exampleTexts = [
    {
      name: "客服对话",
      text: `客服：您好，欢迎致电客服热线，请问有什么可以帮助您的吗？
用户：你好，我想咨询一下产品的使用方法。
客服：好的，请问您购买的是哪款产品呢？
用户：是智能音箱，型号是XYZ-100。
客服：好的，我来为您详细介绍一下使用方法。`,
    },
    {
      name: "教学对话",
      text: `老师：今天我们来学习关于人工智能的基础知识。
学生A：老师，什么是人工智能？
老师：人工智能是让机器能够模拟人类智能的技术。
学生B：那它有哪些应用呢？
老师：比如语音识别、图像识别、自动驾驶等等。`,
    },
    {
      name: "故事对话",
      text: `旁白：在一个美丽的森林里，住着一只聪明的小兔子。
小兔：今天天气真好，我要去采蘑菇。
大灰狼：小兔子，你要去哪里呀？
小兔：我要去森林深处采蘑菇，你要一起来吗？
大灰狼：好啊，我们一起去吧！`,
    },
  ];

  // 解析对话文本
  const parseDialogue = (text: string): { dialogue: DialogueLine[], detectedSpeakers: string[] } => {
    const lines = text.split('\n').filter(line => line.trim());
    const dialogue: DialogueLine[] = [];
    const detectedSpeakers = new Set<string>();

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      if (!trimmedLine) return;

      // 匹配格式：说话者：内容 或 说话者: 内容
      const match = trimmedLine.match(/^([^：:]+)[：:](.+)$/);
      
      if (match) {
        const speaker = match[1]!.trim();
        const text = match[2]!.trim();
        
        dialogue.push({
          speaker,
          text,
          lineNumber: index + 1,
        });
        
        detectedSpeakers.add(speaker);
      } else {
        // 如果没有匹配到说话者格式，可能是旁白或描述
        dialogue.push({
          speaker: "旁白",
          text: trimmedLine,
          lineNumber: index + 1,
        });
        detectedSpeakers.add("旁白");
      }
    });

    return { dialogue, detectedSpeakers: Array.from(detectedSpeakers) };
  };

  // 自动检测并解析
  const handleAutoDetect = () => {
    if (!inputText.trim()) {
      toast({
        title: "请输入对话文本",
        variant: "destructive",
      });
      return;
    }

    setIsAutoDetecting(true);
    
    // 模拟解析延迟
    setTimeout(() => {
      const { dialogue, detectedSpeakers } = parseDialogue(inputText);
      
      // 为每个说话者分配颜色和ID
      const newSpeakers: Speaker[] = detectedSpeakers.map((name, index) => ({
        id: `speaker_${index}`,
        name,
        color: speakerColors[index % speakerColors.length]!,
      }));

      setParsedDialogue(dialogue);
      setSpeakers(newSpeakers);
      setIsAutoDetecting(false);

      toast({
        title: "解析完成",
        description: `检测到 ${detectedSpeakers.length} 个说话者，${dialogue.length} 行对话`,
      });
    }, 1000);
  };

  // 更新说话者配置
  const updateSpeaker = (speakerId: string, field: keyof Speaker, value: any) => {
    setSpeakers(prev => prev.map(speaker => 
      speaker.id === speakerId 
        ? { ...speaker, [field]: value }
        : speaker
    ));
  };

  // 删除说话者
  const removeSpeaker = (speakerId: string) => {
    setSpeakers(prev => prev.filter(speaker => speaker.id !== speakerId));
    setParsedDialogue(prev => prev.filter(line => {
      const speaker = speakers.find(s => s.name === line.speaker);
      return speaker?.id !== speakerId;
    }));
  };

  // 添加说话者
  const addSpeaker = () => {
    const newSpeaker: Speaker = {
      id: `speaker_${Date.now()}`,
      name: `说话者${speakers.length + 1}`,
      color: speakerColors[speakers.length % speakerColors.length]!,
    };
    setSpeakers(prev => [...prev, newSpeaker]);
  };

  // 应用解析结果
  const handleApply = () => {
    if (parsedDialogue.length === 0) {
      toast({
        title: "请先解析对话文本",
        variant: "destructive",
      });
      return;
    }

    onParsedDialogue(parsedDialogue, speakers);
    toast({
      title: "应用成功",
      description: "多人对话配置已应用",
    });
  };

  // 使用示例文本
  const useExampleText = (text: string) => {
    setInputText(text);
    setParsedDialogue([]);
    setSpeakers([]);
  };

  return (
    <div className="space-y-6">
      {/* 输入区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            多人对话文本
          </CardTitle>
          <CardDescription>
            输入多人对话文本，系统将自动识别说话者并分配角色
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 示例文本选择 */}
          <div>
            <label className="text-sm font-medium mb-2 block">快速使用示例</label>
            <div className="flex gap-2 flex-wrap">
              {exampleTexts.map((example, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => useExampleText(example.text)}
                >
                  {example.name}
                </Button>
              ))}
            </div>
          </div>

          {/* 文本输入 */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              对话文本 (格式: 说话者：内容)
            </label>
            <Textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder={`请输入多人对话文本，格式如下：
客服：您好，有什么可以帮助您的吗？
用户：我想咨询产品信息。
客服：好的，请问您需要了解哪款产品？`}
              rows={8}
              className="font-mono text-sm"
            />
            <div className="text-xs text-muted-foreground mt-1">
              支持使用冒号(:)或中文冒号(：)分隔说话者和内容
            </div>
          </div>

          {/* 解析按钮 */}
          <Button
            onClick={handleAutoDetect}
            disabled={isAutoDetecting || !inputText.trim()}
            className="w-full"
          >
            {isAutoDetecting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                解析中...
              </>
            ) : (
              <>
                <Wand2 className="h-4 w-4 mr-2" />
                自动解析对话
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* 解析结果 */}
      {parsedDialogue.length > 0 && (
        <>
          {/* 说话者配置 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>说话者配置</CardTitle>
                  <CardDescription>
                    为每个说话者分配角色和风格
                  </CardDescription>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={addSpeaker}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  添加说话者
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {speakers.map((speaker) => (
                  <div key={speaker.id} className="flex items-center gap-4 p-4 border rounded-lg">
                    <Avatar className="h-10 w-10" style={{ backgroundColor: speaker.color }}>
                      <AvatarImage src={speaker.avatar} />
                      <AvatarFallback className="text-white">
                        {speaker.name[0]}
                      </AvatarFallback>
                    </Avatar>

                    <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="text-sm font-medium">说话者名称</label>
                        <input
                          type="text"
                          value={speaker.name}
                          onChange={(e) => updateSpeaker(speaker.id, 'name', e.target.value)}
                          className="w-full mt-1 px-3 py-2 border rounded-md text-sm"
                        />
                      </div>

                      <div>
                        <label className="text-sm font-medium">角色</label>
                        <Select
                          value={speaker.characterId || ""}
                          onValueChange={(value) => updateSpeaker(speaker.id, 'characterId', value)}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder="选择角色" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="default">默认角色</SelectItem>
                            {availableCharacters.map((char) => (
                              <SelectItem key={char.id} value={char.id}>
                                {char.name || char.characterName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="text-sm font-medium">风格</label>
                        <Select
                          value={speaker.styleId || ""}
                          onValueChange={(value) => updateSpeaker(speaker.id, 'styleId', value)}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder="选择风格" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="default">默认风格</SelectItem>
                            {availableStyles.map((style) => (
                              <SelectItem key={style.id} value={style.id}>
                                {style.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeSpeaker(speaker.id)}
                      className="text-red-500 hover:text-red-600"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 对话预览 */}
          <Card>
            <CardHeader>
              <CardTitle>对话预览</CardTitle>
              <CardDescription>
                预览解析后的对话内容和说话者分配
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {parsedDialogue.map((line, index) => {
                  const speaker = speakers.find(s => s.name === line.speaker);
                  return (
                    <div key={index} className="flex items-start gap-3 p-3 bg-muted/50 rounded-lg">
                      <Avatar className="h-8 w-8 flex-shrink-0" style={{ backgroundColor: speaker?.color || '#6B7280' }}>
                        <AvatarImage src={speaker?.avatar} />
                        <AvatarFallback className="text-white text-xs">
                          {line.speaker[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="outline" style={{ borderColor: speaker?.color }}>
                            {line.speaker}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            第 {line.lineNumber} 行
                          </span>
                        </div>
                        <p className="text-sm">{line.text}</p>
                      </div>
                    </div>
                  );
                })}
              </div>

              <div className="mt-4 pt-4 border-t">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    共 {parsedDialogue.length} 行对话，{speakers.length} 个说话者
                  </div>
                  <Button onClick={handleApply}>
                    <Play className="h-4 w-4 mr-2" />
                    应用配置
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
