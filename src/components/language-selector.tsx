"use client";

import React from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Badge } from "~/components/ui/badge";
import { Globe } from "lucide-react";
import { api } from "~/trpc/react";

interface LanguageSelectorProps {
  selectedLanguageCode?: string;
  onLanguageSelect: (languageCode: string) => void;
  className?: string;
}

export function LanguageSelector({
  selectedLanguageCode = 'en-US',
  onLanguageSelect,
  className = "",
}: LanguageSelectorProps) {
  // 获取支持的语言列表
  const { data: languages, isLoading } = api.voice.getLanguages.useQuery();

  const selectedLanguage = languages?.find(lang => lang.code === selectedLanguageCode);

  if (isLoading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Globe className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm text-muted-foreground">加载语言...</span>
      </div>
    );
  }

  return (
    <div className={className}>
      <Select value={selectedLanguageCode} onValueChange={onLanguageSelect}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder="选择语言">
            {selectedLanguage ? (
              <div className="flex items-center gap-2">
                <span className="text-sm">{selectedLanguage.flag}</span>
                <span className="text-sm">{selectedLanguage.name}</span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                <span>选择语言</span>
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent className="max-h-60">
          {languages?.map((language) => (
            <SelectItem key={language.code} value={language.code}>
              <div className="flex items-center gap-2 w-full">
                <span className="text-lg">{language.flag}</span>
                <div className="flex-1">
                  <div className="font-medium">{language.name}</div>
                  <div className="text-xs text-muted-foreground">
                    {language.nativeName}
                  </div>
                </div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
