"use client";

import React, { useState, useImperativeHandle, forwardRef } from "react";
import { api } from "~/trpc/react";
import { useSession } from "next-auth/react";
import { Button } from "~/components/ui/button";

import { Badge } from "~/components/ui/badge";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Search, Heart, Star, Sparkles, Users, User } from "lucide-react";

interface StyleSelectorProps {
  selectedStyleId?: string;
  onStyleSelect: (style: any) => void;
  onStyleClear: () => void;
  filterMultiSpeaker?: boolean; // 新增：是否过滤掉多人风格，只显示单人风格
  onlyMultiSpeaker?: boolean; // 新增：是否只显示多人风格
}

export interface StyleSelectorRef {
  openDialog: () => void;
}

export const StyleSelector = forwardRef<StyleSelectorRef, StyleSelectorProps>(
  ({ selectedStyleId, onStyleSelect, onStyleClear, filterMultiSpeaker = false, onlyMultiSpeaker = false }, ref) => {
    const { data: session } = useSession();
    const [isOpen, setIsOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState("");
    const [selectedCategory, setSelectedCategory] = useState<string>("all");

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      openDialog: () => setIsOpen(true),
    }));

  // API queries
  const { data: categories } = api.styleManagement.getCategories.useQuery();
  const { data: stylesData } = api.styleManagement.getStyles.useQuery({
    search: searchQuery,
    categoryId: selectedCategory === "all" ? undefined : selectedCategory,
    isPublic: true,
    limit: 100, // 增加限制以显示所有风格
  });
  const { data: myStylesData } = api.styleManagement.getMyStyles.useQuery({}, {
    enabled: !!session, // 只有登录时才查询
  });
  const { data: favoriteStylesData } = api.styleManagement.getFavoriteStyles.useQuery({}, {
    enabled: !!session, // 只有登录时才查询
  });

  // 过滤风格数据
  const filteredStyles = onlyMultiSpeaker
    ? stylesData?.styles?.filter(style => style.type === 'MULTI_SPEAKER')
    : filterMultiSpeaker
    ? stylesData?.styles?.filter(style => style.type !== 'MULTI_SPEAKER')
    : stylesData?.styles;
  const filteredMyStyles = onlyMultiSpeaker
    ? myStylesData?.styles?.filter(style => style.type === 'MULTI_SPEAKER')
    : filterMultiSpeaker
    ? myStylesData?.styles?.filter(style => style.type !== 'MULTI_SPEAKER')
    : myStylesData?.styles;
  const filteredFavoriteStyles = onlyMultiSpeaker
    ? favoriteStylesData?.styles?.filter(style => style.type === 'MULTI_SPEAKER')
    : filterMultiSpeaker
    ? favoriteStylesData?.styles?.filter(style => style.type !== 'MULTI_SPEAKER')
    : favoriteStylesData?.styles;

  // Get selected style details
  const { data: selectedStyle } = api.styleManagement.getStyleById.useQuery(
    { id: selectedStyleId! },
    { enabled: !!selectedStyleId }
  );

  const handleStyleSelect = (style: any) => {
    onStyleSelect(style);
    setIsOpen(false);
  };

  const StyleListItem = ({ style }: { style: any }) => (
    <div
      className="cursor-pointer hover:bg-accent/50 transition-colors p-3 rounded-lg border border-transparent hover:border-primary/20"
      onClick={() => handleStyleSelect(style)}
    >
      <div className="flex items-start gap-3">
        {/* 风格图标 */}
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-lg">
          {style.icon || '🎭'}
        </div>

        {/* 风格信息 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h4 className="font-medium text-sm truncate">{style.name}</h4>
            {style.category && (
              <Badge variant="secondary" className="text-xs flex-shrink-0">
                {style.category.name}
              </Badge>
            )}
          </div>

          <p className="text-xs text-muted-foreground mb-2 line-clamp-1">
            {style.description || "暂无描述"}
          </p>

          {/* 标签 */}
          {style.tags && style.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-2">
              {style.tags.slice(0, 4).map((tag: string, index: number) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {style.tags.length > 4 && (
                <Badge variant="outline" className="text-xs">
                  +{style.tags.length - 4}
                </Badge>
              )}
            </div>
          )}

          {/* 统计信息 */}
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            <span className="flex items-center gap-1">
              <Heart className="h-3 w-3" />
              {style.favoriteCount || 0}
            </span>
            <span className="flex items-center gap-1">
              <Star className="h-3 w-3" />
              {style.usageCount || 0}
            </span>
            <span className="flex items-center gap-1">
              {style.type === 'MULTI_SPEAKER' ? (
                <>
                  <Users className="h-3 w-3" />
                  多人
                </>
              ) : (
                <>
                  <User className="h-3 w-3" />
                  单人
                </>
              )}
            </span>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-3">
      {/* Selected Style Display - 只在选择了风格时显示 */}
      {selectedStyle && (
        <div className="flex items-center justify-between p-3 bg-primary/5 border border-primary/20 rounded-lg">
          <div className="flex items-center gap-2">
            <Sparkles className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium">已选风格: {selectedStyle.name}</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onStyleClear}
            className="text-xs h-6 px-2"
          >
            清除
          </Button>
        </div>
      )}

      {/* Style Selection Dialog */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>选择语音风格</DialogTitle>
            <DialogDescription>
              选择一个风格来改变语音的表达方式和情感
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 flex flex-col space-y-4 min-h-0">
            {/* Search and Filter */}
            <div className="flex gap-4 flex-shrink-0">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索风格..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                    autoFocus={false}
                  />
                </div>
              </div>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="选择分类" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部分类</SelectItem>
                  {categories?.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Style Tabs */}
            <Tabs defaultValue="public" className="flex-1 flex flex-col min-h-0">
              <TabsList className="grid w-full grid-cols-3 flex-shrink-0">
                <TabsTrigger value="public">公开风格</TabsTrigger>
                <TabsTrigger value="my">我的风格</TabsTrigger>
                <TabsTrigger value="favorites">收藏夹</TabsTrigger>
              </TabsList>

              <div className="flex-1 min-h-0 mt-4">
                <TabsContent value="public" className="h-full mt-0">
                  <div className="h-full overflow-y-auto">
                    {filteredStyles && filteredStyles.length > 0 ? (
                      <div className="space-y-2 pr-2">
                        {filteredStyles.map((style) => (
                          <StyleListItem key={style.id} style={style} />
                        ))}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-full text-muted-foreground">
                        没有找到匹配的风格
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="my" className="h-full mt-0">
                  <div className="h-full overflow-y-auto">
                    {filteredMyStyles && filteredMyStyles.length > 0 ? (
                      <div className="space-y-2 pr-2">
                        {filteredMyStyles.map((style) => (
                          <StyleListItem key={style.id} style={style} />
                        ))}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-full text-muted-foreground">
                        您还没有创建任何风格
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="favorites" className="h-full mt-0">
                  <div className="h-full overflow-y-auto">
                    {filteredFavoriteStyles && filteredFavoriteStyles.length > 0 ? (
                      <div className="space-y-2 pr-2">
                        {filteredFavoriteStyles.map((style) => (
                          <StyleListItem key={style.id} style={style} />
                        ))}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-full text-muted-foreground">
                        您还没有收藏任何风格
                      </div>
                    )}
                  </div>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
});

StyleSelector.displayName = "StyleSelector";
