"use client";

import { useSession } from "next-auth/react";
import { useGuestTrial } from "~/hooks/useGuestTrial";
import { Card, CardContent } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Progress } from "~/components/ui/progress";
import { Badge } from "~/components/ui/badge";
import { Gift, Zap, Users, Crown } from "lucide-react";
import Link from "next/link";

export function GuestTrialBanner() {
  const { data: session } = useSession();
  const { trialData, getRemainingQuota, getLimits, isExhausted, isLoaded } = useGuestTrial();

  // 如果用户已登录，不显示访客试用横幅
  if (session || !isLoaded) {
    return null;
  }

  const remaining = getRemainingQuota();
  const limits = getLimits();
  const usagePercentage = (trialData.totalCharacters / limits.maxTotalCharacters) * 100;

  return (
    <Card className="border-amber-200 dark:border-amber-800 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-950/50 dark:to-orange-950/50">
      <CardContent className="p-4">
        <div className="flex items-start justify-between gap-4">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-full bg-gradient-to-r from-amber-500 to-orange-500 flex items-center justify-center">
              <Gift className="h-5 w-5 text-white" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-amber-900 dark:text-amber-100">免费试用体验</h3>
                <Badge variant="secondary" className="bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-200">
                  访客模式
                </Badge>
              </div>
              <p className="text-sm text-amber-700 dark:text-amber-300 mb-2">
                体验 AI 语音生成功能，注册后解锁更多特权！
              </p>
              
              {/* 使用情况 */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-amber-600 dark:text-amber-400">总字符使用量</span>
                  <span className="font-medium text-amber-800 dark:text-amber-200">
                    {trialData.totalCharacters} / {limits.maxTotalCharacters}
                  </span>
                </div>
                <Progress
                  value={usagePercentage}
                  className="h-2 bg-amber-100 dark:bg-amber-900"
                />

                <div className="grid grid-cols-2 gap-4 text-xs">
                  <div className="flex items-center gap-1">
                    <Zap className="h-3 w-3 text-amber-600 dark:text-amber-400" />
                    <span className="text-amber-600 dark:text-amber-400">今日剩余:</span>
                    <span className="font-medium text-amber-800 dark:text-amber-200">
                      {remaining.dailyGenerations} 次
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3 text-amber-600 dark:text-amber-400" />
                    <span className="text-amber-600 dark:text-amber-400">单次限制:</span>
                    <span className="font-medium text-amber-800 dark:text-amber-200">
                      {limits.maxCharactersPerGeneration} 字符
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            {isExhausted() ? (
              <Button asChild size="sm" className="bg-gradient-to-r from-blue-600 to-indigo-600">
                <Link href="/auth/signup">
                  <Crown className="mr-1 h-3 w-3" />
                  立即注册
                </Link>
              </Button>
            ) : (
              <Button asChild variant="outline" size="sm">
                <Link href="/auth/signup">
                  <Crown className="mr-1 h-3 w-3" />
                  升级账户
                </Link>
              </Button>
            )}
          </div>
        </div>

        {/* 额度用尽提示 */}
        {isExhausted() && (
          <div className="mt-3 p-3 bg-amber-100 rounded-lg border border-amber-200">
            <div className="flex items-center gap-2">
              <Gift className="h-4 w-4 text-amber-600" />
              <span className="text-sm font-medium text-amber-800">
                试用额度已用完
              </span>
            </div>
            <p className="text-xs text-amber-700 mt-1">
              注册免费账户即可获得 1000 积分，享受更多语音生成服务！
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export function GuestTrialLimitDialog({ 
  isOpen, 
  onClose, 
  limitType 
}: { 
  isOpen: boolean; 
  onClose: () => void; 
  limitType: "daily" | "characters" | "total";
}) {
  const limits = {
    daily: {
      title: "每日生成次数已达上限",
      description: "访客每天最多可以生成 3 次语音，明天可以继续使用。",
      icon: Zap,
    },
    characters: {
      title: "单次字符数超出限制",
      description: "访客单次最多可以生成 100 字符的语音内容。",
      icon: Users,
    },
    total: {
      title: "总字符数已达上限",
      description: "访客总共可以生成 300 字符的语音内容。",
      icon: Gift,
    },
  };

  const limit = limits[limitType];
  const Icon = limit.icon;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-md mx-4">
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <div className="h-12 w-12 rounded-full bg-gradient-to-r from-amber-500 to-orange-500 flex items-center justify-center mx-auto">
              <Icon className="h-6 w-6 text-white" />
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {limit.title}
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                {limit.description}
              </p>
            </div>

            <div className="space-y-2">
              <Button asChild className="w-full bg-gradient-to-r from-blue-600 to-indigo-600">
                <Link href="/auth/signup">
                  <Crown className="mr-2 h-4 w-4" />
                  注册免费账户
                </Link>
              </Button>
              <Button variant="outline" onClick={onClose} className="w-full">
                继续浏览
              </Button>
            </div>

            <div className="text-xs text-gray-500">
              注册后即可获得 1000 积分，享受更多功能！
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
