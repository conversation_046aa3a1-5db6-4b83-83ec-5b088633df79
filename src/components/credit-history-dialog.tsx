"use client";

import { useState } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "~/components/ui/popover";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { History, Gift, ShoppingCart, Clock, X } from "lucide-react";
import { api } from "~/trpc/react";

interface CreditHistoryDialogProps {
  children: React.ReactNode;
}

export function CreditHistoryDialog({ children }: CreditHistoryDialogProps) {
  const [open, setOpen] = useState(false);

  // 获取使用历史
  const { data: usageHistory } = api.credits.getUsageHistory.useQuery({
    limit: 10,
    offset: 0,
  });

  // 获取赠送历史
  const { data: giftHistory } = api.credits.getGiftHistory.useQuery({
    limit: 10,
    offset: 0,
    type: 'received',
  });

  // 获取购买历史
  const { data: purchaseHistory } = api.creditPurchase.getPurchaseHistory.useQuery({
    limit: 10,
    offset: 0,
  });

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        {children}
      </PopoverTrigger>
      <PopoverContent className="w-[600px] max-h-[500px] overflow-y-auto p-0" side="bottom" align="start">
        <div className="p-4 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <History className="h-5 w-5" />
              <h3 className="font-semibold">积分记录</h3>
            </div>
            <Button variant="ghost" size="sm" onClick={() => setOpen(false)}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="p-4">
          <Tabs defaultValue="usage" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-4">
              <TabsTrigger value="usage">使用记录</TabsTrigger>
              <TabsTrigger value="gifts">赠送记录</TabsTrigger>
              <TabsTrigger value="purchases">购买记录</TabsTrigger>
            </TabsList>
          
            <TabsContent value="usage" className="space-y-3">
              {usageHistory?.records.length ? (
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {usageHistory.records.map((record: any) => (
                    <div key={record.id} className="p-3 bg-muted/50 rounded-lg">
                      <div className="font-medium text-sm">{record.description}</div>
                      <div className="flex justify-between items-center mt-1">
                        <div className="text-xs text-muted-foreground">
                          {new Date(record.createdAt).toLocaleDateString()}
                        </div>
                        <div className="font-bold text-red-500 text-sm">-{record.amount}</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <History className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <div className="text-sm text-muted-foreground">暂无使用记录</div>
                </div>
              )}
            </TabsContent>
          
            <TabsContent value="gifts" className="space-y-3">
              {giftHistory?.records.length ? (
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {giftHistory.records.map((record: any) => (
                    <div key={record.id} className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                      <div className="space-y-1">
                        <div className="font-medium text-sm">{record.reason}</div>
                        <div className="text-xs text-muted-foreground">
                          来自：{record.fromUser?.name || '系统'} • {new Date(record.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-green-600 text-sm">+{record.amount}</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Gift className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <div className="text-sm text-muted-foreground">暂无赠送记录</div>
                </div>
              )}
            </TabsContent>
          
            <TabsContent value="purchases" className="space-y-3">
              {purchaseHistory?.purchases.length ? (
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {purchaseHistory.purchases.map((purchase: any) => (
                    <div key={purchase.id} className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                      <div className="space-y-1">
                        <div className="font-medium text-sm">{purchase.packageName}</div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(purchase.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="text-right space-y-1">
                        <div className="font-bold text-primary text-sm">{purchase.credits} 积分</div>
                        <Badge
                          variant="outline"
                          className={
                            purchase.status === 'COMPLETED' ? 'text-green-600 border-green-200' :
                            purchase.status === 'PENDING' ? 'text-yellow-600 border-yellow-200' :
                            'text-gray-600 border-gray-200'
                          }
                        >
                          {purchase.status === 'COMPLETED' ? '已完成' :
                           purchase.status === 'PENDING' ? '待支付' :
                           purchase.status === 'CANCELLED' ? '已取消' : '失败'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <ShoppingCart className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <div className="text-sm text-muted-foreground">暂无购买记录</div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </PopoverContent>
    </Popover>
  );
}
