"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { Sparkles, RefreshCw, Download, History, Wand2 } from "lucide-react";
import { toast } from "sonner";

interface Character {
  id: string;
  characterName: string;
  characterNameEn: string;
  gender: 'MALE' | 'FEMALE' | 'NEUTRAL';
  style?: string;
  personality?: string;
  description?: string;
  avatarUrl?: string;
}

interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
}

interface AIAvatarGeneratorProps {
  character: Character;
  languages?: Language[];
  onAvatarGenerated?: (avatarUrl: string) => void;
  trigger?: React.ReactNode;
}

interface GenerationHistory {
  id: string;
  avatarUrl: string;
  languageCode?: string;
  createdAt: string;
  generatedByUser: {
    name?: string;
    email?: string;
  };
}

export function AIAvatarGenerator({
  character,
  languages = [],
  onAvatarGenerated,
  trigger
}: AIAvatarGeneratorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState<string>("general");
  const [culturalContext, setCulturalContext] = useState("");
  const [customPrompt, setCustomPrompt] = useState("");
  const [generatedAvatar, setGeneratedAvatar] = useState<string | null>(null);
  const [history, setHistory] = useState<GenerationHistory[]>([]);
  const [showHistory, setShowHistory] = useState(false);

  // 获取生成历史
  const fetchHistory = async () => {
    try {
      const response = await fetch(`/api/ai/generate-avatar?characterId=${character.id}`);
      if (response.ok) {
        const data = await response.json();
        setHistory(data.history || []);
      }
    } catch (error) {
      console.error('获取历史失败:', error);
    }
  };

  // 生成头像
  const generateAvatar = async () => {
    try {
      setIsGenerating(true);

      const requestData = {
        characterId: character.id,
        languageCode: selectedLanguage === "general" ? undefined : selectedLanguage,
        culturalContext: culturalContext || undefined,
        customPrompt: customPrompt || undefined,
      };

      const response = await fetch('/api/ai/generate-avatar', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '生成失败');
      }

      setGeneratedAvatar(data.avatarUrl);
      onAvatarGenerated?.(data.avatarUrl);
      
      toast.success("头像生成成功！", {
        description: data.message,
      });

      // 刷新历史
      await fetchHistory();

    } catch (error) {
      console.error('生成头像失败:', error);
      toast.error("生成失败", {
        description: error instanceof Error ? error.message : "请稍后重试",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // 使用历史头像
  const useHistoryAvatar = (avatarUrl: string) => {
    setGeneratedAvatar(avatarUrl);
    onAvatarGenerated?.(avatarUrl);
    toast.success("头像已应用");
  };

  // 下载头像
  const downloadAvatar = (avatarUrl: string) => {
    const link = document.createElement('a');
    link.href = avatarUrl;
    link.download = `${character.characterName}-avatar.webp`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (open) {
      fetchHistory();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <Sparkles className="h-4 w-4 mr-2" />
            AI生成头像
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5" />
            为 {character.characterName} 生成AI头像
          </DialogTitle>
          <DialogDescription>
            使用AI为语音角色生成专业头像，支持多语言文化特色
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 角色信息 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">角色信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={character.avatarUrl} />
                  <AvatarFallback>
                    {character.characterName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{character.characterName}</div>
                  <div className="text-sm text-muted-foreground">{character.characterNameEn}</div>
                </div>
                <Badge variant={character.gender === 'FEMALE' ? 'default' : 'secondary'}>
                  {character.gender === 'FEMALE' ? '女性' : 
                   character.gender === 'MALE' ? '男性' : '中性'}
                </Badge>
              </div>
              {character.description && (
                <p className="text-sm text-muted-foreground">{character.description}</p>
              )}
            </CardContent>
          </Card>

          {/* 生成选项 */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="language">目标语言（可选）</Label>
              <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                <SelectTrigger>
                  <SelectValue placeholder="选择语言以生成对应文化特色的头像" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="general">通用头像</SelectItem>
                  {languages.map((lang) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      {lang.flag} {lang.name} ({lang.nativeName})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedLanguage && selectedLanguage !== "general" && (
              <div className="space-y-2">
                <Label htmlFor="cultural-context">文化背景描述（可选）</Label>
                <Input
                  id="cultural-context"
                  value={culturalContext}
                  onChange={(e) => setCulturalContext(e.target.value)}
                  placeholder="例如：传统服饰、现代都市、学者风格等"
                />
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="custom-prompt">自定义提示词（可选）</Label>
              <Textarea
                id="custom-prompt"
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                placeholder="添加特殊要求，如服装风格、表情、背景等"
                rows={3}
              />
            </div>
          </div>

          {/* 生成结果 */}
          {generatedAvatar && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">生成结果</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4">
                  <Avatar className="h-20 w-20">
                    <AvatarImage src={generatedAvatar} />
                    <AvatarFallback>
                      {character.characterName.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="text-sm text-muted-foreground mb-2">
                      新头像已生成并自动应用
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => downloadAvatar(generatedAvatar)}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      下载
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 历史记录 */}
          {history.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm">生成历史</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowHistory(!showHistory)}
                  >
                    <History className="h-4 w-4 mr-2" />
                    {showHistory ? '隐藏' : '显示'}历史
                  </Button>
                </div>
              </CardHeader>
              {showHistory && (
                <CardContent>
                  <div className="grid grid-cols-2 gap-3">
                    {history.slice(0, 6).map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center gap-3 p-2 border rounded-lg hover:bg-muted/50 cursor-pointer"
                        onClick={() => useHistoryAvatar(item.avatarUrl)}
                      >
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={item.avatarUrl} />
                          <AvatarFallback>
                            {character.characterName.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="text-xs text-muted-foreground">
                            {item.languageCode || '通用'}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {new Date(item.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              )}
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            关闭
          </Button>
          <Button onClick={generateAvatar} disabled={isGenerating}>
            {isGenerating ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                生成中...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 mr-2" />
                生成头像
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
