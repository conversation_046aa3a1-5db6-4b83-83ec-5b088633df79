"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Upload, Camera, Trash2, RotateCcw } from "lucide-react";
import { toast } from "~/hooks/use-toast";

interface AvatarUploadProps {
  currentAvatarUrl?: string;
  characterName?: string;
  onAvatarChange: (avatarUrl: string | null) => void;
  maxSize?: number; // MB
  allowedTypes?: string[];
}

export function AvatarUpload({
  currentAvatarUrl,
  characterName = "角色",
  onAvatarChange,
  maxSize = 5,
  allowedTypes = ["image/jpeg", "image/png", "image/webp"],
}: AvatarUploadProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentAvatarUrl || null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 压缩图片
  const compressImage = (file: File, maxWidth = 200, quality = 0.8): Promise<Blob> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      const img = new Image();

      img.onload = () => {
        // 计算新尺寸，保持宽高比
        const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
        const newWidth = img.width * ratio;
        const newHeight = img.height * ratio;

        canvas.width = newWidth;
        canvas.height = newHeight;

        // 绘制压缩后的图片
        ctx.drawImage(img, 0, 0, newWidth, newHeight);

        canvas.toBlob(resolve, 'image/jpeg', quality);
      };

      img.src = URL.createObjectURL(file);
    });
  };

  // 上传到服务器
  const uploadToServer = async (blob: Blob): Promise<string> => {
    const formData = new FormData();
    formData.append('file', blob, 'avatar.jpg');

    const response = await fetch('/api/upload/avatar', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Upload failed');
    }

    const result = await response.json();
    return result.url;
  };

  // 处理文件选择
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "文件类型不支持",
        description: `请选择 ${allowedTypes.map(type => type.split('/')[1]).join(', ')} 格式的图片`,
        variant: "destructive",
      });
      return;
    }

    // 验证文件大小
    if (file.size > maxSize * 1024 * 1024) {
      toast({
        title: "文件过大",
        description: `请选择小于 ${maxSize}MB 的图片`,
        variant: "destructive",
      });
      return;
    }

    try {
      setIsUploading(true);

      // 压缩图片
      const compressedBlob = await compressImage(file);
      
      // 创建预览URL
      const previewUrl = URL.createObjectURL(compressedBlob);
      setPreviewUrl(previewUrl);

      // 上传到服务器
      const uploadedUrl = await uploadToServer(compressedBlob);
      
      // 通知父组件
      onAvatarChange(uploadedUrl);

      toast({
        title: "头像上传成功",
        description: "头像已更新",
      });

    } catch (error) {
      console.error('上传失败:', error);
      toast({
        title: "上传失败",
        description: "请重试或选择其他图片",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
      // 清空input值，允许重新选择同一文件
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // 删除头像
  const handleRemoveAvatar = () => {
    setPreviewUrl(null);
    onAvatarChange(null);
    toast({
      title: "头像已删除",
      description: "已恢复默认头像",
    });
  };

  // 重置头像
  const handleResetAvatar = () => {
    setPreviewUrl(currentAvatarUrl || null);
    onAvatarChange(currentAvatarUrl || null);
  };

  // 触发文件选择
  const triggerFileSelect = () => {
    fileInputRef.current?.click();
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex flex-col items-center space-y-4">
          {/* 头像预览 */}
          <div className="relative">
            <Avatar className="h-24 w-24 border-2 border-dashed border-muted-foreground/25">
              <AvatarImage src={previewUrl || undefined} />
              <AvatarFallback className="text-lg">
                {characterName[0] || "角"}
              </AvatarFallback>
            </Avatar>
            
            {/* 上传状态指示器 */}
            {isUploading && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-full">
                <div className="animate-spin rounded-full h-6 w-6 border-2 border-white border-t-transparent"></div>
              </div>
            )}
          </div>

          {/* 头像信息 */}
          <div className="text-center">
            <h3 className="font-medium">{characterName} 头像</h3>
            <p className="text-sm text-muted-foreground">
              推荐尺寸: 200x200px，支持 JPG、PNG、WebP 格式
            </p>
            <p className="text-xs text-muted-foreground">
              文件大小不超过 {maxSize}MB
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={triggerFileSelect}
              disabled={isUploading}
              className="flex items-center gap-2"
            >
              {previewUrl ? (
                <>
                  <Camera className="h-4 w-4" />
                  更换头像
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4" />
                  上传头像
                </>
              )}
            </Button>

            {previewUrl && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRemoveAvatar}
                  disabled={isUploading}
                  className="flex items-center gap-2 text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                  删除
                </Button>

                {currentAvatarUrl && previewUrl !== currentAvatarUrl && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetAvatar}
                    disabled={isUploading}
                    className="flex items-center gap-2"
                  >
                    <RotateCcw className="h-4 w-4" />
                    重置
                  </Button>
                )}
              </>
            )}
          </div>

          {/* 隐藏的文件输入 */}
          <input
            ref={fileInputRef}
            type="file"
            accept={allowedTypes.join(',')}
            onChange={handleFileSelect}
            className="hidden"
          />

          {/* 上传提示 */}
          {!previewUrl && (
            <div className="text-center p-4 border-2 border-dashed border-muted-foreground/25 rounded-lg w-full">
              <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-sm text-muted-foreground">
                点击上传头像或拖拽图片到此处
              </p>
            </div>
          )}

          {/* 预设头像选择 */}
          <div className="w-full">
            <h4 className="text-sm font-medium mb-2">或选择预设头像</h4>
            <div className="grid grid-cols-4 gap-2">
              {[
                "/avatars/default-1.png",
                "/avatars/default-2.png", 
                "/avatars/default-3.png",
                "/avatars/default-4.png",
              ].map((avatarUrl, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setPreviewUrl(avatarUrl);
                    onAvatarChange(avatarUrl);
                  }}
                  className={`relative rounded-full border-2 transition-colors ${
                    previewUrl === avatarUrl
                      ? "border-primary"
                      : "border-muted-foreground/25 hover:border-muted-foreground/50"
                  }`}
                  disabled={isUploading}
                >
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={avatarUrl} />
                    <AvatarFallback>{index + 1}</AvatarFallback>
                  </Avatar>
                </button>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
