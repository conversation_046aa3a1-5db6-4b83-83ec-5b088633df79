"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Progress } from "~/components/ui/progress";
import { Play, Pause, Square, Download, Volume2, Clock, Users } from "lucide-react";
import { toast } from "~/hooks/use-toast";

interface Speaker {
  id: string;
  name: string;
  characterId?: string;
  styleId?: string;
  color: string;
  avatar?: string;
}

interface DialogueLine {
  speaker: string;
  text: string;
  lineNumber: number;
}

interface AudioSegment {
  id: string;
  speaker: string;
  text: string;
  audioUrl?: string;
  duration?: number;
  status: 'pending' | 'generating' | 'completed' | 'error';
}

interface MultiSpeakerGeneratorProps {
  dialogue: DialogueLine[];
  speakers: Speaker[];
  onGenerationComplete?: (audioSegments: AudioSegment[]) => void;
}

export function MultiSpeakerGenerator({
  dialogue,
  speakers,
  onGenerationComplete,
}: MultiSpeakerGeneratorProps) {
  const [audioSegments, setAudioSegments] = useState<AudioSegment[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentSegment, setCurrentSegment] = useState<number>(-1);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPlayingIndex, setCurrentPlayingIndex] = useState<number>(-1);
  const audioRef = useRef<HTMLAudioElement>(null);

  // 初始化音频片段
  const initializeSegments = () => {
    const segments: AudioSegment[] = dialogue.map((line, index) => ({
      id: `segment_${index}`,
      speaker: line.speaker,
      text: line.text,
      status: 'pending',
    }));
    setAudioSegments(segments);
    return segments;
  };

  // 生成单个音频片段
  const generateSegment = async (segment: AudioSegment, speaker: Speaker): Promise<AudioSegment> => {
    try {
      // 构建生成参数
      const params = {
        text: segment.text,
        characterId: speaker.characterId,
        styleId: speaker.styleId,
        speaker: speaker.name,
      };

      // 调用语音生成API
      const response = await fetch('/api/voice/generate-segment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        throw new Error('Generation failed');
      }

      const result = await response.json();
      
      return {
        ...segment,
        audioUrl: result.audioUrl,
        duration: result.duration || 3,
        status: 'completed',
      };
    } catch (error) {
      console.error('生成失败:', error);
      return {
        ...segment,
        status: 'error',
      };
    }
  };

  // 生成所有音频
  const handleGenerateAll = async () => {
    if (dialogue.length === 0) {
      toast({
        title: "没有对话内容",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    const segments = initializeSegments();

    try {
      for (let i = 0; i < segments.length; i++) {
        const segment = segments[i]!;
        const speaker = speakers.find(s => s.name === segment.speaker);
        
        if (!speaker) {
          segments[i] = { ...segment, status: 'error' };
          continue;
        }

        setCurrentSegment(i);
        
        // 更新状态为生成中
        segments[i] = { ...segment, status: 'generating' };
        setAudioSegments([...segments]);

        // 生成音频
        const generatedSegment = await generateSegment(segment, speaker);
        segments[i] = generatedSegment;
        setAudioSegments([...segments]);

        // 短暂延迟，避免API限制
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      setCurrentSegment(-1);
      
      const completedCount = segments.filter(s => s.status === 'completed').length;
      const errorCount = segments.filter(s => s.status === 'error').length;

      toast({
        title: "生成完成",
        description: `成功生成 ${completedCount} 个片段${errorCount > 0 ? `，${errorCount} 个失败` : ''}`,
      });

      if (onGenerationComplete) {
        onGenerationComplete(segments);
      }

    } catch (error) {
      console.error('批量生成失败:', error);
      toast({
        title: "生成失败",
        description: "请重试或检查网络连接",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // 播放单个片段
  const playSegment = (index: number) => {
    const segment = audioSegments[index];
    if (!segment?.audioUrl) return;

    if (audioRef.current) {
      audioRef.current.src = segment.audioUrl;
      audioRef.current.play();
      setIsPlaying(true);
      setCurrentPlayingIndex(index);
    }
  };

  // 播放所有片段
  const playAll = async () => {
    const completedSegments = audioSegments.filter(s => s.status === 'completed');
    if (completedSegments.length === 0) {
      toast({
        title: "没有可播放的音频",
        description: "请先生成音频",
        variant: "destructive",
      });
      return;
    }

    setIsPlaying(true);
    
    for (let i = 0; i < audioSegments.length; i++) {
      const segment = audioSegments[i];
      if (segment?.status === 'completed' && segment.audioUrl) {
        setCurrentPlayingIndex(i);
        
        if (audioRef.current) {
          audioRef.current.src = segment.audioUrl;
          await new Promise((resolve) => {
            const audio = audioRef.current!;
            audio.onended = resolve;
            audio.onerror = resolve;
            audio.play();
          });
        }
        
        // 片段间短暂停顿
        await new Promise(resolve => setTimeout(resolve, 300));
      }
    }
    
    setIsPlaying(false);
    setCurrentPlayingIndex(-1);
  };

  // 停止播放
  const stopPlayback = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
    setIsPlaying(false);
    setCurrentPlayingIndex(-1);
  };

  // 下载合并音频
  const downloadAll = () => {
    // TODO: 实现音频合并和下载功能
    toast({
      title: "功能开发中",
      description: "音频合并下载功能即将推出",
    });
  };

  // 计算进度
  const progress = audioSegments.length > 0 
    ? (audioSegments.filter(s => s.status === 'completed').length / audioSegments.length) * 100
    : 0;

  const totalDuration = audioSegments
    .filter(s => s.duration)
    .reduce((sum, s) => sum + (s.duration || 0), 0);

  return (
    <div className="space-y-6">
      {/* 控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            多人语音生成
          </CardTitle>
          <CardDescription>
            为每个对话片段生成对应的语音
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 统计信息 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{dialogue.length}</div>
                <div className="text-sm text-muted-foreground">对话片段</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{speakers.length}</div>
                <div className="text-sm text-muted-foreground">说话者</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  {audioSegments.filter(s => s.status === 'completed').length}
                </div>
                <div className="text-sm text-muted-foreground">已完成</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  {totalDuration.toFixed(1)}s
                </div>
                <div className="text-sm text-muted-foreground">总时长</div>
              </div>
            </div>

            {/* 进度条 */}
            {audioSegments.length > 0 && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>生成进度</span>
                  <span>{Math.round(progress)}%</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>
            )}

            {/* 控制按钮 */}
            <div className="flex gap-2">
              <Button
                onClick={handleGenerateAll}
                disabled={isGenerating || dialogue.length === 0}
                className="flex-1"
              >
                {isGenerating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                    生成中 ({currentSegment + 1}/{dialogue.length})
                  </>
                ) : (
                  <>
                    <Volume2 className="h-4 w-4 mr-2" />
                    生成所有音频
                  </>
                )}
              </Button>

              <Button
                variant="outline"
                onClick={isPlaying ? stopPlayback : playAll}
                disabled={audioSegments.filter(s => s.status === 'completed').length === 0}
              >
                {isPlaying ? (
                  <Square className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
              </Button>

              <Button
                variant="outline"
                onClick={downloadAll}
                disabled={audioSegments.filter(s => s.status === 'completed').length === 0}
              >
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 音频片段列表 */}
      {audioSegments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>音频片段</CardTitle>
            <CardDescription>
              查看和播放每个对话片段的生成结果
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {audioSegments.map((segment, index) => {
                const speaker = speakers.find(s => s.name === segment.speaker);
                const isCurrentlyPlaying = currentPlayingIndex === index;
                const isCurrentlyGenerating = currentSegment === index;

                return (
                  <div
                    key={segment.id}
                    className={`flex items-center gap-3 p-3 border rounded-lg transition-colors ${
                      isCurrentlyPlaying ? 'bg-primary/10 border-primary/20' : 
                      isCurrentlyGenerating ? 'bg-yellow-50 border-yellow-200' : 
                      'bg-muted/50'
                    }`}
                  >
                    <Avatar className="h-8 w-8 flex-shrink-0" style={{ backgroundColor: speaker?.color || '#6B7280' }}>
                      <AvatarImage src={speaker?.avatar} />
                      <AvatarFallback className="text-white text-xs">
                        {segment.speaker[0]}
                      </AvatarFallback>
                    </Avatar>

                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" style={{ borderColor: speaker?.color }}>
                          {segment.speaker}
                        </Badge>
                        <Badge variant={
                          segment.status === 'completed' ? 'default' :
                          segment.status === 'generating' ? 'secondary' :
                          segment.status === 'error' ? 'destructive' : 'outline'
                        }>
                          {segment.status === 'completed' ? '已完成' :
                           segment.status === 'generating' ? '生成中' :
                           segment.status === 'error' ? '失败' : '待生成'}
                        </Badge>
                        {segment.duration && (
                          <span className="text-xs text-muted-foreground flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {segment.duration.toFixed(1)}s
                          </span>
                        )}
                      </div>
                      <p className="text-sm">{segment.text}</p>
                    </div>

                    <div className="flex items-center gap-2">
                      {segment.status === 'completed' && segment.audioUrl && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => playSegment(index)}
                          disabled={isPlaying && !isCurrentlyPlaying}
                        >
                          {isCurrentlyPlaying ? (
                            <Pause className="h-4 w-4" />
                          ) : (
                            <Play className="h-4 w-4" />
                          )}
                        </Button>
                      )}
                      
                      {segment.status === 'generating' && (
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 隐藏的音频元素 */}
      <audio
        ref={audioRef}
        onEnded={() => {
          setIsPlaying(false);
          setCurrentPlayingIndex(-1);
        }}
        onError={() => {
          setIsPlaying(false);
          setCurrentPlayingIndex(-1);
          toast({
            title: "播放失败",
            description: "音频文件可能已损坏",
            variant: "destructive",
          });
        }}
      />
    </div>
  );
}
