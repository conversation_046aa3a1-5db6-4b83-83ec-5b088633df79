"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Textarea } from "~/components/ui/textarea";
import { Label } from "~/components/ui/label";
import { Card, CardContent } from "~/components/ui/card";
import { Sparkles, Loader2 } from "lucide-react";
import { api } from "~/trpc/react";
import { toast } from "sonner";

interface TextGenerationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerate: (text: string) => void;
}

export function TextGenerationDialog({
  isOpen,
  onClose,
  onGenerate,
}: TextGenerationDialogProps) {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  // 生成简单文本
  const generateText = api.text.generateSimpleText.useMutation({
    onSuccess: (result) => {
      toast.success('文本生成成功！');
      onGenerate(result.text);
      onClose();
      resetForm();
    },
    onError: (error) => {
      toast.error('文本生成失败', {
        description: error.message,
      });
    },
    onSettled: () => {
      setIsGenerating(false);
    },
  });

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast.error('请输入文本描述');
      return;
    }

    setIsGenerating(true);
    generateText.mutate({
      prompt: prompt.trim(),
    });
  };

  const resetForm = () => {
    setPrompt('');
  };

  const handleClose = () => {
    if (!isGenerating) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-blue-500" />
            AI文本生成
          </DialogTitle>
          <DialogDescription>
            简单描述您想要的文本内容，AI将自动生成适合语音转换的文本
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 提示示例 */}
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <Sparkles className="h-5 w-5 text-blue-500 mt-0.5" />
                <div className="space-y-2">
                  <div className="text-sm font-medium text-blue-900">示例提示</div>
                  <div className="text-xs text-blue-700 space-y-1">
                    <div>• "介绍人工智能在教育领域的应用"</div>
                    <div>• "讲解健康饮食的重要性"</div>
                    <div>• "分享旅行的美好回忆"</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 文本描述 */}
          <div className="space-y-2">
            <Label htmlFor="prompt">文本内容描述</Label>
            <Textarea
              id="prompt"
              placeholder="请描述您想要生成的文本内容，例如：介绍人工智能在教育领域的应用..."
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="min-h-[100px] resize-none"
              maxLength={200}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{prompt.length}/200 字符</span>
              <span>简单描述即可，AI会自动生成适合的文本</span>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isGenerating}
            >
              取消
            </Button>
            <Button
              onClick={handleGenerate}
              disabled={isGenerating || !prompt.trim()}
              className="min-w-[120px]"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  生成中...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  生成文本
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
