"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Badge } from "~/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "~/components/ui/dialog";
import { Share2, Copy, Link, QrCode, Clock, Users, Eye, EyeOff, Volume2, Zap, Music } from "lucide-react";
import { toast } from "~/hooks/use-toast";
import { api } from "~/trpc/react";

interface CharacterShareProps {
  character: any;
  onShareCreated?: (shareData: any) => void;
}

export function CharacterShare({ character, onShareCreated }: CharacterShareProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [shareSettings, setShareSettings] = useState({
    expirationDays: 7,
    maxUsage: 10,
    requireAuth: false,
    allowCopy: true,
  });
  const [shareData, setShareData] = useState<any>(null);

  // 创建分享链接
  const createShareMutation = api.characterManagement.createShare.useMutation({
    onSuccess: (data) => {
      setShareData(data);
      onShareCreated?.(data);
      toast({
        title: "分享链接创建成功",
        description: "您可以将链接分享给其他用户",
      });
    },
    onError: (error) => {
      toast({
        title: "创建分享链接失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // 获取分享统计
  const { data: shareStats } = api.characterManagement.getShareStats.useQuery(
    { characterId: character.id },
    { enabled: !!character.id }
  );

  // 创建分享
  const handleCreateShare = () => {
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + shareSettings.expirationDays);

    createShareMutation.mutate({
      characterId: character.id,
      expiresAt,
      maxUsage: shareSettings.maxUsage,
      requireAuth: shareSettings.requireAuth,
      allowCopy: shareSettings.allowCopy,
    });
  };

  // 复制链接
  const copyShareLink = (shareCode: string) => {
    const shareUrl = `${window.location.origin}/share/character/${shareCode}`;
    navigator.clipboard.writeText(shareUrl);
    toast({
      title: "链接已复制",
      description: "分享链接已复制到剪贴板",
    });
  };

  // 复制分享码
  const copyShareCode = (shareCode: string) => {
    navigator.clipboard.writeText(shareCode);
    toast({
      title: "分享码已复制",
      description: "分享码已复制到剪贴板",
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Share2 className="h-4 w-4" />
          分享角色
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            分享角色: {character.name}
          </DialogTitle>
          <DialogDescription>
            创建分享链接，让其他用户可以使用您的自定义角色
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 角色信息 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">角色信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={character.avatarUrl} />
                    <AvatarFallback>{character.name[0]}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h3 className="font-medium text-lg">{character.name}</h3>
                    <p className="text-sm text-muted-foreground">{character.description}</p>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="outline">
                        基于 {character.baseCharacter?.characterName}
                      </Badge>
                      <Badge variant={character.isPublic ? 'default' : 'secondary'}>
                        {character.isPublic ? '公开' : '私有'}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* 语音参数 */}
                <div className="grid grid-cols-3 gap-4 p-3 bg-muted/50 rounded-lg">
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 mb-1">
                      <Zap className="h-3 w-3" />
                      <span className="text-xs font-medium">语速</span>
                    </div>
                    <div className="text-sm font-bold">{character.speed}x</div>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 mb-1">
                      <Music className="h-3 w-3" />
                      <span className="text-xs font-medium">音调</span>
                    </div>
                    <div className="text-sm font-bold">
                      {character.pitch > 0 ? '+' : ''}{character.pitch}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 mb-1">
                      <Volume2 className="h-3 w-3" />
                      <span className="text-xs font-medium">音量</span>
                    </div>
                    <div className="text-sm font-bold">
                      {character.volume > 0 ? '+' : ''}{character.volume}dB
                    </div>
                  </div>
                </div>

                {/* 角色特征 */}
                <div className="space-y-2">
                  {character.personality && character.personality.length > 0 && (
                    <div>
                      <span className="text-sm font-medium">性格特点: </span>
                      <span className="text-sm text-muted-foreground">
                        {character.personality.join(', ')}
                      </span>
                    </div>
                  )}
                  {character.bestFor && character.bestFor.length > 0 && (
                    <div>
                      <span className="text-sm font-medium">适用场景: </span>
                      <span className="text-sm text-muted-foreground">
                        {character.bestFor.join(', ')}
                      </span>
                    </div>
                  )}
                  {character.tags && character.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {character.tags.map((tag: string, index: number) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 分享设置 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">分享设置</CardTitle>
              <CardDescription>
                配置分享链接的有效期和使用限制
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="expirationDays">有效期 (天)</Label>
                  <Select
                    value={shareSettings.expirationDays.toString()}
                    onValueChange={(value) => 
                      setShareSettings(prev => ({ ...prev, expirationDays: parseInt(value) }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1天</SelectItem>
                      <SelectItem value="3">3天</SelectItem>
                      <SelectItem value="7">7天</SelectItem>
                      <SelectItem value="30">30天</SelectItem>
                      <SelectItem value="90">90天</SelectItem>
                      <SelectItem value="365">1年</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="maxUsage">最大使用次数</Label>
                  <Select
                    value={shareSettings.maxUsage.toString()}
                    onValueChange={(value) => 
                      setShareSettings(prev => ({ ...prev, maxUsage: parseInt(value) }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1次</SelectItem>
                      <SelectItem value="5">5次</SelectItem>
                      <SelectItem value="10">10次</SelectItem>
                      <SelectItem value="50">50次</SelectItem>
                      <SelectItem value="100">100次</SelectItem>
                      <SelectItem value="999">无限制</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="requireAuth"
                    checked={shareSettings.requireAuth}
                    onChange={(e) => 
                      setShareSettings(prev => ({ ...prev, requireAuth: e.target.checked }))
                    }
                    className="rounded"
                  />
                  <Label htmlFor="requireAuth" className="text-sm">
                    需要登录才能使用
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="allowCopy"
                    checked={shareSettings.allowCopy}
                    onChange={(e) => 
                      setShareSettings(prev => ({ ...prev, allowCopy: e.target.checked }))
                    }
                    className="rounded"
                  />
                  <Label htmlFor="allowCopy" className="text-sm">
                    允许复制到个人角色库
                  </Label>
                </div>
              </div>

              <Button
                onClick={handleCreateShare}
                disabled={createShareMutation.isPending}
                className="w-full"
              >
                {createShareMutation.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                    创建中...
                  </>
                ) : (
                  <>
                    <Share2 className="h-4 w-4 mr-2" />
                    创建分享链接
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* 分享结果 */}
          {shareData && (
            <Card className="bg-green-50 border-green-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-base text-green-800">分享链接已创建</CardTitle>
                <CardDescription className="text-green-600">
                  您可以通过以下方式分享您的角色
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">分享链接</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Input
                      value={`${window.location.origin}/share/character/${shareData.shareCode}`}
                      readOnly
                      className="font-mono text-sm"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyShareLink(shareData.shareCode)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium">分享码</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Input
                      value={shareData.shareCode}
                      readOnly
                      className="font-mono text-sm"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyShareCode(shareData.shareCode)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4 text-center text-sm">
                  <div>
                    <div className="font-medium text-green-800">有效期</div>
                    <div className="text-green-600 flex items-center justify-center gap-1">
                      <Clock className="h-3 w-3" />
                      {shareSettings.expirationDays}天
                    </div>
                  </div>
                  <div>
                    <div className="font-medium text-green-800">使用限制</div>
                    <div className="text-green-600 flex items-center justify-center gap-1">
                      <Users className="h-3 w-3" />
                      {shareSettings.maxUsage === 999 ? '无限' : `${shareSettings.maxUsage}次`}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium text-green-800">访问权限</div>
                    <div className="text-green-600 flex items-center justify-center gap-1">
                      {shareSettings.requireAuth ? (
                        <>
                          <Eye className="h-3 w-3" />
                          需登录
                        </>
                      ) : (
                        <>
                          <EyeOff className="h-3 w-3" />
                          公开
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 分享统计 */}
          {shareStats && shareStats.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">分享统计</CardTitle>
                <CardDescription>
                  查看您的角色分享使用情况
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {shareStats.map((stat: any) => (
                    <div key={stat.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium text-sm">{stat.shareCode}</div>
                        <div className="text-xs text-muted-foreground">
                          创建于 {new Date(stat.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          {stat.usageCount} / {stat.maxUsage === 999 ? '∞' : stat.maxUsage}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(stat.expiresAt) > new Date() ? '有效' : '已过期'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
