"use client";

import { useState, useEffect } from "react";
import { type VoiceCharacter } from "@prisma/client";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import { Textarea } from "~/components/ui/textarea";
import { Switch } from "~/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Upload, X, User } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

const characterFormSchema = z.object({
  characterName: z.string().min(1, "高棉语名称不能为空").max(100),
  characterNameEn: z.string().min(1, "英文名称不能为空").max(100),
  description: z.string().min(1, "描述不能为空").max(500),
  avatarUrl: z.string().url("请输入有效的URL").optional().or(z.literal("")),
  gender: z.enum(["MALE", "FEMALE", "NEUTRAL"]),
  isActive: z.boolean(),
  sortOrder: z.number().int().min(0),
});

type CharacterFormData = z.infer<typeof characterFormSchema>;

interface CharacterEditDialogProps {
  character: VoiceCharacter | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function CharacterEditDialog({
  character,
  open,
  onOpenChange,
  onSuccess,
}: CharacterEditDialogProps) {
  const [avatarPreview, setAvatarPreview] = useState<string>("");

  const form = useForm<CharacterFormData>({
    resolver: zodResolver(characterFormSchema),
    defaultValues: {
      characterName: "",
      characterNameEn: "",
      description: "",
      avatarUrl: "",
      gender: "MALE",
      isActive: true,
      sortOrder: 0,
    },
  });

  const utils = api.useUtils();
  
  const updateCharacter = api.characterAdmin.updateCharacter.useMutation({
    onSuccess: () => {
      toast.success("角色更新成功");
      utils.characterAdmin.getAllCharacters.invalidate();
      onSuccess?.();
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error(`更新失败: ${error.message}`);
    },
  });

  const createCharacter = api.characterAdmin.createCharacter.useMutation({
    onSuccess: () => {
      toast.success("角色创建成功");
      utils.characterAdmin.getAllCharacters.invalidate();
      onSuccess?.();
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error(`创建失败: ${error.message}`);
    },
  });

  // 当角色数据变化时更新表单
  useEffect(() => {
    if (character) {
      form.reset({
        characterName: character.characterName,
        characterNameEn: character.characterNameEn,
        description: character.description,
        avatarUrl: character.avatarUrl || "",
        gender: character.gender,
        isActive: character.isActive,
        sortOrder: character.sortOrder,
      });
      setAvatarPreview(character.avatarUrl || "");
    } else {
      form.reset({
        characterName: "",
        characterNameEn: "",
        description: "",
        avatarUrl: "",
        gender: "MALE",
        isActive: true,
        sortOrder: 0,
      });
      setAvatarPreview("");
    }
  }, [character, form]);

  const onSubmit = (data: CharacterFormData) => {
    if (character) {
      // 更新现有角色
      updateCharacter.mutate({
        id: character.id,
        ...data,
        avatarUrl: data.avatarUrl || null,
      });
    } else {
      // 创建新角色
      createCharacter.mutate({
        ...data,
        avatarUrl: data.avatarUrl || undefined,
      });
    }
  };

  const handleAvatarUrlChange = (url: string) => {
    form.setValue("avatarUrl", url);
    setAvatarPreview(url);
  };

  const isLoading = updateCharacter.isPending || createCharacter.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {character ? "编辑角色" : "创建新角色"}
          </DialogTitle>
          <DialogDescription>
            {character ? "修改语音角色的信息" : "添加新的语音角色"}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* 头像设置 */}
            <div className="space-y-4">
              <FormLabel>角色头像</FormLabel>
              <div className="flex items-center space-x-4">
                <Avatar className="h-20 w-20">
                  <AvatarImage src={avatarPreview} />
                  <AvatarFallback>
                    <User className="h-8 w-8" />
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 space-y-2">
                  <FormField
                    control={form.control}
                    name="avatarUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <div className="flex space-x-2">
                            <Input
                              placeholder="输入头像URL"
                              {...field}
                              onChange={(e) => {
                                field.onChange(e);
                                handleAvatarUrlChange(e.target.value);
                              }}
                            />
                            {avatarPreview && (
                              <Button
                                type="button"
                                variant="outline"
                                size="icon"
                                onClick={() => handleAvatarUrlChange("")}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </FormControl>
                        <FormDescription>
                          输入头像图片的URL地址
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 高棉语名称 */}
              <FormField
                control={form.control}
                name="characterName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>高棉语名称</FormLabel>
                    <FormControl>
                      <Input placeholder="ខារ៉ុន" {...field} />
                    </FormControl>
                    <FormDescription>
                      用户界面显示的高棉语名称
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 英文名称 */}
              <FormField
                control={form.control}
                name="characterNameEn"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>英文名称</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Charon" 
                        {...field}
                        disabled={!!character} // 编辑时不允许修改英文名称
                      />
                    </FormControl>
                    <FormDescription>
                      {character ? "英文名称不可修改" : "对应的Gemini语音名称"}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 描述 */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>描述</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="សំឡេងប្រុសជ្រៅ និងមានព័ត៌មាន - សម្រាប់ការអាន និងការបង្រៀន"
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    角色的详细描述，用高棉语
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* 性别 */}
              <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>性别</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择性别" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="MALE">男性</SelectItem>
                        <SelectItem value="FEMALE">女性</SelectItem>
                        <SelectItem value="NEUTRAL">中性</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 排序 */}
              <FormField
                control={form.control}
                name="sortOrder"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>排序</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      显示顺序
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 状态 */}
              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                    <div className="space-y-0.5">
                      <FormLabel>启用状态</FormLabel>
                      <FormDescription>
                        是否在前端显示
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                取消
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "保存中..." : character ? "更新" : "创建"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
