"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Badge } from "~/components/ui/badge";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Separator } from "~/components/ui/separator";
import { CreditCard, Zap, Crown, Plus, Sparkles } from "lucide-react";
import { api } from "~/trpc/react";
import { toast } from "sonner";

interface QuotaRechargeDialogProps {
  user: {
    id: string;
    name?: string | null;
    email?: string | null;
    credits?: number;
    usedCredits?: number;
    standardQuota?: number;
    usedStandardQuota?: number;
    professionalQuota?: number;
    usedProfessionalQuota?: number;
  };
  trigger?: React.ReactNode;
  onSuccess?: () => void;
}

export function QuotaRechargeDialog({ user, trigger, onSuccess }: QuotaRechargeDialogProps) {
  const [open, setOpen] = useState(false);
  const [quotaType, setQuotaType] = useState<"STANDARD" | "PROFESSIONAL">("STANDARD");
  const [amount, setAmount] = useState("");
  const [reason, setReason] = useState("");

  const adjustQuota = api.admin.adjustUserQuota.useMutation({
    onSuccess: () => {
      toast.success("配额充值成功！");
      setOpen(false);
      setAmount("");
      setReason("");
      onSuccess?.();
    },
    onError: (error) => {
      toast.error(`充值失败: ${error.message}`);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const amountNum = parseInt(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      toast.error("请输入有效的充值数量");
      return;
    }

    if (!reason.trim()) {
      toast.error("请输入充值原因");
      return;
    }

    adjustQuota.mutate({
      userId: user.id,
      quotaType,
      amount: amountNum,
      reason: reason.trim(),
    });
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm">
      <Plus className="h-4 w-4 mr-1" />
      充值配额
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            配额充值
          </DialogTitle>
          <DialogDescription>
            为用户 {user.name || user.email} 充值积分
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 当前积分状态 */}
          <div className="grid grid-cols-1 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Sparkles className="h-4 w-4 text-primary" />
                  积分余额
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>已用</span>
                    <span>{(user.usedCredits || 0).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>总计</span>
                    <span>{(user.credits || 0).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm font-medium">
                    <span>剩余</span>
                    <span className={(user.credits || 0) - (user.usedCredits || 0) < 0 ? "text-red-500" : "text-green-500"}>
                      {((user.credits || 0) - (user.usedCredits || 0)).toLocaleString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Crown className="h-4 w-4 text-purple-500" />
                  专业配额
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>已用</span>
                    <span>{(user.usedProfessionalQuota || 0).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>总计</span>
                    <span>{(user.professionalQuota || 0).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm font-medium">
                    <span>剩余</span>
                    <span className={(user.professionalQuota || 0) - (user.usedProfessionalQuota || 0) < 0 ? "text-red-500" : "text-green-500"}>
                      {((user.professionalQuota || 0) - (user.usedProfessionalQuota || 0)).toLocaleString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Separator />

          {/* 配额类型选择 */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">选择配额类型</Label>
            <RadioGroup value={quotaType} onValueChange={(value) => setQuotaType(value as "STANDARD" | "PROFESSIONAL")}>
              <div className="flex items-center space-x-2 p-3 border rounded-lg">
                <RadioGroupItem value="STANDARD" id="standard" />
                <Label htmlFor="standard" className="flex-1 cursor-pointer">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4 text-blue-500" />
                      <span className="font-medium">标准配额</span>
                    </div>
                    <Badge variant="secondary">消耗积分少</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">适用于标准质量语音生成</p>
                </Label>
              </div>
              <div className="flex items-center space-x-2 p-3 border rounded-lg">
                <RadioGroupItem value="PROFESSIONAL" id="professional" />
                <Label htmlFor="professional" className="flex-1 cursor-pointer">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Crown className="h-4 w-4 text-purple-500" />
                      <span className="font-medium">专业配额</span>
                    </div>
                    <Badge variant="destructive">消耗积分多</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">适用于专业质量语音生成</p>
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* 充值数量 */}
          <div className="space-y-2">
            <Label htmlFor="amount">充值数量（字符）</Label>
            <Input
              id="amount"
              type="number"
              placeholder="请输入要充值的字符数量"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              min="1"
              required
            />
          </div>

          {/* 充值原因 */}
          <div className="space-y-2">
            <Label htmlFor="reason">充值原因</Label>
            <Textarea
              id="reason"
              placeholder="请输入充值原因，如：用户反馈、补偿、活动奖励等"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={3}
              required
            />
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end gap-3">
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              取消
            </Button>
            <Button type="submit" disabled={adjustQuota.isPending}>
              {adjustQuota.isPending ? "充值中..." : "确认充值"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
