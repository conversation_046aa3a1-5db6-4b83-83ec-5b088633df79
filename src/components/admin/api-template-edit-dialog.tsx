"use client";

import { useState, useEffect } from "react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import { Label } from "~/components/ui/label";
import { Badge } from "~/components/ui/badge";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Switch } from "~/components/ui/switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Separator } from "~/components/ui/separator";
import { Server, User, Loader2, Save, X, Plus, Edit } from "lucide-react";
import { api } from "~/trpc/react";
import { toast } from "sonner";

interface ApiTemplateEditDialogProps {
  template?: any; // undefined for create mode
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function ApiTemplateEditDialog({
  template,
  open,
  onOpenChange,
  onSuccess
}: ApiTemplateEditDialogProps) {
  const isEditMode = !!template;
  
  const [formData, setFormData] = useState({
    apiProvider: 'GEMINI' as 'GEMINI' | 'OPENAI' | 'AZURE',
    apiVoiceName: '',
    originalName: '',
    gender: 'NEUTRAL' as 'MALE' | 'FEMALE' | 'NEUTRAL',
    defaultStyle: '',
    defaultDescription: '',
    sortOrder: 0,
    isActive: true,
  });

  // 重置表单数据
  useEffect(() => {
    if (template) {
      setFormData({
        apiProvider: template.apiProvider || 'GEMINI',
        apiVoiceName: template.apiVoiceName || '',
        originalName: template.originalName || '',
        gender: template.gender || 'NEUTRAL',
        defaultStyle: template.defaultStyle || '',
        defaultDescription: template.defaultDescription || '',
        sortOrder: template.sortOrder || 0,
        isActive: template.isActive ?? true,
      });
    } else {
      setFormData({
        apiProvider: 'GEMINI',
        apiVoiceName: '',
        originalName: '',
        gender: 'NEUTRAL',
        defaultStyle: '',
        defaultDescription: '',
        sortOrder: 0,
        isActive: true,
      });
    }
  }, [template, open]);

  // 创建API模板
  const createTemplateMutation = api.apiCharacterTemplateAdmin.createTemplate.useMutation({
    onSuccess: (data) => {
      toast.success("API模板创建成功", {
        description: data.message,
      });
      onSuccess?.();
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error("API模板创建失败", {
        description: error.message,
      });
    },
  });

  // 更新API模板
  const updateTemplateMutation = api.apiCharacterTemplateAdmin.updateTemplate.useMutation({
    onSuccess: (data) => {
      toast.success("API模板更新成功", {
        description: data.message,
      });
      onSuccess?.();
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error("API模板更新失败", {
        description: error.message,
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.apiVoiceName.trim() || !formData.originalName.trim()) {
      toast.error("请填写必填字段");
      return;
    }

    if (isEditMode) {
      updateTemplateMutation.mutate({
        id: template.id,
        ...formData,
      });
    } else {
      createTemplateMutation.mutate(formData);
    }
  };

  const isLoading = createTemplateMutation.isPending || updateTemplateMutation.isPending;

  const providerOptions = [
    { value: 'GEMINI', label: 'Google Gemini', description: 'Google 的语音合成服务' },
    { value: 'OPENAI', label: 'OpenAI TTS', description: 'OpenAI 的语音合成服务' },
    { value: 'AZURE', label: 'Azure Speech', description: 'Microsoft Azure 语音服务' },
  ];

  const genderOptions = [
    { value: 'MALE', label: '男性', icon: '♂' },
    { value: 'FEMALE', label: '女性', icon: '♀' },
    { value: 'NEUTRAL', label: '中性', icon: '⚪' },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {isEditMode ? (
              <>
                <Edit className="h-5 w-5" />
                编辑 API 模板
              </>
            ) : (
              <>
                <Plus className="h-5 w-5" />
                创建 API 模板
              </>
            )}
          </DialogTitle>
          <DialogDescription>
            {isEditMode ? '修改现有的 API 角色模板信息' : '创建新的 API 角色模板'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* API 提供商 */}
              <div className="space-y-2">
                <Label htmlFor="apiProvider" className="text-sm font-medium">
                  API 提供商 <span className="text-destructive">*</span>
                </Label>
                <Select
                  value={formData.apiProvider}
                  onValueChange={(value: 'GEMINI' | 'OPENAI' | 'AZURE') => 
                    setFormData(prev => ({ ...prev, apiProvider: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择 API 提供商" />
                  </SelectTrigger>
                  <SelectContent>
                    {providerOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex flex-col">
                          <span className="font-medium">{option.label}</span>
                          <span className="text-xs text-muted-foreground">{option.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* API 语音名称 */}
              <div className="space-y-2">
                <Label htmlFor="apiVoiceName" className="text-sm font-medium">
                  API 语音名称 <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="apiVoiceName"
                  value={formData.apiVoiceName}
                  onChange={(e) => setFormData(prev => ({ ...prev, apiVoiceName: e.target.value }))}
                  placeholder="例如：en-US-Journey-D"
                  className="font-mono text-sm"
                />
                <p className="text-xs text-muted-foreground">
                  API 提供商的具体语音标识符
                </p>
              </div>

              {/* 原始名称 */}
              <div className="space-y-2">
                <Label htmlFor="originalName" className="text-sm font-medium">
                  原始名称 <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="originalName"
                  value={formData.originalName}
                  onChange={(e) => setFormData(prev => ({ ...prev, originalName: e.target.value }))}
                  placeholder="例如：Journey"
                />
                <p className="text-xs text-muted-foreground">
                  角色的原始英文名称
                </p>
              </div>
            </CardContent>
          </Card>

          {/* 角色属性 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">角色属性</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 性别 */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">性别</Label>
                <RadioGroup
                  value={formData.gender}
                  onValueChange={(value: 'MALE' | 'FEMALE' | 'NEUTRAL') => 
                    setFormData(prev => ({ ...prev, gender: value }))
                  }
                >
                  <div className="flex gap-6">
                    {genderOptions.map((option) => (
                      <div key={option.value} className="flex items-center space-x-2">
                        <RadioGroupItem value={option.value} id={option.value} />
                        <Label htmlFor={option.value} className="flex items-center gap-2 cursor-pointer">
                          <span className="text-lg">{option.icon}</span>
                          {option.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </div>

              {/* 默认风格 */}
              <div className="space-y-2">
                <Label htmlFor="defaultStyle" className="text-sm font-medium">默认风格</Label>
                <Input
                  id="defaultStyle"
                  value={formData.defaultStyle}
                  onChange={(e) => setFormData(prev => ({ ...prev, defaultStyle: e.target.value }))}
                  placeholder="例如：专业、温暖、活泼"
                />
              </div>

              {/* 默认描述 */}
              <div className="space-y-2">
                <Label htmlFor="defaultDescription" className="text-sm font-medium">默认描述</Label>
                <Textarea
                  id="defaultDescription"
                  value={formData.defaultDescription}
                  onChange={(e) => setFormData(prev => ({ ...prev, defaultDescription: e.target.value }))}
                  placeholder="描述这个语音角色的特点和适用场景..."
                  className="min-h-[80px] resize-y"
                  maxLength={500}
                />
                <div className="text-xs text-muted-foreground text-right">
                  {formData.defaultDescription.length}/500 字符
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 设置选项 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">设置选项</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 排序顺序 */}
              <div className="space-y-2">
                <Label htmlFor="sortOrder" className="text-sm font-medium">排序顺序</Label>
                <Input
                  id="sortOrder"
                  type="number"
                  min="0"
                  value={formData.sortOrder}
                  onChange={(e) => setFormData(prev => ({ ...prev, sortOrder: parseInt(e.target.value) || 0 }))}
                  className="w-32"
                />
                <p className="text-xs text-muted-foreground">
                  数字越小排序越靠前
                </p>
              </div>

              {/* 激活状态 */}
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="isActive" className="text-sm font-medium">激活状态</Label>
                  <p className="text-xs text-muted-foreground">
                    是否在系统中启用此模板
                  </p>
                </div>
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                />
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Server className="h-4 w-4" />
              <span>{isEditMode ? '修改现有模板' : '创建新的 API 模板'}</span>
            </div>

            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                取消
              </Button>
              <Button
                type="submit"
                disabled={isLoading || !formData.apiVoiceName.trim() || !formData.originalName.trim()}
                className="flex items-center gap-2"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    {isEditMode ? '更新中...' : '创建中...'}
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4" />
                    {isEditMode ? '更新模板' : '创建模板'}
                  </>
                )}
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
