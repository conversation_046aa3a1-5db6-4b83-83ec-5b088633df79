"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import { Textarea } from "~/components/ui/textarea";
import { Switch } from "~/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Badge } from "~/components/ui/badge";
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Loader2, User, Save, X } from "lucide-react";

// 表单验证模式
const languageCharacterFormSchema = z.object({
  name: z.string().min(1, "角色名称不能为空").max(100, "角色名称不能超过100字符"),
  description: z.string().min(1, "描述不能为空"),
  gender: z.enum(["MALE", "FEMALE", "NEUTRAL"]).optional(),
  style: z.string().optional(),
  personality: z.string().optional(),
  bestFor: z.string().optional(),
  avatarUrl: z.string().url("请输入有效的URL").optional().or(z.literal("")),
  isActive: z.boolean(),
  sortOrder: z.number().min(0, "排序不能为负数"),
  customSettings: z.string().optional(),
  customPrompt: z.string().optional(),
});

type LanguageCharacterFormData = z.infer<typeof languageCharacterFormSchema>;

interface LanguageCharacterEditDialogProps {
  character?: any; // 语言角色数据
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function LanguageCharacterEditDialog({
  character,
  open,
  onOpenChange,
  onSuccess,
}: LanguageCharacterEditDialogProps) {
  const [avatarPreview, setAvatarPreview] = useState<string>("");

  const form = useForm<LanguageCharacterFormData>({
    resolver: zodResolver(languageCharacterFormSchema),
    defaultValues: {
      name: "",
      description: "",
      gender: undefined,
      style: "",
      personality: "",
      bestFor: "",
      avatarUrl: "",
      isActive: true,
      sortOrder: 0,
      customSettings: "",
      customPrompt: "",
    },
  });

  const utils = api.useUtils();
  
  const updateCharacter = api.languageCharacterAdmin.updateLanguageCharacter.useMutation({
    onSuccess: () => {
      toast.success("语言角色更新成功");
      utils.languageCharacterAdmin.getLanguageCharacters.invalidate();
      onSuccess?.();
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error(`更新失败: ${error.message}`);
    },
  });

  // 当角色数据变化时，更新表单
  useEffect(() => {
    if (character && open) {
      form.reset({
        name: character.name || "",
        description: character.description || "",
        gender: character.gender || "inherit",
        style: character.style || "",
        personality: character.personality || "",
        bestFor: character.bestFor || "",
        avatarUrl: character.avatarUrl || "",
        isActive: character.isActive ?? true,
        sortOrder: character.sortOrder || 0,
        customSettings: character.customSettings || "",
        customPrompt: character.customPrompt || "",
      });
      setAvatarPreview(character.avatarUrl || "");
    }
  }, [character, open, form]);

  const onSubmit = (data: LanguageCharacterFormData) => {
    if (character) {
      updateCharacter.mutate({
        id: character.id,
        ...data,
        gender: data.gender === "inherit" ? undefined : data.gender,
        avatarUrl: data.avatarUrl || undefined,
      });
    }
  };

  const handleAvatarUrlChange = (url: string) => {
    form.setValue("avatarUrl", url);
    setAvatarPreview(url);
  };

  const isLoading = updateCharacter.isPending;

  // 获取性别显示文本
  const getGenderText = (gender?: string) => {
    switch (gender) {
      case 'MALE': return '男性';
      case 'FEMALE': return '女性';
      case 'NEUTRAL': return '中性';
      default: return '未设置';
    }
  };

  // 获取性别图标
  const getGenderIcon = (gender?: string) => {
    switch (gender) {
      case 'MALE': return '♂️';
      case 'FEMALE': return '♀️';
      case 'NEUTRAL': return '⚧️';
      default: return '❓';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            编辑语言角色
          </DialogTitle>
          <DialogDescription>
            修改语言角色的信息和属性设置
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* 角色基本信息 */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">基本信息</h3>
              
              {/* 显示模板信息 */}
              {character?.template && (
                <div className="p-4 bg-muted rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">关联模板</span>
                    <Badge variant="secondary">
                      {character.template.apiProvider}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">API名称：</span>
                      <span className="font-mono">{character.template.apiVoiceName}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">原始名称：</span>
                      <span>{character.template.originalName}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">模板性别：</span>
                      <span>
                        {getGenderIcon(character.template.gender)} {getGenderText(character.template.gender)}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* 角色名称 */}
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>角色名称</FormLabel>
                      <FormControl>
                        <Input placeholder="输入角色名称" {...field} />
                      </FormControl>
                      <FormDescription>
                        在该语言下显示的角色名称
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 性别选择 */}
                <FormField
                  control={form.control}
                  name="gender"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>性别</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value || ""}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择性别" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="inherit">
                            继承模板 ({character?.template?.gender ? getGenderText(character.template.gender) : '未设置'})
                          </SelectItem>
                          <SelectItem value="MALE">♂️ 男性</SelectItem>
                          <SelectItem value="FEMALE">♀️ 女性</SelectItem>
                          <SelectItem value="NEUTRAL">⚧️ 中性</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        可以覆盖模板的默认性别设置
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* 描述 */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>描述</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="描述角色的特点和适用场景"
                        className="min-h-[80px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 角色属性 */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">角色属性</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* 风格 */}
                <FormField
                  control={form.control}
                  name="style"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>语音风格</FormLabel>
                      <FormControl>
                        <Input placeholder="如：温和、活泼、专业" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 个性 */}
                <FormField
                  control={form.control}
                  name="personality"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>个性特点</FormLabel>
                      <FormControl>
                        <Input placeholder="如：友善、严肃、幽默" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* 适用场景 */}
              <FormField
                control={form.control}
                name="bestFor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>适用场景</FormLabel>
                    <FormControl>
                      <Input placeholder="如：新闻播报、儿童故事、商务演示" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                <X className="h-4 w-4 mr-2" />
                取消
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                保存更改
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
