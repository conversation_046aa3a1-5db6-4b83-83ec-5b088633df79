"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Textarea } from "~/components/ui/textarea";
import { Label } from "~/components/ui/label";
import { Badge } from "~/components/ui/badge";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { Switch } from "~/components/ui/switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Volume2, Wand2, User, Loader2, Play, Pause, RefreshCw } from "lucide-react";
import { api } from "~/trpc/react";
import { toast } from "sonner";

interface VoiceDemoGenerationDialogProps {
  character: any;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function VoiceDemoGenerationDialog({
  character,
  open,
  onOpenChange,
  onSuccess
}: VoiceDemoGenerationDialogProps) {
  const [demoText, setDemoText] = useState("你好，这是一个语音试听测试。Hello, this is a voice demo test.");
  const [quality, setQuality] = useState<'standard' | 'professional'>('standard');
  const [regenerate, setRegenerate] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);

  // 生成语音试听
  const generateVoiceDemoMutation = api.aiGeneration.generateVoiceDemo.useMutation({
    onSuccess: (data) => {
      toast.success("语音试听生成成功", {
        description: data.message,
      });
      onSuccess?.();
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error("语音试听生成失败", {
        description: error.message,
      });
    },
  });

  const handleGenerate = () => {
    if (!demoText.trim()) {
      toast.error("请输入试听文本");
      return;
    }

    generateVoiceDemoMutation.mutate({
      characterId: character.id,
      quality,
      demoText: demoText.trim(),
      regenerate,
    });
  };

  const handlePlayPreview = () => {
    // 这里可以添加预览播放逻辑
    setIsPlaying(!isPlaying);
    // 模拟播放
    if (!isPlaying) {
      setTimeout(() => setIsPlaying(false), 3000);
    }
  };

  const defaultTexts = {
    standard: [
      "你好，这是一个语音试听测试。",
      "Hello, this is a voice demo test.",
      "欢迎使用我们的语音生成服务。",
      "Welcome to our voice generation service.",
    ],
    professional: [
      "尊敬的用户，欢迎体验我们的专业级语音合成技术。我们致力于为您提供最自然、最富有表现力的语音体验。",
      "Dear users, welcome to experience our professional voice synthesis technology. We are committed to providing you with the most natural and expressive voice experience.",
      "在这个数字化时代，语音技术正在改变我们与世界互动的方式。让我们一起探索语音的无限可能。",
      "In this digital age, voice technology is changing the way we interact with the world. Let's explore the infinite possibilities of voice together.",
    ]
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Volume2 className="h-5 w-5" />
            生成语音试听
          </DialogTitle>
          <DialogDescription>
            为角色生成高质量的语音试听样本
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 角色信息 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">角色信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={character.avatarUrl} />
                  <AvatarFallback>
                    <User className="h-6 w-6" />
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h3 className="font-medium">{character.name}</h3>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline" className="text-xs">
                      {character.template?.gender === 'MALE' ? '♂ 男性' : 
                       character.template?.gender === 'FEMALE' ? '♀ 女性' : '⚪ 中性'}
                    </Badge>
                    {character.style && (
                      <Badge variant="secondary" className="text-xs">
                        {character.style}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 质量选择 */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">生成质量</Label>
            <RadioGroup value={quality} onValueChange={(value: 'standard' | 'professional') => setQuality(value)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="standard" id="standard" />
                <Label htmlFor="standard" className="flex items-center gap-2 cursor-pointer">
                  <Volume2 className="h-4 w-4" />
                  标准质量
                  <Badge variant="outline" className="text-xs">快速</Badge>
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="professional" id="professional" />
                <Label htmlFor="professional" className="flex items-center gap-2 cursor-pointer">
                  <Wand2 className="h-4 w-4" />
                  专业质量
                  <Badge variant="outline" className="text-xs">高质量</Badge>
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* 试听文本 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">试听文本</Label>
              <div className="flex items-center gap-2">
                <Label htmlFor="regenerate" className="text-xs text-muted-foreground">
                  重新生成
                </Label>
                <Switch
                  id="regenerate"
                  checked={regenerate}
                  onCheckedChange={setRegenerate}
                />
              </div>
            </div>
            
            <Textarea
              value={demoText}
              onChange={(e) => setDemoText(e.target.value)}
              placeholder="请输入试听文本..."
              className="min-h-[120px] resize-y"
              maxLength={500}
            />
            
            <div className="text-xs text-muted-foreground text-right">
              {demoText.length}/500 字符
            </div>

            {/* 预设文本 */}
            <div className="space-y-2">
              <Label className="text-xs text-muted-foreground">快速选择：</Label>
              <div className="grid grid-cols-1 gap-2">
                {defaultTexts[quality].map((text, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => setDemoText(text)}
                    className="justify-start text-left h-auto py-2 px-3"
                  >
                    <span className="text-xs line-clamp-2">{text}</span>
                  </Button>
                ))}
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center justify-between pt-4 border-t">
            <Button
              variant="outline"
              onClick={handlePlayPreview}
              disabled={!demoText.trim()}
              className="flex items-center gap-2"
            >
              {isPlaying ? (
                <>
                  <Pause className="h-4 w-4" />
                  停止预览
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  预览效果
                </>
              )}
            </Button>

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={generateVoiceDemoMutation.isPending}
              >
                取消
              </Button>
              <Button
                onClick={handleGenerate}
                disabled={generateVoiceDemoMutation.isPending || !demoText.trim()}
                className="flex items-center gap-2"
              >
                {generateVoiceDemoMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    生成中...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4" />
                    生成试听
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
