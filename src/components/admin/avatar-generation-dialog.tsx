"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Textarea } from "~/components/ui/textarea";
import { Label } from "~/components/ui/label";
import { Badge } from "~/components/ui/badge";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { Switch } from "~/components/ui/switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Input } from "~/components/ui/input";
import { Image, User, Loader2, RefreshCw, Palette, Sparkles, Eye } from "lucide-react";
import { api } from "~/trpc/react";
import { toast } from "sonner";

interface AvatarGenerationDialogProps {
  character: any;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function AvatarGenerationDialog({
  character,
  open,
  onOpenChange,
  onSuccess
}: AvatarGenerationDialogProps) {
  const [style, setStyle] = useState('professional');
  const [customPrompt, setCustomPrompt] = useState('');
  const [useCustomPrompt, setUseCustomPrompt] = useState(false);
  const [regenerate, setRegenerate] = useState(true);

  // 生成头像
  const generateAvatarMutation = api.aiGeneration.generateCharacterAvatar.useMutation({
    onSuccess: (data) => {
      toast.success("头像生成成功", {
        description: data.message,
      });
      onSuccess?.();
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error("头像生成失败", {
        description: error.message,
      });
    },
  });

  const handleGenerate = () => {
    generateAvatarMutation.mutate({
      characterId: character.id,
      characterName: character.name,
      gender: character.template?.gender || 'NEUTRAL',
      style,
      customPrompt: useCustomPrompt && customPrompt.trim() ? customPrompt.trim() : undefined,
    });
  };

  const styleOptions = [
    {
      value: 'professional',
      label: '专业商务',
      description: '正式、专业的商务风格',
      icon: '👔'
    },
    {
      value: 'casual',
      label: '休闲自然',
      description: '轻松、自然的日常风格',
      icon: '😊'
    },
    {
      value: 'artistic',
      label: '艺术创意',
      description: '富有创意和艺术感的风格',
      icon: '🎨'
    },
    {
      value: 'anime',
      label: '动漫风格',
      description: '日式动漫角色风格',
      icon: '🎭'
    },
    {
      value: 'realistic',
      label: '写实风格',
      description: '真实、自然的人物风格',
      icon: '📸'
    },
    {
      value: 'minimalist',
      label: '简约风格',
      description: '简洁、现代的设计风格',
      icon: '⚪'
    }
  ];

  const promptTemplates = [
    "高质量专业头像，现代商务风格，友好的表情",
    "温暖亲切的笑容，自然光线，高清细节",
    "创意艺术风格，独特的视觉效果，富有表现力",
    "简约现代设计，干净的背景，专业摄影质感",
    "动漫风格角色设计，精美的细节，鲜明的色彩"
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Image className="h-5 w-5" />
            AI 头像生成
          </DialogTitle>
          <DialogDescription>
            使用 AI 为角色生成个性化头像
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 角色信息 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">角色信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={character.avatarUrl} />
                  <AvatarFallback>
                    <User className="h-6 w-6" />
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h3 className="font-medium">{character.name}</h3>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline" className="text-xs">
                      {character.template?.gender === 'MALE' ? '♂ 男性' : 
                       character.template?.gender === 'FEMALE' ? '♀ 女性' : '⚪ 中性'}
                    </Badge>
                    {character.style && (
                      <Badge variant="secondary" className="text-xs">
                        {character.style}
                      </Badge>
                    )}
                  </div>
                  {character.description && (
                    <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                      {character.description}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 风格选择 */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">头像风格</Label>
            <RadioGroup value={style} onValueChange={setStyle}>
              <div className="grid grid-cols-2 gap-3">
                {styleOptions.map((option) => (
                  <div key={option.value} className="flex items-center space-x-2">
                    <RadioGroupItem value={option.value} id={option.value} />
                    <Label 
                      htmlFor={option.value} 
                      className="flex items-center gap-2 cursor-pointer flex-1 p-2 rounded border hover:bg-accent"
                    >
                      <span className="text-lg">{option.icon}</span>
                      <div className="flex-1">
                        <div className="font-medium text-sm">{option.label}</div>
                        <div className="text-xs text-muted-foreground">{option.description}</div>
                      </div>
                    </Label>
                  </div>
                ))}
              </div>
            </RadioGroup>
          </div>

          {/* 自定义提示词 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">自定义提示词</Label>
              <div className="flex items-center gap-2">
                <Label htmlFor="custom-prompt" className="text-xs text-muted-foreground">
                  使用自定义
                </Label>
                <Switch
                  id="custom-prompt"
                  checked={useCustomPrompt}
                  onCheckedChange={setUseCustomPrompt}
                />
              </div>
            </div>
            
            {useCustomPrompt && (
              <>
                <Textarea
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  placeholder="描述您想要的头像风格、表情、背景等..."
                  className="min-h-[100px] resize-y"
                  maxLength={300}
                />
                
                <div className="text-xs text-muted-foreground text-right">
                  {customPrompt.length}/300 字符
                </div>

                {/* 提示词模板 */}
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">快速选择：</Label>
                  <div className="grid grid-cols-1 gap-2">
                    {promptTemplates.map((template, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => setCustomPrompt(template)}
                        className="justify-start text-left h-auto py-2 px-3"
                      >
                        <span className="text-xs">{template}</span>
                      </Button>
                    ))}
                  </div>
                </div>
              </>
            )}
          </div>

          {/* 生成选项 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">生成选项</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="regenerate"
                checked={regenerate}
                onCheckedChange={setRegenerate}
              />
              <Label htmlFor="regenerate" className="text-sm">
                覆盖现有头像
              </Label>
            </div>
            
            <p className="text-xs text-muted-foreground">
              {regenerate ? '将替换角色的当前头像' : '如果角色已有头像，将跳过生成'}
            </p>
          </div>

          {/* 预览区域 */}
          {character.avatarUrl && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  当前头像
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-3">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={character.avatarUrl} />
                    <AvatarFallback>
                      <User className="h-8 w-8" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-xs text-muted-foreground">
                    {regenerate ? '此头像将被新生成的头像替换' : '将保留此头像'}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 操作按钮 */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Sparkles className="h-4 w-4" />
              <span>使用 Gemini AI 生成高质量头像</span>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={generateAvatarMutation.isPending}
              >
                取消
              </Button>
              <Button
                onClick={handleGenerate}
                disabled={generateAvatarMutation.isPending}
                className="flex items-center gap-2"
              >
                {generateAvatarMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    生成中...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4" />
                    生成头像
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
