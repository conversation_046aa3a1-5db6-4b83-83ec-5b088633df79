"use client";

import { useState, useRef, useEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Play, Pause, Loader2, User, UserCheck } from "lucide-react";
import { api } from "~/trpc/react";
import { type Language } from '~/lib/translations';

interface AudioPlayerState {
  characterId: string;
  isPlaying: boolean;
  isLoading: boolean;
}

interface SelectedCharacter {
  id: string;
  characterName: string;
  gender: string;
  avatarUrl?: string;
}

interface HeroVoiceShowcaseProps {
  language: Language;
}

export function HeroVoiceShowcase({ language }: HeroVoiceShowcaseProps) {
  const [audioState, setAudioState] = useState<AudioPlayerState | null>(null);
  const [selectedCharacter, setSelectedCharacter] = useState<SelectedCharacter | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // 根据界面语言获取对应的语音角色
  const getLanguageCode = (lang: Language): string => {
    const languageMap: Record<Language, string> = {
      'en': 'en-US',
      'cn': 'zh-CN',
    };
    return languageMap[lang] || 'en-US';
  };

  const languageCode = getLanguageCode(language);

  // 获取性别图标
  const getGenderIcon = (gender: string) => {
    switch (gender) {
      case 'MALE':
        return '♂';
      case 'FEMALE':
        return '♀';
      default:
        return '⚪';
    }
  };

  // 获取当前语言的语音角色
  const { data: charactersData, isLoading } = api.voice.getCharacters.useQuery({
    languageCode,
  });

  // 获取试听文本
  const getDemoTextForLanguage = (langCode: string): string => {
    const demoTexts: Record<string, string> = {
      'zh-CN': '你好，我是AI语音助手',
      'en-US': 'Hello, I am an AI voice assistant',
      'km-KH': 'សួស្តី ខ្ញុំជាជំនួយការសំឡេងAI',
      'th-TH': 'สวัสดี ฉันเป็นผู้ช่วยเสียง AI',
      'vi-VN': 'Xin chào, tôi là trợ lý giọng nói AI',
    };
    return demoTexts[langCode] || demoTexts['en-US']!;
  };

  // 音频播放处理
  const handlePlayAudio = async (characterId: string) => {
    try {
      // 找到被点击的角色
      const character = charactersData?.find(c => c.id === characterId);
      if (character) {
        // 设置选中的角色
        setSelectedCharacter({
          id: character.id,
          characterName: character.characterName,
          gender: character.gender,
          avatarUrl: character.avatarUrl || undefined,
        });
      }

      // 停止当前播放的音频
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }

      // 如果点击的是正在播放的角色，则停止播放
      if (audioState?.characterId === characterId && audioState?.isPlaying) {
        setAudioState(null);
        return;
      }

      setAudioState({ characterId, isPlaying: false, isLoading: true });

      // 使用试听文本
      const demoText = getDemoTextForLanguage(languageCode);

      // 生成语音
      const result = await fetch('/api/tts/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: demoText,
          characterId: characterId,
          quality: 'fast', // 使用快速质量进行试听
        }),
      });

      if (!result.ok) {
        throw new Error('语音生成失败');
      }

      const data = await result.json();
      
      if (data.audioUrl) {
        // 播放音频
        const audio = new Audio(data.audioUrl);
        audioRef.current = audio;
        
        audio.onloadstart = () => {
          setAudioState({ characterId, isPlaying: false, isLoading: true });
        };
        
        audio.oncanplay = () => {
          setAudioState({ characterId, isPlaying: true, isLoading: false });
          audio.play();
        };
        
        audio.onended = () => {
          setAudioState(null);
        };
        
        audio.onerror = () => {
          setAudioState(null);
          console.error('音频播放失败');
        };
      }
    } catch (error) {
      console.error('播放音频失败:', error);
      setAudioState(null);
    }
  };

  // 清理音频
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  if (isLoading || !charactersData) {
    return (
      <div className="animate-fade-in-up animation-delay-1200">
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
        </div>
      </div>
    );
  }

  const characters = charactersData.slice(0, 30); // 确保最多30个角色
  const firstRow = characters.slice(0, 15);
  const secondRow = characters.slice(15, 30);

  const renderCharacterRow = (rowCharacters: any[], rowIndex: number) => (
    <div 
      key={rowIndex}
      className={`flex justify-center items-center gap-2 sm:gap-3 animate-fade-in-up animation-delay-${1200 + rowIndex * 200}`}
    >
      {rowCharacters.map((character, index) => (
        <div
          key={character.id}
          className="relative group"
        >
          <Button
            variant="ghost"
            size="sm"
            className={`relative p-0 h-12 w-12 sm:h-14 sm:w-14 rounded-full overflow-hidden hover:scale-110 transition-all duration-300 border-2 ${
              selectedCharacter?.id === character.id
                ? 'border-primary shadow-lg scale-105'
                : 'border-transparent hover:border-primary/30'
            }`}
            onClick={() => handlePlayAudio(character.id)}
            disabled={audioState?.isLoading}
          >
            <Avatar className="h-full w-full">
              <AvatarImage 
                src={character.avatarUrl || undefined} 
                alt={character.characterName}
                className="object-cover"
              />
              <AvatarFallback className="text-xs">
                <User className="h-4 w-4" />
              </AvatarFallback>
            </Avatar>
            
            {/* 播放状态覆盖层 */}
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full flex items-center justify-center">
              {audioState?.characterId === character.id && audioState?.isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin text-white" />
              ) : audioState?.characterId === character.id && audioState?.isPlaying ? (
                <Pause className="h-4 w-4 text-white" />
              ) : (
                <Play className="h-4 w-4 text-white" />
              )}
            </div>
          </Button>
          
          {/* 角色名称提示 */}
          <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
            <div className="bg-popover text-popover-foreground text-xs px-2 py-1 rounded whitespace-nowrap border shadow-md">
              {character.characterName}
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="animate-fade-in-up animation-delay-1200">
      <div className="space-y-4 sm:space-y-6">
        {/* 第一行 */}
        {firstRow.length > 0 && renderCharacterRow(firstRow, 0)}

        {/* 第二行 */}
        {secondRow.length > 0 && renderCharacterRow(secondRow, 1)}
      </div>

      {/* 选中角色信息 */}
      {selectedCharacter && (
        <div className="flex items-center justify-center mt-6 animate-fade-in-up animation-delay-1400">
          <div className="flex items-center gap-3 bg-card/80 backdrop-blur-sm rounded-full px-4 py-2 border border-primary/20">
            <Avatar className="h-8 w-8">
              <AvatarImage
                src={selectedCharacter.avatarUrl}
                alt={selectedCharacter.characterName}
                className="object-cover"
              />
              <AvatarFallback className="text-xs">
                <User className="h-4 w-4" />
              </AvatarFallback>
            </Avatar>
            <span className="font-medium text-sm text-card-foreground">{selectedCharacter.characterName}</span>
            <Badge variant="secondary" className="text-xs">
              {getGenderIcon(selectedCharacter.gender)} {selectedCharacter.gender === 'MALE' ? (language === 'en' ? 'Male' : '男性') : selectedCharacter.gender === 'FEMALE' ? (language === 'en' ? 'Female' : '女性') : (language === 'en' ? 'Neutral' : '中性')}
            </Badge>
          </div>
        </div>
      )}

      {/* 提示文字 */}
      <div className="text-center mt-6 animate-fade-in-up animation-delay-1600">
        <p className="text-sm text-muted-foreground">
          {language === 'en'
            ? 'Click on any avatar to hear a voice demo'
            : '点击任意头像试听语音效果'
          }
        </p>
      </div>
    </div>
  );
}
