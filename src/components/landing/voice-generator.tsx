"use client";

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '~/components/ui/button';
import { type Language } from '~/lib/translations';
import { ProfessionalSingleVoiceGenerator } from './professional-single-voice-generator';
import { ProfessionalMultiSpeakerGenerator } from './professional-multi-speaker-generator';
import {
  Maximize2,
  Minimize2,
  Users,
  User
} from 'lucide-react';

type GenerationMode = 'single' | 'dialogue';

interface VoiceGeneratorProps {
  language: Language;
  isGenerating: boolean;
  onGeneratingChange: (generating: boolean) => void;
}

export function VoiceGenerator({ language, isGenerating, onGeneratingChange }: VoiceGeneratorProps) {
  const { data: session } = useSession();
  const [mode, setMode] = useState<GenerationMode>('single');
  const [isFullscreen, setIsFullscreen] = useState(false);

  return (
    <div className={`mx-auto transition-all duration-300 ${isFullscreen ? 'fixed inset-0 z-50 bg-background p-4 overflow-auto' : 'max-w-6xl'}`}>
      {/* Header with Mode Switch and Fullscreen Toggle */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-bold text-foreground">{language === 'en' ? 'Voice Generator' : '语音生成器'}</h2>

          {/* Mode Switch */}
          <div className="flex items-center gap-2 p-1 bg-muted rounded-lg">
            <Button
              variant={mode === 'single' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setMode('single')}
              className="gap-2"
            >
              <User className="h-4 w-4" />
              {language === 'en' ? 'Single' : '单人'}
            </Button>
            <Button
              variant={mode === 'dialogue' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => {
                if (!session) {
                  // 访客用户点击多人对话时提示注册
                  alert(language === 'en'
                    ? 'Multi-speaker dialogue is available for registered users only. Please sign up for free!'
                    : '多人对话功能仅对注册用户开放，请免费注册！'
                  );
                  return;
                }
                setMode('dialogue');
              }}
              className="gap-2"
              disabled={!session}
            >
              <Users className="h-4 w-4" />
              {language === 'en' ? 'Dialogue' : '对话'}
              {!session && (
                <span className="text-xs bg-amber-100 text-amber-800 px-1 rounded">
                  {language === 'en' ? 'Pro' : '专业版'}
                </span>
              )}
            </Button>
          </div>
        </div>

        {/* Fullscreen Toggle */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsFullscreen(!isFullscreen)}
          className="gap-2"
        >
          {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          {isFullscreen ? (language === 'en' ? 'Exit' : '退出') : (language === 'en' ? 'Fullscreen' : '全屏')}
        </Button>
      </div>

      {/* Render appropriate component based on mode */}
      {mode === 'single' ? (
        <ProfessionalSingleVoiceGenerator language={language} />
      ) : (
        <ProfessionalMultiSpeakerGenerator language={language} />
      )}
    </div>
  );
}
