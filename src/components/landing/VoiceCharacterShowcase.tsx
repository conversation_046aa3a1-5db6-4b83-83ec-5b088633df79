"use client";

import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { t, type Locale, defaultLocale } from "~/lib/i18n";
import { Card, CardContent } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Play, Pause, Volume2, User, Loader2, Globe, Mic, ExternalLink, ArrowRight } from "lucide-react";
import { api } from "~/trpc/react";

interface AudioPlayerState {
  characterId: string;
  isPlaying: boolean;
  isLoading: boolean;
}

interface VoiceCharacterShowcaseProps {
  locale?: Locale;
}

export function VoiceCharacterShowcase({ locale = defaultLocale }: VoiceCharacterShowcaseProps) {
  const translate = (key: string, params?: Record<string, string | number>) => t(key, locale, params);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('all');
  const [audioState, setAudioState] = useState<AudioPlayerState | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // 获取启用的语言和角色
  const { data: showcaseData, isLoading } = api.voice.getActiveLanguagesWithCharacters.useQuery({
    limit: 9, // 显示9个角色
  });

  // 过滤角色
  const filteredCharacters = showcaseData?.characters?.filter(character => {
    if (selectedLanguage === 'all') return true;
    return character.language.code === selectedLanguage;
  }) || [];

  // 音频播放处理
  const handlePlayAudio = async (characterId: string, sampleText?: string) => {
    try {
      // 停止当前播放的音频
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }

      // 如果点击的是正在播放的角色，则停止播放
      if (audioState?.characterId === characterId && audioState?.isPlaying) {
        setAudioState(null);
        return;
      }

      setAudioState({ characterId, isPlaying: false, isLoading: true });

      // 获取角色信息
      const character = showcaseData?.characters.find(c => c.id === characterId);
      if (!character) {
        throw new Error('角色未找到');
      }

      // 使用默认试听文本
      const demoText = sampleText || getDemoTextForLanguage(character.language.code);

      // 生成语音
      const result = await fetch('/api/tts/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: demoText,
          characterId: character.id,
          quality: 'fast', // 使用快速质量进行试听
        }),
      });

      if (!result.ok) {
        throw new Error('语音生成失败');
      }

      const data = await result.json();
      
      if (data.audioUrl) {
        // 播放音频
        const audio = new Audio(data.audioUrl);
        audioRef.current = audio;
        
        audio.onloadstart = () => {
          setAudioState({ characterId, isPlaying: false, isLoading: true });
        };
        
        audio.oncanplay = () => {
          setAudioState({ characterId, isPlaying: true, isLoading: false });
          audio.play();
        };
        
        audio.onended = () => {
          setAudioState(null);
        };
        
        audio.onerror = () => {
          setAudioState(null);
          console.error('音频播放失败');
        };
      }
    } catch (error) {
      console.error('播放音频失败:', error);
      setAudioState(null);
    }
  };

  // 获取语言对应的试听文本
  const getDemoTextForLanguage = (languageCode: string): string => {
    const demoTexts: Record<string, string> = {
      'zh-CN': '你好，我是AI语音助手，很高兴为您服务。',
      'en-US': 'Hello, I am an AI voice assistant, nice to serve you.',
      'km-KH': 'សួស្តី ខ្ញុំជាជំនួយការសំឡេងAI រីករាយដែលបានបម្រើអ្នក។',
      'th-TH': 'สวัสดี ฉันเป็นผู้ช่วยเสียง AI ยินดีที่ได้ให้บริการคุณ',
      'vi-VN': 'Xin chào, tôi là trợ lý giọng nói AI, rất vui được phục vụ bạn.',
      'ja-JP': 'こんにちは、私はAI音声アシスタントです。お役に立てて嬉しいです。',
      'ko-KR': '안녕하세요, 저는 AI 음성 어시스턴트입니다. 서비스를 제공하게 되어 기쁩니다.',
      'ar-EG': 'مرحباً، أنا مساعد صوتي ذكي، سعيد لخدمتك.',
      'es-US': 'Hola, soy un asistente de voz AI, encantado de servirte.',
      'fr-FR': 'Bonjour, je suis un assistant vocal IA, ravi de vous servir.',
      'hi-IN': 'नमस्ते, मैं एक AI वॉयस असिस्टेंट हूं, आपकी सेवा करके खुशी हुई।',
    };
    
    return demoTexts[languageCode] || demoTexts['en-US'];
  };

  // 清理音频
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">{translate('common.loading')}</span>
      </div>
    );
  }

  if (!showcaseData || showcaseData.characters.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">{translate('characters.no_characters')}</p>
      </div>
    );
  }

  return (
    <section id="characters" className="py-16 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight mb-4">
            {translate('characters.title')}
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {translate('characters.subtitle')}
          </p>
          
          {/* Language Stats */}
          <div className="flex items-center justify-center gap-6 mt-6">
            <div className="flex items-center gap-2">
              <Globe className="h-5 w-5 text-primary" />
              <span className="text-sm font-medium">
                {showcaseData.totalLanguages} {translate('characters.languages')}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <User className="h-5 w-5 text-primary" />
              <span className="text-sm font-medium">
                {showcaseData.totalCharacters}+ {translate('characters.voice_characters')}
              </span>
            </div>
          </div>
        </div>

        {/* Language Filter */}
        <div className="max-w-4xl mx-auto mb-8">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div>
              <h3 className="text-lg font-semibold mb-1">{translate('characters.filter_by_language')}</h3>
              <p className="text-sm text-muted-foreground">
                {translate('characters.showing_characters', {
                  count: filteredCharacters.length,
                  total: showcaseData.characters.length
                })}
              </p>
            </div>

            <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
              <SelectTrigger className="w-full sm:w-64">
                <SelectValue placeholder={translate('characters.select_language')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    <span>{translate('characters.all_languages')}</span>
                    <Badge variant="secondary" className="text-xs">
                      {showcaseData.characters.length}
                    </Badge>
                  </div>
                </SelectItem>
                {showcaseData.languages.map((language) => (
                  <SelectItem key={language.code} value={language.code}>
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2">
                        <span>{language.flag}</span>
                        <span>{language.nativeName}</span>
                        <Badge variant="secondary" className="text-xs">
                          {language.characterCount}
                        </Badge>
                      </div>
                      <Link
                        href={`/languages/${language.code}`}
                        className="ml-2 opacity-60 hover:opacity-100 transition-opacity"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Link>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Characters List */}
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="p-0">
              <div className="divide-y divide-border">
                {filteredCharacters.map((character, index) => (
                  <div key={character.id} className="p-4 hover:bg-muted/50 transition-colors">
                    {/* Desktop Layout */}
                    <div className="hidden sm:flex items-center gap-4">
                      {/* Avatar */}
                      <Avatar className="h-16 w-16 flex-shrink-0">
                        <AvatarImage src={character.avatarUrl || undefined} />
                        <AvatarFallback>
                          <User className="h-8 w-8" />
                        </AvatarFallback>
                      </Avatar>

                      {/* Character Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-semibold text-lg truncate">{character.characterName}</h3>
                          <Badge variant="secondary" className="text-xs flex-shrink-0">
                            {translate(`characters.${character.gender.toLowerCase()}`)}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2 truncate">
                          {character.characterNameEn}
                        </p>

                        {/* Language */}
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{character.language.flag}</span>
                          <span className="text-sm font-medium">{character.language.nativeName}</span>
                          <span className="text-xs text-muted-foreground">({character.language.code})</span>
                        </div>
                      </div>

                      {/* Play Button */}
                      <div className="flex-shrink-0">
                        <Button
                          size="lg"
                          variant={audioState?.characterId === character.id && audioState?.isPlaying ? "default" : "outline"}
                          onClick={() => handlePlayAudio(character.id)}
                          disabled={audioState?.isLoading}
                          className="gap-2 min-w-[120px]"
                        >
                          {audioState?.characterId === character.id && audioState?.isLoading ? (
                            <>
                              <Loader2 className="h-4 w-4 animate-spin" />
                              <span>{translate('common.loading')}</span>
                            </>
                          ) : audioState?.characterId === character.id && audioState?.isPlaying ? (
                            <>
                              <Pause className="h-4 w-4" />
                              <span>{translate('characters.playing')}</span>
                            </>
                          ) : (
                            <>
                              <Play className="h-4 w-4" />
                              <span>{translate('characters.play_demo')}</span>
                            </>
                          )}
                        </Button>
                      </div>
                    </div>

                    {/* Mobile Layout */}
                    <div className="sm:hidden">
                      <div className="flex items-center gap-3 mb-3">
                        {/* Avatar */}
                        <Avatar className="h-12 w-12 flex-shrink-0">
                          <AvatarImage src={character.avatarUrl || undefined} />
                          <AvatarFallback>
                            <User className="h-6 w-6" />
                          </AvatarFallback>
                        </Avatar>

                        {/* Character Info */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-semibold truncate">{character.characterName}</h3>
                            <Badge variant="secondary" className="text-xs flex-shrink-0">
                              {translate(`characters.${character.gender.toLowerCase()}`)}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground truncate">
                            {character.characterNameEn}
                          </p>
                        </div>
                      </div>

                      {/* Language and Play Button */}
                      <div className="flex items-center justify-between gap-3">
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{character.language.flag}</span>
                          <span className="text-sm font-medium">{character.language.nativeName}</span>
                        </div>

                        <Button
                          size="sm"
                          variant={audioState?.characterId === character.id && audioState?.isPlaying ? "default" : "outline"}
                          onClick={() => handlePlayAudio(character.id)}
                          disabled={audioState?.isLoading}
                          className="gap-2 flex-shrink-0"
                        >
                          {audioState?.characterId === character.id && audioState?.isLoading ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : audioState?.characterId === character.id && audioState?.isPlaying ? (
                            <Pause className="h-4 w-4" />
                          ) : (
                            <Play className="h-4 w-4" />
                          )}
                          <span className="text-xs">
                            {audioState?.characterId === character.id && audioState?.isPlaying
                              ? translate('characters.playing')
                              : translate('characters.play_demo')
                            }
                          </span>
                        </Button>
                      </div>
                    </div>

                    {/* Optional: Style tags for desktop */}
                    {(character.style || character.personality) && (
                      <div className="hidden sm:block mt-3 pt-3 border-t border-border/50">
                        <div className="flex flex-wrap gap-1">
                          {character.style && (
                            <Badge variant="outline" className="text-xs">
                              {character.style}
                            </Badge>
                          )}
                          {character.personality && (
                            <Badge variant="outline" className="text-xs">
                              {Array.isArray(character.personality)
                                ? character.personality.slice(0, 2).join(', ')
                                : character.personality
                              }
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-12">
          <p className="text-muted-foreground mb-4">
            {translate('characters.cta_description')}
          </p>
          <Button asChild size="lg" className="gap-2">
            <a href="/demo">
              <Mic className="h-4 w-4" />
              {translate('characters.try_now')}
            </a>
          </Button>
        </div>

        {/* 体验更多语言 CTA */}
        <div className="max-w-4xl mx-auto mt-12">
          <Card className="bg-gradient-to-r from-primary/5 to-blue-50 border-primary/20">
            <CardContent className="p-8 text-center">
              <h3 className="text-xl font-bold mb-2">{translate('characters.explore_more_languages')}</h3>
              <p className="text-muted-foreground mb-6">
                {translate('characters.explore_description')}
              </p>

              <div className="flex flex-wrap justify-center gap-3 mb-6">
                {showcaseData.languages.slice(0, 5).map((language) => (
                  <Link key={language.code} href={`/languages/${language.code}`}>
                    <Button variant="outline" className="gap-2 hover:bg-primary/10">
                      <span>{language.flag}</span>
                      <span>{language.nativeName}</span>
                      <ArrowRight className="h-3 w-3" />
                    </Button>
                  </Link>
                ))}
              </div>

              <p className="text-sm text-muted-foreground">
                {translate('characters.total_languages_available', {
                  count: showcaseData.totalLanguages
                })}
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
