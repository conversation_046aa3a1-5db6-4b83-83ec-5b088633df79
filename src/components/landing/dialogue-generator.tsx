"use client";

import { useState } from 'react';
import { But<PERSON> } from '~/components/ui/button';
import { Textarea } from '~/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { Input } from '~/components/ui/input';
import { Badge } from '~/components/ui/badge';
import { type Language } from '~/lib/translations';
import { api } from '~/trpc/react';
import {
  Mic,
  Download,
  Share,
  Loader2,
  Plus,
  Trash2,
  Users
} from 'lucide-react';

interface Speaker {
  id: string;
  name: string;
  voiceId: string | null;
  text: string;
}

interface DialogueGeneratorProps {
  language: Language;
  isFullscreen?: boolean;
}

export function DialogueGenerator({ language, isFullscreen = false }: DialogueGeneratorProps) {
  const [speakers, setSpeakers] = useState<Speaker[]>([
    { id: '1', name: 'Speaker 1', voiceId: null, text: '' },
    { id: '2', name: 'Speaker 2', voiceId: null, text: '' }
  ]);
  const [selectedLanguage, setSelectedLanguage] = useState('en-US');
  const [selectedGender, setSelectedGender] = useState('all');
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  // API queries
  const { data: languagesData } = api.voice.getActiveLanguagesWithCharacters.useQuery();
  const { data: charactersData } = api.voice.getCharacters.useQuery({
    languageCode: selectedLanguage,
  });

  // Filter characters by gender
  const filteredCharacters = charactersData?.filter(character => 
    selectedGender === 'all' || character.gender?.toLowerCase() === selectedGender.toLowerCase()
  ) || [];

  const addSpeaker = () => {
    const newId = (speakers.length + 1).toString();
    setSpeakers([...speakers, {
      id: newId,
      name: `Speaker ${newId}`,
      voiceId: null,
      text: ''
    }]);
  };

  const removeSpeaker = (id: string) => {
    if (speakers.length > 2) {
      setSpeakers(speakers.filter(speaker => speaker.id !== id));
    }
  };

  const updateSpeaker = (id: string, updates: Partial<Speaker>) => {
    setSpeakers(speakers.map(speaker => 
      speaker.id === id ? { ...speaker, ...updates } : speaker
    ));
  };

  const handleGenerate = async () => {
    const validSpeakers = speakers.filter(s => s.text.trim() && s.voiceId);
    if (validSpeakers.length === 0) return;
    
    setIsGenerating(true);
    
    try {
      // TODO: Implement actual dialogue generation API call
      await new Promise(resolve => setTimeout(resolve, 3000)); // Simulate API call
      
      // Mock audio URL for now
      setAudioUrl('https://example.com/dialogue.mp3');
    } catch (error) {
      console.error('Generation failed:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const canGenerate = speakers.some(s => s.text.trim() && s.voiceId);

  return (
    <div className="space-y-6">
      {/* Speakers Section */}
      <Card>
        <CardContent className="pt-6 space-y-3">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              <span className="font-medium">{language === 'en' ? 'Dialogue Speakers' : '对话说话人'}</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={addSpeaker}
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              {language === 'en' ? 'Add Speaker' : '添加说话人'}
            </Button>
          </div>
          {speakers.map((speaker, index) => (
            <div key={speaker.id} className="border rounded-lg p-4 space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">{speaker.name}</Badge>
                  <Input
                    value={speaker.name}
                    onChange={(e) => updateSpeaker(speaker.id, { name: e.target.value })}
                    className="w-32 h-8"
                  />
                </div>
                {speakers.length > 2 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeSpeaker(speaker.id)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>

              <Textarea
                placeholder={language === 'en' ? `Enter ${speaker.name}'s dialogue...` : `输入${speaker.name}的对话...`}
                value={speaker.text}
                onChange={(e) => updateSpeaker(speaker.id, { text: e.target.value })}
                className="min-h-[80px] resize-none"
              />

              {/* Voice Selection for this speaker */}
              <div>
                <label className="text-sm font-medium mb-2 block">
                  {language === 'en' ? 'Voice Character' : '语音角色'}
                </label>
                <Select value={speaker.voiceId || ''} onValueChange={(value) => updateSpeaker(speaker.id, { voiceId: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === 'en' ? 'Select a voice character' : '选择语音角色'} />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredCharacters.map((character) => (
                      <SelectItem key={character.id} value={character.id}>
                        <div className="flex items-center gap-2">
                          <span>{character.gender?.toLowerCase() === 'female' ? '👩' : '👨'}</span>
                          <span>{character.characterName}</span>
                          <span className="text-xs text-muted-foreground">
                            {character.gender} • {character.style}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Voice Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                {language === 'en' ? 'Language' : '语言'}
              </label>
              <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {languagesData?.languages ? languagesData.languages.map((lang) => (
                    <SelectItem key={lang.id} value={lang.code}>
                      <div className="flex items-center gap-2">
                        <span>{lang.flag}</span>
                        <span>{lang.name}</span>
                      </div>
                    </SelectItem>
                  )) : (
                    <SelectItem value="en-US">English (US)</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">
                {language === 'en' ? 'Gender' : '性别'}
              </label>
              <Select value={selectedGender} onValueChange={setSelectedGender}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{language === 'en' ? 'All Genders' : '所有性别'}</SelectItem>
                  <SelectItem value="female">{language === 'en' ? 'Female' : '女性'}</SelectItem>
                  <SelectItem value="male">{language === 'en' ? 'Male' : '男性'}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Generate Button */}
      <div className="text-center">
        <Button
          size="lg"
          onClick={handleGenerate}
          disabled={!canGenerate || isGenerating}
          className="gap-3 px-8 py-4 text-lg font-semibold"
        >
          {isGenerating ? (
            <>
              <Loader2 className="h-5 w-5 animate-spin" />
              {language === 'en' ? 'Generating Dialogue...' : '生成对话中...'}
            </>
          ) : (
            <>
              <Mic className="h-5 w-5" />
              {language === 'en' ? 'Generate Dialogue' : '生成对话'}
            </>
          )}
        </Button>
      </div>

      {/* Audio Player Section */}
      {audioUrl && (
        <Card className="bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-700 dark:text-green-400">
                  {language === 'en' ? 'Dialogue Generated Successfully!' : '对话生成成功！'}
                </span>
              </div>

              <audio controls className="w-full max-w-md mx-auto rounded-lg">
                <source src={audioUrl} type="audio/mpeg" />
                Your browser does not support the audio element.
              </audio>

              <div className="flex justify-center gap-3">
                <Button variant="outline" size="sm" className="gap-2">
                  <Download className="h-4 w-4" />
                  {language === 'en' ? 'Download' : '下载'}
                </Button>
                <Button variant="outline" size="sm" className="gap-2">
                  <Share className="h-4 w-4" />
                  {language === 'en' ? 'Share' : '分享'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
