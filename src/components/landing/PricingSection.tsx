"use client";

import { useState } from "react";
import { t, type Locale, defaultLocale } from "~/lib/i18n";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Switch } from "~/components/ui/switch";
import { Check, Star } from "lucide-react";
import { api } from "~/trpc/react";
import Link from "next/link";

interface PricingSectionProps {
  locale?: Locale;
}

export function PricingSection({ locale = defaultLocale }: PricingSectionProps) {
  const translate = (key: string, params?: Record<string, string | number>) => t(key, locale, params);
  const [isYearly, setIsYearly] = useState(false);
  const [currency, setCurrency] = useState<'USD' | 'KHR'>('USD');

  // Fetch pricing plans
  const { data: pricingPlans, isLoading } = api.subscription.getPricingPlans.useQuery();

  // Exchange rate (approximate)
  const USD_TO_KHR = 4100;

  const formatPrice = (priceUsd: number, priceKhr?: number) => {
    if (currency === 'KHR') {
      const khrPrice = priceKhr || (priceUsd * USD_TO_KHR);
      return `៛${khrPrice.toLocaleString()}`;
    }
    return `$${priceUsd}`;
  };

  const getYearlyPrice = (monthlyPrice: number) => {
    return monthlyPrice * 12 * 0.8; // 20% discount for yearly
  };

  if (isLoading) {
    return (
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-96 mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <section className="py-16 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight mb-4">
            {t('pricing.title')}
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-8">
            {t('pricing.subtitle')}
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center gap-4 mb-6">
            <span className={`text-sm ${!isYearly ? 'font-medium' : 'text-muted-foreground'}`}>
              {t('pricing.monthly')}
            </span>
            <Switch
              checked={isYearly}
              onCheckedChange={setIsYearly}
            />
            <span className={`text-sm ${isYearly ? 'font-medium' : 'text-muted-foreground'}`}>
              {t('pricing.yearly')}
            </span>
            {isYearly && (
              <Badge variant="secondary" className="ml-2">
                {t('pricing.save_percent', { percent: 20 })}
              </Badge>
            )}
          </div>

          {/* Currency Toggle */}
          <div className="flex items-center justify-center gap-2">
            <Button
              variant={currency === 'USD' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setCurrency('USD')}
            >
              {t('pricing.currency_usd')}
            </Button>
            <Button
              variant={currency === 'KHR' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setCurrency('KHR')}
            >
              {t('pricing.currency_khr')}
            </Button>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
          {pricingPlans?.map((plan, index) => {
            const isPopular = plan.name === 'PROFESSIONAL';
            const isFree = plan.name === 'FREE';
            const isEnterprise = plan.name === 'ENTERPRISE';
            
            const monthlyPrice = plan.priceUsd;
            const yearlyPrice = getYearlyPrice(monthlyPrice);
            const displayPrice = isYearly ? yearlyPrice : monthlyPrice;
            
            const features = Array.isArray(plan.features) ? plan.features : [];

            return (
              <Card 
                key={plan.id} 
                className={`relative ${isPopular ? 'border-primary shadow-lg scale-105' : ''}`}
              >
                {isPopular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-primary text-primary-foreground px-3 py-1">
                      <Star className="h-3 w-3 mr-1" />
                      {t('pricing.most_popular')}
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-4">
                  <CardTitle className="text-xl">
                    {t(`pricing.plans.${plan.name.toLowerCase()}.name`)}
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    {t(`pricing.plans.${plan.name.toLowerCase()}.description`)}
                  </p>
                  
                  <div className="mt-4">
                    {isFree ? (
                      <div className="text-3xl font-bold">
                        {t('pricing.plans.free.name')}
                      </div>
                    ) : isEnterprise ? (
                      <div className="text-2xl font-bold">
                        {t('pricing.contact_sales')}
                      </div>
                    ) : (
                      <div>
                        <div className="text-3xl font-bold">
                          {formatPrice(displayPrice, plan.priceKhr ? (isYearly ? plan.priceKhr * 12 * 0.8 : plan.priceKhr) : undefined)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {isYearly ? t('pricing.per_year') : t('pricing.per_month')}
                        </div>
                      </div>
                    )}
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Features List */}
                  <div className="space-y-3">
                    {/* Monthly Quota */}
                    <div className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-green-500" />
                      <span className="text-sm">
                        {plan.monthlyQuota === -1 
                          ? t('pricing.features.unlimited_characters')
                          : t('pricing.features.characters', { count: plan.monthlyQuota.toLocaleString() })
                        }
                      </span>
                    </div>

                    {/* Flash TTS */}
                    {plan.flashTtsQuota > 0 && (
                      <div className="flex items-center gap-2">
                        <Check className="h-4 w-4 text-green-500" />
                        <span className="text-sm">{t('pricing.features.flash_tts')}</span>
                      </div>
                    )}

                    {/* Pro TTS */}
                    {plan.proTtsQuota > 0 && (
                      <div className="flex items-center gap-2">
                        <Check className="h-4 w-4 text-green-500" />
                        <span className="text-sm">{t('pricing.features.pro_tts')}</span>
                      </div>
                    )}

                    {/* Additional Features */}
                    {features.map((feature: string, featureIndex: number) => (
                      <div key={featureIndex} className="flex items-center gap-2">
                        <Check className="h-4 w-4 text-green-500" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* CTA Button */}
                  <div className="pt-4">
                    {isFree ? (
                      <Button asChild className="w-full">
                        <Link href="/auth/signin">
                          {t('pricing.free_trial')}
                        </Link>
                      </Button>
                    ) : isEnterprise ? (
                      <Button variant="outline" className="w-full">
                        {t('pricing.contact_sales')}
                      </Button>
                    ) : (
                      <Button 
                        asChild 
                        className={`w-full ${isPopular ? 'bg-primary' : ''}`}
                        variant={isPopular ? 'default' : 'outline'}
                      >
                        <Link href="/auth/signin">
                          {t('pricing.get_started')}
                        </Link>
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Additional Info */}
        <div className="text-center mt-12">
          <p className="text-sm text-muted-foreground">
            {t('pricing.features.commercial_license')} • {t('pricing.features.api_access')}
          </p>
        </div>
      </div>
    </section>
  );
}

export default PricingSection;
