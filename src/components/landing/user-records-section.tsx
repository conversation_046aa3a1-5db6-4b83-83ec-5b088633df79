"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { AudioPlayer } from "~/components/audio/audio-player";
import { useToast } from "~/hooks/use-toast";
import {
  History,
  Music,
  Trash2,
  Search,
  Sparkles
} from "lucide-react";
import { api } from "~/trpc/react";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";
import { getTranslation, type Language } from '~/lib/translations';

interface UserRecordsSectionProps {
  language: Language;
}

export function UserRecordsSection({ language }: UserRecordsSectionProps) {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedHistoryItems, setSelectedHistoryItems] = useState<string[]>([]);
  
  const t = getTranslation(language);

  // API 查询
  const { data: historyData, refetch: refetchHistory } = api.user.getActivity.useQuery({
    limit: 20,
    offset: 0,
  }, {
    enabled: !!session
  });

  // Mutations
  const deleteHistory = api.user.deleteHistory.useMutation({
    onSuccess: () => {
      toast({
        title: language === 'en' ? "Deleted successfully" : "删除成功",
        description: language === 'en' ? "Selected records have been deleted" : "选中的历史记录已删除",
      });
      setSelectedHistoryItems([]);
      refetchHistory();
    },
    onError: (error) => {
      toast({
        title: language === 'en' ? "Delete failed" : "删除失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // 过滤历史记录
  const filteredHistory = historyData?.generations.filter(item =>
    searchQuery === "" ||
    item.inputText.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.template?.originalName?.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  if (!session) {
    return null; // 未登录用户不显示此区域
  }

  return (
    <section id="user-records" className="py-16 bg-background">
      <div className="container mx-auto px-4">
        <div className="space-y-8">
          {/* 标题 */}
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-4">
              {language === 'en' ? 'My Account' : '我的账户'}
            </h2>
            <p className="text-muted-foreground">
              {language === 'en' ? 'Manage your credits and generation history' : '管理您的积分和生成记录'}
            </p>
          </div>

          {/* 用户信息卡片 */}
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-3">
                  <Avatar className="w-12 h-12">
                    <AvatarImage src={session?.user?.image || undefined} />
                    <AvatarFallback>
                      {session?.user?.name?.[0] || session?.user?.email?.[0] || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <span className="text-lg">{session?.user?.name || (language === 'en' ? 'User' : '用户')}</span>
                    <p className="text-sm text-muted-foreground font-normal">
                      {session?.user?.email}
                    </p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-center">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">
                      {language === 'en' ? 'Total Generations' : '总生成数'}
                    </span>
                    <Badge variant="secondary" className="text-base px-3 py-1">
                      {historyData?.totalCount || 0}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 历史记录 */}
          <Card className="max-w-6xl mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <History className="w-5 h-5" />
                <span>{language === 'en' ? 'Generation History' : '生成记录'}</span>
              </CardTitle>
              <CardDescription>
                {language === 'en' ? 'View and manage your voice generation history' : '查看和管理您的语音生成历史记录'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 搜索和操作栏 */}
              <div className="flex items-center space-x-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder={language === 'en' ? "Search history..." : "搜索历史记录..."}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                {selectedHistoryItems.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deleteHistory.mutate({ ids: selectedHistoryItems })}
                    disabled={deleteHistory.isPending}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    {language === 'en' ? `Delete Selected (${selectedHistoryItems.length})` : `删除选中 (${selectedHistoryItems.length})`}
                  </Button>
                )}
              </div>

              {/* 历史记录列表 */}
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {filteredHistory.length === 0 ? (
                  <div className="text-center py-8">
                    <Music className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">
                      {language === 'en' ? 'No generation history' : '暂无生成记录'}
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      {searchQuery 
                        ? (language === 'en' ? "No matching records found" : "没有找到符合条件的记录")
                        : (language === 'en' ? "You haven't generated any voice yet. Start creating!" : "您还没有生成过语音，去开始创作吧！")
                      }
                    </p>
                    {!searchQuery && (
                      <Button onClick={() => {
                        const element = document.getElementById('voice-generator');
                        if (element) {
                          element.scrollIntoView({ behavior: 'smooth' });
                        }
                      }}>
                        <Sparkles className="w-4 h-4 mr-2" />
                        {language === 'en' ? 'Start Generating' : '开始生成语音'}
                      </Button>
                    )}
                  </div>
                ) : (
                  filteredHistory.map((item) => (
                    <Card key={item.id} className="overflow-hidden">
                      <CardContent className="p-4">
                        <div className="flex items-start space-x-4">
                          {/* 选择框 */}
                          <input
                            type="checkbox"
                            checked={selectedHistoryItems.includes(item.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedHistoryItems([...selectedHistoryItems, item.id]);
                              } else {
                                setSelectedHistoryItems(selectedHistoryItems.filter(id => id !== item.id));
                              }
                            }}
                            className="mt-2"
                          />

                          {/* 角色头像 */}
                          <Avatar className="w-10 h-10">
                            <AvatarFallback>
                              {item.template?.originalName?.[0] || 'V'}
                            </AvatarFallback>
                          </Avatar>

                          {/* 内容区域 */}
                          <div className="flex-1 min-w-0 space-y-2">
                            <div className="flex items-start justify-between">
                              <div>
                                <h4 className="font-medium text-sm">
                                  {item.template?.originalName || (language === 'en' ? 'Unknown Character' : '未知角色')}
                                </h4>
                                <p className="text-xs text-muted-foreground">
                                  {format(new Date(item.createdAt), 'yyyy年MM月dd日 HH:mm', { locale: language === 'en' ? undefined : zhCN })}
                                </p>
                              </div>
                              <Badge variant="secondary" className="text-xs">
                                {item.template?.gender || (language === 'en' ? 'Unknown' : '未知')}
                              </Badge>
                            </div>

                            {/* 文本内容 */}
                            <div className="bg-muted/50 p-3 rounded-lg">
                              <p className="text-sm">{item.inputText}</p>
                            </div>

                            {/* 音频播放器 */}
                            {item.outputAudioUrl && (
                              <AudioPlayer
                                audioUrl={item.outputAudioUrl}
                                title={`${item.template?.originalName || (language === 'en' ? 'Voice' : '语音')} - ${item.inputText.slice(0, 20)}...`}
                                subtitle={format(new Date(item.createdAt), 'yyyy-MM-dd HH:mm')}
                                className="bg-background"
                              />
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
