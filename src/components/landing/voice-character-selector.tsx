"use client";

import { useState, useEffect } from 'react';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { getTranslation, type Language } from '~/lib/translations';
import { api } from '~/trpc/react';
import {
  Play,
  Pause,
  User,
  Filter,
  Loader2
} from 'lucide-react';

interface VoiceCharacterSelectorProps {
  language: Language;
  selectedCharacter: string | null;
  onCharacterSelect: (characterId: string) => void;
}

interface Character {
  id: string;
  characterName: string;
  characterNameEn: string;
  originalName: string;
  apiProvider: string;
  apiVoiceName: string;
  gender: 'MALE' | 'FEMALE' | 'NEUTRAL';
  description: string;
  style: string;
  personality: string[];
  bestFor: string[];
  avatarUrl?: string;
  isActive: boolean;
  sortOrder: number;
  isCustom: boolean;
  templateId?: string;
  languageId: string;
}

export function VoiceCharacterSelector({ 
  language, 
  selectedCharacter, 
  onCharacterSelect 
}: VoiceCharacterSelectorProps) {
  const [selectedLanguageCode, setSelectedLanguageCode] = useState<string>('en-US');
  const [playingCharacter, setPlayingCharacter] = useState<string | null>(null);
  const [genderFilter, setGenderFilter] = useState<string>('all');
  
  const t = getTranslation(language);

  // 获取活跃语言和角色
  const { data: languagesData, isLoading: languagesLoading } = api.voice.getActiveLanguagesWithCharacters.useQuery({
    limit: 8
  });

  // 获取特定语言的角色
  const { data: characters, isLoading: charactersLoading } = api.voice.getCharacters.useQuery({
    languageCode: selectedLanguageCode
  });

  // 设置默认语言 - 根据界面语言自动选择对应的角色语言
  useEffect(() => {
    if (language === 'cn') {
      setSelectedLanguageCode('zh-CN');
    } else {
      setSelectedLanguageCode('en-US');
    }
  }, [language]);

  const handlePlayPreview = async (characterId: string) => {
    if (playingCharacter === characterId) {
      setPlayingCharacter(null);
      // TODO: Stop audio playback
    } else {
      setPlayingCharacter(characterId);
      // TODO: Play audio preview
      
      // Simulate audio playback
      setTimeout(() => {
        setPlayingCharacter(null);
      }, 3000);
    }
  };

  const filteredCharacters = characters?.filter(character => {
    if (genderFilter === 'all') return true;
    return character.gender?.toLowerCase() === genderFilter.toLowerCase();
  }) || [];

  const getGenderIcon = (gender: string) => {
    switch (gender?.toLowerCase()) {
      case 'male':
        return '👨';
      case 'female':
        return '👩';
      default:
        return '👤';
    }
  };

  const getGenderColor = (gender: string) => {
    switch (gender?.toLowerCase()) {
      case 'male':
        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400';
      case 'female':
        return 'bg-pink-100 text-pink-700 dark:bg-pink-900/30 dark:text-pink-400';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  if (languagesLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">{t.common.loading}</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Language and Filter Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <label className="text-sm font-medium mb-2 block">
            {t.generator.filterByLanguage}
          </label>
          <Select value={selectedLanguageCode} onValueChange={setSelectedLanguageCode}>
            <SelectTrigger>
              <SelectValue placeholder={t.generator.allLanguages} />
            </SelectTrigger>
            <SelectContent>
              {languagesData?.languages ? languagesData.languages.map((lang) => (
                <SelectItem key={lang.id} value={lang.code}>
                  <div className="flex items-center gap-2">
                    <span>{lang.flag}</span>
                    <span>{lang.name}</span>
                  </div>
                </SelectItem>
              )) : (
                <SelectItem value="en-US">English (US)</SelectItem>
              )}
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex-1">
          <label className="text-sm font-medium mb-2 block">
            <Filter className="h-4 w-4 inline mr-1" />
            {language === 'en' ? 'Gender' : '性别'}
          </label>
          <Select value={genderFilter} onValueChange={setGenderFilter}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{language === 'en' ? 'All Genders' : '所有性别'}</SelectItem>
              <SelectItem value="male">{t.voices.maleVoice}</SelectItem>
              <SelectItem value="female">{t.voices.femaleVoice}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Character List */}
      {charactersLoading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">{t.common.loading}</span>
        </div>
      ) : filteredCharacters.length > 0 ? (
        <div className="h-96 overflow-y-auto border border-border rounded-lg bg-muted/50">
          <div className="space-y-2 p-2">
            {filteredCharacters.map((character) => (
              <div
                key={character.id}
                className={`flex items-center gap-4 p-3 rounded-lg cursor-pointer transition-all duration-300 hover:shadow-md ${
                  selectedCharacter === character.id
                    ? 'bg-primary/10 ring-2 ring-primary shadow-md'
                    : 'bg-card hover:bg-accent'
                }`}
                onClick={() => onCharacterSelect(character.id)}
              >
                {/* 头像 */}
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-100 to-blue-100 dark:from-purple-900/30 dark:to-blue-900/30 rounded-full flex items-center justify-center shadow-sm">
                    <span className="text-xl">
                      {getGenderIcon(character.gender || '')}
                    </span>
                  </div>
                </div>

                {/* 角色信息 */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold text-sm text-foreground truncate">
                      {character.characterName}
                    </h3>
                    <Badge
                      variant="secondary"
                      className={`text-xs flex-shrink-0 ${getGenderColor(character.gender || '')}`}
                    >
                      {character.gender || 'Unknown'}
                    </Badge>
                    {/* 风格标签 - 放在性别后面 */}
                    {character.style && (
                      <Badge variant="outline" className="text-xs flex-shrink-0">
                        {character.style}
                      </Badge>
                    )}
                  </div>

                  {/* 描述 */}
                  <p className="text-xs text-muted-foreground line-clamp-2">
                    {character.description}
                  </p>
                </div>

                {/* 试听播放按钮 */}
                <div className="flex-shrink-0">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handlePlayPreview(character.id);
                    }}
                    className={`h-10 w-10 p-0 rounded-full transition-all duration-300 ${
                      playingCharacter === character.id
                        ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                        : 'hover:bg-primary/10'
                    }`}
                  >
                    {playingCharacter === character.id ? (
                      <Pause className="h-4 w-4" />
                    ) : (
                      <Play className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="text-center py-8 text-muted-foreground">
          <User className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>{language === 'en' ? 'No characters found' : '未找到角色'}</p>
        </div>
      )}
    </div>
  );
}
