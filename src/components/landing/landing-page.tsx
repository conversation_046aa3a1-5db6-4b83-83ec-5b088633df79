"use client";

import { useState, useEffect } from 'react';
import { useSession, signOut } from "next-auth/react";

import { HeroSection } from './hero-section';
import { VoiceGenerator } from './voice-generator';
import { LanguageSwitcher } from './language-switcher';
import { CreditsSection } from './credits-section';
import { UserRecordsSection } from './user-records-section';

import { ThemeToggle } from '~/components/theme-toggle';
import { Button } from '~/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { LogOut, User, ChevronDown } from "lucide-react";
import { getTranslation, type Language } from '~/lib/translations';
import { UpgradeButtonFloating } from '~/components/credits/upgrade-button';

interface LandingPageProps {
  language: Language;
}

export function LandingPage({ language }: LandingPageProps) {
  const { data: session, status } = useSession();
  const [isGenerating, setIsGenerating] = useState(false);
  
  const t = getTranslation(language);

  // 不再自动重定向已登录用户，让他们在 landing 页面使用完整功能

  if (status === "loading") {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // 已登录用户也可以使用 landing 页面的完整功能

  const scrollToGenerator = () => {
    const element = document.getElementById('voice-generator');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleSignOut = () => {
    signOut({ callbackUrl: '/' });
  };

  const scrollToUserRecords = () => {
    const element = document.getElementById('user-records');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };



  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <nav className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-8">
              <a href={language === 'en' ? '/' : '/cn'} className="text-2xl font-bold text-primary">
                {t.nav.brand}
              </a>
            </div>
            <div className="flex items-center gap-4">
              {/* 生成语音按钮 */}
              <Button
                variant="outline"
                onClick={scrollToGenerator}
                className="hidden md:inline-flex"
              >
                {t.hero.scrollToGenerator}
              </Button>

              {/* 主题切换 */}
              <ThemeToggle />

              {/* 语言切换 */}
              <LanguageSwitcher currentLanguage={language} />

              {session ? (
                // 已登录用户显示头像下拉菜单
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="flex items-center gap-2 h-auto p-2">
                      <Avatar className="w-8 h-8">
                        <AvatarImage
                          src={session.user?.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(session.user?.name || session.user?.email || 'User')}&background=random`}
                          alt={session.user?.name || 'User'}
                        />
                        <AvatarFallback>
                          {session.user?.name?.[0] || session.user?.email?.[0] || 'U'}
                        </AvatarFallback>
                      </Avatar>
                      <div className="hidden sm:flex flex-col items-start">
                        <span className="text-sm font-medium">
                          {session.user?.name || session.user?.email?.split('@')[0]}
                        </span>
                      </div>
                      <ChevronDown className="w-3 h-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <div className="flex items-center gap-2 p-2">
                      <Avatar className="w-8 h-8">
                        <AvatarImage
                          src={session.user?.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(session.user?.name || session.user?.email || 'User')}&background=random`}
                          alt={session.user?.name || 'User'}
                        />
                        <AvatarFallback>
                          {session.user?.name?.[0] || session.user?.email?.[0] || 'U'}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <span className="text-sm font-medium">
                          {session.user?.name || (language === 'en' ? 'User' : '用户')}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {session.user?.email}
                        </span>
                      </div>
                    </div>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={scrollToUserRecords}>
                      <User className="w-4 h-4 mr-2" />
                      {language === 'en' ? 'My Records' : '我的记录'}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleSignOut}>
                      <LogOut className="w-4 h-4 mr-2" />
                      {language === 'en' ? 'Sign Out' : '退出登录'}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                // 未登录用户显示登录注册按钮
                <>
                  <Button variant="outline" asChild>
                    <a href="/auth/signin">
                      {t.nav.login}
                    </a>
                  </Button>
                  <Button asChild>
                    <a href="/auth/signup">
                      {t.nav.signup}
                    </a>
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <HeroSection 
        language={language} 
        onScrollToGenerator={scrollToGenerator}
      />

      {/* Voice Generator Section */}
      <section id="voice-generator" className="py-16 bg-muted/50">
        <div className="container mx-auto px-4">
          <VoiceGenerator
            language={language}
            isGenerating={isGenerating}
            onGeneratingChange={setIsGenerating}
          />
        </div>
      </section>

      {/* User Records Section - 仅已登录用户显示 */}
      {session && <UserRecordsSection language={language} />}

      {/* Credits Section */}
      <CreditsSection language={language} />

      {/* 浮动充值提醒 */}
      <UpgradeButtonFloating />
    </div>
  );
}
