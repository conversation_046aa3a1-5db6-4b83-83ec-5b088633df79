"use client";

import { useState } from "react";
import { api } from "~/trpc/react";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Progress } from "~/components/ui/progress";
import { 
  Sparkles, 
  Star, 
  Loader2, 
  CreditCard, 
  Zap,
  Crown,
  Gift,
  ArrowRight,
  Check,
  TrendingUp
} from "lucide-react";
import { toast } from "sonner";
import { cn } from "~/lib/utils";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { getTranslation, type Language } from '~/lib/translations';

interface CreditsSectionProps {
  language: Language;
}

export function CreditsSection({ language }: CreditsSectionProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
  const [isCreatingPurchase, setIsCreatingPurchase] = useState(false);

  const t = getTranslation(language);

  // 获取用户积分余额
  const { data: balance } = api.credits.getBalance.useQuery(undefined, {
    enabled: !!session
  });

  // 获取积分包列表
  const { data: creditPackages } = api.creditPurchase.getPackages.useQuery();

  // 创建购买订单
  const createPurchaseMutation = api.creditPurchase.createPurchase.useMutation({
    onSuccess: (data) => {
      toast.success(language === 'en' ? 'Order created successfully' : '订单创建成功', {
        description: language === 'en' 
          ? `Order: ${data.purchase.orderNumber}`
          : `订单号：${data.purchase.orderNumber}`,
      });
      // 跳转到支付页面
      window.location.href = `/payment/${data.payment.id}`;
    },
    onError: (error) => {
      toast.error(language === 'en' ? 'Failed to create order' : '创建订单失败', {
        description: error.message,
      });
    },
    onSettled: () => {
      setIsCreatingPurchase(false);
      setSelectedPackage(null);
    },
  });

  const handlePurchase = async (packageId: string) => {
    if (!session) {
      toast.error(language === 'en' ? 'Please sign in first' : '请先登录');
      router.push('/auth/signin');
      return;
    }

    setSelectedPackage(packageId);
    setIsCreatingPurchase(true);

    try {
      await createPurchaseMutation.mutateAsync({
        packageId,
        paymentProvider: 'PAYPAL',
      });
    } catch (error) {
      // Error handled in mutation
    }
  };

  // 计算使用百分比
  const usagePercentage = balance 
    ? Math.round((balance.used / balance.total) * 100)
    : 0;

  const remainingCredits = balance ? balance.total - balance.used : 0;
  const isLowCredits = remainingCredits < 100;

  return (
    <section id="credits" className="py-16 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20">
      <div className="container mx-auto px-4">
        {/* 标题 */}
        <div className="text-center space-y-6 mb-12">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-700 dark:text-blue-300 text-sm font-medium">
            <Sparkles className="w-4 h-4" />
            {language === 'en' ? 'Credit Packages' : '积分充值'}
          </div>
          <h2 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            {language === 'en' ? 'Upgrade Your Experience' : '升级您的语音生成体验'}
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {language === 'en' 
              ? 'Choose the perfect credit package for your voice generation needs'
              : '选择适合您的积分包，享受更多高质量语音生成服务'
            }
          </p>
        </div>

        {/* 当前积分状态 - 仅登录用户显示 */}
        {session && balance && (
          <Card className="mb-8 border-2 border-blue-200 dark:border-blue-800 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 justify-center">
                <CreditCard className="w-5 h-5 text-blue-600" />
                {language === 'en' ? 'Current Credits' : '当前积分状态'}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <div className={cn(
                  "text-3xl font-bold",
                  isLowCredits ? "text-red-600" : "text-blue-600"
                )}>
                  {remainingCredits.toLocaleString()}
                </div>
                <p className="text-sm text-muted-foreground">
                  {language === 'en' ? 'Available Credits' : '可用积分'}
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{language === 'en' ? 'Usage' : '使用进度'}</span>
                  <span>{usagePercentage}%</span>
                </div>
                <Progress value={usagePercentage} className="h-2" />
              </div>

              {isLowCredits && (
                <div className="bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300 p-3 rounded-lg text-sm text-center">
                  {language === 'en' 
                    ? '⚠️ Low credits! Consider upgrading for uninterrupted service.'
                    : '⚠️ 积分不足！建议及时充值以确保服务正常使用。'
                  }
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* 积分包网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {creditPackages?.map((pkg) => {
            const isPopular = pkg.name.includes('专业') || pkg.name.includes('标准') || pkg.name.includes('Professional') || pkg.name.includes('Standard');
            const isSelected = selectedPackage === pkg.id;
            const isLoading = isCreatingPurchase && isSelected;

            return (
              <Card 
                key={pkg.id} 
                className={cn(
                  "relative transition-all duration-300 hover:shadow-lg cursor-pointer",
                  isPopular && "border-2 border-blue-500 shadow-lg scale-105",
                  isSelected && "ring-2 ring-blue-500"
                )}
                onClick={() => !isLoading && handlePurchase(pkg.id)}
              >
                {isPopular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-3 py-1">
                      <Star className="w-3 h-3 mr-1" />
                      {language === 'en' ? 'Popular' : '推荐'}
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-4">
                  <div className="flex items-center justify-center mb-2">
                    {(pkg.name.includes('入门') || pkg.name.includes('Basic')) && <Gift className="w-8 h-8 text-green-500" />}
                    {(pkg.name.includes('基础') || pkg.name.includes('Starter')) && <Zap className="w-8 h-8 text-blue-500" />}
                    {(pkg.name.includes('标准') || pkg.name.includes('Standard')) && <Star className="w-8 h-8 text-purple-500" />}
                    {(pkg.name.includes('专业') || pkg.name.includes('Professional')) && <Crown className="w-8 h-8 text-yellow-500" />}
                    {(pkg.name.includes('企业') || pkg.name.includes('Enterprise')) && <Sparkles className="w-8 h-8 text-red-500" />}
                  </div>
                  <CardTitle className="text-xl">{pkg.name}</CardTitle>
                  <div className="text-3xl font-bold text-blue-600">
                    ${pkg.price}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {pkg.credits.toLocaleString()} {language === 'en' ? 'Credits' : '积分'}
                  </p>
                </CardHeader>

                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-sm text-muted-foreground">
                      {language === 'en' 
                        ? `~${Math.floor(pkg.credits / 10).toLocaleString()} voice generations`
                        : `约可生成 ${Math.floor(pkg.credits / 10).toLocaleString()} 次语音`
                      }
                    </div>
                    <div className="text-xs text-green-600 mt-1">
                      {language === 'en' 
                        ? `$${(pkg.price / pkg.credits).toFixed(4)} per credit`
                        : `每积分约 $${(pkg.price / pkg.credits).toFixed(4)}`
                      }
                    </div>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <Check className="w-4 h-4 text-green-500" />
                      <span>{language === 'en' ? 'All voice characters' : '支持所有语音角色'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Check className="w-4 h-4 text-green-500" />
                      <span>{language === 'en' ? 'High quality audio' : '高质量音频输出'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Check className="w-4 h-4 text-green-500" />
                      <span>{language === 'en' ? 'No time limits' : '无使用时间限制'}</span>
                    </div>
                    {isPopular && (
                      <div className="flex items-center gap-2">
                        <Check className="w-4 h-4 text-green-500" />
                        <span className="text-blue-600 font-medium">
                          {language === 'en' ? 'Priority support' : '优先客服支持'}
                        </span>
                      </div>
                    )}
                  </div>

                  <Button 
                    className={cn(
                      "w-full transition-all duration-300",
                      isPopular 
                        ? "bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600" 
                        : "bg-blue-600 hover:bg-blue-700"
                    )}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        {language === 'en' ? 'Creating...' : '创建订单中...'}
                      </>
                    ) : (
                      <>
                        {language === 'en' ? 'Purchase Now' : '立即购买'}
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* 使用说明 */}
        <Card className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 justify-center">
              <TrendingUp className="w-5 h-5" />
              {language === 'en' ? 'How Credits Work' : '积分使用说明'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-semibold text-blue-600">
                  {language === 'en' ? 'Credit Usage' : '积分消耗规则'}
                </h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• {language === 'en' ? 'Standard quality: ~1 credit/100 chars' : '标准质量：约 1 积分/100字符'}</li>
                  <li>• {language === 'en' ? 'High quality: ~2 credits/100 chars' : '高质量：约 2 积分/100字符'}</li>
                  <li>• {language === 'en' ? 'Multi-speaker: calculated by total chars' : '多人对话：按总字符数计算'}</li>
                  <li>• {language === 'en' ? 'Credits never expire' : '积分永不过期'}</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-green-600">
                  {language === 'en' ? 'Purchase Benefits' : '购买优势'}
                </h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• {language === 'en' ? 'Secure PayPal payment' : '支持PayPal安全支付'}</li>
                  <li>• {language === 'en' ? 'Instant credit delivery' : '即时到账，无需等待'}</li>
                  <li>• {language === 'en' ? '7-day money back guarantee' : '7天无理由退款'}</li>
                  <li>• {language === 'en' ? '24/7 customer support' : '24/7客服支持'}</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}
