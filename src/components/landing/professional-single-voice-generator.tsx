"use client";

import React, { useState, useRef } from "react";
import { api } from "~/trpc/react";
import { useSession } from "next-auth/react";
import { Card, CardContent } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Textarea } from "~/components/ui/textarea";
import { Slider } from "~/components/ui/slider";
import { Label } from "~/components/ui/label";
import { VoiceCharacterSelector } from "~/components/voice-character-selector";
import { LanguageSelector } from "~/components/language-selector";
import { SingleSpeakerStyleSelector, type SingleSpeakerStyleSelectorRef } from "~/components/single-speaker-style-selector";
import { WaveformPlayer } from "~/components/waveform-player";
import { useAudioPlayer } from "~/contexts/AudioPlayerContext";
import { TextGenerationDialog } from "~/components/text-generation-dialog";
import { VoiceGenerationStats } from "~/app/_components/voice-generation-stats";
import { Guest<PERSON>rialBanner, GuestTrialLimitDialog } from "~/components/guest-trial-banner";
import { useGuestTrial } from "~/hooks/useGuestTrial";
import {
  Play,
  Volume2,
  Settings,
  Mic,
  Loader2,
  User,
  Zap,
  Crown,
  Globe,
  Palette,
  Sparkles
} from "lucide-react";
import { toast } from "sonner";
import { type Language } from '~/lib/translations';

interface ProfessionalSingleVoiceGeneratorProps {
  language: Language;
}

export function ProfessionalSingleVoiceGenerator({ language }: ProfessionalSingleVoiceGeneratorProps) {
  const { data: session } = useSession();
  const styleSelectorRef = useRef<SingleSpeakerStyleSelectorRef>(null);
  const { stopAllOthers } = useAudioPlayer();

  // 访客试用功能
  const { canGenerate, recordGeneration, isExhausted, getRemainingQuota, getLimits } = useGuestTrial();
  const [showLimitDialog, setShowLimitDialog] = useState(false);
  const [limitType, setLimitType] = useState<"daily" | "characters" | "total">("daily");

  const [selectedCharacterId, setSelectedCharacterId] = useState<string>("");
  const [selectedLanguageCode, setSelectedLanguageCode] = useState("en-US");
  const [inputText, setInputText] = useState("");
  const [speed, setSpeed] = useState([1.0]);
  const [pitch, setPitch] = useState([0]);
  const [volume, setVolume] = useState([1.0]);
  const [quality, setQuality] = useState<'fast' | 'high'>('fast');
  const [stylePrompt, setStylePrompt] = useState("");
  const [showTextGenDialog, setShowTextGenDialog] = useState(false);
  const [selectedStyle, setSelectedStyle] = useState<any>(null);

  const [generatedAudio, setGeneratedAudio] = useState<{
    audioUrl?: string;
    audioData?: string;
    character: { characterName: string; characterNameEn: string };
    characterCount: number;
    duration?: number;
    cost?: number;
  } | null>(null);
  
  const [generationProgress, setGenerationProgress] = useState<{
    stage: 'idle' | 'validating' | 'generating' | 'uploading' | 'complete' | 'error';
    message: string;
    progress: number;
  }>({
    stage: 'idle',
    message: '',
    progress: 0,
  });

  // 获取角色列表
  const { data: characters, isLoading: charactersLoading } = api.voice.getCharacters.useQuery({
    languageCode: selectedLanguageCode,
  });

  // 获取用户配额信息
  const { data: usageStats } = api.tts.getUsageStats.useQuery();

  // 默认选择第一个角色
  React.useEffect(() => {
    if (characters && characters.length > 0 && !selectedCharacterId) {
      setSelectedCharacterId(characters[0]!.id);
    }
  }, [characters, selectedCharacterId]);

  // 当语言改变时，重置角色选择并自动选择新语言的默认角色
  React.useEffect(() => {
    if (characters && characters.length > 0) {
      const currentCharacterExists = characters.some(char => char.id === selectedCharacterId);
      if (!currentCharacterExists) {
        setSelectedCharacterId(characters[0]!.id);
      }
    } else {
      setSelectedCharacterId("");
    }
  }, [selectedLanguageCode, characters]);

  // 风格选择处理函数
  const handleStyleSelect = (style: any) => {
    setSelectedStyle(style);
    setStylePrompt(style.prompt);
  };

  const handleStyleClear = () => {
    setSelectedStyle(null);
    setStylePrompt("");
  };

  // 生成语音
  const generateSpeech = api.tts.generateSpeech.useMutation({
    onMutate: () => {
      setGenerationProgress({
        stage: 'validating',
        message: language === 'en' ? 'Validating input...' : '正在验证输入...',
        progress: 10,
      });
    },
    onSuccess: (data) => {
      setGenerationProgress({
        stage: 'complete',
        message: language === 'en' ? 'Generation complete!' : '生成完成！',
        progress: 100,
      });
      setGeneratedAudio(data);

      // 如果是访客用户，记录使用情况
      if (!session) {
        try {
          recordGeneration(inputText.trim().length);
          const remaining = getRemainingQuota();

          if (remaining.dailyGenerations <= 1 || remaining.totalCharacters <= 50) {
            toast.success(language === 'en' ? 'Generation complete!' : '生成完成！', {
              description: language === 'en'
                ? `${remaining.dailyGenerations} generations left today. Sign up for unlimited access!`
                : `今日还剩 ${remaining.dailyGenerations} 次生成机会。注册即可无限使用！`,
              action: {
                label: language === 'en' ? 'Sign Up' : '注册',
                onClick: () => window.location.href = '/auth/signup'
              }
            });
          }
        } catch (error) {
          console.error('Failed to record guest generation:', error);
        }
      }

      setTimeout(() => {
        setGenerationProgress({
          stage: 'idle',
          message: '',
          progress: 0,
        });
      }, 3000);
    },
    onError: (error) => {
      setGenerationProgress({
        stage: 'error',
        message: language === 'en' ? `Generation failed: ${error.message}` : `生成失败：${error.message}`,
        progress: 0,
      });

      toast.error(language === 'en' ? 'Voice generation failed' : '语音生成失败', {
        description: error.message,
        duration: 6000,
      });

      setTimeout(() => {
        setGenerationProgress({
          stage: 'idle',
          message: '',
          progress: 0,
        });
      }, 5000);
    },
  });

  const handleGenerate = () => {
    if (!selectedCharacterId || !inputText.trim()) {
      toast.error(language === 'en' ? 'Please select a character and enter text' : '请选择角色并输入文本');
      return;
    }

    const textLength = inputText.trim().length;

    // 如果用户未登录，检查访客试用限制
    if (!session) {
      const limits = getLimits();

      // 检查是否超出单次字符限制
      if (textLength > limits.maxCharactersPerGeneration) {
        setLimitType("characters");
        setShowLimitDialog(true);
        return;
      }

      // 检查是否可以生成
      if (!canGenerate(textLength)) {
        if (isExhausted()) {
          setLimitType("total");
        } else {
          setLimitType("daily");
        }
        setShowLimitDialog(true);
        return;
      }

      // 检查质量限制（访客只能使用标准质量）
      if (quality !== 'fast') {
        toast.error(language === 'en' ? 'Guest users can only use standard quality' : '访客只能使用标准质量', {
          description: language === 'en' ? 'Sign up for free to unlock professional quality' : '免费注册即可解锁专业质量',
          action: {
            label: language === 'en' ? 'Sign Up' : '注册',
            onClick: () => window.location.href = '/auth/signup'
          }
        });
        return;
      }
    }

    stopAllOthers('');

    const simulateProgress = () => {
      setTimeout(() => {
        setGenerationProgress({
          stage: 'generating',
          message: language === 'en' ? 'Generating voice...' : '正在生成语音...',
          progress: 30,
        });
      }, 500);

      setTimeout(() => {
        setGenerationProgress({
          stage: 'generating',
          message: language === 'en' ? 'Synthesizing speech...' : '语音合成中...',
          progress: 60,
        });
      }, 2000);

      setTimeout(() => {
        setGenerationProgress({
          stage: 'uploading',
          message: language === 'en' ? 'Saving audio...' : '正在保存音频...',
          progress: 90,
        });
      }, 4000);
    };

    simulateProgress();

    generateSpeech.mutate({
      text: inputText,
      characterId: selectedCharacterId,
      speed: speed[0],
      pitch: pitch[0],
      volumeGainDb: ((volume[0] ?? 1.0) - 1.0) * 10,
      format: 'WAV',
      quality,
      stylePrompt: stylePrompt.trim() || undefined,
    });
  };

  // 对于 landing 页面，允许未登录用户查看界面，但在生成时提示登录

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      {/* 访客试用横幅 */}
      {!session && <GuestTrialBanner />}

      {/* 三栏布局 - 调整宽度比例为 3-6-3 */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 lg:gap-6">
        {/* 左栏：语言选择、语音角色选择和语音设置 */}
        <div className="lg:col-span-3 space-y-4 lg:space-y-6">
          {/* 语言选择 */}
          <Card className="p-4">
            <div className="space-y-3">
              <h3 className="flex items-center gap-2 text-sm font-medium">
                <Globe className="h-4 w-4" />
                {language === 'en' ? 'Select Language' : '选择语言'}
              </h3>
              <LanguageSelector
                selectedLanguageCode={selectedLanguageCode}
                onLanguageSelect={setSelectedLanguageCode}
              />
            </div>
          </Card>

          {/* 角色选择 */}
          <Card className="p-4">
            <div className="space-y-3">
              <h3 className="flex items-center gap-2 text-sm font-medium">
                <User className="h-4 w-4" />
                {language === 'en' ? 'Select Voice Character' : '选择语音角色'}
              </h3>
              {charactersLoading ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  <span className="text-sm text-muted-foreground">
                    {language === 'en' ? 'Loading...' : '加载中...'}
                  </span>
                </div>
              ) : characters && characters.length > 0 ? (
                <VoiceCharacterSelector
                  characters={characters as any}
                  selectedCharacterId={selectedCharacterId}
                  onSelect={setSelectedCharacterId}
                  selectedLanguageCode={selectedLanguageCode}
                />
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-muted-foreground">
                    {language === 'en' ? 'No characters available' : '暂无可用角色'}
                  </p>
                </div>
              )}
            </div>
          </Card>

          {/* 语音设置 */}
          <Card className="p-4">
            <div className="space-y-3">
              <h3 className="flex items-center gap-2 text-sm font-medium">
                <Settings className="h-4 w-4" />
                {language === 'en' ? 'Voice Settings' : '语音设置'}
              </h3>
              
              {/* 质量选择 */}
              <div className="space-y-2">
                <Label className="text-xs font-medium">
                  {language === 'en' ? 'Quality' : '质量'}
                </Label>
                <div className="space-y-1">
                  <div
                    className={`p-2 border rounded cursor-pointer transition-all ${
                      quality === 'fast'
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => setQuality('fast')}
                  >
                    <div className="flex items-center gap-2">
                      <Zap className="h-3 w-3" />
                      <span className="text-xs font-medium">
                        {language === 'en' ? 'Standard (Fast)' : '标准（快速）'}
                      </span>
                    </div>
                  </div>
                  <div
                    className={`p-2 border rounded cursor-pointer transition-all ${
                      quality === 'high'
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => setQuality('high')}
                  >
                    <div className="flex items-center gap-2">
                      <Crown className="h-3 w-3" />
                      <span className="text-xs font-medium">
                        {language === 'en' ? 'Professional (High Quality)' : '专业（高质量）'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 语音参数 */}
              <div className="space-y-3">
                <div className="space-y-1">
                  <Label className="text-xs">
                    {language === 'en' ? `Speed: ${speed[0]}x` : `语速: ${speed[0]}x`}
                  </Label>
                  <Slider
                    value={speed}
                    onValueChange={setSpeed}
                    max={2.0}
                    min={0.5}
                    step={0.1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-1">
                  <Label className="text-xs">
                    {language === 'en' 
                      ? `Pitch: ${(pitch[0] ?? 0) > 0 ? `+${pitch[0] ?? 0}` : `${pitch[0] ?? 0}`}`
                      : `音调: ${(pitch[0] ?? 0) > 0 ? `+${pitch[0] ?? 0}` : `${pitch[0] ?? 0}`}`
                    }
                  </Label>
                  <Slider
                    value={pitch}
                    onValueChange={setPitch}
                    max={10}
                    min={-10}
                    step={1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-1">
                  <Label className="text-xs">
                    {language === 'en' 
                      ? `Volume: ${((volume[0] ?? 1.0) * 100).toFixed(0)}%`
                      : `音量: ${((volume[0] ?? 1.0) * 100).toFixed(0)}%`
                    }
                  </Label>
                  <Slider
                    value={volume}
                    onValueChange={setVolume}
                    max={1.5}
                    min={0.3}
                    step={0.1}
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* 中栏：输入文本和风格提示 */}
        <div className="lg:col-span-6 space-y-4 lg:space-y-6">
          {/* 文本输入 */}
          <Card className="p-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="flex items-center gap-2 text-sm font-medium">
                  <Mic className="h-4 w-4" />
                  {language === 'en' ? 'Input Text' : '输入文本'}
                </h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowTextGenDialog(true)}
                  className="text-blue-600 border-blue-200 hover:bg-blue-50"
                >
                  <Sparkles className="h-4 w-4 mr-2" />
                  {language === 'en' ? 'AI Generate' : 'AI生成文本'}
                </Button>
              </div>
              <Textarea
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder={language === 'en' 
                  ? 'Enter the text you want to convert to speech...'
                  : '请输入您想要转换为语音的高质量文本...'
                }
                className="min-h-[180px] lg:min-h-[250px] max-h-[400px] resize-y text-sm overflow-y-auto"
                maxLength={6000}
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{inputText.length}/6000 {language === 'en' ? 'characters' : '字符'}</span>
              </div>
            </div>
          </Card>

          {/* 生成按钮和进度 */}
          <Card className="p-4">
            <div className="space-y-3">
              <Button
                onClick={handleGenerate}
                disabled={!selectedCharacterId || !inputText.trim() || generationProgress.stage !== 'idle'}
                className="w-full"
                size="default"
              >
                {generationProgress.stage === 'idle' ? (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    {language === 'en' ? 'Generate Speech' : '生成语音'}
                  </>
                ) : generationProgress.stage === 'error' ? (
                  <>
                    <span className="mr-2">❌</span>
                    {language === 'en' ? 'Generation Failed' : '生成失败'}
                  </>
                ) : generationProgress.stage === 'complete' ? (
                  <>
                    <span className="mr-2">✅</span>
                    {language === 'en' ? 'Generation Complete' : '生成完成'}
                  </>
                ) : (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {generationProgress.message}
                  </>
                )}
              </Button>

              {/* 进度条 */}
              {generationProgress.stage !== 'idle' && (
                <div className="space-y-2">
                  <div className="flex justify-between text-xs">
                    <span className="text-muted-foreground">{generationProgress.message}</span>
                    <span className="text-muted-foreground">{generationProgress.progress}%</span>
                  </div>
                  <div className="w-full bg-secondary rounded-full h-1.5">
                    <div
                      className={`h-1.5 rounded-full transition-all duration-500 ${
                        generationProgress.stage === 'error'
                          ? 'bg-red-500'
                          : generationProgress.stage === 'complete'
                          ? 'bg-green-500'
                          : 'bg-primary'
                      }`}
                      style={{ width: `${generationProgress.progress}%` }}
                    />
                  </div>

                  {/* 进度阶段指示器 */}
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span className={generationProgress.stage === 'validating' ? 'text-primary font-medium' : ''}>
                      {language === 'en' ? 'Validating' : '验证中'}
                    </span>
                    <span className={generationProgress.stage === 'generating' ? 'text-primary font-medium' : ''}>
                      {language === 'en' ? 'Generating' : '生成中'}
                    </span>
                    <span className={generationProgress.stage === 'uploading' ? 'text-primary font-medium' : ''}>
                      {language === 'en' ? 'Outputting' : '输出中'}
                    </span>
                    <span className={generationProgress.stage === 'complete' ? 'text-green-600 font-medium' : ''}>
                      {language === 'en' ? 'Complete' : '完成'}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </Card>

          {/* 风格选择 */}
          <Card className="p-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="flex items-center gap-2 text-sm font-medium">
                  <Palette className="h-4 w-4" />
                  {language === 'en' ? 'Voice Style' : '语音风格'}
                </h3>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs h-6 px-2"
                  onClick={() => {
                    styleSelectorRef.current?.openDialog();
                  }}
                >
                  <Palette className="h-3 w-3 mr-1" />
                  {language === 'en' ? 'Quick Select' : '快速选择风格'}
                </Button>
              </div>

              <SingleSpeakerStyleSelector
                ref={styleSelectorRef}
                selectedStyleId={selectedStyle?.id}
                onStyleSelect={handleStyleSelect}
                onStyleClear={handleStyleClear}
              />

              <div className="space-y-2">
                <Textarea
                  placeholder={language === 'en' ? 'Describe the voice style you want...' : '描述您想要的语音风格...'}
                  value={stylePrompt}
                  onChange={(e) => setStylePrompt(e.target.value)}
                  className="min-h-[80px] text-sm"
                  maxLength={200}
                />
                <div className="text-right text-xs text-muted-foreground">
                  <span>{stylePrompt.length}/200</span>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* 右栏：生成结果、使用统计 */}
        <div className="lg:col-span-3 space-y-4 lg:space-y-6">
          {/* 生成结果 */}
          <Card className={`p-4 transition-all duration-300 ${
            generatedAudio
              ? 'border-green-200 bg-gradient-to-br from-green-50 to-blue-50 dark:border-green-800 dark:from-green-900/20 dark:to-blue-900/20 shadow-lg'
              : 'border-gray-200 bg-gradient-to-br from-gray-50 to-gray-100 dark:border-gray-700 dark:from-gray-800 dark:to-gray-900'
          }`}>
            <div className="space-y-3">
              <h3 className={`flex items-center gap-2 text-sm font-medium transition-colors ${
                generatedAudio
                  ? 'text-green-700 dark:text-green-300'
                  : 'text-muted-foreground'
              }`}>
                <Volume2 className="h-4 w-4" />
                {generatedAudio 
                  ? (language === 'en' ? 'Generation Success' : '生成成功')
                  : (language === 'en' ? 'Generation Result' : '生成结果')
                }
              </h3>

              <div className="space-y-3">
                {/* 波纹播放器 */}
                {generatedAudio?.audioUrl ? (
                  <WaveformPlayer
                    audioUrl={generatedAudio.audioUrl}
                    className="w-full"
                    playerId="admin-generate-main"
                  />
                ) : (
                  <div className="flex items-center justify-center h-16 border border-dashed border-muted-foreground/30 rounded-lg bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800">
                    <div className="text-center">
                      <div className="text-sm text-muted-foreground">
                        {language === 'en' ? 'Waiting for audio generation' : '等待生成音频'}
                      </div>
                      <div className="text-xs text-muted-foreground/70 mt-1">
                        {language === 'en' ? 'Audio will appear here after generation' : '音频生成后将在此处显示'}
                      </div>
                    </div>
                  </div>
                )}

                {/* 生成信息 */}
                <div className="space-y-2 text-xs">
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">
                      {language === 'en' ? 'Character Name:' : '角色名称:'}
                    </span>
                    <span className={`font-medium ${
                      generatedAudio
                        ? 'text-foreground'
                        : 'text-muted-foreground'
                    }`}>
                      {generatedAudio ? (() => {
                        const currentCharacter = characters?.find(c => c.characterNameEn === generatedAudio.character.characterNameEn);
                        if (currentCharacter) {
                          return currentCharacter.characterName;
                        }
                        return generatedAudio.character.characterName;
                      })() : (() => {
                        const currentCharacter = characters?.find(c => c.id === selectedCharacterId);
                        return currentCharacter ? currentCharacter.characterName : (language === 'en' ? 'Not selected' : '未选择');
                      })()}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">
                      {language === 'en' ? 'Format:' : '生成格式:'}
                    </span>
                    <span className={`font-medium ${
                      generatedAudio
                        ? 'text-foreground'
                        : 'text-muted-foreground'
                    }`}>WAV</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">
                      {language === 'en' ? 'Character Count:' : '字符数量:'}
                    </span>
                    <span className={`font-medium ${
                      generatedAudio
                        ? 'text-foreground'
                        : 'text-muted-foreground'
                    }`}>
                      {generatedAudio 
                        ? `${generatedAudio.characterCount} ${language === 'en' ? 'characters' : '字符'}` 
                        : `${inputText.length} ${language === 'en' ? 'characters' : '字符'}`
                      }
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">
                      {language === 'en' ? 'Quality:' : '质量:'}
                    </span>
                    <span className={`font-medium ${
                      generatedAudio
                        ? 'text-foreground'
                        : 'text-muted-foreground'
                    }`}>
                      {quality === 'high' 
                        ? (language === 'en' ? 'High Quality' : '高质量')
                        : (language === 'en' ? 'Standard Quality' : '标准质量')
                      }
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* 使用统计 */}
          <VoiceGenerationStats showRefresh={true} />
        </div>
      </div>

      {/* AI文本生成对话框 */}
      <TextGenerationDialog
        isOpen={showTextGenDialog}
        onClose={() => setShowTextGenDialog(false)}
        onGenerate={(text) => setInputText(text)}
      />

      {/* 访客试用限制对话框 */}
      <GuestTrialLimitDialog
        isOpen={showLimitDialog}
        onClose={() => setShowLimitDialog(false)}
        limitType={limitType}
      />
    </div>
  );
}
