"use client";

import React, { useState, useRef, useMemo } from "react";
import { Card } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Textarea } from "~/components/ui/textarea";
import { Label } from "~/components/ui/label";
import { Badge } from "~/components/ui/badge";
import { Slider } from "~/components/ui/slider";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { MultiSpeakerStyleSelector, type MultiSpeakerStyleSelectorRef } from "~/components/multi-speaker-style-selector";
import { LanguageSelector } from "~/components/language-selector";
import { VoiceCharacterSelector } from "~/components/voice-character-selector";
import { VoiceGenerationStats } from "~/app/_components/voice-generation-stats";
import { Users, Wand2, Volume2, Settings, Play, Trash2, Plus, Palette, FileText, Mic, Loader2, User, Zap, Crown, Search, Sparkles } from "lucide-react";
import { api } from "~/trpc/react";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { ConversationGenerationDialog } from "~/components/conversation-generation-dialog";
import { WaveformPlayer } from "~/components/waveform-player";
import { useAudioPlayer } from "~/contexts/AudioPlayerContext";
import { useSession } from "next-auth/react";
import { toast } from "sonner";
import { type Language } from '~/lib/translations';

interface Speaker {
  id: string;
  name: string;
  characterId?: string;
  styleId?: string;
  color: string;
  avatar?: string;
}

interface DialogueLine {
  speaker: string;
  text: string;
  lineNumber: number;
}

interface AudioSegment {
  id: string;
  speaker: string;
  text: string;
  audioUrl?: string;
  duration?: number;
  status: 'pending' | 'generating' | 'completed' | 'error';
  cost?: number;
  characterCount?: number;
  segmentCount?: number;
}

interface DialogueTemplate {
  id: string;
  name: string;
  description: string;
  content: string;
  speakers: string[];
}

interface ProfessionalMultiSpeakerGeneratorProps {
  language: Language;
}

export function ProfessionalMultiSpeakerGenerator({ language }: ProfessionalMultiSpeakerGeneratorProps) {
  const { data: session } = useSession();
  const styleSelectorRef = useRef<MultiSpeakerStyleSelectorRef>(null);
  const { stopAllOthers } = useAudioPlayer();

  // 基础状态
  const [selectedLanguageCode, setSelectedLanguageCode] = useState<string>("en-US");

  // 获取语言列表
  api.voice.getLanguages.useQuery();

  // 根据选择的语言获取角色列表
  const { data: characters } = api.voice.getCharacters.useQuery({
    languageCode: selectedLanguageCode,
  });

  // 获取所有对话模板 - 只有登录用户才能访问
  const { data: allConversationTemplates, error: templatesError } = api.conversation.getTemplates.useQuery({
    includePublic: true,
  }, {
    enabled: !!session, // 只有登录时才查询
  });

  // 默认对话模板（用于未登录用户）
  const defaultTemplates = useMemo(() => {
    const isEnglish = !selectedLanguageCode.startsWith('zh');

    if (isEnglish) {
      return [
        {
          id: 'default-1',
          name: 'Casual Conversation',
          description: 'A friendly chat between two people',
          content: 'Speaker 1: Hi there! How are you doing today?\nSpeaker 2: I\'m doing great, thanks for asking! How about you?\nSpeaker 1: I\'m wonderful! It\'s such a beautiful day outside.\nSpeaker 2: Yes, it really is! Perfect weather for a walk.',
          speakers: ['Speaker 1', 'Speaker 2']
        },
        {
          id: 'default-2',
          name: 'Business Meeting',
          description: 'Professional discussion about a project',
          content: 'Manager: Good morning everyone. Let\'s discuss the quarterly results.\nEmployee: The numbers look very promising this quarter.\nManager: That\'s excellent news. What were the key factors?\nEmployee: Our marketing campaign and customer service improvements made a big difference.',
          speakers: ['Manager', 'Employee']
        },
        {
          id: 'default-3',
          name: 'Customer Service',
          description: 'Helpful customer support interaction',
          content: 'Customer: Hello, I need help with my order.\nAgent: I\'d be happy to help you with that. What seems to be the issue?\nCustomer: I haven\'t received my package yet.\nAgent: Let me check the tracking information for you right away.',
          speakers: ['Customer', 'Agent']
        }
      ];
    } else {
      return [
        {
          id: 'default-1',
          name: '日常对话',
          description: '两人之间的友好聊天',
          content: '说话人1：你好！今天过得怎么样？\n说话人2：很好，谢谢你的关心！你呢？\n说话人1：我也很好！今天天气真不错。\n说话人2：是的，确实很棒！很适合散步。',
          speakers: ['说话人1', '说话人2']
        },
        {
          id: 'default-2',
          name: '商务会议',
          description: '关于项目的专业讨论',
          content: '经理：大家早上好。我们来讨论一下季度业绩。\n员工：这个季度的数据看起来很有希望。\n经理：这是个好消息。主要因素是什么？\n员工：我们的营销活动和客户服务改进起到了很大作用。',
          speakers: ['经理', '员工']
        },
        {
          id: 'default-3',
          name: '客户服务',
          description: '有用的客户支持互动',
          content: '客户：你好，我需要帮助处理我的订单。\n客服：我很乐意为您提供帮助。请问遇到了什么问题？\n客户：我还没有收到我的包裹。\n客服：让我立即为您查看物流信息。',
          speakers: ['客户', '客服']
        }
      ];
    }
  }, [selectedLanguageCode]);

  // 在前端根据语言过滤模板
  const conversationTemplates = useMemo(() => {
    // 如果用户未登录或没有模板数据，使用默认模板
    if (!session || !allConversationTemplates) {
      return defaultTemplates;
    }

    const filtered = allConversationTemplates.filter(template => {
      // 如果模板有languageCode字段，使用它进行过滤
      if (template.languageCode) {
        return template.languageCode === selectedLanguageCode;
      }

      // 如果没有languageCode字段，根据内容判断
      try {
        const dialogues = template.dialogues;
        if (dialogues.length > 0) {
          const firstDialogue = dialogues[0];
          const text = firstDialogue.text || '';

          // 简单的语言检测：如果包含中文字符，认为是中文模板
          const hasChinese = /[\u4e00-\u9fff]/.test(text);

          if (selectedLanguageCode.startsWith('zh') && hasChinese) {
            return true;
          } else if (!selectedLanguageCode.startsWith('zh') && !hasChinese) {
            return true;
          }
        }
        return false;
      } catch (error) {
        console.error('Error filtering template:', error);
        return false;
      }
    });

    // 如果没有找到匹配的模板，返回默认模板
    return filtered.length > 0 ? filtered : defaultTemplates;
  }, [allConversationTemplates, selectedLanguageCode, session, defaultTemplates]);

  // 说话人管理
  const [speakers, setSpeakers] = useState<Speaker[]>([
    { id: '1', name: language === 'en' ? 'Speaker 1' : '说话人1', color: '#3B82F6', characterId: undefined },
    { id: '2', name: language === 'en' ? 'Speaker 2' : '说话人2', color: '#EF4444', characterId: undefined },
  ]);

  // 对话内容
  const [dialogueText, setDialogueText] = useState("");
  const [parsedDialogues, setParsedDialogues] = useState<DialogueLine[]>([]);

  // 语音设置
  const [speed, setSpeed] = useState([1.0]);
  const [pitch, setPitch] = useState([0]);
  const [volume, setVolume] = useState([1.0]);
  const [quality, setQuality] = useState<'fast' | 'high'>('fast');

  // 生成状态
  const [audioSegments, setAudioSegments] = useState<AudioSegment[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);

  // 对话框状态
  const [showConversationDialog, setShowConversationDialog] = useState(false);
  const [showTemplateDialog, setShowTemplateDialog] = useState(false);

  // 搜索状态
  const [templateSearchTerm, setTemplateSearchTerm] = useState("");

  // 过滤模板
  const filteredTemplates = conversationTemplates.filter(template =>
    template.name.toLowerCase().includes(templateSearchTerm.toLowerCase()) ||
    template.description.toLowerCase().includes(templateSearchTerm.toLowerCase())
  );

  // 对于 landing 页面，允许未登录用户查看界面，但在生成时提示登录

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      {/* 四栏布局 - 调整宽度比例为 3-6-3 */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 lg:gap-6">

        {/* 左栏：语言选择和说话人管理 */}
        <div className="lg:col-span-3 space-y-4 lg:space-y-6">
          {/* 语言选择 */}
          <Card className="p-4">
            <div className="space-y-3">
              <h3 className="flex items-center gap-2 text-sm font-medium">
                <Settings className="h-4 w-4" />
                {language === 'en' ? 'Language Settings' : '语言设置'}
              </h3>
              <LanguageSelector
                selectedLanguageCode={selectedLanguageCode}
                onLanguageSelect={setSelectedLanguageCode}
              />
            </div>
          </Card>

          {/* 说话人管理 */}
          <Card className="p-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="flex items-center gap-2 text-sm font-medium">
                  <Users className="h-4 w-4" />
                  {language === 'en' ? 'Speakers' : '说话人管理'}
                </h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newId = (speakers.length + 1).toString();
                    setSpeakers([...speakers, {
                      id: newId,
                      name: language === 'en' ? `Speaker ${newId}` : `说话人${newId}`,
                      color: `hsl(${Math.random() * 360}, 70%, 50%)`,
                      characterId: undefined,
                    }]);
                  }}
                  className="text-xs h-6 px-2"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  {language === 'en' ? 'Add' : '添加'}
                </Button>
              </div>

              <div className="space-y-2">
                {speakers.map((speaker, index) => (
                  <div key={speaker.id} className="flex items-center gap-2 p-2 border rounded-lg">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: speaker.color }}
                    />
                    <Input
                      value={speaker.name}
                      onChange={(e) => {
                        const newSpeakers = [...speakers];
                        newSpeakers[index] = { ...speaker, name: e.target.value };
                        setSpeakers(newSpeakers);
                      }}
                      className="flex-1 h-6 text-xs"
                    />
                    {speakers.length > 2 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSpeakers(speakers.filter(s => s.id !== speaker.id));
                        }}
                        className="h-6 w-6 p-0"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </Card>

          {/* 语音设置 */}
          <Card className="p-4">
            <div className="space-y-3">
              <h3 className="flex items-center gap-2 text-sm font-medium">
                <Settings className="h-4 w-4" />
                {language === 'en' ? 'Voice Settings' : '语音设置'}
              </h3>
              
              {/* 质量选择 */}
              <div className="space-y-2">
                <Label className="text-xs font-medium">
                  {language === 'en' ? 'Quality' : '质量'}
                </Label>
                <div className="space-y-1">
                  <div
                    className={`p-2 border rounded cursor-pointer transition-all ${
                      quality === 'fast'
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => setQuality('fast')}
                  >
                    <div className="flex items-center gap-2">
                      <Zap className="h-3 w-3" />
                      <span className="text-xs font-medium">
                        {language === 'en' ? 'Standard (Fast)' : '标准（快速）'}
                      </span>
                    </div>
                  </div>
                  <div
                    className={`p-2 border rounded cursor-pointer transition-all ${
                      quality === 'high'
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => setQuality('high')}
                  >
                    <div className="flex items-center gap-2">
                      <Crown className="h-3 w-3" />
                      <span className="text-xs font-medium">
                        {language === 'en' ? 'Professional (High Quality)' : '专业（高质量）'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 语音参数 */}
              <div className="space-y-3">
                <div className="space-y-1">
                  <Label className="text-xs">
                    {language === 'en' ? `Speed: ${speed[0]}x` : `语速: ${speed[0]}x`}
                  </Label>
                  <Slider
                    value={speed}
                    onValueChange={setSpeed}
                    max={2.0}
                    min={0.5}
                    step={0.1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-1">
                  <Label className="text-xs">
                    {language === 'en' 
                      ? `Pitch: ${(pitch[0] ?? 0) > 0 ? `+${pitch[0] ?? 0}` : `${pitch[0] ?? 0}`}`
                      : `音调: ${(pitch[0] ?? 0) > 0 ? `+${pitch[0] ?? 0}` : `${pitch[0] ?? 0}`}`
                    }
                  </Label>
                  <Slider
                    value={pitch}
                    onValueChange={setPitch}
                    max={10}
                    min={-10}
                    step={1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-1">
                  <Label className="text-xs">
                    {language === 'en' 
                      ? `Volume: ${((volume[0] ?? 1.0) * 100).toFixed(0)}%`
                      : `音量: ${((volume[0] ?? 1.0) * 100).toFixed(0)}%`
                    }
                  </Label>
                  <Slider
                    value={volume}
                    onValueChange={setVolume}
                    max={1.5}
                    min={0.3}
                    step={0.1}
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* 中栏：对话内容和模板 */}
        <div className="lg:col-span-6 space-y-4 lg:space-y-6">
          {/* 对话模板选择 */}
          <Card className="p-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="flex items-center gap-2 text-sm font-medium">
                  <FileText className="h-4 w-4" />
                  {language === 'en' ? 'Dialogue Templates' : '对话模板'}
                </h3>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowConversationDialog(true)}
                    className="text-primary border-primary/20 hover:bg-primary/10"
                  >
                    <Sparkles className="h-4 w-4 mr-2" />
                    {language === 'en' ? 'AI Generate' : 'AI生成对话'}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowTemplateDialog(true)}
                    className="text-xs h-6 px-2"
                  >
                    <Search className="h-3 w-3 mr-1" />
                    {language === 'en' ? 'Browse Templates' : '浏览模板'}
                  </Button>
                </div>
              </div>

              {/* 快速模板选择 */}
              {filteredTemplates.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {filteredTemplates.slice(0, 3).map((template) => (
                    <Button
                      key={template.id}
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setDialogueText(template.content);
                        // 解析对话内容
                        const lines = template.content.split('\n').filter(line => line.trim());
                        const parsed: DialogueLine[] = [];
                        lines.forEach((line, index) => {
                          const match = line.match(/^(.+?)[:：]\s*(.+)$/);
                          if (match) {
                            parsed.push({
                              speaker: match[1].trim(),
                              text: match[2].trim(),
                              lineNumber: index + 1,
                            });
                          }
                        });
                        setParsedDialogues(parsed);
                      }}
                      className="text-xs"
                    >
                      {template.name}
                    </Button>
                  ))}
                </div>
              )}
            </div>
          </Card>

          {/* 对话内容输入 */}
          <Card className="p-4">
            <div className="space-y-3">
              <h3 className="flex items-center gap-2 text-sm font-medium">
                <Mic className="h-4 w-4" />
                {language === 'en' ? 'Dialogue Content' : '对话内容'}
              </h3>
              <Textarea
                value={dialogueText}
                onChange={(e) => {
                  setDialogueText(e.target.value);
                  // 实时解析对话
                  const lines = e.target.value.split('\n').filter(line => line.trim());
                  const parsed: DialogueLine[] = [];
                  lines.forEach((line, index) => {
                    const match = line.match(/^(.+?)[:：]\s*(.+)$/);
                    if (match) {
                      parsed.push({
                        speaker: match[1].trim(),
                        text: match[2].trim(),
                        lineNumber: index + 1,
                      });
                    }
                  });
                  setParsedDialogues(parsed);
                }}
                placeholder={language === 'en'
                  ? 'Enter dialogue in format:\nSpeaker 1: Hello, how are you?\nSpeaker 2: I\'m doing great, thanks!'
                  : '请输入对话内容，格式如下：\n说话人1：你好，最近怎么样？\n说话人2：我很好，谢谢！'
                }
                className="min-h-[200px] lg:min-h-[300px] max-h-[400px] resize-y text-sm overflow-y-auto"
                maxLength={10000}
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>
                  {language === 'en'
                    ? `${parsedDialogues.length} dialogue lines parsed`
                    : `已解析 ${parsedDialogues.length} 行对话`
                  }
                </span>
                <span>{dialogueText.length}/10000 {language === 'en' ? 'characters' : '字符'}</span>
              </div>
            </div>
          </Card>

          {/* 对话预览 */}
          {parsedDialogues.length > 0 && (
            <Card className="p-4">
              <div className="space-y-3">
                <h3 className="flex items-center gap-2 text-sm font-medium">
                  <Volume2 className="h-4 w-4" />
                  {language === 'en' ? 'Dialogue Preview' : '对话预览'}
                </h3>
                <div className="space-y-2 max-h-[200px] overflow-y-auto">
                  {parsedDialogues.map((dialogue, index) => {
                    const speaker = speakers.find(s => s.name === dialogue.speaker);
                    return (
                      <div key={index} className="flex items-start gap-2 p-2 border rounded-lg">
                        <div
                          className="w-3 h-3 rounded-full mt-1 flex-shrink-0"
                          style={{ backgroundColor: speaker?.color || '#6B7280' }}
                        />
                        <div className="flex-1 min-w-0">
                          <div className="text-xs font-medium text-muted-foreground">
                            {dialogue.speaker}
                          </div>
                          <div className="text-sm">{dialogue.text}</div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </Card>
          )}

          {/* 生成按钮 */}
          <Card className="p-4">
            <div className="space-y-3">
              <Button
                onClick={() => {
                  if (parsedDialogues.length === 0) {
                    toast.error(language === 'en' ? 'Please enter dialogue content' : '请输入对话内容');
                    return;
                  }

                  if (!session) {
                    toast.error(language === 'en' ? 'Please sign in to generate voice' : '请登录后生成语音', {
                      description: language === 'en' ? 'You need to create an account to use voice generation' : '您需要创建账户才能使用语音生成功能',
                      action: {
                        label: language === 'en' ? 'Sign In' : '登录',
                        onClick: () => window.location.href = '/api/auth/signin'
                      }
                    });
                    return;
                  }

                  // 检查是否所有说话人都有对应的角色
                  const speakerNames = [...new Set(parsedDialogues.map(d => d.speaker))];
                  const missingCharacters = speakerNames.filter(name => {
                    const speaker = speakers.find(s => s.name === name);
                    return !speaker?.characterId;
                  });

                  if (missingCharacters.length > 0) {
                    toast.error(
                      language === 'en'
                        ? `Please assign voice characters to: ${missingCharacters.join(', ')}`
                        : `请为以下说话人分配语音角色：${missingCharacters.join('、')}`
                    );
                    return;
                  }

                  toast.success(language === 'en' ? 'Starting generation...' : '开始生成...');
                }}
                disabled={parsedDialogues.length === 0 || isGenerating}
                className="w-full"
                size="default"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {language === 'en' ? 'Generating...' : '生成中...'}
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    {language === 'en' ? 'Generate Dialogue' : '生成对话'}
                  </>
                )}
              </Button>

              {/* 进度条 */}
              {isGenerating && (
                <div className="space-y-2">
                  <div className="flex justify-between text-xs">
                    <span className="text-muted-foreground">
                      {language === 'en' ? 'Generating dialogue...' : '正在生成对话...'}
                    </span>
                    <span className="text-muted-foreground">{generationProgress}%</span>
                  </div>
                  <div className="w-full bg-secondary rounded-full h-1.5">
                    <div
                      className="h-1.5 rounded-full transition-all duration-500 bg-primary"
                      style={{ width: `${generationProgress}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* 右栏：角色分配和生成结果 */}
        <div className="lg:col-span-3 space-y-4 lg:space-y-6">
          {/* 角色分配 */}
          <Card className="p-4">
            <div className="space-y-3">
              <h3 className="flex items-center gap-2 text-sm font-medium">
                <User className="h-4 w-4" />
                {language === 'en' ? 'Character Assignment' : '角色分配'}
              </h3>
              <div className="space-y-3">
                {speakers.map((speaker) => (
                  <div key={speaker.id} className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: speaker.color }}
                      />
                      <span className="text-xs font-medium">{speaker.name}</span>
                    </div>
                    {characters && characters.length > 0 ? (
                      <VoiceCharacterSelector
                        characters={characters as any}
                        selectedCharacterId={speaker.characterId || ""}
                        onSelect={(characterId) => {
                          const newSpeakers = speakers.map(s =>
                            s.id === speaker.id ? { ...s, characterId } : s
                          );
                          setSpeakers(newSpeakers);
                        }}
                        selectedLanguageCode={selectedLanguageCode}
                        compact={true}
                      />
                    ) : (
                      <div className="text-xs text-muted-foreground p-2 border rounded">
                        {language === 'en' ? 'No characters available' : '暂无可用角色'}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </Card>

          {/* 生成结果 */}
          <Card className={`p-4 transition-all duration-300 ${
            audioSegments.length > 0
              ? 'border-green-200 bg-gradient-to-br from-green-50 to-blue-50 dark:border-green-800 dark:from-green-900/20 dark:to-blue-900/20 shadow-lg'
              : 'border-gray-200 bg-gradient-to-br from-gray-50 to-gray-100 dark:border-gray-700 dark:from-gray-800 dark:to-gray-900'
          }`}>
            <div className="space-y-3">
              <h3 className={`flex items-center gap-2 text-sm font-medium transition-colors ${
                audioSegments.length > 0
                  ? 'text-green-700 dark:text-green-300'
                  : 'text-muted-foreground'
              }`}>
                <Volume2 className="h-4 w-4" />
                {audioSegments.length > 0
                  ? (language === 'en' ? 'Generated Audio' : '生成的音频')
                  : (language === 'en' ? 'Generation Result' : '生成结果')
                }
              </h3>

              <div className="space-y-3">
                {audioSegments.length > 0 ? (
                  <div className="space-y-2 max-h-[300px] overflow-y-auto">
                    {audioSegments.map((segment) => (
                      <div key={segment.id} className="p-2 border rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="text-xs font-medium">{segment.speaker}</div>
                          <Badge variant={
                            segment.status === 'completed' ? 'default' :
                            segment.status === 'generating' ? 'secondary' :
                            segment.status === 'error' ? 'destructive' : 'outline'
                          }>
                            {segment.status === 'completed' ? (language === 'en' ? 'Complete' : '完成') :
                             segment.status === 'generating' ? (language === 'en' ? 'Generating' : '生成中') :
                             segment.status === 'error' ? (language === 'en' ? 'Error' : '错误') :
                             (language === 'en' ? 'Pending' : '等待中')}
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground mb-2">
                          {segment.text.substring(0, 50)}
                          {segment.text.length > 50 && '...'}
                        </div>
                        {segment.audioUrl && (
                          <WaveformPlayer
                            audioUrl={segment.audioUrl}
                            className="w-full"
                            playerId={`multi-speaker-${segment.id}`}
                          />
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-32 border border-dashed border-muted-foreground/30 rounded-lg bg-muted/20">
                    <div className="text-center">
                      <div className="text-sm text-muted-foreground">
                        {language === 'en' ? 'Waiting for dialogue generation' : '等待对话生成'}
                      </div>
                      <div className="text-xs text-muted-foreground/70 mt-1">
                        {language === 'en' ? 'Audio segments will appear here' : '音频片段将在此处显示'}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Card>

          {/* 使用统计 */}
          <VoiceGenerationStats showRefresh={true} />
        </div>
      </div>

      {/* 对话框 */}
      <ConversationGenerationDialog
        isOpen={showConversationDialog}
        onClose={() => setShowConversationDialog(false)}
        onGenerate={(conversation) => {
          setDialogueText(conversation);
          // 解析生成的对话
          const lines = conversation.split('\n').filter(line => line.trim());
          const parsed: DialogueLine[] = [];
          lines.forEach((line, index) => {
            const match = line.match(/^(.+?)[:：]\s*(.+)$/);
            if (match) {
              parsed.push({
                speaker: match[1].trim(),
                text: match[2].trim(),
                lineNumber: index + 1,
              });
            }
          });
          setParsedDialogues(parsed);
        }}
      />

      {/* 模板浏览对话框 */}
      <Dialog open={showTemplateDialog} onOpenChange={setShowTemplateDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>
              {language === 'en' ? 'Browse Dialogue Templates' : '浏览对话模板'}
            </DialogTitle>
            <DialogDescription>
              {language === 'en'
                ? 'Select a template to quickly start your dialogue generation'
                : '选择一个模板来快速开始您的对话生成'
              }
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* 搜索框 */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={language === 'en' ? 'Search templates...' : '搜索模板...'}
                value={templateSearchTerm}
                onChange={(e) => setTemplateSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* 模板列表 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[400px] overflow-y-auto">
              {filteredTemplates.map((template) => (
                <Card key={template.id} className="p-4 cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => {
                    setDialogueText(template.content);
                    // 解析对话内容
                    const lines = template.content.split('\n').filter(line => line.trim());
                    const parsed: DialogueLine[] = [];
                    lines.forEach((line, index) => {
                      const match = line.match(/^(.+?)[:：]\s*(.+)$/);
                      if (match) {
                        parsed.push({
                          speaker: match[1].trim(),
                          text: match[2].trim(),
                          lineNumber: index + 1,
                        });
                      }
                    });
                    setParsedDialogues(parsed);
                    setShowTemplateDialog(false);
                  }}
                >
                  <div className="space-y-2">
                    <h4 className="font-medium">{template.name}</h4>
                    <p className="text-sm text-muted-foreground">{template.description}</p>
                    <div className="flex flex-wrap gap-1">
                      {template.speakers?.map((speaker, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {speaker}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {filteredTemplates.length === 0 && (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  {language === 'en' ? 'No templates found' : '未找到模板'}
                </p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
