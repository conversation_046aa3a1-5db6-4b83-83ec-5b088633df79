"use client";

import { useState, useRef, useEffect } from "react";
import { t, type Locale, defaultLocale } from "~/lib/i18n";
import { Card, CardContent } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Play, Pause, Volume2, User, Loader2 } from "lucide-react";
import { api } from "~/trpc/react";

interface AudioPlayerState {
  characterId: string;
  quality: 'fast' | 'high';
  isPlaying: boolean;
  isLoading: boolean;
}

interface CharacterShowcaseProps {
  locale?: Locale;
}

export function CharacterShowcase({ locale = defaultLocale }: CharacterShowcaseProps) {
  const translate = (key: string, params?: Record<string, string | number>) => t(key, locale, params);
  const [genderFilter, setGenderFilter] = useState<string>('all');
  const [tierFilter, setTierFilter] = useState<string>('all');
  const [audioState, setAudioState] = useState<AudioPlayerState | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Fetch characters
  const { data: characters, isLoading } = api.tts.getCharacters.useQuery({});

  // Filter characters based on selected filters
  const filteredCharacters = characters?.filter(character => {
    const genderMatch = genderFilter === 'all' || character.gender === genderFilter;
    const tierMatch = tierFilter === 'all' || character.tier === tierFilter;
    return genderMatch && tierMatch;
  }) || [];

  // Handle audio playback
  const handlePlayAudio = async (characterId: string, audioUrl: string, quality: 'fast' | 'high') => {
    try {
      // Stop current audio if playing
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }

      // If clicking the same audio that's currently playing, just stop
      if (audioState?.characterId === characterId && audioState?.quality === quality && audioState?.isPlaying) {
        setAudioState(null);
        return;
      }

      // Set loading state
      setAudioState({
        characterId,
        quality,
        isPlaying: false,
        isLoading: true
      });

      // Create and play new audio
      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      audio.addEventListener('loadstart', () => {
        setAudioState(prev => prev ? { ...prev, isLoading: true } : null);
      });

      audio.addEventListener('canplay', () => {
        setAudioState(prev => prev ? { ...prev, isLoading: false, isPlaying: true } : null);
        audio.play();
      });

      audio.addEventListener('ended', () => {
        setAudioState(null);
        audioRef.current = null;
      });

      audio.addEventListener('error', () => {
        setAudioState(null);
        audioRef.current = null;
        console.error('Audio playback error');
      });

      audio.load();

    } catch (error) {
      console.error('Error playing audio:', error);
      setAudioState(null);
    }
  };

  // Cleanup audio on unmount
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">{translate('common.loading')}</span>
      </div>
    );
  }

  return (
    <section className="py-16 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight mb-4">
            {translate('characters.title')}
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {translate('characters.subtitle')}
          </p>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8 justify-center">
          <Select value={genderFilter} onValueChange={setGenderFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder={translate('characters.filter_by_gender')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{translate('characters.all_characters')}</SelectItem>
              <SelectItem value="MALE">{translate('characters.male')}</SelectItem>
              <SelectItem value="FEMALE">{translate('characters.female')}</SelectItem>
              <SelectItem value="NEUTRAL">{translate('characters.neutral')}</SelectItem>
            </SelectContent>
          </Select>

          <Select value={tierFilter} onValueChange={setTierFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder={translate('characters.filter_by_tier')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{translate('characters.all_characters')}</SelectItem>
              <SelectItem value="FREE">{translate('characters.free_tier')}</SelectItem>
              <SelectItem value="BASIC">{translate('characters.basic_tier')}</SelectItem>
              <SelectItem value="PRO">{translate('characters.pro_tier')}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Characters Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCharacters.map((character) => (
            <Card key={character.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                {/* Character Info */}
                <div className="flex items-center gap-3 mb-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={character.avatarUrl || undefined} />
                    <AvatarFallback>
                      <User className="h-6 w-6" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg">{character.characterName}</h3>
                    <p className="text-sm text-muted-foreground">{character.characterNameEn}</p>
                  </div>
                  <div className="flex flex-col gap-1">
                    <Badge variant="outline" className="text-xs">
                      {translate(`characters.${character.gender.toLowerCase()}`)}
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      {translate(`characters.${character.tier.toLowerCase()}_tier`)}
                    </Badge>
                  </div>
                </div>

                {/* Description */}
                {character.description && (
                  <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                    {character.description}
                  </p>
                )}

                {/* Audio Preview */}
                <div className="space-y-3">
                  <h4 className="text-sm font-medium flex items-center gap-2">
                    <Volume2 className="h-4 w-4" />
                    {translate('characters.preview_audio')}
                  </h4>

                  <div className="space-y-2">
                    {/* Fast Quality */}
                    {character.previewAudioFast && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">
                          {translate('characters.fast_quality')}
                        </span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handlePlayAudio(character.id, character.previewAudioFast!, 'fast')}
                          disabled={audioState?.isLoading}
                          className="gap-2"
                        >
                          {audioState?.characterId === character.id && 
                           audioState?.quality === 'fast' && 
                           audioState?.isLoading ? (
                            <Loader2 className="h-3 w-3 animate-spin" />
                          ) : audioState?.characterId === character.id && 
                               audioState?.quality === 'fast' && 
                               audioState?.isPlaying ? (
                            <Pause className="h-3 w-3" />
                          ) : (
                            <Play className="h-3 w-3" />
                          )}
                          {translate('characters.play')}
                        </Button>
                      </div>
                    )}

                    {/* High Quality */}
                    {character.previewAudioHigh && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">
                          {translate('characters.high_quality')}
                        </span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handlePlayAudio(character.id, character.previewAudioHigh!, 'high')}
                          disabled={audioState?.isLoading}
                          className="gap-2"
                        >
                          {audioState?.characterId === character.id && 
                           audioState?.quality === 'high' && 
                           audioState?.isLoading ? (
                            <Loader2 className="h-3 w-3 animate-spin" />
                          ) : audioState?.characterId === character.id && 
                               audioState?.quality === 'high' && 
                               audioState?.isPlaying ? (
                            <Pause className="h-3 w-3" />
                          ) : (
                            <Play className="h-3 w-3" />
                          )}
                          {translate('characters.play')}
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* No Results */}
        {filteredCharacters.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              {translate('characters.error_loading')}
            </p>
          </div>
        )}
      </div>
    </section>
  );
}

export default CharacterShowcase;
