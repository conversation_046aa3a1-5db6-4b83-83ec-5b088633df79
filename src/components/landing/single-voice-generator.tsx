"use client";

import { useState } from 'react';
import { But<PERSON> } from '~/components/ui/button';
import { Textarea } from '~/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { Input } from '~/components/ui/input';
import { type Language } from '~/lib/translations';
import { api } from '~/trpc/react';
import {
  Mic,
  Download,
  Share,
  Loader2,
  Play,
  Pause
} from 'lucide-react';

interface SingleVoiceGeneratorProps {
  language: Language;
  isFullscreen?: boolean;
}

export function SingleVoiceGenerator({ language, isFullscreen = false }: SingleVoiceGeneratorProps) {
  const [text, setText] = useState('');
  const [styleDescription, setStyleDescription] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState('en-US');
  const [selectedGender, setSelectedGender] = useState('all');
  const [selectedVoice, setSelectedVoice] = useState<string | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  // API queries
  const { data: languagesData } = api.voice.getActiveLanguagesWithCharacters.useQuery();
  const { data: charactersData } = api.voice.getCharacters.useQuery({
    languageCode: selectedLanguage,
  });

  // Filter characters by gender
  const filteredCharacters = charactersData?.filter(character => 
    selectedGender === 'all' || character.gender?.toLowerCase() === selectedGender.toLowerCase()
  ) || [];

  const handleGenerate = async () => {
    if (!text.trim() || !selectedVoice) return;
    
    setIsGenerating(true);
    
    try {
      // TODO: Implement actual voice generation API call
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      
      // Mock audio URL for now
      setAudioUrl('https://example.com/audio.mp3');
    } catch (error) {
      console.error('Generation failed:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const characterCount = text.length;
  const maxCharacters = 500;

  return (
    <div className="space-y-6">
      {/* Text Input Section */}
      <Card>
        <CardContent className="pt-6 space-y-3">
          <Textarea
            placeholder={language === 'en' ? 'Enter your text here...' : '在此输入您的文本...'}
            value={text}
            onChange={(e) => setText(e.target.value)}
            className="min-h-[120px] resize-none"
            maxLength={maxCharacters}
          />
          <div className="flex justify-between items-center text-sm text-muted-foreground">
            <span>{characterCount}/{maxCharacters} {language === 'en' ? 'characters' : '字符'}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setText('')}
              disabled={!text}
            >
              {language === 'en' ? 'Clear' : '清除'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Style Description Section */}
      <Card>
        <CardContent className="pt-6">
          <Input
            placeholder={language === 'en' ? 'Describe the voice style (optional)' : '描述语音风格（可选）'}
            value={styleDescription}
            onChange={(e) => setStyleDescription(e.target.value)}
          />
        </CardContent>
      </Card>

      {/* Voice Selection Section */}
      <Card>
        <CardContent className="pt-6 space-y-3">
          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                {language === 'en' ? 'Language' : '语言'}
              </label>
              <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {languagesData?.languages ? languagesData.languages.map((lang) => (
                    <SelectItem key={lang.id} value={lang.code}>
                      <div className="flex items-center gap-2">
                        <span>{lang.flag}</span>
                        <span>{lang.name}</span>
                      </div>
                    </SelectItem>
                  )) : (
                    <SelectItem value="en-US">English (US)</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">
                {language === 'en' ? 'Gender' : '性别'}
              </label>
              <Select value={selectedGender} onValueChange={setSelectedGender}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{language === 'en' ? 'All Genders' : '所有性别'}</SelectItem>
                  <SelectItem value="female">{language === 'en' ? 'Female' : '女性'}</SelectItem>
                  <SelectItem value="male">{language === 'en' ? 'Male' : '男性'}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">
                {language === 'en' ? 'Voice Character' : '语音角色'}
              </label>
              <Select value={selectedVoice || ''} onValueChange={setSelectedVoice}>
                <SelectTrigger>
                  <SelectValue placeholder={language === 'en' ? 'Select a voice character' : '选择语音角色'} />
                </SelectTrigger>
                <SelectContent>
                  {filteredCharacters.map((character) => (
                    <SelectItem key={character.id} value={character.id}>
                      <div className="flex items-center gap-2">
                        <span>{character.gender?.toLowerCase() === 'female' ? '👩' : '👨'}</span>
                        <span>{character.characterName}</span>
                        <span className="text-xs text-muted-foreground">
                          {character.gender} • {character.style}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Generate Button */}
      <div className="text-center">
        <Button
          size="lg"
          onClick={handleGenerate}
          disabled={!text.trim() || !selectedVoice || isGenerating}
          className="gap-3 px-8 py-4 text-lg font-semibold"
        >
          {isGenerating ? (
            <>
              <Loader2 className="h-5 w-5 animate-spin" />
              {language === 'en' ? 'Generating...' : '生成中...'}
            </>
          ) : (
            <>
              <Mic className="h-5 w-5" />
              {language === 'en' ? 'Generate Speech' : '生成语音'}
            </>
          )}
        </Button>
      </div>

      {/* Audio Player Section */}
      {audioUrl && (
        <Card className="bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-700 dark:text-green-400">
                  {language === 'en' ? 'Audio Generated Successfully!' : '音频生成成功！'}
                </span>
              </div>

              <audio controls className="w-full max-w-md mx-auto rounded-lg">
                <source src={audioUrl} type="audio/mpeg" />
                Your browser does not support the audio element.
              </audio>

              <div className="flex justify-center gap-3">
                <Button variant="outline" size="sm" className="gap-2">
                  <Download className="h-4 w-4" />
                  {language === 'en' ? 'Download' : '下载'}
                </Button>
                <Button variant="outline" size="sm" className="gap-2">
                  <Share className="h-4 w-4" />
                  {language === 'en' ? 'Share' : '分享'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
