"use client";

import React, { useState, useRef } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Play, Pause, Volume2, User, Sparkles } from "lucide-react";
import { api } from "~/trpc/react";

interface VoiceSelectorProps {
  selectedCharacterId?: string;
  selectedLanguageCode?: string;
  onCharacterSelect: (characterId: string) => void;
  onLanguageSelect: (languageCode: string) => void;
}

export function VoiceSelector({
  selectedCharacterId,
  selectedLanguageCode = 'en-US',
  onCharacterSelect,
  onLanguageSelect,
}: VoiceSelectorProps) {
  const [activeTab, setActiveTab] = useState<'female' | 'male'>('female');
  const [playingDemo, setPlayingDemo] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // 获取语言列表
  const { data: languages } = api.voice.getLanguages.useQuery();
  
  // 获取语音角色列表
  const { data: characters } = api.voice.getCharacters.useQuery();
  
  // 获取试听音频
  const { data: voiceDemos } = api.voice.getVoiceDemos.useQuery({
    languageCode: selectedLanguageCode,
  });

  // 播放试听音频
  const playDemo = async (characterId: string) => {
    if (playingDemo === characterId) {
      // 停止播放
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
      setPlayingDemo(null);
      return;
    }

    // 查找试听音频
    const demo = voiceDemos?.find(d => d.voiceCharacterId === characterId);
    if (!demo?.audioUrl) {
      console.warn('未找到试听音频');
      return;
    }

    try {
      // 停止当前播放的音频
      if (audioRef.current) {
        audioRef.current.pause();
      }

      // 创建新的音频元素
      const audio = new Audio(demo.audioUrl);
      audioRef.current = audio;
      
      audio.onended = () => setPlayingDemo(null);
      audio.onerror = () => {
        console.error('音频播放失败');
        setPlayingDemo(null);
      };

      setPlayingDemo(characterId);
      await audio.play();
    } catch (error) {
      console.error('播放音频失败:', error);
      setPlayingDemo(null);
    }
  };

  // 过滤角色
  const femaleCharacters = characters?.filter(c => c.gender === 'FEMALE') || [];
  const maleCharacters = characters?.filter(c => c.gender === 'MALE') || [];

  // 渲染角色卡片
  const renderCharacterCard = (character: any) => {
    const isSelected = selectedCharacterId === character.id;
    const isPlaying = playingDemo === character.id;
    const hasDemo = voiceDemos?.some(d => d.voiceCharacterId === character.id);

    return (
      <Card
        key={character.id}
        className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
          isSelected ? 'ring-2 ring-primary' : ''
        }`}
        onClick={() => onCharacterSelect(character.id)}
      >
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-white" />
              </div>
              <div>
                <CardTitle className="text-base">{character.characterName}</CardTitle>
                <CardDescription className="text-xs">{character.style}</CardDescription>
              </div>
            </div>
            {hasDemo && (
              <Button
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  playDemo(character.id);
                }}
              >
                {isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <p className="text-sm text-muted-foreground mb-3">
            {character.description}
          </p>
          
          {/* 个性标签 */}
          {character.personality && (
            <div className="flex flex-wrap gap-1 mb-2">
              {JSON.parse(character.personality).slice(0, 3).map((trait: string, index: number) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {trait}
                </Badge>
              ))}
            </div>
          )}

          {/* 适用场景 */}
          {character.bestFor && (
            <div className="flex items-center space-x-1 text-xs text-muted-foreground">
              <Sparkles className="h-3 w-3" />
              <span>适合: {JSON.parse(character.bestFor).slice(0, 2).join(', ')}</span>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* 语言选择 */}
      <div className="space-y-2">
        <label className="text-sm font-medium">选择语言</label>
        <Select value={selectedLanguageCode} onValueChange={onLanguageSelect}>
          <SelectTrigger>
            <SelectValue placeholder="选择语言" />
          </SelectTrigger>
          <SelectContent>
            {languages?.map((lang) => (
              <SelectItem key={lang.code} value={lang.code}>
                <div className="flex items-center space-x-2">
                  <span>{lang.flag}</span>
                  <span>{lang.name}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* 语音角色选择 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium">选择语音角色</label>
          <div className="flex items-center space-x-1 text-xs text-muted-foreground">
            <Volume2 className="h-3 w-3" />
            <span>点击播放按钮试听</span>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'female' | 'male')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="female">女性角色 ({femaleCharacters.length})</TabsTrigger>
            <TabsTrigger value="male">男性角色 ({maleCharacters.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="female" className="mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {femaleCharacters.map(renderCharacterCard)}
            </div>
          </TabsContent>

          <TabsContent value="male" className="mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {maleCharacters.map(renderCharacterCard)}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* 选择总结 */}
      {selectedCharacterId && selectedLanguageCode && (
        <Card className="bg-muted/50">
          <CardContent className="pt-4">
            <div className="flex items-center space-x-2 text-sm">
              <span className="font-medium">已选择:</span>
              <Badge variant="outline">
                {languages?.find(l => l.code === selectedLanguageCode)?.flag}{' '}
                {languages?.find(l => l.code === selectedLanguageCode)?.name}
              </Badge>
              <span>+</span>
              <Badge variant="outline">
                {characters?.find(c => c.id === selectedCharacterId)?.characterName}
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
