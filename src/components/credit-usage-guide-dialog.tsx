"use client";

import { useState } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "~/components/ui/popover";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Sparkles, Zap, Gift, Star, X } from "lucide-react";

interface CreditUsageGuideDialogProps {
  children: React.ReactNode;
}

export function CreditUsageGuideDialog({ children }: CreditUsageGuideDialogProps) {
  const [open, setOpen] = useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        {children}
      </PopoverTrigger>
      <PopoverContent className="w-[500px] max-h-[400px] overflow-y-auto p-0" side="bottom" align="start">
        <div className="p-4 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              <h3 className="font-semibold">积分使用说明</h3>
            </div>
            <Button variant="ghost" size="sm" onClick={() => setOpen(false)}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="p-4 space-y-4">

          <div className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-primary" />
                <span className="font-medium">语音生成消耗</span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center p-2 bg-muted/50 rounded-lg">
                  <span className="text-sm">快速模式</span>
                  <Badge variant="secondary" className="text-xs">0.1积分/字符</Badge>
                </div>
                <div className="flex justify-between items-center p-2 bg-muted/50 rounded-lg">
                  <span className="text-sm">高质量模式</span>
                  <Badge variant="secondary" className="text-xs">0.2积分/字符</Badge>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-primary" />
                <span className="font-medium">积分保障</span>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  <span>积分永不过期，随时使用</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  <span>支持多设备同步使用</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  <span>未使用积分支持退款</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
