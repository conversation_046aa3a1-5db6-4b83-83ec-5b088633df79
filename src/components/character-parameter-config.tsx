"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Label } from "~/components/ui/label";
import { Slider } from "~/components/ui/slider";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Play, Pause, Volume2, Zap, Music, RotateCcw } from "lucide-react";

interface CharacterParameterConfigProps {
  baseCharacter?: any;
  initialSpeed?: number;
  initialPitch?: number;
  initialVolume?: number;
  onParametersChange: (params: { speed: number; pitch: number; volume: number }) => void;
  onPreview?: (params: { speed: number; pitch: number; volume: number }) => void;
}

export function CharacterParameterConfig({
  baseCharacter,
  initialSpeed = 1.0,
  initialPitch = 0.0,
  initialVolume = 0.0,
  onParametersChange,
  onPreview,
}: CharacterParameterConfigProps) {
  const [speed, setSpeed] = useState(initialSpeed);
  const [pitch, setPitch] = useState(initialPitch);
  const [volume, setVolume] = useState(initialVolume);
  const [isPlaying, setIsPlaying] = useState(false);

  // 预设配置
  const presets = [
    {
      name: "默认",
      description: "标准语音参数",
      icon: "🎯",
      speed: 1.0,
      pitch: 0.0,
      volume: 0.0,
    },
    {
      name: "活泼",
      description: "快速、高音调",
      icon: "⚡",
      speed: 1.2,
      pitch: 2.0,
      volume: 2.0,
    },
    {
      name: "沉稳",
      description: "慢速、低音调",
      icon: "🏔️",
      speed: 0.8,
      pitch: -2.0,
      volume: 1.0,
    },
    {
      name: "专业",
      description: "清晰、标准",
      icon: "💼",
      speed: 0.9,
      pitch: 0.5,
      volume: 1.5,
    },
    {
      name: "温暖",
      description: "温和、亲切",
      icon: "🌟",
      speed: 0.95,
      pitch: 1.0,
      volume: 0.5,
    },
    {
      name: "动感",
      description: "快速、有力",
      icon: "🚀",
      speed: 1.3,
      pitch: 1.5,
      volume: 3.0,
    },
  ];

  // 参数变化时通知父组件
  useEffect(() => {
    onParametersChange({ speed, pitch, volume });
  }, [speed, pitch, volume, onParametersChange]);

  // 应用预设
  const applyPreset = (preset: typeof presets[0]) => {
    setSpeed(preset.speed);
    setPitch(preset.pitch);
    setVolume(preset.volume);
  };

  // 重置参数
  const resetParameters = () => {
    setSpeed(1.0);
    setPitch(0.0);
    setVolume(0.0);
  };

  // 预览语音
  const handlePreview = async () => {
    if (onPreview) {
      setIsPlaying(true);
      try {
        await onPreview({ speed, pitch, volume });
      } catch (error) {
        console.error('预览失败:', error);
      } finally {
        // 模拟播放时间
        setTimeout(() => setIsPlaying(false), 3000);
      }
    }
  };

  // 获取参数描述
  const getSpeedDescription = (value: number) => {
    if (value < 0.7) return "很慢";
    if (value < 0.9) return "较慢";
    if (value < 1.1) return "正常";
    if (value < 1.3) return "较快";
    return "很快";
  };

  const getPitchDescription = (value: number) => {
    if (value < -10) return "很低";
    if (value < -2) return "较低";
    if (value < 2) return "正常";
    if (value < 10) return "较高";
    return "很高";
  };

  const getVolumeDescription = (value: number) => {
    if (value < -20) return "很小";
    if (value < -5) return "较小";
    if (value < 5) return "正常";
    if (value < 10) return "较大";
    return "很大";
  };

  return (
    <div className="space-y-6">
      {/* 基础角色信息 */}
      {baseCharacter && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">基础角色</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3">
              <Avatar className="h-12 w-12">
                <AvatarImage src={baseCharacter.avatarUrl} />
                <AvatarFallback>{baseCharacter.characterName[0]}</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h3 className="font-medium">{baseCharacter.characterName}</h3>
                <p className="text-sm text-muted-foreground">
                  {baseCharacter.characterNameEn} • {baseCharacter.gender === 'FEMALE' ? '女性' : '男性'}
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  {baseCharacter.description}
                </p>
              </div>
              <Badge variant="outline">
                {baseCharacter.apiProvider}
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 快速预设 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">快速预设</CardTitle>
          <CardDescription>选择预设配置快速调整参数</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {presets.map((preset, index) => (
              <Button
                key={index}
                variant="outline"
                className="h-auto p-3 flex flex-col items-start gap-1"
                onClick={() => applyPreset(preset)}
              >
                <div className="flex items-center gap-2 w-full">
                  <span className="text-lg">{preset.icon}</span>
                  <span className="font-medium text-sm">{preset.name}</span>
                </div>
                <span className="text-xs text-muted-foreground text-left">
                  {preset.description}
                </span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 参数调节 */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-base">语音参数</CardTitle>
              <CardDescription>精确调节语音特征</CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={resetParameters}
                className="text-xs"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                重置
              </Button>
              {onPreview && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreview}
                  disabled={isPlaying}
                  className="text-xs"
                >
                  {isPlaying ? (
                    <Pause className="h-3 w-3 mr-1" />
                  ) : (
                    <Play className="h-3 w-3 mr-1" />
                  )}
                  {isPlaying ? "播放中" : "预览"}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 语速控制 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                语速
              </Label>
              <div className="text-right">
                <div className="font-medium">{speed.toFixed(2)}x</div>
                <div className="text-xs text-muted-foreground">
                  {getSpeedDescription(speed)}
                </div>
              </div>
            </div>
            <Slider
              value={[speed]}
              onValueChange={(value) => setSpeed(value[0]!)}
              min={0.25}
              max={4.0}
              step={0.05}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>0.25x (很慢)</span>
              <span>1.0x (正常)</span>
              <span>4.0x (很快)</span>
            </div>
          </div>

          {/* 音调控制 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="flex items-center gap-2">
                <Music className="h-4 w-4" />
                音调
              </Label>
              <div className="text-right">
                <div className="font-medium">
                  {pitch > 0 ? '+' : ''}{pitch.toFixed(1)}
                </div>
                <div className="text-xs text-muted-foreground">
                  {getPitchDescription(pitch)}
                </div>
              </div>
            </div>
            <Slider
              value={[pitch]}
              onValueChange={(value) => setPitch(value[0]!)}
              min={-20.0}
              max={20.0}
              step={0.5}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>-20 (很低)</span>
              <span>0 (正常)</span>
              <span>+20 (很高)</span>
            </div>
          </div>

          {/* 音量控制 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="flex items-center gap-2">
                <Volume2 className="h-4 w-4" />
                音量
              </Label>
              <div className="text-right">
                <div className="font-medium">
                  {volume > 0 ? '+' : ''}{volume.toFixed(1)}dB
                </div>
                <div className="text-xs text-muted-foreground">
                  {getVolumeDescription(volume)}
                </div>
              </div>
            </div>
            <Slider
              value={[volume]}
              onValueChange={(value) => setVolume(value[0]!)}
              min={-96.0}
              max={16.0}
              step={1.0}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>-96dB (很小)</span>
              <span>0dB (正常)</span>
              <span>+16dB (很大)</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 参数总览 */}
      <Card className="bg-muted/50">
        <CardHeader className="pb-3">
          <CardTitle className="text-base">参数总览</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-primary">{speed.toFixed(2)}x</div>
              <div className="text-xs text-muted-foreground">语速</div>
            </div>
            <div>
              <div className="text-lg font-bold text-primary">
                {pitch > 0 ? '+' : ''}{pitch.toFixed(1)}
              </div>
              <div className="text-xs text-muted-foreground">音调</div>
            </div>
            <div>
              <div className="text-lg font-bold text-primary">
                {volume > 0 ? '+' : ''}{volume.toFixed(1)}dB
              </div>
              <div className="text-xs text-muted-foreground">音量</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
