"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "~/components/ui/card";
import { Progress } from "~/components/ui/progress";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { 
  <PERSON><PERSON>ard, 
  TrendingUp, 
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON>rkles,
  History,
  Plus
} from "lucide-react";
import { api } from "~/trpc/react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { cn } from "~/lib/utils";

interface CreditStatusProps {
  variant?: "default" | "compact" | "detailed";
  showUpgradeButton?: boolean;
  className?: string;
}

export function CreditStatus({ 
  variant = "default",
  showUpgradeButton = true,
  className 
}: CreditStatusProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const { data: balance } = api.credits.getBalance.useQuery(undefined, {
    enabled: !!session
  });

  if (!session || !balance) {
    return null;
  }

  const remainingCredits = balance.total - balance.used;
  const usagePercentage = Math.round((balance.used / balance.total) * 100);
  const isLowCredits = remainingCredits < 100;
  const isCriticalCredits = remainingCredits < 50;

  const getStatusColor = () => {
    if (isCriticalCredits) return "text-red-600 dark:text-red-400";
    if (isLowCredits) return "text-yellow-600 dark:text-yellow-400";
    return "text-green-600 dark:text-green-400";
  };

  const getStatusBadge = () => {
    if (isCriticalCredits) return { text: "紧急", variant: "destructive" as const };
    if (isLowCredits) return { text: "不足", variant: "secondary" as const };
    return { text: "充足", variant: "default" as const };
  };

  if (variant === "compact") {
    return (
      <div className={cn("flex items-center gap-3", className)}>
        <div className="flex items-center gap-2">
          <CreditCard className="w-4 h-4 text-blue-500" />
          <span className="text-sm font-medium">
            {remainingCredits.toLocaleString()}
          </span>
          <Badge variant={getStatusBadge().variant} className="text-xs">
            {getStatusBadge().text}
          </Badge>
        </div>
        {showUpgradeButton && isLowCredits && (
          <Button 
            size="sm" 
            variant="outline"
            onClick={() => router.push('/credits')}
            className="text-xs"
          >
            <Plus className="w-3 h-3 mr-1" />
            充值
          </Button>
        )}
      </div>
    );
  }

  return (
    <Card className={cn(
      "transition-all duration-300",
      isCriticalCredits && "border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-900/10",
      isLowCredits && !isCriticalCredits && "border-yellow-200 bg-yellow-50/50 dark:border-yellow-800 dark:bg-yellow-900/10",
      className
    )}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CreditCard className="w-5 h-5 text-blue-500" />
            <span className="text-lg">积分余额</span>
          </div>
          <Badge variant={getStatusBadge().variant}>
            {getStatusBadge().text}
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 主要积分显示 */}
        <div className="text-center">
          <div className={cn("text-3xl font-bold", getStatusColor())}>
            {remainingCredits.toLocaleString()}
          </div>
          <p className="text-sm text-muted-foreground">可用积分</p>
        </div>

        {/* 使用进度 */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>使用进度</span>
            <span>{usagePercentage}%</span>
          </div>
          <Progress 
            value={usagePercentage} 
            className={cn(
              "h-2",
              isCriticalCredits && "bg-red-100 dark:bg-red-900/20",
              isLowCredits && !isCriticalCredits && "bg-yellow-100 dark:bg-yellow-900/20"
            )}
          />
        </div>

        {variant === "detailed" && (
          <>
            {/* 详细统计 */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <TrendingUp className="w-3 h-3 text-blue-500" />
                  <span className="text-muted-foreground">总积分</span>
                </div>
                <div className="font-medium">{balance.total.toLocaleString()}</div>
              </div>
              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <History className="w-3 h-3 text-purple-500" />
                  <span className="text-muted-foreground">已使用</span>
                </div>
                <div className="font-medium">{balance.used.toLocaleString()}</div>
              </div>
            </div>

            {/* 使用预估 */}
            <div className="bg-muted/50 rounded-lg p-3 text-sm">
              <div className="flex items-center gap-2 mb-2">
                <Sparkles className="w-4 h-4 text-blue-500" />
                <span className="font-medium">使用预估</span>
              </div>
              <div className="space-y-1 text-muted-foreground">
                <div>• 约可生成 {Math.floor(remainingCredits / 10).toLocaleString()} 次标准语音</div>
                <div>• 约可生成 {Math.floor(remainingCredits / 20).toLocaleString()} 次高质量语音</div>
              </div>
            </div>
          </>
        )}

        {/* 警告提示 */}
        {isLowCredits && (
          <div className={cn(
            "flex items-start gap-2 p-3 rounded-lg text-sm",
            isCriticalCredits 
              ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300"
              : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300"
          )}>
            <AlertTriangle className="w-4 h-4 mt-0.5 flex-shrink-0" />
            <div>
              <div className="font-medium">
                {isCriticalCredits ? "积分严重不足" : "积分余额不足"}
              </div>
              <div className="text-xs mt-1">
                {isCriticalCredits 
                  ? "建议立即充值以避免服务中断"
                  : "建议及时充值以确保服务正常使用"
                }
              </div>
            </div>
          </div>
        )}

        {/* 充值按钮 */}
        {showUpgradeButton && (
          <Button 
            onClick={() => router.push('/credits')}
            className={cn(
              "w-full",
              isLowCredits 
                ? "bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
                : "bg-blue-600 hover:bg-blue-700"
            )}
          >
            <Plus className="w-4 h-4 mr-2" />
            {isLowCredits ? "立即充值" : "购买更多积分"}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}

// 预设变体
export function CreditStatusCompact(props: Omit<CreditStatusProps, "variant">) {
  return <CreditStatus {...props} variant="compact" />;
}

export function CreditStatusDetailed(props: Omit<CreditStatusProps, "variant">) {
  return <CreditStatus {...props} variant="detailed" />;
}
