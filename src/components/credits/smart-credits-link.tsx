"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { Button } from "~/components/ui/button";

interface SmartCreditsLinkProps {
  children: React.ReactNode;
  className?: string;
  variant?: "default" | "outline" | "ghost" | "link";
}

export function SmartCreditsLink({ 
  children, 
  className,
  variant = "link"
}: SmartCreditsLinkProps) {
  const pathname = usePathname();
  
  // 检查是否在 landing 页面
  const isLandingPage = pathname === '/' || pathname === '/cn';

  const scrollToCredits = () => {
    const element = document.getElementById('credits');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  if (isLandingPage) {
    // 在 landing 页面，使用滚动
    return (
      <Button
        variant={variant}
        onClick={scrollToCredits}
        className={className}
      >
        {children}
      </Button>
    );
  } else {
    // 在其他页面，使用链接跳转
    return (
      <Button
        variant={variant}
        asChild
        className={className}
      >
        <Link href="/credits">
          {children}
        </Link>
      </Button>
    );
  }
}
