"use client";

import { <PERSON><PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { 
  <PERSON><PERSON>les, 
  CreditCard, 
  ArrowRight,
  Zap
} from "lucide-react";
import { useRouter, usePathname } from "next/navigation";
import { useSession } from "next-auth/react";
import { api } from "~/trpc/react";
import { cn } from "~/lib/utils";

interface UpgradeButtonProps {
  variant?: "default" | "outline" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  showBalance?: boolean;
  showBadge?: boolean;
  children?: React.ReactNode;
}

export function UpgradeButton({
  variant = "default",
  size = "default",
  className,
  showBalance = false,
  showBadge = true,
  children
}: UpgradeButtonProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { data: session } = useSession();
  const { data: balance } = api.credits.getBalance.useQuery(undefined, {
    enabled: !!session && showBalance
  });

  const isLandingPage = pathname === '/' || pathname === '/cn';

  const handleUpgrade = () => {
    if (!session) {
      router.push('/auth/signin?callbackUrl=/credits');
    } else if (isLandingPage) {
      // 在 landing 页面，滚动到充值区域
      const element = document.getElementById('credits');
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    } else {
      router.push('/credits');
    }
  };

  const remainingCredits = balance ? balance.total - balance.used : 0;
  const isLowCredits = remainingCredits < 100;

  return (
    <div className="relative">
      {showBadge && isLowCredits && session && (
        <Badge 
          variant="destructive" 
          className="absolute -top-2 -right-2 z-10 animate-pulse"
        >
          积分不足
        </Badge>
      )}
      
      <Button
        variant={variant}
        size={size}
        onClick={handleUpgrade}
        className={cn(
          "relative overflow-hidden group transition-all duration-300",
          variant === "default" && "bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600",
          className
        )}
      >
        <div className="flex items-center gap-2">
          {variant === "default" ? (
            <Sparkles className="w-4 h-4" />
          ) : (
            <CreditCard className="w-4 h-4" />
          )}
          
          {children || (
            <>
              {session ? '充值积分' : '登录充值'}
              {size !== "sm" && <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />}
            </>
          )}
        </div>

        {/* 闪光效果 */}
        {variant === "default" && (
          <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/20 to-transparent" />
        )}
      </Button>

      {/* 积分余额显示 */}
      {showBalance && session && balance && (
        <div className="absolute -bottom-6 left-0 right-0 text-center">
          <div className={cn(
            "text-xs px-2 py-1 rounded-full inline-flex items-center gap-1",
            isLowCredits 
              ? "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300" 
              : "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300"
          )}>
            <Zap className="w-3 h-3" />
            {remainingCredits.toLocaleString()} 积分
          </div>
        </div>
      )}
    </div>
  );
}

// 预设的升级按钮变体
export function UpgradeButtonCompact() {
  return (
    <UpgradeButton 
      variant="outline" 
      size="sm" 
      showBadge={false}
      className="border-blue-200 text-blue-600 hover:bg-blue-50 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/20"
    >
      升级
    </UpgradeButton>
  );
}

export function UpgradeButtonWithBalance() {
  return (
    <UpgradeButton 
      showBalance={true}
      className="mb-6"
    />
  );
}

export function UpgradeButtonFloating() {
  const { data: session } = useSession();
  const { data: balance } = api.credits.getBalance.useQuery(undefined, {
    enabled: !!session
  });

  const remainingCredits = balance ? balance.total - balance.used : 0;
  const shouldShow = session && remainingCredits < 50;

  if (!shouldShow) return null;

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <UpgradeButton 
        size="lg"
        className="shadow-lg animate-bounce"
        showBadge={true}
      >
        <div className="flex flex-col items-center">
          <span className="text-sm font-medium">积分不足</span>
          <span className="text-xs opacity-90">立即充值</span>
        </div>
      </UpgradeButton>
    </div>
  );
}
