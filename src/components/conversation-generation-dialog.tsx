"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Textarea } from "~/components/ui/textarea";
import { Label } from "~/components/ui/label";
import { Card, CardContent } from "~/components/ui/card";
import { Sparkles, Loader2, MessageSquare } from "lucide-react";
import { api } from "~/trpc/react";
import { toast } from "sonner";

interface ConversationGenerationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerate: (conversation: { text: string; speakers: string[] }) => void;
}

export function ConversationGenerationDialog({
  isOpen,
  onClose,
  onGenerate,
}: ConversationGenerationDialogProps) {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  // 生成简单对话
  const generateConversation = api.text.generateSimpleConversation.useMutation({
    onSuccess: (result) => {
      toast.success('对话生成成功！');

      // 解析生成的对话文本，提取说话者
      const speakers = extractSpeakersFromText(result.text);

      onGenerate({
        text: result.text,
        speakers: speakers,
      });
      onClose();
      resetForm();
    },
    onError: (error) => {
      toast.error('对话生成失败', {
        description: error.message,
      });
    },
    onSettled: () => {
      setIsGenerating(false);
    },
  });

  // 从生成的文本中提取说话者名称
  const extractSpeakersFromText = (text: string): string[] => {
    const lines = text.split('\n');
    const speakers = new Set<string>();
    
    lines.forEach(line => {
      const match = line.match(/^([^：:]+)[：:]/);
      if (match) {
        const speaker = match[1].trim();
        if (speaker) {
          speakers.add(speaker);
        }
      }
    });
    
    return Array.from(speakers);
  };

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast.error('请输入对话描述');
      return;
    }

    setIsGenerating(true);
    generateConversation.mutate({
      prompt: prompt.trim(),
    });
  };

  const resetForm = () => {
    setPrompt('');
  };

  const handleClose = () => {
    if (!isGenerating) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5 text-purple-500" />
            AI对话生成
          </DialogTitle>
          <DialogDescription>
            简单描述您想要的对话场景，AI将自动生成多人对话内容
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 提示示例 */}
          <Card className="bg-purple-50 border-purple-200">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <Sparkles className="h-5 w-5 text-purple-500 mt-0.5" />
                <div className="space-y-2">
                  <div className="text-sm font-medium text-purple-900">示例提示</div>
                  <div className="text-xs text-purple-700 space-y-1">
                    <div>• "两个朋友在咖啡厅讨论周末旅行计划"</div>
                    <div>• "医生和患者讨论健康检查结果"</div>
                    <div>• "老师和学生讨论学习方法"</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 对话描述 */}
          <div className="space-y-2">
            <Label htmlFor="prompt">对话场景描述</Label>
            <Textarea
              id="prompt"
              placeholder="请简单描述您想要的对话场景，例如：两个朋友在咖啡厅讨论周末的旅行计划..."
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="min-h-[100px] resize-none"
              maxLength={200}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{prompt.length}/200 字符</span>
              <span>简单描述即可，AI会自动生成自然对话</span>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isGenerating}
            >
              取消
            </Button>
            <Button
              onClick={handleGenerate}
              disabled={isGenerating || !prompt.trim()}
              className="min-w-[120px] bg-purple-600 hover:bg-purple-700"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  生成中...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  生成对话
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
