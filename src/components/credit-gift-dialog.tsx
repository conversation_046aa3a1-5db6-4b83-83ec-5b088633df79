"use client";

import { useState } from "react";
import { api } from "~/trpc/react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON>alog<PERSON><PERSON>le,
  DialogTrigger,
} from "~/components/ui/dialog";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Gift, Loader2 } from "lucide-react";
import { toast } from "sonner";

interface CreditGiftDialogProps {
  children: React.ReactNode;
}

export function CreditGiftDialog({ children }: CreditGiftDialogProps) {
  const [open, setOpen] = useState(false);
  const [toUserId, setToUserId] = useState("");
  const [amount, setAmount] = useState("");
  const [reason, setReason] = useState("");

  // 获取用户积分余额
  const { data: balance } = api.credits.getBalance.useQuery();

  // 赠送积分
  const giftMutation = api.credits.giftCredits.useMutation({
    onSuccess: (data) => {
      toast.success("积分赠送成功", {
        description: `已向 ${data.gift.toUser.name} 赠送 ${data.gift.amount} 积分`,
      });
      setOpen(false);
      resetForm();
    },
    onError: (error) => {
      toast.error("赠送失败", {
        description: error.message,
      });
    },
  });

  const resetForm = () => {
    setToUserId("");
    setAmount("");
    setReason("");
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!toUserId.trim()) {
      toast.error("请输入接收者用户ID");
      return;
    }

    const amountNum = parseInt(amount);
    if (!amountNum || amountNum <= 0) {
      toast.error("请输入有效的积分数量");
      return;
    }

    if (!reason.trim()) {
      toast.error("请输入赠送原因");
      return;
    }

    if (balance && amountNum > balance.availableCredits) {
      toast.error("积分余额不足");
      return;
    }

    giftMutation.mutate({
      toUserId: toUserId.trim(),
      amount: amountNum,
      reason: reason.trim(),
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            赠送积分
          </DialogTitle>
          <DialogDescription>
            将您的积分赠送给其他用户
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="toUserId">接收者用户ID</Label>
            <Input
              id="toUserId"
              placeholder="输入用户ID"
              value={toUserId}
              onChange={(e) => setToUserId(e.target.value)}
              disabled={giftMutation.isPending}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="amount">积分数量</Label>
            <Input
              id="amount"
              type="number"
              placeholder="输入积分数量"
              min="1"
              max={balance?.availableCredits || 1000}
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              disabled={giftMutation.isPending}
            />
            {balance && (
              <p className="text-xs text-muted-foreground">
                当前可用积分：{balance.availableCredits}
              </p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="reason">赠送原因</Label>
            <Textarea
              id="reason"
              placeholder="请输入赠送原因..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              disabled={giftMutation.isPending}
              maxLength={200}
            />
            <p className="text-xs text-muted-foreground">
              {reason.length}/200
            </p>
          </div>
          
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={giftMutation.isPending}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={giftMutation.isPending}
            >
              {giftMutation.isPending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              确认赠送
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
