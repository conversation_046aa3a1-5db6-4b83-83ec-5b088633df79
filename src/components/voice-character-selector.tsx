"use client";

import { useState, useRef } from "react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Badge } from "~/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Search, User, Play, Pause, Volume2, Loader2 } from "lucide-react";
import { useTranslation } from "~/contexts/LanguageContext";
import { api } from "~/trpc/react";

interface VoiceCharacter {
  id: string;
  characterName: string;
  characterNameEn: string;
  originalName: string;
  gender: 'MALE' | 'FEMALE' | 'NEUTRAL';
  style?: string;
  personality?: string; // JSON string of personality traits
  bestFor?: string; // JSON string of best use cases
  avatarUrl?: string;
  description?: string;
  apiProvider: string;
  apiVoiceName: string;
}

interface VoiceCharacterSelectorProps {
  characters: VoiceCharacter[];
  selectedCharacterId: string;
  onSelect: (characterId: string) => void;
  selectedLanguageCode?: string; // 新增：选择的语言代码
  trigger?: React.ReactNode;
}

export function VoiceCharacterSelector({
  characters,
  selectedCharacterId,
  onSelect,
  selectedLanguageCode = 'en-US',
  trigger
}: VoiceCharacterSelectorProps) {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedGender, setSelectedGender] = useState<string>("all");
  const [playingDemo, setPlayingDemo] = useState<string | null>(null);
  const [loadingDemo, setLoadingDemo] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // 获取选中的角色
  const selectedCharacter = characters.find(c => c.id === selectedCharacterId);

  // 获取试听音频
  const { data: voiceDemos } = api.voice.getVoiceDemos.useQuery({
    languageCode: selectedLanguageCode,
  });

  // 筛选角色
  const filteredCharacters = characters.filter(character => {
    const matchesSearch =
      character.characterName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      character.characterNameEn.toLowerCase().includes(searchQuery.toLowerCase()) ||
      character.originalName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (character.style && character.style.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (character.description && character.description.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesGender = selectedGender === "all" || character.gender === selectedGender;

    return matchesSearch && matchesGender;
  });

  // 按性别分组
  const femaleCharacters = filteredCharacters.filter(c => c.gender === 'FEMALE');
  const maleCharacters = filteredCharacters.filter(c => c.gender === 'MALE');
  const neutralCharacters = filteredCharacters.filter(c => c.gender === 'NEUTRAL');

  // 播放试听音频
  const playDemo = async (characterId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // 防止触发角色选择

    if (playingDemo === characterId) {
      // 停止播放
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
      setPlayingDemo(null);
      return;
    }

    // 查找试听音频
    const demo = voiceDemos?.find(d => d.voiceCharacterId === characterId);
    if (!demo?.audioUrl) {
      console.warn('未找到试听音频');
      return;
    }

    try {
      setLoadingDemo(characterId);

      // 停止当前播放的音频
      if (audioRef.current) {
        audioRef.current.pause();
      }

      // 创建新的音频元素
      const audio = new Audio(demo.audioUrl);
      audioRef.current = audio;

      audio.onloadstart = () => setLoadingDemo(characterId);
      audio.oncanplay = () => setLoadingDemo(null);
      audio.onended = () => setPlayingDemo(null);
      audio.onerror = () => {
        console.error('音频播放失败');
        setPlayingDemo(null);
        setLoadingDemo(null);
      };

      setPlayingDemo(characterId);
      await audio.play();
      setLoadingDemo(null);
    } catch (error) {
      console.error('播放音频失败:', error);
      setPlayingDemo(null);
      setLoadingDemo(null);
    }
  };

  const handleSelect = (characterId: string) => {
    onSelect(characterId);
    setOpen(false);
  };

  const defaultTrigger = (
    <Button variant="outline" className="w-full justify-start h-12 p-3">
      {selectedCharacter ? (
        <div className="flex items-center gap-3 w-full">
          <Avatar className="h-8 w-8 flex-shrink-0">
            <AvatarImage src={selectedCharacter.avatarUrl || undefined} />
            <AvatarFallback>
              <User className="h-4 w-4" />
            </AvatarFallback>
          </Avatar>
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <span className="font-medium text-sm truncate">{selectedCharacter.characterName}</span>
            <Badge variant="outline" className="text-xs flex-shrink-0 ml-auto">
              {selectedCharacter.gender === 'MALE' ? '♂' : selectedCharacter.gender === 'FEMALE' ? '♀' : '⚪'}
            </Badge>
          </div>
        </div>
      ) : (
        <div className="flex items-center gap-2 text-muted-foreground">
          <User className="h-4 w-4" />
          <span>选择语音角色</span>
        </div>
      )}
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {t('generate.select_character')}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 搜索和筛选 */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('generate.search_characters')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Tabs value={selectedGender} onValueChange={setSelectedGender} className="w-auto">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="all" className="text-xs">全部 ({filteredCharacters.length})</TabsTrigger>
                <TabsTrigger value="FEMALE" className="text-xs">♀ 女性 ({femaleCharacters.length})</TabsTrigger>
                <TabsTrigger value="MALE" className="text-xs">♂ 男性 ({maleCharacters.length})</TabsTrigger>
                <TabsTrigger value="NEUTRAL" className="text-xs">⚪ 中性 ({neutralCharacters.length})</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* 角色列表 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
            {filteredCharacters.map((character) => (
              <div
                key={character.id}
                className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                  selectedCharacterId === character.id
                    ? 'border-primary bg-primary/5 ring-1 ring-primary/30'
                    : 'border-border hover:border-primary/50'
                }`}
                onClick={() => handleSelect(character.id)}
              >
                <div className="flex items-center space-x-3 mb-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={character.avatarUrl || undefined} />
                    <AvatarFallback>
                      <User className="h-5 w-5" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-sm truncate">{character.characterName}</h4>
                      <div className="flex items-center gap-2">
                        {/* 试听按钮 */}
                        {voiceDemos?.find(d => d.voiceCharacterId === character.id) && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 hover:bg-primary/10"
                            onClick={(e) => playDemo(character.id, e)}
                            disabled={loadingDemo === character.id}
                          >
                            {loadingDemo === character.id ? (
                              <Loader2 className="h-3 w-3 animate-spin" />
                            ) : playingDemo === character.id ? (
                              <Pause className="h-3 w-3" />
                            ) : (
                              <Play className="h-3 w-3" />
                            )}
                          </Button>
                        )}
                        <Badge variant="outline" className="text-xs">
                          {character.gender === 'MALE' ? '♂' : character.gender === 'FEMALE' ? '♀' : '⚪'}
                        </Badge>
                      </div>
                    </div>
                    <div className="space-y-1">
                      {character.style && (
                        <p className="text-xs text-primary font-medium truncate">
                          {character.style}
                        </p>
                      )}
                      <p className="text-xs text-muted-foreground truncate">
                        {character.description || character.originalName}
                      </p>
                    </div>
                  </div>
                </div>

                {/* 角色特征 */}
                <div className="space-y-2 pt-2">
                  {/* 个性标签 */}
                  {character.personality && (
                    <div>
                      <div className="flex flex-wrap gap-1">
                        {JSON.parse(character.personality).slice(0, 3).map((trait: string, index: number) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {trait}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* 适用场景 */}
                  {character.bestFor && (
                    <div className="text-xs text-muted-foreground">
                      <span className="font-medium">适合: </span>
                      {JSON.parse(character.bestFor).slice(0, 2).join(', ')}
                    </div>
                  )}


                </div>
              </div>
            ))}
          </div>

          {filteredCharacters.length === 0 && (
            <div className="text-center py-8">
              <User className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">未找到匹配的角色</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
