"use client";

import React, { useState, useRef, useEffect } from "react";
import { Button } from "~/components/ui/button";
import { Play, Pause, Download } from "lucide-react";
import { useAudioPlayer } from "~/contexts/AudioPlayerContext";
import { downloadAudio } from "~/lib/audio-download";

interface WaveformPlayerProps {
  audioUrl: string;
  className?: string;
  playerId?: string; // 可选的播放器ID，如果不提供会自动生成
}

export function WaveformPlayer({ audioUrl, className = "", playerId }: WaveformPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const waveformRef = useRef<HTMLDivElement>(null);

  // 生成唯一的播放器ID
  const playerIdRef = useRef(playerId || `player-${Math.random().toString(36).substring(2, 11)}`);
  const { registerPlayer, unregisterPlayer, stopAllOthers } = useAudioPlayer();

  // 注册和注销播放器
  useEffect(() => {
    const audio = audioRef.current;
    if (audio) {
      registerPlayer(playerIdRef.current, audio);

      return () => {
        unregisterPlayer(playerIdRef.current);
      };
    }
  }, [registerPlayer, unregisterPlayer]);

  // 当audioUrl改变时重置状态
  useEffect(() => {
    setIsPlaying(false);
    setCurrentTime(0);
    setDuration(0);
    setIsLoaded(false);
    setError(null);
  }, [audioUrl]);

  // 音频事件处理
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
      setIsLoaded(true);
      setError(null);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    const handleError = () => {
      setError('音频加载失败');
      setIsLoaded(false);
    };

    const handlePlay = () => {
      setIsPlaying(true);
    };

    const handlePause = () => {
      setIsPlaying(false);
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
    };
  }, [audioUrl]);

  // 生成等高波纹条
  const generateWaveformBars = () => {
    const barCount = 80; // 固定数量的波纹条
    const bars = [];

    for (let i = 0; i < barCount; i++) {
      const progress = duration > 0 ? currentTime / duration : 0;
      const isPlayed = i / barCount < progress;
      const isActive = isPlaying && Math.abs(i / barCount - progress) < 0.02;

      bars.push(
        <div
          key={i}
          className={`w-1 h-8 rounded-sm transition-all duration-200 ${
            isPlayed
              ? 'bg-primary shadow-sm' // 已播放：主题色 + 轻微阴影
              : 'bg-muted-foreground/20 dark:bg-muted-foreground/30' // 未播放：明暗主题适配
          } ${
            isActive
              ? 'animate-pulse bg-primary/80 scale-110' // 当前播放位置：脉冲动画 + 缩放
              : ''
          } hover:bg-primary/60 cursor-pointer`} // 悬停效果
        />
      );
    }

    return bars;
  };

  // 播放/暂停控制
  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio || !isLoaded) return;

    if (isPlaying) {
      audio.pause();
    } else {
      // 先停止所有其他播放器，然后播放当前音频
      stopAllOthers(playerIdRef.current);
      audio.play().catch((e) => {
        console.error('Play error:', e);
        setError('播放失败');
      });
    }
  };

  // 点击波形跳转
  const handleWaveformClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const audio = audioRef.current;
    const waveform = waveformRef.current;
    if (!audio || !waveform || !isLoaded) return;

    const rect = waveform.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const progress = x / rect.width;
    const newTime = progress * duration;

    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  // 下载音频
  const handleDownloadAudio = async () => {
    await downloadAudio(audioUrl, {
      filename: `audio-${Date.now()}.wav`,
      onSuccess: (filename) => {
        console.log('音频下载成功:', filename);
      },
      onError: (error) => {
        console.error('音频下载失败:', error);
      }
    });
  };

  // 格式化时间
  const formatTime = (time: number) => {
    if (isNaN(time)) return "0:00";
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  return (
    <div className={`space-y-3 p-3 rounded-lg bg-card dark:bg-card border border-border dark:border-border shadow-sm dark:shadow-none transition-colors duration-200 ${className}`}>
      <audio ref={audioRef} src={audioUrl} preload="metadata" />

      {error && (
        <div className="text-red-500 dark:text-red-400 text-sm text-center bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-lg p-2">
          {error}
        </div>
      )}

      {/* 波形显示 */}
      <div className="relative">
        <div
          ref={waveformRef}
          className={`flex items-end justify-center gap-0.5 h-16 cursor-pointer rounded-lg p-2 transition-all duration-200 ${
            isLoaded
              ? 'bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-900/30 dark:hover:to-purple-900/30'
              : 'bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 border border-dashed border-muted-foreground/30'
          }`}
          onClick={handleWaveformClick}
        >
          {isLoaded ? generateWaveformBars() : (
            <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-r from-white/90 to-gray-50/90 dark:from-gray-800/90 dark:to-gray-900/90 backdrop-blur-sm rounded-lg">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                <div className="text-sm text-muted-foreground">加载音频中...</div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 控制按钮 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={togglePlayPause}
            disabled={!isLoaded}
            className={`h-8 w-8 p-0 transition-all duration-200 ${
              isPlaying
                ? 'bg-primary text-primary-foreground hover:bg-primary/90 border-primary dark:bg-primary dark:text-primary-foreground dark:border-primary dark:hover:bg-primary/90'
                : 'bg-background text-foreground border-border hover:bg-muted dark:hover:bg-muted/80 dark:bg-background dark:text-foreground dark:border-border'
            } ${
              !isLoaded
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:scale-105'
            }`}
          >
            {isPlaying ? (
              <Pause className="h-4 w-4" />
            ) : (
              <Play className="h-4 w-4" />
            )}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleDownloadAudio}
            disabled={!isLoaded}
            className={`h-8 w-8 p-0 transition-all duration-200 bg-background text-foreground border-border hover:bg-muted dark:hover:bg-muted/80 dark:bg-background dark:text-foreground dark:border-border ${
              !isLoaded
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:scale-105'
            }`}
          >
            <Download className="h-4 w-4" />
          </Button>
        </div>

        {/* 时间显示 */}
        <div className="text-xs text-muted-foreground dark:text-muted-foreground/90 font-mono bg-muted/30 dark:bg-muted/20 px-2 py-1 rounded">
          <span className="text-foreground dark:text-foreground/90 font-medium">{formatTime(currentTime)}</span>
          <span className="mx-1">/</span>
          <span>{formatTime(duration)}</span>
        </div>
      </div>
    </div>
  );
}
