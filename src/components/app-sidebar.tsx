"use client";

import * as React from "react";
import {
  GalleryVerticalEnd,
  Settings,
  Shield,
  ShoppingBag,
  Palette,
  Users,
  FileText,
  BarChart3,
  UserCog,
  Package,
  Languages,
  Settings2,
  Database,
} from "lucide-react";
import { useTranslation } from "~/contexts/LanguageContext";

import { NavMain } from "~/components/nav-main";
import { NavUser } from "~/components/nav-user";
import { TeamSwitcher } from "~/components/team-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "~/components/ui/sidebar";
import { useSession } from "next-auth/react";
import { api } from "~/trpc/react";



export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { data: session } = useSession();
  const { t } = useTranslation();

  // 获取用户角色信息
  const { data: userInfo } = api.admin.getUserRole.useQuery(undefined, {
    enabled: !!session?.user?.id,
    retry: false,
  });



  // 动态生成导航数据 - 只保留管理员功能
  const navMainData = React.useMemo(() => [], [t]);

  // 只有超级管理员才能看到管理员导航
  const navMainWithAdmin = React.useMemo(() => {
    const baseNav = [...navMainData];

    if (userInfo?.role === 'SUPER_ADMIN') {
      baseNav.push({
        title: t('sidebar.admin'),
        url: "/admin",
        icon: Shield,
        items: [
          {
            title: t('navigation.dashboard'),
            url: "/admin/dashboard",
            icon: BarChart3,
          },
          {
            title: t('sidebar.user_management'),
            url: "/admin/users",
            icon: UserCog,
          },
          {
            title: t('sidebar.order_management'),
            url: "/admin/orders",
            icon: ShoppingBag,
          },
          {
            title: "语言管理",
            url: "/admin/languages",
            icon: Languages,
          },
          {
            title: "语言角色管理",
            url: "/admin/language-characters",
            icon: Users,
          },
          {
            title: "角色管理",
            url: "/admin/characters",
            icon: Users,
          },
          {
            title: "风格管理",
            url: "/admin/styles",
            icon: Palette,
          },
          {
            title: "模板管理",
            url: "/admin/templates",
            icon: FileText,
          },
          {
            title: "接口管理",
            url: "/admin/api-management",
            icon: Settings2,
          },
          {
            title: "套餐管理",
            url: "/admin/packages",
            icon: Package,
          },
          {
            title: "语音统计",
            url: "/admin/voice-stats",
            icon: BarChart3,
          },
          {
            title: "系统设置",
            url: "/admin/settings",
            icon: Settings,
          },
          {
            title: "调试工具",
            url: "/admin/debug",
            icon: Database,
          },
          {
            title: "AI 测试",
            url: "/admin/test-ai",
            icon: Settings2,
          },
        ],
      });
    }

    return baseNav;
  }, [userInfo?.role, t]);

  // 更新用户信息
  const userData = React.useMemo(() => ({
    name: session?.user?.name || t('navigation.profile'),
    email: session?.user?.email || "",
    avatar: session?.user?.image || "",
  }), [session, t]);

  // 团队数据
  const teamData = React.useMemo(() => ([
    {
      name: "Voctana",
      logo: GalleryVerticalEnd,
      plan: t('dashboard.free_plan'),
    },
  ]), [t]);

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={teamData} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navMainWithAdmin} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={userData} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
