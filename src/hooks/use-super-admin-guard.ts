import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { api } from "~/trpc/react";

export function useSuperAdminGuard() {
  const { data: session, status } = useSession();
  const router = useRouter();

  // 获取用户角色信息
  const { data: userInfo, isLoading: userLoading } = api.admin.getUserRole.useQuery(
    undefined,
    {
      enabled: status === 'authenticated' && !!session?.user?.id,
      retry: false,
    }
  );

  // 权限检查
  useEffect(() => {
    if (status === 'loading' || userLoading) return;

    if (status === 'unauthenticated' || !session?.user) {
      router.push('/auth/signin');
      return;
    }

    if (userInfo && userInfo.role !== 'SUPER_ADMIN') {
      router.push('/dashboard');
      return;
    }
  }, [session, userInfo, status, userLoading, router]);

  return {
    session,
    userInfo,
    isLoading: status === 'loading' || userLoading,
    isAuthenticated: status === 'authenticated',
    isSuperAdmin: userInfo?.role === 'SUPER_ADMIN',
  };
}
