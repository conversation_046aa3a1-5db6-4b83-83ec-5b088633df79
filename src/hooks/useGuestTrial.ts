"use client";

import { useState, useEffect } from "react";

interface GuestTrialData {
  dailyUsage: number;
  totalCharacters: number;
  lastResetDate: string;
  generationsToday: number;
}

interface GuestTrialLimits {
  maxDailyGenerations: number;
  maxCharactersPerGeneration: number;
  maxTotalCharacters: number;
  allowedQuality: "standard" | "professional";
  allowedModes: ("single" | "multi")[];
}

const GUEST_TRIAL_LIMITS: GuestTrialLimits = {
  maxDailyGenerations: 3,
  maxCharactersPerGeneration: 100,
  maxTotalCharacters: 300,
  allowedQuality: "standard",
  allowedModes: ["single"], // 访客只能使用单人模式
};

const STORAGE_KEY = "voctana_guest_trial";

export function useGuestTrial() {
  const [trialData, setTrialData] = useState<GuestTrialData>({
    dailyUsage: 0,
    totalCharacters: 0,
    lastResetDate: new Date().toDateString(),
    generationsToday: 0,
  });

  const [isLoaded, setIsLoaded] = useState(false);

  // 从 localStorage 加载数据
  useEffect(() => {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      try {
        const data: GuestTrialData = JSON.parse(stored);
        const today = new Date().toDateString();
        
        // 如果是新的一天，重置每日使用量
        if (data.lastResetDate !== today) {
          const resetData = {
            ...data,
            dailyUsage: 0,
            generationsToday: 0,
            lastResetDate: today,
          };
          setTrialData(resetData);
          localStorage.setItem(STORAGE_KEY, JSON.stringify(resetData));
        } else {
          setTrialData(data);
        }
      } catch (error) {
        console.error("Failed to parse guest trial data:", error);
      }
    }
    setIsLoaded(true);
  }, []);

  // 保存数据到 localStorage
  const saveTrialData = (data: GuestTrialData) => {
    setTrialData(data);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  };

  // 检查是否可以生成语音
  const canGenerate = (textLength: number) => {
    if (!isLoaded) return false;
    
    return (
      trialData.generationsToday < GUEST_TRIAL_LIMITS.maxDailyGenerations &&
      textLength <= GUEST_TRIAL_LIMITS.maxCharactersPerGeneration &&
      trialData.totalCharacters + textLength <= GUEST_TRIAL_LIMITS.maxTotalCharacters
    );
  };

  // 记录一次生成
  const recordGeneration = (textLength: number) => {
    if (!canGenerate(textLength)) {
      throw new Error("Generation limit exceeded");
    }

    const newData = {
      ...trialData,
      dailyUsage: trialData.dailyUsage + textLength,
      totalCharacters: trialData.totalCharacters + textLength,
      generationsToday: trialData.generationsToday + 1,
    };

    saveTrialData(newData);
  };

  // 获取剩余额度
  const getRemainingQuota = () => {
    return {
      dailyGenerations: GUEST_TRIAL_LIMITS.maxDailyGenerations - trialData.generationsToday,
      totalCharacters: GUEST_TRIAL_LIMITS.maxTotalCharacters - trialData.totalCharacters,
      maxCharactersPerGeneration: GUEST_TRIAL_LIMITS.maxCharactersPerGeneration,
    };
  };

  // 获取限制信息
  const getLimits = () => GUEST_TRIAL_LIMITS;

  // 检查是否已用完所有额度
  const isExhausted = () => {
    return (
      trialData.generationsToday >= GUEST_TRIAL_LIMITS.maxDailyGenerations ||
      trialData.totalCharacters >= GUEST_TRIAL_LIMITS.maxTotalCharacters
    );
  };

  return {
    trialData,
    isLoaded,
    canGenerate,
    recordGeneration,
    getRemainingQuota,
    getLimits,
    isExhausted,
  };
}
