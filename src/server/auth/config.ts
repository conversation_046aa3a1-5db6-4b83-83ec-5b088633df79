import { type DefaultSession, type NextAuthConfig } from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import bcrypt from "bcryptjs";
import { z } from "zod";

import { db } from "~/server/db";

/**
 * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`
 * object and keep type safety.
 *
 * @see https://next-auth.js.org/getting-started/typescript#module-augmentation
 */
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      role: string;
      isActive: boolean;
    } & DefaultSession["user"];
  }

  interface User {
    id: string;
    role: string;
    isActive: boolean;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    role: string;
    isActive: boolean;
  }
}

/**
 * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.
 *
 * @see https://next-auth.js.org/configuration/options
 */
export const authConfig = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        console.log('🔐 Authorize attempt:', { email: credentials?.email });

        const parsedCredentials = z
          .object({ email: z.string().email(), password: z.string().min(6) })
          .safeParse(credentials);

        if (!parsedCredentials.success) {
          console.log('❌ Credential validation failed:', parsedCredentials.error);
          return null;
        }

        const { email, password } = parsedCredentials.data;

        const user = await db.user.findUnique({
          where: { email }
        });

        console.log('👤 User found:', user ? 'Yes' : 'No');

        if (!user || !user.password) {
          console.log('❌ User not found or no password');
          return null;
        }

        const passwordsMatch = await bcrypt.compare(password, user.password);
        console.log('🔑 Password match:', passwordsMatch);

        if (passwordsMatch) {
          console.log('✅ Login successful for:', email);
          return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
          };
        }

        console.log('❌ Password mismatch');
        return null;
      },
    }),
  ],
  pages: {
    signIn: "/auth/signin",
    signUp: "/auth/signup",
  },
  session: {
    strategy: "jwt",
  },
  callbacks: {
    signIn: async ({ user, account, profile }) => {
      // 自动创建用户记录（如果不存在）
      if (user.id && user.email) {
        try {
          const { db } = await import("~/server/db");

          const existingUser = await db.user.findUnique({
            where: { id: user.id },
          });

          if (!existingUser) {
            await db.user.create({
              data: {
                id: user.id,
                email: user.email,
                name: user.name,
                image: user.image,
                credits: 1000, // 新用户默认1000积分
                usedCredits: 0,
              },
            });
            console.log('✅ Created new user record:', user.email);
          }
        } catch (error) {
          console.error('❌ Error creating user record:', error);
          // 不阻止登录，即使创建用户记录失败
        }
      }
      return true;
    },
    jwt: async ({ token, user }) => {
      if (user) {
        token.id = user.id;
      }

      // 每次都从数据库获取最新的用户信息，包括角色
      if (token.id) {
        try {
          const dbUser = await db.user.findUnique({
            where: { id: token.id as string },
            select: {
              id: true,
              role: true,
              isActive: true,
            },
          });

          if (dbUser) {
            token.role = dbUser.role;
            token.isActive = dbUser.isActive;
          }
        } catch (error) {
          console.error('Error fetching user role in JWT callback:', error);
        }
      }

      return token;
    },
    session: ({ session, token }) => ({
      ...session,
      user: {
        ...session.user,
        id: token.id as string,
        role: token.role as string,
        isActive: token.isActive as boolean,
      },
    }),
  },
} satisfies NextAuthConfig;
