import { postRouter } from "~/server/api/routers/post";
import { ttsRouter } from "~/server/api/routers/tts";
import { preferencesRouter } from "~/server/api/routers/preferences";
import { characterAdminRouter } from "~/server/api/routers/character-admin";
import { authRouter } from "~/server/api/routers/auth";
import { characterRouter } from "~/server/api/routers/character";
import { adminRouter } from "~/server/api/routers/admin";

import { orderRouter } from "~/server/api/routers/order";
import { paypalRouter } from "~/server/api/routers/paypal";
import { invoiceRouter } from "~/server/api/routers/invoice";
import { creditsRouter } from "~/server/api/routers/credits";
import { creditPurchaseRouter } from "~/server/api/routers/credit-purchase";
import { creditUsageRouter } from "~/server/api/routers/credit-usage";
import { packageAdminRouter } from "~/server/api/routers/package-admin";
import { voiceRouter } from "~/server/api/routers/voice";
import { conversationRouter } from "~/server/api/routers/conversation";
import { languageAdminRouter } from "~/server/api/routers/language-admin";
import { voiceAdminRouter } from "~/server/api/routers/voice-admin";
import { apiManagementRouter } from "~/server/api/routers/api-management";
import { styleManagementRouter } from "~/server/api/routers/style-management";
import { characterManagementRouter } from "~/server/api/routers/character-management";
import { communityRouter } from "~/server/api/routers/community";
import { textRouter } from "~/server/api/routers/text";
import { languageAvatarRouter } from "~/server/api/routers/language-avatar";
import { languageCharacterAdminRouter, apiCharacterTemplateAdminRouter } from "~/server/api/routers/language-character-admin";
import { voiceAdminNewRouter } from "~/server/api/routers/voice-admin-new";
import { aiGenerationRouter } from "~/server/api/routers/ai-generation";
import { voiceDemoRouter } from "~/server/api/routers/voice-demo";
import { shareRouter } from "~/server/api/routers/share";
import { userRouter } from "~/server/api/routers/user";
import { createCallerFactory, createTRPCRouter } from "~/server/api/trpc";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  post: postRouter,
  tts: ttsRouter,
  auth: authRouter,
  character: characterRouter,
  admin: adminRouter,

  order: orderRouter,
  paypal: paypalRouter,
  invoice: invoiceRouter,
  preferences: preferencesRouter,
  characterAdmin: characterAdminRouter,
  credits: creditsRouter,
  creditPurchase: creditPurchaseRouter,
  creditUsage: creditUsageRouter,
  packageAdmin: packageAdminRouter,
  voice: voiceRouter,
  conversation: conversationRouter,
  languageAdmin: languageAdminRouter,
  voiceAdmin: voiceAdminRouter,
  apiManagement: apiManagementRouter,
  styleManagement: styleManagementRouter,
  characterManagement: characterManagementRouter,
  community: communityRouter,
  text: textRouter,
  languageAvatar: languageAvatarRouter,
  languageCharacterAdmin: languageCharacterAdminRouter,
  apiCharacterTemplateAdmin: apiCharacterTemplateAdminRouter,
  voiceAdminNew: voiceAdminNewRouter,
  aiGeneration: aiGenerationRouter,
  voiceDemo: voiceDemoRouter,
  share: shareRouter,
  user: userRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
