import { z } from "zod";
import { createTR<PERSON>Router, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { aiAvatarGenerator } from "~/lib/ai-avatar-generator";
import { r2Storage, R2StorageService } from "~/lib/r2-storage";

// 管理员权限检查中间件
const adminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });

  if (!user || !['ADMIN', 'SUPER_ADMIN'].includes(user.role)) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Admin access required',
    });
  }

  return next({
    ctx: {
      ...ctx,
      user: {
        ...ctx.session.user,
        role: user.role,
      },
    },
  });
});

// 超级管理员权限检查中间件
const superAdminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });

  if (!user || user.role !== 'SUPER_ADMIN') {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Super admin access required',
    });
  }

  return next({
    ctx: {
      ...ctx,
      user: {
        ...ctx.session.user,
        role: user.role,
      },
    },
  });
});

// 提取生成头像的核心逻辑
async function generateLanguageAvatarLogic({
  ctx,
  input,
}: {
  ctx: any;
  input: {
    characterId: string;
    languageId: string;
    culturalContext?: string;
    localizedName?: string;
    description?: string;
  };
}) {
  // 获取角色信息
  const character = await ctx.db.voiceCharacter.findUnique({
    where: { id: input.characterId },
  });

  if (!character) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '角色不存在',
    });
  }

  // 获取语言信息
  const language = await ctx.db.language.findUnique({
    where: { id: input.languageId },
  });

  if (!language) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '语言不存在',
    });
  }

  // 检查AI头像生成服务是否可用
  if (!aiAvatarGenerator.isAvailable()) {
    throw new TRPCError({
      code: 'SERVICE_UNAVAILABLE',
      message: 'AI头像生成服务不可用',
    });
  }

  // 检查R2存储是否配置
  if (!r2Storage.isConfigured()) {
    throw new TRPCError({
      code: 'SERVICE_UNAVAILABLE',
      message: 'R2存储服务未配置',
    });
  }

  console.log(`🎨 为角色 ${character.characterName} 生成 ${language.name} 语言头像...`);

  // 生成头像
  const result = await aiAvatarGenerator.generateAvatar({
    characterName: character.characterName,
    gender: character.gender,
    style: character.style || undefined,
    personality: character.personality || undefined,
    description: input.description || character.description,
    languageCode: language.code,
    culturalContext: input.culturalContext,
  });

  // 生成文件名
  const fileName = R2StorageService.generateAvatarFileName(
    input.characterId,
    language.code,
    'webp'
  );

  // 上传到R2
  const avatarUrl = await r2Storage.uploadAvatar(
    result.imageData,
    fileName,
    'image/webp'
  );

  console.log(`✅ ${language.name} 语言头像已上传到R2: ${avatarUrl}`);

  // 保存或更新语言特定头像
  const languageAvatar = await ctx.db.languageCharacterAvatar.upsert({
    where: {
      characterId_languageId: {
        characterId: input.characterId,
        languageId: input.languageId,
      },
    },
    update: {
      avatarUrl,
      localizedName: input.localizedName,
      description: input.description,
      updatedAt: new Date(),
    },
    create: {
      characterId: input.characterId,
      languageId: input.languageId,
      languageCode: language.code,
      avatarUrl,
      localizedName: input.localizedName,
      description: input.description,
    },
  });

  // 记录生成历史
  await ctx.db.avatarGenerationHistory.create({
    data: {
      characterId: input.characterId,
      languageCode: language.code,
      avatarUrl,
      prompt: result.prompt,
      metadata: JSON.stringify(result.metadata),
      generatedBy: ctx.session.user.id,
    },
  });

  return {
    success: true,
    avatar: languageAvatar,
    message: `${character.characterName} 的 ${language.name} 语言头像生成成功`,
  };
}

export const languageAvatarRouter = createTRPCRouter({
  // 获取角色在特定语言下的头像
  getCharacterLanguageAvatar: adminProcedure
    .input(z.object({
      characterId: z.string(),
      languageId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const avatar = await ctx.db.languageCharacterAvatar.findUnique({
        where: {
          characterId_languageId: {
            characterId: input.characterId,
            languageId: input.languageId,
          },
        },
        include: {
          character: {
            select: {
              characterName: true,
              characterNameEn: true,
              gender: true,
              style: true,
              personality: true,
              description: true,
            },
          },
          language: {
            select: {
              code: true,
              name: true,
              nativeName: true,
              flag: true,
            },
          },
        },
      });

      return avatar;
    }),

  // 获取角色在所有语言下的头像
  getCharacterAllLanguageAvatars: adminProcedure
    .input(z.object({
      characterId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const avatars = await ctx.db.languageCharacterAvatar.findMany({
        where: {
          characterId: input.characterId,
          isActive: true,
        },
        include: {
          language: {
            select: {
              code: true,
              name: true,
              nativeName: true,
              flag: true,
            },
          },
        },
        orderBy: {
          language: {
            sortOrder: 'asc',
          },
        },
      });

      return avatars;
    }),

  // 为角色在特定语言下生成AI头像
  generateLanguageAvatar: superAdminProcedure
    .input(z.object({
      characterId: z.string(),
      languageId: z.string(),
      culturalContext: z.string().optional(),
      localizedName: z.string().optional(),
      description: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        return await generateLanguageAvatarLogic({ ctx, input });
      } catch (error) {
        console.error('❌ 语言头像生成失败:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `头像生成失败: ${error instanceof Error ? error.message : '未知错误'}`,
        });
      }
    }),

  // 更新语言特定头像信息
  updateLanguageAvatar: superAdminProcedure
    .input(z.object({
      characterId: z.string(),
      languageId: z.string(),
      localizedName: z.string().optional(),
      description: z.string().optional(),
      isActive: z.boolean().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const avatar = await ctx.db.languageCharacterAvatar.update({
        where: {
          characterId_languageId: {
            characterId: input.characterId,
            languageId: input.languageId,
          },
        },
        data: {
          localizedName: input.localizedName,
          description: input.description,
          isActive: input.isActive,
          updatedAt: new Date(),
        },
        include: {
          character: {
            select: {
              characterName: true,
              characterNameEn: true,
            },
          },
          language: {
            select: {
              name: true,
              nativeName: true,
            },
          },
        },
      });

      return {
        success: true,
        avatar,
        message: '语言头像信息更新成功',
      };
    }),

  // 删除语言特定头像
  deleteLanguageAvatar: superAdminProcedure
    .input(z.object({
      characterId: z.string(),
      languageId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 获取头像信息以便删除R2文件
      const avatar = await ctx.db.languageCharacterAvatar.findUnique({
        where: {
          characterId_languageId: {
            characterId: input.characterId,
            languageId: input.languageId,
          },
        },
        include: {
          character: {
            select: {
              characterName: true,
            },
          },
          language: {
            select: {
              name: true,
            },
          },
        },
      });

      if (!avatar) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '语言头像不存在',
        });
      }

      // 删除数据库记录
      await ctx.db.languageCharacterAvatar.delete({
        where: {
          characterId_languageId: {
            characterId: input.characterId,
            languageId: input.languageId,
          },
        },
      });

      // 尝试删除R2文件
      if (r2Storage.isConfigured() && avatar.avatarUrl) {
        try {
          const fileName = R2StorageService.extractFileNameFromUrl(avatar.avatarUrl);
          if (fileName) {
            await r2Storage.deleteAvatar(fileName);
            console.log(`🗑️ 已删除R2文件: ${fileName}`);
          }
        } catch (error) {
          console.warn('⚠️ 删除R2文件失败:', error);
          // 不抛出错误，因为数据库记录已删除
        }
      }

      return {
        success: true,
        message: `${avatar.character.characterName} 的 ${avatar.language.name} 语言头像已删除`,
      };
    }),

  // 批量为角色生成所有语言的头像
  generateAllLanguageAvatars: superAdminProcedure
    .input(z.object({
      characterId: z.string(),
      languageIds: z.array(z.string()).optional(), // 如果不提供，则为所有活跃语言生成
    }))
    .mutation(async ({ ctx, input }) => {
      // 获取角色信息
      const character = await ctx.db.voiceCharacter.findUnique({
        where: { id: input.characterId },
      });

      if (!character) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '角色不存在',
        });
      }

      // 获取目标语言列表
      const languages = await ctx.db.language.findMany({
        where: {
          isActive: true,
          ...(input.languageIds ? { id: { in: input.languageIds } } : {}),
        },
        orderBy: { sortOrder: 'asc' },
      });

      if (languages.length === 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '没有找到可用的语言',
        });
      }

      const results = [];
      const errors = [];

      // 为每种语言生成头像
      for (const language of languages) {
        try {
          console.log(`🎨 为 ${character.characterName} 生成 ${language.name} 头像...`);

          // 直接调用生成逻辑而不是通过procedures
          const result = await generateLanguageAvatarLogic({
            ctx,
            input: {
              characterId: input.characterId,
              languageId: language.id,
            },
          });

          results.push({
            language: language.name,
            success: true,
            avatarUrl: result.avatar.avatarUrl,
          });

          // 添加延迟避免API限制
          await new Promise(resolve => setTimeout(resolve, 2000));

        } catch (error) {
          console.error(`❌ ${language.name} 头像生成失败:`, error);
          errors.push({
            language: language.name,
            error: error instanceof Error ? error.message : '未知错误',
          });
        }
      }

      return {
        success: results.length > 0,
        results,
        errors,
        message: `批量生成完成: ${results.length} 成功, ${errors.length} 失败`,
      };
    }),
});
