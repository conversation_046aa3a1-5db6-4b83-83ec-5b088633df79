import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

export const characterAdminRouter = createTRPCRouter({
  
  // 获取所有角色（管理员专用）
  getAllCharacters: protectedProcedure
    .query(async ({ ctx }) => {
      // 这里可以添加管理员权限检查
      return ctx.db.voiceCharacter.findMany({
        orderBy: { sortOrder: 'asc' },
      });
    }),

  // 更新角色信息
  updateCharacter: protectedProcedure
    .input(z.object({
      id: z.string(),
      characterName: z.string().min(1).max(100).optional(),
      characterNameEn: z.string().min(1).max(100).optional(),
      description: z.string().min(1).max(500).optional(),
      avatarUrl: z.string().url().optional().nullable(),
      gender: z.enum(['MALE', 'FEMALE', 'NEUTRAL']).optional(),
      isActive: z.boolean().optional(),
      sortOrder: z.number().int().min(0).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      // 检查角色是否存在
      const existingCharacter = await ctx.db.voiceCharacter.findUnique({
        where: { id },
      });

      if (!existingCharacter) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '角色不存在',
        });
      }

      // 如果更新了英文名称，需要同步更新API名称
      const finalUpdateData = { ...updateData };
      if (updateData.characterNameEn) {
        finalUpdateData.apiVoiceName = updateData.characterNameEn.toLowerCase();
      }

      return ctx.db.voiceCharacter.update({
        where: { id },
        data: finalUpdateData,
      });
    }),

  // 创建新角色
  createCharacter: protectedProcedure
    .input(z.object({
      characterName: z.string().min(1).max(100),
      characterNameEn: z.string().min(1).max(100),
      description: z.string().min(1).max(500),
      avatarUrl: z.string().url().optional(),
      gender: z.enum(['MALE', 'FEMALE', 'NEUTRAL']),
      apiProvider: z.enum(['GEMINI', 'OPENAI']).default('GEMINI'),
      tier: z.enum(['FREE', 'BASIC', 'PRO']).default('FREE'),
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查英文名称是否已存在
      const existingCharacter = await ctx.db.voiceCharacter.findFirst({
        where: {
          characterNameEn: input.characterNameEn,
          apiProvider: input.apiProvider,
        },
      });

      if (existingCharacter) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: '该英文名称已存在',
        });
      }

      // 获取下一个排序号
      const lastCharacter = await ctx.db.voiceCharacter.findFirst({
        orderBy: { sortOrder: 'desc' },
      });
      const nextSortOrder = (lastCharacter?.sortOrder || 0) + 1;

      return ctx.db.voiceCharacter.create({
        data: {
          ...input,
          apiVoiceName: input.characterNameEn.toLowerCase(),
          sortOrder: nextSortOrder,
          isActive: true,
        },
      });
    }),

  // 删除角色
  deleteCharacter: protectedProcedure
    .input(z.object({
      id: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查是否有相关的音频生成记录
      const hasGenerations = await ctx.db.audioGeneration.findFirst({
        where: { characterId: input.id },
      });

      if (hasGenerations) {
        throw new TRPCError({
          code: 'PRECONDITION_FAILED',
          message: '该角色已有生成记录，无法删除。请先禁用该角色。',
        });
      }

      return ctx.db.voiceCharacter.delete({
        where: { id: input.id },
      });
    }),

  // 批量更新排序
  updateSortOrder: protectedProcedure
    .input(z.object({
      updates: z.array(z.object({
        id: z.string(),
        sortOrder: z.number().int().min(0),
      })),
    }))
    .mutation(async ({ ctx, input }) => {
      const { updates } = input;

      // 使用事务批量更新
      return ctx.db.$transaction(
        updates.map(update =>
          ctx.db.voiceCharacter.update({
            where: { id: update.id },
            data: { sortOrder: update.sortOrder },
          })
        )
      );
    }),

  // 批量启用/禁用
  bulkToggleActive: protectedProcedure
    .input(z.object({
      ids: z.array(z.string()),
      isActive: z.boolean(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { ids, isActive } = input;

      return ctx.db.voiceCharacter.updateMany({
        where: {
          id: { in: ids },
        },
        data: { isActive },
      });
    }),

  // 上传头像
  uploadAvatar: protectedProcedure
    .input(z.object({
      characterId: z.string(),
      avatarUrl: z.string().url(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { characterId, avatarUrl } = input;

      return ctx.db.voiceCharacter.update({
        where: { id: characterId },
        data: { avatarUrl },
      });
    }),

  // 获取角色统计
  getCharacterStats: protectedProcedure
    .query(async ({ ctx }) => {
      const total = await ctx.db.voiceCharacter.count();
      const active = await ctx.db.voiceCharacter.count({
        where: { isActive: true },
      });
      const byGender = await ctx.db.voiceCharacter.groupBy({
        by: ['gender'],
        _count: { gender: true },
      });
      const byProvider = await ctx.db.voiceCharacter.groupBy({
        by: ['apiProvider'],
        _count: { apiProvider: true },
      });

      return {
        total,
        active,
        inactive: total - active,
        byGender: byGender.reduce((acc, item) => {
          acc[item.gender] = item._count.gender;
          return acc;
        }, {} as Record<string, number>),
        byProvider: byProvider.reduce((acc, item) => {
          acc[item.apiProvider] = item._count.apiProvider;
          return acc;
        }, {} as Record<string, number>),
      };
    }),
});
