import { z } from "zod";
import { createTRPCRouter, publicProcedure, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

export const communityRouter = createTRPCRouter({
  // 搜索风格和角色
  search: publicProcedure
    .input(z.object({
      query: z.string().min(1).max(100),
      type: z.enum(['all', 'styles', 'characters']).default('all'),
      categoryId: z.string().optional(),
      sortBy: z.enum(['popular', 'latest', 'favorites']).default('popular'),
      limit: z.number().min(1).max(50).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ ctx, input }) => {
      try {
        const { query, type, categoryId, sortBy, limit, offset } = input;

        let styles: any[] = [];
        let characters: any[] = [];

        // 构建排序条件
        const getOrderBy = () => {
          switch (sortBy) {
            case 'latest':
              return { createdAt: 'desc' as const };
            case 'favorites':
              return { favoriteCount: 'desc' as const };
            case 'popular':
            default:
              return [
                { favoriteCount: 'desc' as const },
                { usageCount: 'desc' as const },
                { createdAt: 'desc' as const },
              ];
          }
        };

        // 搜索风格
        if (type === 'all' || type === 'styles') {
          const styleWhere = {
            isPublic: true,
            AND: [
              {
                OR: [
                  { name: { contains: query, mode: 'insensitive' as const } },
                  { description: { contains: query, mode: 'insensitive' as const } },
                  { tags: { hasSome: [query] } },
                ],
              },
              categoryId ? { categoryId } : {},
            ],
          };

          styles = await ctx.db.userStyle.findMany({
            where: styleWhere,
            include: {
              category: true,
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
              _count: {
                select: {
                  favorites: true,
                },
              },
            },
            orderBy: getOrderBy(),
            take: type === 'styles' ? limit : Math.ceil(limit / 2),
            skip: type === 'styles' ? offset : 0,
          });
        }

        // 搜索角色
        if (type === 'all' || type === 'characters') {
          const characterWhere = {
            isPublic: true,
            OR: [
              { name: { contains: query, mode: 'insensitive' as const } },
              { description: { contains: query, mode: 'insensitive' as const } },
              { tags: { hasSome: [query] } },
              { personality: { hasSome: [query] } },
              { bestFor: { hasSome: [query] } },
            ],
          };

          characters = await ctx.db.customVoiceCharacter.findMany({
            where: characterWhere,
            include: {
              baseCharacter: true,
              defaultStyle: {
                include: {
                  category: true,
                },
              },
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
              _count: {
                select: {
                  favorites: true,
                },
              },
            },
            orderBy: getOrderBy(),
            take: type === 'characters' ? limit : Math.ceil(limit / 2),
            skip: type === 'characters' ? offset : 0,
          });
        }

        // 获取总数
        const totalStyles = type === 'all' || type === 'styles' 
          ? await ctx.db.userStyle.count({
              where: {
                isPublic: true,
                AND: [
                  {
                    OR: [
                      { name: { contains: query, mode: 'insensitive' as const } },
                      { description: { contains: query, mode: 'insensitive' as const } },
                      { tags: { hasSome: [query] } },
                    ],
                  },
                  categoryId ? { categoryId } : {},
                ],
              },
            })
          : 0;

        const totalCharacters = type === 'all' || type === 'characters'
          ? await ctx.db.customVoiceCharacter.count({
              where: {
                isPublic: true,
                OR: [
                  { name: { contains: query, mode: 'insensitive' as const } },
                  { description: { contains: query, mode: 'insensitive' as const } },
                  { tags: { hasSome: [query] } },
                  { personality: { hasSome: [query] } },
                  { bestFor: { hasSome: [query] } },
                ],
              },
            })
          : 0;

        console.log(`✅ 社区搜索: "${query}" - 找到 ${styles.length} 个风格, ${characters.length} 个角色`);

        return {
          styles,
          characters,
          total: {
            styles: totalStyles,
            characters: totalCharacters,
          },
          hasMore: {
            styles: offset + styles.length < totalStyles,
            characters: offset + characters.length < totalCharacters,
          },
        };
      } catch (error) {
        console.error('Community search error:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '搜索失败',
        });
      }
    }),

  // 获取热门内容
  getTrending: publicProcedure
    .input(z.object({
      type: z.enum(['styles', 'characters']),
      period: z.enum(['day', 'week', 'month', 'all']).default('week'),
      limit: z.number().min(1).max(50).default(12),
    }))
    .query(async ({ ctx, input }) => {
      try {
        const { type, period, limit } = input;

        // 计算时间范围
        const now = new Date();
        let dateFilter = {};
        
        if (period !== 'all') {
          const daysAgo = period === 'day' ? 1 : period === 'week' ? 7 : 30;
          const startDate = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);
          dateFilter = { createdAt: { gte: startDate } };
        }

        if (type === 'styles') {
          const styles = await ctx.db.userStyle.findMany({
            where: {
              isPublic: true,
              ...dateFilter,
            },
            include: {
              category: true,
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
              _count: {
                select: {
                  favorites: true,
                },
              },
            },
            orderBy: [
              { favoriteCount: 'desc' },
              { usageCount: 'desc' },
              { createdAt: 'desc' },
            ],
            take: limit,
          });

          return { styles };
        } else {
          const characters = await ctx.db.customVoiceCharacter.findMany({
            where: {
              isPublic: true,
              ...dateFilter,
            },
            include: {
              baseCharacter: true,
              defaultStyle: {
                include: {
                  category: true,
                },
              },
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
              _count: {
                select: {
                  favorites: true,
                },
              },
            },
            orderBy: [
              { favoriteCount: 'desc' },
              { usageCount: 'desc' },
              { createdAt: 'desc' },
            ],
            take: limit,
          });

          return { characters };
        }
      } catch (error) {
        console.error('Get trending error:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取热门内容失败',
        });
      }
    }),

  // 获取社区统计
  getStats: publicProcedure
    .query(async ({ ctx }) => {
      try {
        const [
          totalStyles,
          totalCharacters,
          totalUsers,
          totalShares,
          totalFavorites,
        ] = await Promise.all([
          ctx.db.userStyle.count({ where: { isPublic: true } }),
          ctx.db.customVoiceCharacter.count({ where: { isPublic: true } }),
          ctx.db.user.count(),
          ctx.db.styleShare.count() + ctx.db.characterShare.count(),
          ctx.db.styleFavorite.count() + ctx.db.characterFavorite.count(),
        ]);

        return {
          totalStyles,
          totalCharacters,
          totalUsers,
          totalShares,
          totalFavorites,
        };
      } catch (error) {
        console.error('Get stats error:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取统计数据失败',
        });
      }
    }),

  // 获取推荐内容
  getRecommendations: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(20).default(8),
    }))
    .query(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;

        // 获取用户收藏的风格分类
        const userFavoriteCategories = await ctx.db.styleFavorite.findMany({
          where: { userId },
          include: {
            style: {
              select: { categoryId: true },
            },
          },
        });

        const preferredCategoryIds = [
          ...new Set(
            userFavoriteCategories
              .map(fav => fav.style.categoryId)
              .filter(Boolean)
          ),
        ];

        // 基于用户偏好推荐风格
        const recommendedStyles = await ctx.db.userStyle.findMany({
          where: {
            isPublic: true,
            userId: { not: userId }, // 排除自己的风格
            categoryId: preferredCategoryIds.length > 0 
              ? { in: preferredCategoryIds }
              : undefined,
          },
          include: {
            category: true,
            user: {
              select: {
                id: true,
                name: true,
              },
            },
            _count: {
              select: {
                favorites: true,
              },
            },
          },
          orderBy: [
            { favoriteCount: 'desc' },
            { usageCount: 'desc' },
          ],
          take: Math.ceil(input.limit / 2),
        });

        // 推荐热门角色
        const recommendedCharacters = await ctx.db.customVoiceCharacter.findMany({
          where: {
            isPublic: true,
            userId: { not: userId }, // 排除自己的角色
          },
          include: {
            baseCharacter: true,
            defaultStyle: {
              include: {
                category: true,
              },
            },
            user: {
              select: {
                id: true,
                name: true,
              },
            },
            _count: {
              select: {
                favorites: true,
              },
            },
          },
          orderBy: [
            { favoriteCount: 'desc' },
            { usageCount: 'desc' },
          ],
          take: Math.floor(input.limit / 2),
        });

        console.log(`✅ 为用户 ${userId} 生成推荐: ${recommendedStyles.length} 个风格, ${recommendedCharacters.length} 个角色`);

        return {
          styles: recommendedStyles,
          characters: recommendedCharacters,
        };
      } catch (error) {
        console.error('Get recommendations error:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取推荐内容失败',
        });
      }
    }),
});
