import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 超级管理员权限检查中间件
const superAdminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });

  if (!user || user.role !== 'SUPER_ADMIN') {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Super admin access required',
    });
  }

  return next({
    ctx: {
      ...ctx,
      user: {
        ...ctx.session.user,
        role: user.role,
      },
    },
  });
});

// 风格类型枚举
const StyleTypeSchema = z.enum(['SINGLE_SPEAKER', 'MULTI_SPEAKER']);
const StyleSourceSchema = z.enum(['OFFICIAL', 'USER_CREATED', 'COMMUNITY']);

export const adminRouter = createTRPCRouter({
  // 获取当前用户角色（用于权限检查）
  getUserRole: protectedProcedure
    .query(async ({ ctx }) => {
      // 先尝试通过ID查找
      let user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { id: true, email: true, role: true },
      });

      // 如果通过ID找不到，尝试通过邮箱查找（处理session ID不一致的情况）
      if (!user && ctx.session.user.email) {
        user = await ctx.db.user.findUnique({
          where: { email: ctx.session.user.email },
          select: { id: true, email: true, role: true },
        });
      }

      return user;
    }),

  // 获取用户列表
  getUsers: superAdminProcedure
    .input(z.object({
      page: z.number().default(1),
      limit: z.number().default(20),
      search: z.string().optional(),
      role: z.enum(['USER', 'ADMIN', 'SUPER_ADMIN']).optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { page, limit, search, role } = input;
      const skip = (page - 1) * limit;

      const where = {
        ...(search && {
          OR: [
            { name: { contains: search, mode: 'insensitive' as const } },
            { email: { contains: search, mode: 'insensitive' as const } },
          ],
        }),
        ...(role && role !== 'ADMIN' && { role }),
      };

      const [users, total] = await Promise.all([
        ctx.db.user.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            credits: true,
            usedCredits: true,
            isActive: true,
            lastActiveAt: true,
            createdAt: true,
            updatedAt: true,
            _count: {
              select: {
                audioGenerations: true,
              },
            },
          },
        }),
        ctx.db.user.count({ where }),
      ]);

      return {
        users,
        total,
        pages: Math.ceil(total / limit),
        currentPage: page,
      };
    }),

  // 调整用户配额（新版本，支持标准和专业配额）
  adjustUserQuota: superAdminProcedure
    .input(z.object({
      userId: z.string(),
      quotaType: z.enum(['STANDARD', 'PROFESSIONAL']),
      amount: z.number(),
      reason: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { userId, quotaType, amount, reason } = input;

      // 检查用户是否存在
      const user = await ctx.db.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }

      // 更新积分
      const updateData = { credits: { increment: amount } };

      // 执行配额调整
      const [updatedUser, adjustment] = await Promise.all([
        ctx.db.user.update({
          where: { id: userId },
          data: updateData,
        }),
        ctx.db.quotaAdjustment.create({
          data: {
            userId,
            quotaType,
            amount,
            reason,
            adminId: ctx.session.user.id,
          },
        }),
      ]);

      return {
        user: updatedUser,
        adjustment,
      };
    }),

  // 兼容旧版本的配额调整（已废弃，保持向后兼容）
  adjustUserQuotaLegacy: superAdminProcedure
    .input(z.object({
      userId: z.string(),
      amount: z.number(),
      reason: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { userId, amount, reason } = input;

      // 检查用户是否存在
      const user = await ctx.db.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }

      // 执行配额调整（更新新字段）
      const [updatedUser, adjustment] = await Promise.all([
        ctx.db.user.update({
          where: { id: userId },
          data: {
            credits: {
              increment: amount,
            },
          },
        }),
        ctx.db.quotaAdjustment.create({
          data: {
            userId,
            quotaType: 'STANDARD',
            amount,
            reason,
            adminId: ctx.session.user.id,
          },
        }),
      ]);

      return {
        user: updatedUser,
        adjustment,
      };
    }),

  // 重置用户已用配额
  resetUserUsage: superAdminProcedure
    .input(z.object({
      userId: z.string(),
      reason: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { userId, reason } = input;

      const user = await ctx.db.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }

      const resetAmount = -user.usedCredits;

      const [updatedUser, adjustment] = await Promise.all([
        ctx.db.user.update({
          where: { id: userId },
          data: {
            usedCredits: 0,
          },
        }),
        ctx.db.quotaAdjustment.create({
          data: {
            userId,
            quotaType: 'STANDARD', // 保持兼容性，但实际上是积分重置
            amount: resetAmount,
            reason: `Reset credits usage: ${reason}`,
            adminId: ctx.session.user.id,
          },
        }),
      ]);

      return {
        user: updatedUser,
        adjustment: adjustment,
      };
    }),

  // 更新用户角色
  updateUserRole: superAdminProcedure
    .input(z.object({
      userId: z.string(),
      role: z.enum(['USER', 'SUPER_ADMIN']),
    }))
    .mutation(async ({ ctx, input }) => {
      const { userId, role } = input;

      // 只有SUPER_ADMIN可以设置SUPER_ADMIN角色
      if (role === 'SUPER_ADMIN' && ctx.user.role !== 'SUPER_ADMIN') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Only super admin can assign super admin roles',
        });
      }

      const updatedUser = await ctx.db.user.update({
        where: { id: userId },
        data: { role },
      });

      return updatedUser;
    }),

  // 获取配额调整历史
  getQuotaAdjustments: superAdminProcedure
    .input(z.object({
      userId: z.string().optional(),
      page: z.number().default(1),
      limit: z.number().default(20),
    }))
    .query(async ({ ctx, input }) => {
      const { userId, page, limit } = input;
      const skip = (page - 1) * limit;

      const where = userId ? { userId } : {};

      const [adjustments, total] = await Promise.all([
        ctx.db.quotaAdjustment.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        }),
        ctx.db.quotaAdjustment.count({ where }),
      ]);

      return {
        adjustments,
        total,
        pages: Math.ceil(total / limit),
        currentPage: page,
      };
    }),

  // 获取系统统计 (别名为getStats)
  getStats: superAdminProcedure
    .query(async ({ ctx }) => {
      const [
        totalUsers,
        activeUsers,
        totalGenerations,
        todayGenerations,
        todayNewUsers,
        totalCharacters,
        todayCharacters,
      ] = await Promise.all([
        ctx.db.user.count(),
        ctx.db.user.count({
          where: {
            audioGenerations: {
              some: {
                createdAt: {
                  gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天内活跃
                },
              },
            },
          },
        }),
        ctx.db.audioGeneration.count(),
        ctx.db.audioGeneration.count({
          where: {
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0)),
            },
          },
        }),
        ctx.db.user.count({
          where: {
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0)),
            },
          },
        }),
        ctx.db.audioGeneration.aggregate({
          _sum: {
            characterCount: true,
          },
        }),
        ctx.db.audioGeneration.aggregate({
          where: {
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0)),
            },
          },
          _sum: {
            characterCount: true,
          },
        }),
      ]);

      return {
        totalUsers,
        activeUsers,
        totalGenerations,
        todayGenerations,
        todayNewUsers,
        totalCharacters: totalCharacters._sum.characterCount || 0,
        todayCharacters: todayCharacters._sum.characterCount || 0,
      };
    }),

  // 获取用户统计
  getUserStats: superAdminProcedure
    .query(async ({ ctx }) => {
      const [
        total,
        active,
        admins,
        todayNew,
      ] = await Promise.all([
        ctx.db.user.count(),
        ctx.db.user.count({
          where: { isActive: true },
        }),
        ctx.db.user.count({
          where: {
            role: 'SUPER_ADMIN',
          },
        }),
        ctx.db.user.count({
          where: {
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0)),
            },
          },
        }),
      ]);

      return {
        total,
        active,
        admins,
        todayNew,
      };
    }),

  // 切换用户状态
  toggleUserStatus: superAdminProcedure
    .input(z.object({
      userId: z.string(),
      isActive: z.boolean(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { userId, isActive } = input;

      const updatedUser = await ctx.db.user.update({
        where: { id: userId },
        data: { isActive },
      });

      return updatedUser;
    }),

  // 获取系统统计 (保持向后兼容)
  getSystemStats: superAdminProcedure
    .query(async ({ ctx }) => {
      const [
        totalUsers,
        activeUsers,
        totalGenerations,
        todayGenerations,
        totalCharacters,
        todayCharacters,
      ] = await Promise.all([
        ctx.db.user.count(),
        ctx.db.user.count({
          where: {
            audioGenerations: {
              some: {
                createdAt: {
                  gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天内活跃
                },
              },
            },
          },
        }),
        ctx.db.audioGeneration.count(),
        ctx.db.audioGeneration.count({
          where: {
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0)),
            },
          },
        }),
        ctx.db.audioGeneration.aggregate({
          _sum: {
            characterCount: true,
          },
        }),
        ctx.db.audioGeneration.aggregate({
          where: {
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0)),
            },
          },
          _sum: {
            characterCount: true,
          },
        }),
      ]);

      return {
        totalUsers,
        activeUsers,
        totalGenerations,
        todayGenerations,
        totalCharacters: totalCharacters._sum.characterCount || 0,
        todayCharacters: todayCharacters._sum.characterCount || 0,
      };
    }),

  // 获取用户配额调整历史
  getUserQuotaHistory: superAdminProcedure
    .input(z.object({
      userId: z.string(),
      page: z.number().default(1),
      limit: z.number().default(10),
    }))
    .query(async ({ ctx, input }) => {
      const { userId, page, limit } = input;
      const skip = (page - 1) * limit;

      const [adjustments, total] = await Promise.all([
        ctx.db.quotaAdjustment.findMany({
          where: { userId },
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        }),
        ctx.db.quotaAdjustment.count({
          where: { userId },
        }),
      ]);

      return {
        adjustments,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    }),

  // ============================================================================
  // 风格管理 API - Style Management
  // ============================================================================

  // 获取所有风格（包括用户和官方风格）
  getAllStyles: superAdminProcedure
    .input(z.object({
      search: z.string().optional(),
      categoryId: z.string().optional(),
      status: z.string().optional(), // 'active' | 'inactive'
      type: StyleTypeSchema.optional(),
      includeUserStyles: z.boolean().default(false),
      limit: z.number().default(50),
      offset: z.number().default(0),
    }))
    .query(async ({ ctx, input }) => {
      const { search, categoryId, status, type, includeUserStyles, limit, offset } = input;

      // 构建查询条件
      const where: any = {};

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { nameEn: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { tags: { hasSome: [search] } },
        ];
      }

      if (categoryId && categoryId !== 'all') {
        where.categoryId = categoryId;
      }

      if (status && status !== 'all') {
        where.isActive = status === 'active';
      }

      if (type) {
        where.type = type;
      }

      // 只显示官方风格，除非明确要求包含用户风格
      if (!includeUserStyles) {
        where.isOfficial = true;
      }

      const [styles, total] = await Promise.all([
        ctx.db.userStyle.findMany({
          where,
          include: {
            category: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            _count: {
              select: {
                favorites: true,
                audioGenerations: true,
              },
            },
          },
          orderBy: [
            { isOfficial: 'desc' },
            { favoriteCount: 'desc' },
            { usageCount: 'desc' },
            { createdAt: 'desc' },
          ],
          take: limit,
          skip: offset,
        }),
        ctx.db.userStyle.count({ where }),
      ]);

      return {
        styles: styles.map(style => ({
          ...style,
          creator: style.user,
          favoriteCount: style._count.favorites,
          usageCount: style._count.audioGenerations,
        })),
        total,
        page: Math.floor(offset / limit) + 1,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    }),

  // 创建官方风格
  createSystemStyle: superAdminProcedure
    .input(z.object({
      name: z.string().min(1, '风格名称不能为空'),
      nameEn: z.string().optional(),
      description: z.string().optional(),
      prompt: z.string().min(1, '提示词不能为空'),
      type: StyleTypeSchema.default('SINGLE_SPEAKER'),
      categoryId: z.string().optional(),
      tags: z.array(z.string()).default([]),
      parameters: z.record(z.any()).optional(),
      speakerConfig: z.record(z.any()).optional(),
      icon: z.string().optional(),
      color: z.string().optional(),
      isPublic: z.boolean().default(true),
      isOfficial: z.boolean().default(true),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // 验证分类是否存在
        if (input.categoryId) {
          const category = await ctx.db.styleCategory.findUnique({
            where: { id: input.categoryId },
          });
          if (!category) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: '指定的分类不存在',
            });
          }
        }

        const style = await ctx.db.userStyle.create({
          data: {
            ...input,
            userId: ctx.session.user.id, // 系统管理员作为创建者
            source: 'OFFICIAL',
          },
          include: {
            category: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });

        return style;
      } catch (error) {
        console.error('Error creating system style:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '创建官方风格失败',
        });
      }
    }),

  // 更新风格（管理员权限）
  updateStyle: superAdminProcedure
    .input(z.object({
      id: z.string(),
      name: z.string().optional(),
      nameEn: z.string().optional(),
      description: z.string().optional(),
      prompt: z.string().optional(),
      type: StyleTypeSchema.optional(),
      categoryId: z.string().optional(),
      tags: z.array(z.string()).optional(),
      parameters: z.record(z.any()).optional(),
      speakerConfig: z.record(z.any()).optional(),
      icon: z.string().optional(),
      color: z.string().optional(),
      isPublic: z.boolean().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { id, categoryId, ...updateData } = input;

        // 验证风格是否存在
        const existingStyle = await ctx.db.userStyle.findUnique({
          where: { id },
        });

        if (!existingStyle) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '风格不存在',
          });
        }

        // 验证分类是否存在
        if (categoryId) {
          const category = await ctx.db.styleCategory.findUnique({
            where: { id: categoryId },
          });
          if (!category) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: '指定的分类不存在',
            });
          }
        }

        const updatedStyle = await ctx.db.userStyle.update({
          where: { id },
          data: {
            ...updateData,
            categoryId,
          },
          include: {
            category: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });

        return updatedStyle;
      } catch (error) {
        console.error('Error updating style:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '更新风格失败',
        });
      }
    }),

  // 删除风格（管理员权限）
  deleteStyle: superAdminProcedure
    .input(z.object({
      id: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { id } = input;

        // 验证风格是否存在
        const existingStyle = await ctx.db.userStyle.findUnique({
          where: { id },
          include: {
            _count: {
              select: {
                audioGenerations: true,
                favorites: true,
              },
            },
          },
        });

        if (!existingStyle) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '风格不存在',
          });
        }

        // 检查是否有关联的音频生成记录
        if (existingStyle._count.audioGenerations > 0) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: '该风格已被使用生成音频，无法删除',
          });
        }

        await ctx.db.userStyle.delete({
          where: { id },
        });

        return { success: true };
      } catch (error) {
        console.error('Error deleting style:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '删除风格失败',
        });
      }
    }),

  // 切换风格状态（启用/禁用）
  toggleStyleStatus: superAdminProcedure
    .input(z.object({
      id: z.string(),
      isActive: z.boolean(),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { id, isActive } = input;

        const updatedStyle = await ctx.db.userStyle.update({
          where: { id },
          data: { isActive },
          include: {
            category: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });

        return updatedStyle;
      } catch (error) {
        console.error('Error toggling style status:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '更新风格状态失败',
        });
      }
    }),

  // ============================================================================
  // 对话模板管理 API - Conversation Template Management
  // ============================================================================

  // 获取所有模板（包括用户和官方模板）
  getAllTemplates: superAdminProcedure
    .input(z.object({
      search: z.string().optional(),
      includeUserTemplates: z.boolean().default(false),
      limit: z.number().default(50),
      offset: z.number().default(0),
    }))
    .query(async ({ ctx, input }) => {
      const { search, includeUserTemplates, limit, offset } = input;

      // 构建查询条件
      const where: any = {};

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      // 只显示官方模板，除非明确要求包含用户模板
      if (!includeUserTemplates) {
        where.userId = null; // 官方模板
      }

      const [templates, total] = await Promise.all([
        ctx.db.conversationTemplate.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: [
            { userId: 'asc' }, // 官方模板优先
            { createdAt: 'desc' },
          ],
          take: limit,
          skip: offset,
        }),
        ctx.db.conversationTemplate.count({ where }),
      ]);

      return {
        templates: templates.map(template => ({
          ...template,
          characters: JSON.parse(template.characters),
          dialogues: JSON.parse(template.dialogues),
          isOfficial: template.userId === null,
        })),
        total,
        page: Math.floor(offset / limit) + 1,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    }),

  // 创建官方模板
  createSystemTemplate: superAdminProcedure
    .input(z.object({
      name: z.string().min(1, '模板名称不能为空'),
      description: z.string().optional(),
      characters: z.array(z.object({
        name: z.string(),
        voiceId: z.string(),
        speed: z.number().min(0.5).max(2.0),
        pitch: z.number().min(-20).max(20),
        volume: z.number().min(0.1).max(2.0),
        emotion: z.string(),
      })),
      dialogues: z.array(z.object({
        characterId: z.string(),
        text: z.string().min(1),
        order: z.number(),
      })),
      isPublic: z.boolean().default(true),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const template = await ctx.db.conversationTemplate.create({
          data: {
            userId: null, // 官方模板
            name: input.name,
            description: input.description,
            characters: JSON.stringify(input.characters),
            dialogues: JSON.stringify(input.dialogues),
            isPublic: input.isPublic,
          },
        });

        return {
          ...template,
          characters: input.characters,
          dialogues: input.dialogues,
          isOfficial: true,
        };
      } catch (error) {
        console.error('Error creating system template:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '创建官方模板失败',
        });
      }
    }),

  // 更新模板（管理员权限）
  updateTemplate: superAdminProcedure
    .input(z.object({
      id: z.string(),
      name: z.string().optional(),
      description: z.string().optional(),
      characters: z.array(z.object({
        name: z.string(),
        voiceId: z.string(),
        speed: z.number().min(0.5).max(2.0),
        pitch: z.number().min(-20).max(20),
        volume: z.number().min(0.1).max(2.0),
        emotion: z.string(),
      })).optional(),
      dialogues: z.array(z.object({
        characterId: z.string(),
        text: z.string().min(1),
        order: z.number(),
      })).optional(),
      isPublic: z.boolean().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { id, characters, dialogues, ...updateData } = input;

        // 验证模板是否存在
        const existingTemplate = await ctx.db.conversationTemplate.findUnique({
          where: { id },
        });

        if (!existingTemplate) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '模板不存在',
          });
        }

        const updatedTemplate = await ctx.db.conversationTemplate.update({
          where: { id },
          data: {
            ...updateData,
            characters: characters ? JSON.stringify(characters) : undefined,
            dialogues: dialogues ? JSON.stringify(dialogues) : undefined,
          },
        });

        return {
          ...updatedTemplate,
          characters: characters ? characters : JSON.parse(updatedTemplate.characters),
          dialogues: dialogues ? dialogues : JSON.parse(updatedTemplate.dialogues),
          isOfficial: updatedTemplate.userId === null,
        };
      } catch (error) {
        console.error('Error updating template:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '更新模板失败',
        });
      }
    }),

  // 删除模板（管理员权限）
  deleteTemplate: superAdminProcedure
    .input(z.object({
      id: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { id } = input;

        // 验证模板是否存在
        const existingTemplate = await ctx.db.conversationTemplate.findUnique({
          where: { id },
        });

        if (!existingTemplate) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '模板不存在',
          });
        }

        await ctx.db.conversationTemplate.delete({
          where: { id },
        });

        return { success: true };
      } catch (error) {
        console.error('Error deleting template:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '删除模板失败',
        });
      }
    }),
});
