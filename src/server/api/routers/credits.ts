import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

export const creditsRouter = createTRPCRouter({
  
  // 获取用户积分余额
  getBalance: protectedProcedure
    .query(async ({ ctx }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: {
          credits: true,
          usedCredits: true,
        },
      });

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }

      return {
        totalCredits: user.credits,
        usedCredits: user.usedCredits,
        availableCredits: user.credits - user.usedCredits,
      };
    }),

  // 消耗积分
  consumeCredits: protectedProcedure
    .input(z.object({
      amount: z.number().min(1),
      type: z.enum(['VOICE_GENERATION_FAST', 'VOICE_GENERATION_HIGH', 'TEXT_GENERATION', 'MULTI_SPEAKER', 'API_USAGE', 'OTHER']),
      description: z.string().optional(),
      metadata: z.record(z.any()).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { amount, type, description, metadata } = input;

      // 检查用户积分余额
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: {
          credits: true,
          usedCredits: true,
        },
      });

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }

      const availableCredits = user.credits - user.usedCredits;
      if (availableCredits < amount) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: `积分不足。当前可用积分：${availableCredits}，需要：${amount}`,
        });
      }

      // 使用事务确保数据一致性
      const result = await ctx.db.$transaction(async (tx) => {
        // 更新用户已使用积分
        const updatedUser = await tx.user.update({
          where: { id: ctx.session.user.id },
          data: {
            usedCredits: {
              increment: amount,
            },
          },
          select: {
            credits: true,
            usedCredits: true,
          },
        });

        // 记录积分使用历史
        await tx.creditUsage.create({
          data: {
            userId: ctx.session.user.id,
            amount,
            type,
            description,
            metadata,
          },
        });

        return updatedUser;
      });

      return {
        success: true,
        consumedCredits: amount,
        remainingCredits: result.credits - result.usedCredits,
        description,
      };
    }),

  // 充值积分（管理员功能或支付完成后调用）
  addCredits: protectedProcedure
    .input(z.object({
      amount: z.number().min(1),
      description: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { amount, description } = input;

      const updatedUser = await ctx.db.user.update({
        where: { id: ctx.session.user.id },
        data: {
          credits: {
            increment: amount,
          },
        },
        select: {
          credits: true,
          usedCredits: true,
        },
      });

      return {
        success: true,
        addedCredits: amount,
        totalCredits: updatedUser.credits,
        availableCredits: updatedUser.credits - updatedUser.usedCredits,
        description,
      };
    }),

  // 获取积分使用历史
  getUsageHistory: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
      type: z.enum(['VOICE_GENERATION_FAST', 'VOICE_GENERATION_HIGH', 'TEXT_GENERATION', 'MULTI_SPEAKER', 'API_USAGE', 'OTHER']).optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { limit, offset, type } = input;

      const where = {
        userId: ctx.session.user.id,
        ...(type && { type }),
      };

      const [records, total] = await Promise.all([
        ctx.db.creditUsage.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip: offset,
          select: {
            id: true,
            amount: true,
            type: true,
            description: true,
            metadata: true,
            createdAt: true,
          },
        }),
        ctx.db.creditUsage.count({ where }),
      ]);

      return {
        records,
        total,
        hasMore: offset + limit < total,
      };
    }),

  // 获取积分赠送历史
  getGiftHistory: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
      type: z.enum(['sent', 'received']).default('received'),
    }))
    .query(async ({ ctx, input }) => {
      const { limit, offset, type } = input;

      const where = type === 'sent'
        ? { fromUserId: ctx.session.user.id }
        : { toUserId: ctx.session.user.id };

      const [records, total] = await Promise.all([
        ctx.db.creditGift.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip: offset,
          include: {
            fromUser: {
              select: { id: true, name: true, email: true },
            },
            toUser: {
              select: { id: true, name: true, email: true },
            },
          },
        }),
        ctx.db.creditGift.count({ where }),
      ]);

      return {
        records,
        total,
        hasMore: offset + limit < total,
      };
    }),
  // 赠送积分（用户间赠送）
  giftCredits: protectedProcedure
    .input(z.object({
      toUserId: z.string(),
      amount: z.number().min(1).max(1000), // 限制单次赠送最多1000积分
      reason: z.string().min(1).max(200),
    }))
    .mutation(async ({ ctx, input }) => {
      const { toUserId, amount, reason } = input;

      // 检查赠送者积分余额
      const fromUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: {
          credits: true,
          usedCredits: true,
        },
      });

      if (!fromUser) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }

      const availableCredits = fromUser.credits - fromUser.usedCredits;
      if (availableCredits < amount) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: `积分不足。当前可用积分：${availableCredits}，需要：${amount}`,
        });
      }

      // 检查接收者是否存在
      const toUser = await ctx.db.user.findUnique({
        where: { id: toUserId },
        select: { id: true, name: true },
      });

      if (!toUser) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '接收者不存在',
        });
      }

      // 不能赠送给自己
      if (ctx.session.user.id === toUserId) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '不能赠送积分给自己',
        });
      }

      // 使用事务处理赠送
      const result = await ctx.db.$transaction(async (tx) => {
        // 扣除赠送者积分
        await tx.user.update({
          where: { id: ctx.session.user.id },
          data: {
            usedCredits: { increment: amount },
          },
        });

        // 增加接收者积分
        await tx.user.update({
          where: { id: toUserId },
          data: {
            credits: { increment: amount },
          },
        });

        // 记录赠送历史
        const gift = await tx.creditGift.create({
          data: {
            fromUserId: ctx.session.user.id,
            toUserId,
            amount,
            reason,
            type: 'USER_GIFT',
          },
          include: {
            toUser: {
              select: { name: true, email: true },
            },
          },
        });

        return gift;
      });

      return {
        success: true,
        gift: result,
      };
    }),

  // 系统赠送积分（管理员功能）
  systemGift: protectedProcedure
    .input(z.object({
      toUserId: z.string(),
      amount: z.number().min(1),
      reason: z.string().min(1).max(200),
      type: z.enum(['WELCOME_BONUS', 'REFERRAL_BONUS', 'ADMIN_GIFT', 'PROMOTION']),
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查权限（只有管理员可以系统赠送）
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (!currentUser || (currentUser.role !== 'ADMIN' && currentUser.role !== 'SUPER_ADMIN')) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '权限不足',
        });
      }

      const { toUserId, amount, reason, type } = input;

      // 检查接收者是否存在
      const toUser = await ctx.db.user.findUnique({
        where: { id: toUserId },
        select: { id: true, name: true },
      });

      if (!toUser) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '接收者不存在',
        });
      }

      // 使用事务处理系统赠送
      const result = await ctx.db.$transaction(async (tx) => {
        // 增加接收者积分
        await tx.user.update({
          where: { id: toUserId },
          data: {
            credits: { increment: amount },
          },
        });

        // 记录赠送历史
        const gift = await tx.creditGift.create({
          data: {
            fromUserId: null, // 系统赠送
            toUserId,
            amount,
            reason,
            type,
          },
          include: {
            toUser: {
              select: { name: true, email: true },
            },
          },
        });

        return gift;
      });

      return {
        success: true,
        gift: result,
      };
    }),

  // 推荐奖励
  referralBonus: protectedProcedure
    .input(z.object({
      referredUserId: z.string(), // 被推荐用户ID
    }))
    .mutation(async ({ ctx, input }) => {
      const { referredUserId } = input;
      const referrerId = ctx.session.user.id;

      // 检查被推荐用户是否存在且是新用户
      const referredUser = await ctx.db.user.findUnique({
        where: { id: referredUserId },
        select: {
          id: true,
          name: true,
          createdAt: true,
          receivedGifts: {
            where: { type: 'REFERRAL_BONUS' },
            select: { id: true },
          },
        },
      });

      if (!referredUser) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '被推荐用户不存在',
        });
      }

      // 检查是否已经获得过推荐奖励
      if (referredUser.receivedGifts.length > 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '该用户已经获得过推荐奖励',
        });
      }

      // 检查是否是新用户（注册7天内）
      const daysSinceRegistration = Math.floor(
        (Date.now() - referredUser.createdAt.getTime()) / (1000 * 60 * 60 * 24)
      );

      if (daysSinceRegistration > 7) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '推荐奖励仅适用于注册7天内的新用户',
        });
      }

      // 推荐奖励配置
      const REFERRER_BONUS = 500; // 推荐者获得500积分
      const REFERRED_BONUS = 200;  // 被推荐者获得200积分

      // 使用事务处理推荐奖励
      const result = await ctx.db.$transaction(async (tx) => {
        // 给推荐者增加积分
        await tx.user.update({
          where: { id: referrerId },
          data: {
            credits: { increment: REFERRER_BONUS },
          },
        });

        // 给被推荐者增加积分
        await tx.user.update({
          where: { id: referredUserId },
          data: {
            credits: { increment: REFERRED_BONUS },
          },
        });

        // 记录推荐者奖励
        const referrerGift = await tx.creditGift.create({
          data: {
            fromUserId: null,
            toUserId: referrerId,
            amount: REFERRER_BONUS,
            reason: `推荐用户 ${referredUser.name || '新用户'} 获得奖励`,
            type: 'REFERRAL_BONUS',
          },
        });

        // 记录被推荐者奖励
        const referredGift = await tx.creditGift.create({
          data: {
            fromUserId: null,
            toUserId: referredUserId,
            amount: REFERRED_BONUS,
            reason: '新用户推荐奖励',
            type: 'REFERRAL_BONUS',
          },
        });

        return { referrerGift, referredGift };
      });

      return {
        success: true,
        referrerBonus: REFERRER_BONUS,
        referredBonus: REFERRED_BONUS,
        gifts: result,
      };
    }),

});
