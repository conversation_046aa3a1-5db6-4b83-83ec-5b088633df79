/**
 * 语言角色管理API路由
 * 基于新的数据结构：API模板 + 语言特定角色
 */

import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 超级管理员权限检查中间件
const superAdminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });

  if (!user || user.role !== 'SUPER_ADMIN') {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Super admin access required',
    });
  }

  return next();
});

// 语言角色数据验证模式
const LanguageCharacterSchema = z.object({
  id: z.string().optional(),
  languageId: z.string(),
  templateId: z.string().optional(), // 可为空（自定义角色）
  name: z.string().min(1).max(100),
  description: z.string().min(1),
  gender: z.enum(['MALE', 'FEMALE', 'NEUTRAL']).optional(), // 新增性别字段
  style: z.string().optional(),
  personality: z.string().optional(),
  bestFor: z.string().optional(),
  avatarUrl: z.string().url().optional(),
  isActive: z.boolean().default(true),
  sortOrder: z.number().int().min(0).default(0),
  isCustom: z.boolean().default(false),
  customSettings: z.string().optional(),
  customPrompt: z.string().optional(),
});

// API角色模板管理路由
export const apiCharacterTemplateAdminRouter = createTRPCRouter({
  // 获取所有API角色模板
  getAllTemplates: superAdminProcedure
    .input(z.object({
      includeInactive: z.boolean().default(false),
      apiProvider: z.enum(['GEMINI', 'OPENAI', 'AZURE']).optional(),
    }))
    .query(async ({ ctx, input }) => {
      const where: any = {};

      if (!input.includeInactive) {
        where.isActive = true;
      }

      if (input.apiProvider) {
        where.apiProvider = input.apiProvider;
      }

      const templates = await ctx.db.apiCharacterTemplate.findMany({
        where,
        orderBy: [
          { apiProvider: 'asc' },
          { sortOrder: 'asc' },
          { originalName: 'asc' },
        ],
      });

      return templates;
    }),

  // 创建API角色模板
  createTemplate: superAdminProcedure
    .input(z.object({
      apiProvider: z.enum(['GEMINI', 'OPENAI', 'AZURE']),
      apiVoiceName: z.string().min(1),
      originalName: z.string().min(1),
      gender: z.enum(['MALE', 'FEMALE', 'NEUTRAL']),
      defaultStyle: z.string().optional(),
      defaultDescription: z.string().optional(),
      sortOrder: z.number().int().min(0).default(0),
    }))
    .mutation(async ({ ctx, input }) => {
      const template = await ctx.db.apiCharacterTemplate.create({
        data: input,
      });

      return {
        success: true,
        template,
        message: `API角色模板 ${template.originalName} 创建成功`,
      };
    }),

  // 更新API角色模板
  updateTemplate: superAdminProcedure
    .input(z.object({
      id: z.string(),
      apiProvider: z.enum(['GEMINI', 'OPENAI', 'AZURE']).optional(),
      apiVoiceName: z.string().optional(),
      originalName: z.string().optional(),
      gender: z.enum(['MALE', 'FEMALE', 'NEUTRAL']).optional(),
      defaultStyle: z.string().optional(),
      defaultDescription: z.string().optional(),
      isActive: z.boolean().optional(),
      sortOrder: z.number().int().min(0).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      const template = await ctx.db.apiCharacterTemplate.update({
        where: { id },
        data,
      });

      return {
        success: true,
        template,
        message: `API角色模板 ${template.originalName} 更新成功`,
      };
    }),
});

export const languageCharacterAdminRouter = createTRPCRouter({
  // 获取指定语言的所有角色
  getLanguageCharacters: superAdminProcedure
    .input(z.object({
      languageId: z.string(),
      includeInactive: z.boolean().default(false),
    }))
    .query(async ({ ctx, input }) => {
      const where: any = {
        languageId: input.languageId,
      };

      if (!input.includeInactive) {
        where.isActive = true;
      }

      const characters = await ctx.db.languageCharacter.findMany({
        where,
        include: {
          language: {
            select: {
              id: true,
              code: true,
              name: true,
              flag: true,
            },
          },
          template: {
            select: {
              id: true,
              apiProvider: true,
              apiVoiceName: true,
              originalName: true,
              gender: true,
              defaultStyle: true,
            },
          },
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: [
          { isCustom: 'asc' }, // 系统角色在前
          { sortOrder: 'asc' },
          { name: 'asc' },
        ],
      });

      return characters;
    }),

  // 获取单个语言角色详情
  getLanguageCharacterById: superAdminProcedure
    .input(z.object({
      id: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const character = await ctx.db.languageCharacter.findUnique({
        where: { id: input.id },
        include: {
          language: true,
          template: true,
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!character) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '语言角色不存在',
        });
      }

      return character;
    }),

  // 创建语言角色
  createLanguageCharacter: superAdminProcedure
    .input(LanguageCharacterSchema.omit({ id: true }))
    .mutation(async ({ ctx, input }) => {
      // 验证语言是否存在
      const language = await ctx.db.language.findUnique({
        where: { id: input.languageId },
      });

      if (!language) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '语言不存在',
        });
      }

      // 如果指定了模板，验证模板是否存在
      if (input.templateId) {
        const template = await ctx.db.apiCharacterTemplate.findUnique({
          where: { id: input.templateId },
        });

        if (!template) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'API角色模板不存在',
          });
        }

        // 检查该语言下是否已存在基于此模板的角色
        const existingCharacter = await ctx.db.languageCharacter.findUnique({
          where: {
            languageId_templateId: {
              languageId: input.languageId,
              templateId: input.templateId,
            },
          },
        });

        if (existingCharacter) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: '该语言下已存在基于此模板的角色',
          });
        }
      }

      const character = await ctx.db.languageCharacter.create({
        data: {
          ...input,
          createdBy: ctx.session.user.id,
        },
        include: {
          language: true,
          template: true,
        },
      });

      return {
        success: true,
        character,
        message: `语言角色 ${character.name} 创建成功`,
      };
    }),

  // 更新语言角色
  updateLanguageCharacter: superAdminProcedure
    .input(LanguageCharacterSchema.partial().extend({
      id: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      // 检查角色是否存在
      const existingCharacter = await ctx.db.languageCharacter.findUnique({
        where: { id },
      });

      if (!existingCharacter) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '语言角色不存在',
        });
      }

      const updatedCharacter = await ctx.db.languageCharacter.update({
        where: { id },
        data,
        include: {
          language: true,
          template: true,
        },
      });

      return {
        success: true,
        character: updatedCharacter,
        message: `语言角色 ${updatedCharacter.name} 更新成功`,
      };
    }),

  // 删除语言角色
  deleteLanguageCharacter: superAdminProcedure
    .input(z.object({
      id: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const character = await ctx.db.languageCharacter.findUnique({
        where: { id: input.id },
      });

      if (!character) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '语言角色不存在',
        });
      }

      await ctx.db.languageCharacter.delete({
        where: { id: input.id },
      });

      return {
        success: true,
        message: `语言角色 ${character.name} 删除成功`,
      };
    }),

  // 批量更新语言角色状态
  batchUpdateLanguageCharacters: superAdminProcedure
    .input(z.object({
      characterIds: z.array(z.string()),
      isActive: z.boolean().optional(),
      sortOrder: z.number().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const updateData: any = {};

      if (input.isActive !== undefined) {
        updateData.isActive = input.isActive;
      }

      if (input.sortOrder !== undefined) {
        updateData.sortOrder = input.sortOrder;
      }

      await ctx.db.languageCharacter.updateMany({
        where: {
          id: { in: input.characterIds },
        },
        data: updateData,
      });

      return {
        success: true,
        message: `成功更新 ${input.characterIds.length} 个语言角色`,
      };
    }),

  // 从API模板创建语言角色
  createFromTemplate: superAdminProcedure
    .input(z.object({
      languageId: z.string(),
      templateId: z.string(),
      customizations: z.object({
        name: z.string().optional(),
        description: z.string().optional(),
        style: z.string().optional(),
        personality: z.string().optional(),
        bestFor: z.string().optional(),
        avatarUrl: z.string().optional(),
      }).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 获取模板信息
      const template = await ctx.db.apiCharacterTemplate.findUnique({
        where: { id: input.templateId },
      });

      if (!template) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'API角色模板不存在',
        });
      }

      // 检查是否已存在
      const existingCharacter = await ctx.db.languageCharacter.findUnique({
        where: {
          languageId_templateId: {
            languageId: input.languageId,
            templateId: input.templateId,
          },
        },
      });

      if (existingCharacter) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: '该语言下已存在基于此模板的角色',
        });
      }

      // 创建语言角色
      const character = await ctx.db.languageCharacter.create({
        data: {
          languageId: input.languageId,
          templateId: input.templateId,
          name: input.customizations?.name || template.originalName,
          description: input.customizations?.description || template.defaultDescription || '',
          style: input.customizations?.style || template.defaultStyle,
          personality: input.customizations?.personality,
          bestFor: input.customizations?.bestFor,
          avatarUrl: input.customizations?.avatarUrl,
          isActive: true,
          sortOrder: template.sortOrder,
          isCustom: false,
          createdBy: ctx.session.user.id,
        },
        include: {
          language: true,
          template: true,
        },
      });

      return {
        success: true,
        character,
        message: `基于模板 ${template.originalName} 创建语言角色成功`,
      };
    }),

  // 获取语言角色统计
  getLanguageCharacterStats: superAdminProcedure
    .input(z.object({
      languageId: z.string().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const where: any = {};
      if (input.languageId) {
        where.languageId = input.languageId;
      }

      const totalCharacters = await ctx.db.languageCharacter.count({ where });
      const activeCharacters = await ctx.db.languageCharacter.count({
        where: { ...where, isActive: true },
      });
      const customCharacters = await ctx.db.languageCharacter.count({
        where: { ...where, isCustom: true },
      });
      const systemCharacters = await ctx.db.languageCharacter.count({
        where: { ...where, isCustom: false },
      });

      return {
        totalCharacters,
        activeCharacters,
        customCharacters,
        systemCharacters,
      };
    }),
});
