import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 创建自定义角色的输入验证
const CreateCustomCharacterSchema = z.object({
  name: z.string().min(1).max(100),
  nameEn: z.string().optional(),
  nameKhmer: z.string().optional(),
  description: z.string().optional(),
  baseTemplateId: z.string(), // API模板ID
  speed: z.number().min(0.25).max(4.0).default(1.0),
  pitch: z.number().min(-20.0).max(20.0).default(0.0),
  volume: z.number().min(-96.0).max(16.0).default(0.0),
  defaultStyleId: z.string().optional(),
  avatarUrl: z.string().url().optional().or(z.literal("")),
  personality: z.array(z.string()).default([]),
  bestFor: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
  isPublic: z.boolean().default(false),
});

// 更新自定义角色的输入验证
const UpdateCustomCharacterSchema = CreateCustomCharacterSchema.partial().extend({
  id: z.string(),
});

// 查询自定义角色的输入验证
const GetCustomCharactersSchema = z.object({
  baseTemplateId: z.string().optional(),
  userId: z.string().optional(),
  isPublic: z.boolean().optional(),
  search: z.string().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
});

export const characterManagementRouter = createTRPCRouter({
  // 创建自定义角色
  createCustomCharacter: protectedProcedure
    .input(CreateCustomCharacterSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;

        // 检查API模板是否存在
        const baseTemplate = await ctx.db.apiCharacterTemplate.findUnique({
          where: { id: input.baseTemplateId },
        });

        if (!baseTemplate) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '指定的API模板不存在',
          });
        }

        // 检查默认风格是否存在
        if (input.defaultStyleId) {
          const defaultStyle = await ctx.db.userStyle.findUnique({
            where: { id: input.defaultStyleId },
          });
          if (!defaultStyle) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: '指定的默认风格不存在',
            });
          }
        }

        const customCharacter = await ctx.db.customVoiceCharacter.create({
          data: {
            ...input,
            userId,
          },
          include: {
            baseTemplate: true,
            defaultStyle: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });

        console.log(`✅ 用户 ${userId} 创建了自定义角色: ${customCharacter.name}`);
        return customCharacter;
      } catch (error) {
        console.error('Error creating custom character:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '创建自定义角色失败',
        });
      }
    }),

  // 获取自定义角色列表
  getCustomCharacters: publicProcedure
    .input(GetCustomCharactersSchema)
    .query(async ({ ctx, input }) => {
      try {
        const {
          baseTemplateId,
          userId,
          isPublic,
          search,
          limit,
          offset,
        } = input;

        // 构建查询条件
        const where: any = {
          isActive: true,
        };

        if (baseTemplateId) {
          where.baseTemplateId = baseTemplateId;
        }

        if (userId) {
          where.userId = userId;
        }

        if (isPublic !== undefined) {
          where.isPublic = isPublic;
        }

        if (search) {
          where.OR = [
            { name: { contains: search, mode: 'insensitive' } },
            { nameEn: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
            { tags: { has: search } },
          ];
        }

        // 获取自定义角色列表
        const characters = await ctx.db.customVoiceCharacter.findMany({
          where,
          include: {
            baseTemplate: true,
            defaultStyle: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            _count: {
              select: {
                favorites: true,
              },
            },
          },
          orderBy: [
            { favoriteCount: 'desc' },
            { usageCount: 'desc' },
            { createdAt: 'desc' },
          ],
          take: limit,
          skip: offset,
        });

        // 获取总数
        const total = await ctx.db.customVoiceCharacter.count({ where });

        console.log(`✅ 获取到 ${characters.length} 个自定义角色，总数: ${total}`);
        return {
          characters,
          total,
          hasMore: offset + limit < total,
        };
      } catch (error) {
        console.error('Error fetching custom characters:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取自定义角色列表失败',
        });
      }
    }),

  // 获取单个自定义角色详情
  getCustomCharacterById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      try {
        const character = await ctx.db.customVoiceCharacter.findUnique({
          where: { id: input.id },
          include: {
            baseTemplate: true,
            defaultStyle: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            _count: {
              select: {
                favorites: true,
              },
            },
          },
        });

        if (!character) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '自定义角色不存在',
          });
        }

        // 增加使用次数
        await ctx.db.customVoiceCharacter.update({
          where: { id: input.id },
          data: { usageCount: { increment: 1 } },
        });

        console.log(`✅ 获取自定义角色详情: ${character.name}`);
        return character;
      } catch (error) {
        console.error('Error fetching custom character:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取自定义角色详情失败',
        });
      }
    }),

  // 更新自定义角色
  updateCustomCharacter: protectedProcedure
    .input(UpdateCustomCharacterSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;
        const { id, ...updateData } = input;

        // 检查角色是否存在且属于当前用户
        const existingCharacter = await ctx.db.customVoiceCharacter.findUnique({
          where: { id },
        });

        if (!existingCharacter) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '自定义角色不存在',
          });
        }

        if (existingCharacter.userId !== userId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: '无权限修改此自定义角色',
          });
        }

        // 检查API模板是否存在
        if (updateData.baseTemplateId) {
          const baseTemplate = await ctx.db.apiCharacterTemplate.findUnique({
            where: { id: updateData.baseTemplateId },
          });
          if (!baseTemplate) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: '指定的API模板不存在',
            });
          }
        }

        // 检查默认风格是否存在
        if (updateData.defaultStyleId) {
          const defaultStyle = await ctx.db.userStyle.findUnique({
            where: { id: updateData.defaultStyleId },
          });
          if (!defaultStyle) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: '指定的默认风格不存在',
            });
          }
        }

        const updatedCharacter = await ctx.db.customVoiceCharacter.update({
          where: { id },
          data: updateData,
          include: {
            baseTemplate: true,
            defaultStyle: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });

        console.log(`✅ 用户 ${userId} 更新了自定义角色: ${updatedCharacter.name}`);
        return updatedCharacter;
      } catch (error) {
        console.error('Error updating custom character:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '更新自定义角色失败',
        });
      }
    }),

  // 删除自定义角色
  deleteCustomCharacter: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;

        // 检查角色是否存在且属于当前用户
        const existingCharacter = await ctx.db.customVoiceCharacter.findUnique({
          where: { id: input.id },
        });

        if (!existingCharacter) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '自定义角色不存在',
          });
        }

        if (existingCharacter.userId !== userId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: '无权限删除此自定义角色',
          });
        }

        await ctx.db.customVoiceCharacter.delete({
          where: { id: input.id },
        });

        console.log(`✅ 用户 ${userId} 删除了自定义角色: ${existingCharacter.name}`);
        return { success: true };
      } catch (error) {
        console.error('Error deleting custom character:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '删除自定义角色失败',
        });
      }
    }),

  // 收藏/取消收藏自定义角色
  toggleCharacterFavorite: protectedProcedure
    .input(z.object({ characterId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;

        // 检查角色是否存在
        const character = await ctx.db.customVoiceCharacter.findUnique({
          where: { id: input.characterId },
        });

        if (!character) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '自定义角色不存在',
          });
        }

        // 检查是否已收藏
        const existingFavorite = await ctx.db.characterFavorite.findUnique({
          where: {
            userId_characterId: {
              userId,
              characterId: input.characterId,
            },
          },
        });

        if (existingFavorite) {
          // 取消收藏
          await ctx.db.$transaction([
            ctx.db.characterFavorite.delete({
              where: { id: existingFavorite.id },
            }),
            ctx.db.customVoiceCharacter.update({
              where: { id: input.characterId },
              data: { favoriteCount: { decrement: 1 } },
            }),
          ]);

          console.log(`✅ 用户 ${userId} 取消收藏自定义角色: ${character.name}`);
          return { isFavorited: false };
        } else {
          // 添加收藏
          await ctx.db.$transaction([
            ctx.db.characterFavorite.create({
              data: {
                userId,
                characterId: input.characterId,
              },
            }),
            ctx.db.customVoiceCharacter.update({
              where: { id: input.characterId },
              data: { favoriteCount: { increment: 1 } },
            }),
          ]);

          console.log(`✅ 用户 ${userId} 收藏了自定义角色: ${character.name}`);
          return { isFavorited: true };
        }
      } catch (error) {
        console.error('Error toggling character favorite:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '收藏操作失败',
        });
      }
    }),

  // 获取用户收藏的自定义角色
  getFavoriteCharacters: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;

        const favorites = await ctx.db.characterFavorite.findMany({
          where: { userId },
          include: {
            character: {
              include: {
                baseTemplate: true,
                defaultStyle: true,
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
                _count: {
                  select: {
                    favorites: true,
                  },
                },
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          take: input.limit,
          skip: input.offset,
        });

        const total = await ctx.db.characterFavorite.count({
          where: { userId },
        });

        const characters = favorites.map(fav => fav.character);

        console.log(`✅ 获取用户 ${userId} 的收藏角色: ${characters.length} 个`);
        return {
          characters,
          total,
          hasMore: input.offset + input.limit < total,
        };
      } catch (error) {
        console.error('Error fetching favorite characters:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取收藏角色失败',
        });
      }
    }),

  // 获取用户创建的自定义角色
  getMyCustomCharacters: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;

        const characters = await ctx.db.customVoiceCharacter.findMany({
          where: {
            userId,
            isActive: true,
          },
          include: {
            baseTemplate: true,
            defaultStyle: true,
            _count: {
              select: {
                favorites: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          take: input.limit,
          skip: input.offset,
        });

        const total = await ctx.db.customVoiceCharacter.count({
          where: {
            userId,
            isActive: true,
          },
        });

        console.log(`✅ 获取用户 ${userId} 创建的自定义角色: ${characters.length} 个`);
        return {
          characters,
          total,
          hasMore: input.offset + input.limit < total,
        };
      } catch (error) {
        console.error('Error fetching user custom characters:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取我的自定义角色失败',
        });
      }
    }),

  // 获取API模板列表（用于创建自定义角色）
  getBaseCharacters: publicProcedure
    .query(async ({ ctx }) => {
      try {
        const templates = await ctx.db.apiCharacterTemplate.findMany({
          where: {
            isActive: true,
          },
          select: {
            id: true,
            originalName: true,
            apiProvider: true,
            apiVoiceName: true,
            gender: true,
            defaultStyle: true,
            defaultDescription: true,
          },
          orderBy: [
            { apiProvider: 'asc' },
            { originalName: 'asc' },
          ],
        });

        console.log(`✅ 获取到 ${templates.length} 个API模板`);
        return templates;
      } catch (error) {
        console.error('Error fetching API templates:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取API模板列表失败',
        });
      }
    }),

  // 创建角色分享链接
  createShare: protectedProcedure
    .input(z.object({
      characterId: z.string(),
      expiresAt: z.date(),
      maxUsage: z.number().min(1).max(999).default(10),
      requireAuth: z.boolean().default(false),
      allowCopy: z.boolean().default(true),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;

        // 验证角色所有权
        const character = await ctx.db.customVoiceCharacter.findFirst({
          where: {
            id: input.characterId,
            userId,
          },
        });

        if (!character) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "角色不存在或无权限",
          });
        }

        // 生成分享码
        const shareCode = `char_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

        const share = await ctx.db.characterShare.create({
          data: {
            characterId: input.characterId,
            sharedBy: userId,
            shareCode,
            expiresAt: input.expiresAt,
            maxUsage: input.maxUsage,
            requireAuth: input.requireAuth,
            allowCopy: input.allowCopy,
          },
        });

        console.log(`✅ 用户 ${userId} 创建了角色分享: ${shareCode}`);
        return share;
      } catch (error) {
        console.error('Error creating character share:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '创建分享链接失败',
        });
      }
    }),

  // 获取角色分享统计
  getShareStats: protectedProcedure
    .input(z.object({
      characterId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;

        const shares = await ctx.db.characterShare.findMany({
          where: {
            characterId: input.characterId,
            sharedBy: userId,
          },
          orderBy: { createdAt: 'desc' },
        });

        console.log(`✅ 获取角色 ${input.characterId} 的分享统计: ${shares.length} 个`);
        return shares;
      } catch (error) {
        console.error('Error fetching character share stats:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取分享统计失败',
        });
      }
    }),

  // 通过分享码获取角色
  getSharedCharacter: publicProcedure
    .input(z.object({
      shareCode: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      try {
        const share = await ctx.db.characterShare.findUnique({
          where: { shareCode: input.shareCode },
          include: {
            character: {
              include: {
                baseTemplate: true,
                defaultStyle: true,
                user: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        });

        if (!share) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "分享链接不存在",
          });
        }

        // 检查是否过期
        if (new Date() > share.expiresAt) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "分享链接已过期",
          });
        }

        // 检查使用次数
        if (share.usageCount >= share.maxUsage) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "分享链接使用次数已达上限",
          });
        }

        // 更新使用次数
        await ctx.db.characterShare.update({
          where: { id: share.id },
          data: { usageCount: { increment: 1 } },
        });

        console.log(`✅ 分享码 ${input.shareCode} 被访问，使用次数: ${share.usageCount + 1}`);
        return {
          character: share.character,
          shareInfo: {
            shareCode: share.shareCode,
            allowCopy: share.allowCopy,
            requireAuth: share.requireAuth,
            usageCount: share.usageCount + 1,
            maxUsage: share.maxUsage,
          },
        };
      } catch (error) {
        console.error('Error fetching shared character:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取分享角色失败',
        });
      }
    }),
});
