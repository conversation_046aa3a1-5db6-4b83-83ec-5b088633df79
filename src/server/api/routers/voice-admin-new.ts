/**
 * 新的语音管理API路由 - 基于新的数据结构
 * 使用LanguageCharacter和ApiCharacterTemplate模型
 */

import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 超级管理员权限检查中间件
const superAdminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });

  if (!user || user.role !== 'SUPER_ADMIN') {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Super admin access required',
    });
  }

  return next();
});

export const voiceAdminNewRouter = createTRPCRouter({
  // 获取所有语音角色（管理员用）- 基于语言角色
  getAllCharacters: superAdminProcedure
    .input(z.object({
      includeCustom: z.boolean().default(true),
      isActive: z.boolean().optional(),
      languageCode: z.string().optional(), // 语言代码参数
    }).optional())
    .query(async ({ ctx, input }) => {
      console.log('🔍 API调试: getAllCharacters 被调用，参数:', input);
      
      // 如果没有提供语言代码，默认使用英文
      const languageCode = input?.languageCode || 'en-US';
      
      // 获取语言信息
      const language = await ctx.db.language.findFirst({
        where: { code: languageCode },
      });

      if (!language) {
        console.log(`❌ 未找到语言代码: ${languageCode}`);
        return [];
      }

      const where: any = {
        languageId: language.id,
      };
      
      if (input?.isActive !== undefined) {
        where.isActive = input.isActive;
      }
      
      if (!input?.includeCustom) {
        where.isCustom = false;
      }

      const languageCharacters = await ctx.db.languageCharacter.findMany({
        where,
        include: {
          template: {
            select: {
              id: true,
              apiProvider: true,
              apiVoiceName: true,
              originalName: true,
              gender: true,
              defaultStyle: true,
            },
          },
          language: {
            select: {
              id: true,
              code: true,
              name: true,
            },
          },
        },
        orderBy: [
          { isCustom: 'asc' }, // 系统角色在前
          { sortOrder: 'asc' },
          { name: 'asc' },
        ],
      });

      // 转换为兼容的格式
      const characters = languageCharacters.map(langChar => ({
        id: langChar.id,
        characterName: langChar.name,
        characterNameEn: langChar.template?.originalName || langChar.name,
        originalName: langChar.template?.originalName || langChar.name,
        apiProvider: langChar.template?.apiProvider || 'GEMINI',
        apiVoiceName: langChar.template?.apiVoiceName || langChar.name,
        gender: langChar.template?.gender || 'NEUTRAL',
        description: langChar.description,
        style: langChar.style,
        personality: langChar.personality,
        bestFor: langChar.bestFor,
        avatarUrl: langChar.avatarUrl,
        isActive: langChar.isActive,
        sortOrder: langChar.sortOrder,
        isCustom: langChar.isCustom,
        createdBy: langChar.createdBy,
        customSettings: langChar.customSettings,
        customPrompt: langChar.customPrompt,
        createdAt: langChar.createdAt,
        updatedAt: langChar.updatedAt,
        // 新字段
        templateId: langChar.templateId,
        languageId: langChar.languageId,
        languageCode: langChar.language.code,
      }));

      console.log(`🔍 API调试: 从数据库获取到 ${characters.length} 个 ${languageCode} 角色`);
      return characters;
    }),

  // 更新语言角色状态
  updateCharacterStatus: superAdminProcedure
    .input(z.object({
      id: z.string(),
      isActive: z.boolean(),
    }))
    .mutation(async ({ ctx, input }) => {
      const character = await ctx.db.languageCharacter.update({
        where: { id: input.id },
        data: { isActive: input.isActive },
        include: {
          template: true,
          language: true,
        },
      });

      return {
        success: true,
        character,
        message: `角色 ${character.name} 状态已更新为 ${input.isActive ? '启用' : '禁用'}`,
      };
    }),

  // 获取语言角色详情
  getCharacterById: superAdminProcedure
    .input(z.object({
      id: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const character = await ctx.db.languageCharacter.findUnique({
        where: { id: input.id },
        include: {
          template: true,
          language: true,
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!character) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '角色不存在',
        });
      }

      return character;
    }),

  // 批量更新角色状态
  batchUpdateCharacterStatus: superAdminProcedure
    .input(z.object({
      characterIds: z.array(z.string()),
      isActive: z.boolean(),
    }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db.languageCharacter.updateMany({
        where: {
          id: { in: input.characterIds },
        },
        data: {
          isActive: input.isActive,
        },
      });

      return {
        success: true,
        message: `成功更新 ${input.characterIds.length} 个角色状态`,
      };
    }),

  // 获取语言角色统计
  getCharacterStats: superAdminProcedure
    .input(z.object({
      languageCode: z.string().optional(),
    }).optional())
    .query(async ({ ctx, input }) => {
      const languageCode = input?.languageCode;
      let where: any = {};

      if (languageCode) {
        const language = await ctx.db.language.findFirst({
          where: { code: languageCode },
        });
        if (language) {
          where.languageId = language.id;
        }
      }

      const totalCharacters = await ctx.db.languageCharacter.count({ where });
      const activeCharacters = await ctx.db.languageCharacter.count({
        where: { ...where, isActive: true },
      });
      const customCharacters = await ctx.db.languageCharacter.count({
        where: { ...where, isCustom: true },
      });
      const systemCharacters = await ctx.db.languageCharacter.count({
        where: { ...where, isCustom: false },
      });

      return {
        totalCharacters,
        activeCharacters,
        customCharacters,
        systemCharacters,
      };
    }),

  // 获取API模板列表
  getApiTemplates: superAdminProcedure
    .input(z.object({
      includeInactive: z.boolean().default(false),
      apiProvider: z.enum(['GEMINI', 'OPENAI', 'AZURE']).optional(),
    }).optional())
    .query(async ({ ctx, input }) => {
      const where: any = {};

      if (!input?.includeInactive) {
        where.isActive = true;
      }

      if (input?.apiProvider) {
        where.apiProvider = input.apiProvider;
      }

      const templates = await ctx.db.apiCharacterTemplate.findMany({
        where,
        orderBy: [
          { apiProvider: 'asc' },
          { sortOrder: 'asc' },
          { originalName: 'asc' },
        ],
      });

      return templates;
    }),

  // 获取支持的语言列表
  getSupportedLanguages: superAdminProcedure
    .query(async ({ ctx }) => {
      const languages = await ctx.db.language.findMany({
        where: { isActive: true },
        include: {
          _count: {
            select: {
              characters: true,
            },
          },
        },
        orderBy: { sortOrder: 'asc' },
      });

      return languages;
    }),
});
