import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

export const shareRouter = createTRPCRouter({
  // 创建音频分享链接
  createAudioShare: protectedProcedure
    .input(z.object({
      audioId: z.string(),
      allowCopy: z.boolean().default(true),
      requireAuth: z.boolean().default(false),
      maxUsage: z.number().min(1).max(1000).default(100),
      expiresInDays: z.number().min(1).max(365).default(30),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;

        // 生成分享码
        const shareCode = `audio_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

        // 计算过期时间
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + input.expiresInDays);

        // 创建分享记录（这里简化处理，实际应该有专门的AudioShare表）
        // 由于没有专门的音频分享表，我们可以创建一个通用的分享记录
        const shareData = {
          shareCode,
          audioId: input.audioId,
          sharedBy: userId,
          expiresAt,
          maxUsage: input.maxUsage,
          allowCopy: input.allowCopy,
          requireAuth: input.requireAuth,
          usageCount: 0,
          isActive: true,
          createdAt: new Date(),
        };

        // 这里可以存储到数据库或缓存中
        // 为了演示，我们直接返回分享码
        console.log(`✅ 创建音频分享链接: ${shareCode}`, shareData);

        return {
          shareCode,
          shareUrl: `/share/audio/${shareCode}`,
          expiresAt,
          maxUsage: input.maxUsage,
        };
      } catch (error) {
        console.error('Error creating audio share:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '创建分享链接失败',
        });
      }
    }),

  // 获取分享的音频信息
  getSharedAudio: publicProcedure
    .input(z.object({
      shareCode: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      try {
        // 这里应该从数据库查询分享记录
        // 为了演示，我们返回模拟数据
        if (!input.shareCode.startsWith('audio_')) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '分享链接不存在或已过期',
          });
        }

        // 模拟分享数据
        return {
          shareCode: input.shareCode,
          audioUrl: '/demo-audio.wav', // 这里应该是实际的音频URL
          title: '分享的语音作品',
          description: '这是一个分享的语音生成作品',
          allowCopy: true,
          requireAuth: false,
          usageCount: 0,
          maxUsage: 100,
          expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
          sharedBy: {
            name: '匿名用户',
          },
        };
      } catch (error) {
        console.error('Error fetching shared audio:', error);
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '分享链接不存在或已过期',
        });
      }
    }),

  // 记录分享访问
  recordShareAccess: publicProcedure
    .input(z.object({
      shareCode: z.string(),
      accessType: z.enum(['view', 'play', 'download']).default('view'),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // 这里应该记录访问统计
        console.log(`📊 分享访问记录: ${input.shareCode} - ${input.accessType}`);
        
        return {
          success: true,
          message: '访问记录成功',
        };
      } catch (error) {
        console.error('Error recording share access:', error);
        // 访问记录失败不应该影响用户体验，所以不抛出错误
        return {
          success: false,
          message: '记录访问失败',
        };
      }
    }),

  // 获取用户的分享统计
  getMyShares: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;

        // 这里应该从数据库查询用户的分享记录
        // 为了演示，返回模拟数据
        const mockShares = [
          {
            id: '1',
            shareCode: 'audio_1234567890_abcdef',
            title: '我的语音作品1',
            createdAt: new Date(),
            expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            usageCount: 5,
            maxUsage: 100,
            isActive: true,
          },
          {
            id: '2',
            shareCode: 'audio_0987654321_fedcba',
            title: '我的语音作品2',
            createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            expiresAt: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000),
            usageCount: 12,
            maxUsage: 100,
            isActive: true,
          },
        ];

        return {
          shares: mockShares.slice(input.offset, input.offset + input.limit),
          total: mockShares.length,
          hasMore: input.offset + input.limit < mockShares.length,
        };
      } catch (error) {
        console.error('Error fetching user shares:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取分享记录失败',
        });
      }
    }),
});
