import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { TextGenerationManager } from "~/lib/text/text-generation-manager";
import { GeminiTextGenerator } from "~/lib/text/gemini-text-generator";

export const textRouter = createTRPCRouter({
  // 生成简单文本（简化版本）
  generateSimpleText: protectedProcedure
    .input(z.object({
      prompt: z.string().min(1).max(200),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      try {
        const generator = new GeminiTextGenerator();
        const result = await generator.generateSimpleText(input.prompt);

        // 使用新的积分计算服务消耗积分
        try {
          const { CreditCalculationService } = await import('~/lib/credits/credit-calculation-service');
          const creditService = new CreditCalculationService(ctx.db);

          // 使用API提供的真实token数据
          const tokenUsage = {
            inputTokens: result.inputTokens || Math.ceil(result.tokenCount * 0.1),
            outputTokens: result.outputTokens || Math.ceil(result.tokenCount * 0.9),
            totalTokens: result.tokenCount,
          };

          const calculation = await creditService.calculateTextGenerationCredits(tokenUsage);

          console.log(`💰 文本生成积分计算: ${calculation.explanation}`);

          // 检查用户积分是否足够
          const creditCheck = await creditService.checkUserCredits(userId, calculation.creditsNeeded);
          if (!creditCheck.isEnough) {
            console.warn(`⚠️ 积分不足但文本已生成: 需要 ${calculation.creditsNeeded} 积分，可用 ${creditCheck.availableCredits} 积分`);
            // 文本已生成，不抛出错误，但记录警告
          } else {
            // 消耗积分并记录使用历史
            await creditService.consumeCredits(
              userId,
              calculation,
              `AI文本生成 - ${input.prompt.substring(0, 20)}...`
            );
            console.log(`✅ 文本生成积分扣除成功: ${calculation.creditsNeeded}积分`);
          }

        } catch (creditError) {
          console.error('❌ AI文本生成积分消耗失败:', creditError);
          // 积分消耗失败不影响文本生成结果，只记录错误
        }

        return {
          text: result.text,
        };
      } catch (error) {
        console.error('Simple text generation error:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '文本生成失败，请稍后重试',
        });
      }
    }),

  // 生成简单对话（简化版本）
  generateSimpleConversation: protectedProcedure
    .input(z.object({
      prompt: z.string().min(1).max(200),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      try {
        const generator = new GeminiTextGenerator();
        const result = await generator.generateSimpleConversation(input.prompt);

        // 简化版本不记录到数据库，直接返回结果
        return {
          text: result.text,
        };
      } catch (error) {
        console.error('Simple conversation generation error:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '对话生成失败，请稍后重试',
        });
      }
    }),

  // 生成单个文本
  generateSingle: protectedProcedure
    .input(z.object({
      type: z.enum(['speech', 'story', 'news', 'product', 'announcement', 'tutorial']),
      topic: z.string().min(1).max(200),
      language: z.string().default('zh-CN'),
      length: z.enum(['short', 'medium', 'long']),
      style: z.enum(['formal', 'casual', 'humorous', 'professional', 'friendly']),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      try {
        const manager = new TextGenerationManager();
        const result = await manager.generateText(
          {
            userId,
            type: 'SINGLE',
            options: input,
            languageCode: input.language,
          },
          ctx.db
        );

        // 使用新的积分计算服务消耗积分
        if (userId) {
          try {
            const { CreditCalculationService } = await import('~/lib/credits/credit-calculation-service');
            const creditService = new CreditCalculationService(ctx.db);

            // 使用API提供的真实token数据
            const tokenUsage = {
              inputTokens: result.inputTokens || Math.ceil(result.tokenCount * 0.1),
              outputTokens: result.outputTokens || Math.ceil(result.tokenCount * 0.9),
              totalTokens: result.tokenCount,
            };

            const calculation = await creditService.calculateTextGenerationCredits(tokenUsage);

            console.log(`💰 文本生成积分计算: ${calculation.explanation}`);

            // 检查用户积分是否足够
            const creditCheck = await creditService.checkUserCredits(userId, calculation.creditsNeeded);
            if (!creditCheck.isEnough) {
              console.warn(`⚠️ 积分不足但文本已生成: 需要 ${calculation.creditsNeeded} 积分，可用 ${creditCheck.availableCredits} 积分`);
              // 文本已生成，不抛出错误，但记录警告
            } else {
              // 消耗积分并记录使用历史
              await creditService.consumeCredits(
                userId,
                calculation,
                `文本生成 - ${input.type} (${input.length})`
              );
              console.log(`✅ 文本生成积分扣除成功: ${calculation.creditsNeeded}积分`);
            }
          } catch (creditError) {
            console.error('积分消耗失败:', creditError);
            // 积分消耗失败不影响文本生成结果，只记录错误
          }
        }

        return {
          id: result.id,
          text: result.text,
          tokenCount: result.tokenCount,
          cost: result.cost,
          createdAt: result.createdAt,
        };
      } catch (error) {
        console.error('Single text generation error:', error);
        
        if (error instanceof Error) {
          if (error.message.includes('配额不足')) {
            throw new TRPCError({
              code: 'BAD_REQUEST',
              message: error.message,
            });
          }
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '文本生成失败，请稍后重试',
        });
      }
    }),

  // 生成对话文本
  generateConversation: protectedProcedure
    .input(z.object({
      scenario: z.string().min(1).max(300),
      participantCount: z.number().min(2).max(6),
      conversationType: z.enum(['business', 'casual', 'customer_service', 'interview', 'education']),
      language: z.string().default('zh-CN'),
      length: z.enum(['short', 'medium', 'long']),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      try {
        const manager = new TextGenerationManager();
        const result = await manager.generateText(
          {
            userId,
            type: 'CONVERSATION',
            options: input,
            languageCode: input.language,
          },
          ctx.db
        );

        // 使用新的积分计算服务消耗积分
        try {
          const { CreditCalculationService } = await import('~/lib/credits/credit-calculation-service');
          const creditService = new CreditCalculationService(ctx.db);

          // 使用API提供的真实token数据
          const tokenUsage = {
            inputTokens: Math.ceil(result.tokenCount * 0.1), // 估算输入token（约10%）
            outputTokens: Math.ceil(result.tokenCount * 0.9), // 估算输出token（约90%）
            totalTokens: result.tokenCount,
          };

          const calculation = await creditService.calculateTextGenerationCredits(tokenUsage);

          console.log(`💰 对话生成积分计算: ${calculation.explanation}`);

          // 检查用户积分是否足够
          const creditCheck = await creditService.checkUserCredits(userId, calculation.creditsNeeded);
          if (!creditCheck.isEnough) {
            console.warn(`⚠️ 积分不足但对话已生成: 需要 ${calculation.creditsNeeded} 积分，可用 ${creditCheck.availableCredits} 积分`);
            // 对话已生成，不抛出错误，但记录警告
          } else {
            // 消耗积分并记录使用历史
            await creditService.consumeCredits(
              userId,
              calculation,
              `对话生成 - ${input.conversationType} (${input.participantCount}人)`
            );
            console.log(`✅ 对话生成积分扣除成功: ${calculation.creditsNeeded}积分`);
          }
        } catch (creditError) {
          console.error('❌ 对话生成积分消耗失败:', creditError);
          // 积分消耗失败不影响文本生成结果，只记录错误
        }

        return {
          id: result.id,
          text: result.text,
          tokenCount: result.tokenCount,
          cost: result.cost,
          createdAt: result.createdAt,
        };
      } catch (error) {
        console.error('Conversation generation error:', error);
        
        if (error instanceof Error) {
          if (error.message.includes('配额不足')) {
            throw new TRPCError({
              code: 'BAD_REQUEST',
              message: error.message,
            });
          }
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '对话生成失败，请稍后重试',
        });
      }
    }),

  // 获取用户文本生成历史
  getUserHistory: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(50).default(10),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      try {
        const manager = new TextGenerationManager();
        return await manager.getUserTextGenerations(
          userId,
          input.limit,
          input.offset,
          ctx.db
        );
      } catch (error) {
        console.error('Get user history error:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取历史记录失败',
        });
      }
    }),

  // 获取用户文本生成统计
  getUserStats: protectedProcedure
    .query(async ({ ctx }) => {
      const userId = ctx.session.user.id;

      try {
        const manager = new TextGenerationManager();
        return await manager.getUserTextGenerationStats(userId, ctx.db);
      } catch (error) {
        console.error('Get user stats error:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取统计信息失败',
        });
      }
    }),

  // 检查配额（用于前端预检查）
  checkQuota: protectedProcedure
    .input(z.object({
      type: z.enum(['SINGLE', 'CONVERSATION']),
      options: z.union([
        z.object({
          type: z.enum(['speech', 'story', 'news', 'product', 'announcement', 'tutorial']),
          length: z.enum(['short', 'medium', 'long']),
        }),
        z.object({
          participantCount: z.number().min(2).max(6),
          length: z.enum(['short', 'medium', 'long']),
        }),
      ]),
    }))
    .query(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      try {
        // 估算token使用量
        let estimatedTokens: number;
        if (input.type === 'SINGLE') {
          const options = input.options as { length: 'short' | 'medium' | 'long' };
          const lengthMap = { short: 800, medium: 1200, long: 1800 };
          estimatedTokens = lengthMap[options.length];
        } else {
          const options = input.options as { participantCount: number; length: 'short' | 'medium' | 'long' };
          const lengthMap = { short: 600, medium: 1000, long: 1500 };
          estimatedTokens = lengthMap[options.length] * options.participantCount * 0.5;
        }

        const hasQuota = await TextGenerationManager.checkQuota(userId, estimatedTokens, ctx.db);

        return {
          hasQuota,
          estimatedTokens,
        };
      } catch (error) {
        console.error('Check quota error:', error);
        return {
          hasQuota: false,
          estimatedTokens: 0,
        };
      }
    }),

  // 获取文本生成选项（用于前端表单）
  getGenerationOptions: publicProcedure
    .query(() => {
      return {
        singleTextTypes: [
          { value: 'speech', label: '演讲稿', description: '适合公开演讲和发言' },
          { value: 'story', label: '故事', description: '创意故事和叙述内容' },
          { value: 'news', label: '新闻稿', description: '新闻报道和资讯内容' },
          { value: 'product', label: '产品介绍', description: '产品说明和营销文案' },
          { value: 'announcement', label: '公告', description: '通知和公告内容' },
          { value: 'tutorial', label: '教程', description: '教学和指导内容' },
        ],
        conversationTypes: [
          { value: 'business', label: '商务会议', description: '商务讨论和会议对话' },
          { value: 'casual', label: '朋友聊天', description: '轻松的日常对话' },
          { value: 'customer_service', label: '客服对话', description: '客户服务场景对话' },
          { value: 'interview', label: '面试对话', description: '面试和问答场景' },
          { value: 'education', label: '教学对话', description: '教学和学习场景' },
        ],
        lengths: [
          { value: 'short', label: '短', description: '简短内容' },
          { value: 'medium', label: '中', description: '中等长度' },
          { value: 'long', label: '长', description: '详细内容' },
        ],
        styles: [
          { value: 'formal', label: '正式', description: '正式和严肃的语调' },
          { value: 'casual', label: '轻松', description: '轻松和随意的语调' },
          { value: 'humorous', label: '幽默', description: '幽默和有趣的语调' },
          { value: 'professional', label: '专业', description: '专业和权威的语调' },
          { value: 'friendly', label: '友好', description: '友好和亲切的语调' },
        ],
        languages: [
          { value: 'zh-CN', label: '中文', description: '简体中文' },
          { value: 'en-US', label: 'English', description: '英语' },
          { value: 'km-KH', label: 'ខ្មែរ', description: '高棉语' },
        ],
      };
    }),
});
