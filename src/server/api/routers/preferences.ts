import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";

export const preferencesRouter = createTRPCRouter({
  
  // 获取用户偏好
  getUserPreferences: protectedProcedure
    .query(async ({ ctx }) => {
      const userId = ctx.session.user.id;
      
      let preferences = await ctx.db.userPreference.findUnique({
        where: { userId },
      });

      // 如果没有偏好记录，创建默认的
      if (!preferences) {
        preferences = await ctx.db.userPreference.create({
          data: {
            userId,
            defaultQuality: 'fast',
            defaultSpeed: 1.0,
            defaultPitch: 0,
            defaultVolumeGainDb: 0,
            enableRecommendations: true,
            autoSelectQuality: false,
          },
        });
      }

      return preferences;
    }),

  // 更新用户偏好
  updatePreferences: protectedProcedure
    .input(z.object({
      defaultCharacterId: z.string().optional(),
      defaultQuality: z.enum(['fast', 'high']).optional(),
      defaultSpeed: z.number().min(0.5).max(2.0).optional(),
      defaultPitch: z.number().min(-10).max(10).optional(),
      defaultVolumeGainDb: z.number().min(-20).max(20).optional(),
      enableRecommendations: z.boolean().optional(),
      autoSelectQuality: z.boolean().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      return ctx.db.userPreference.upsert({
        where: { userId },
        update: input,
        create: {
          userId,
          ...input,
        },
      });
    }),

  // 添加收藏角色
  addFavoriteCharacter: protectedProcedure
    .input(z.object({
      characterId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;
      const { characterId } = input;

      const preferences = await ctx.db.userPreference.findUnique({
        where: { userId },
      });

      let favoriteCharacters: string[] = [];
      if (preferences?.favoriteCharacters) {
        try {
          favoriteCharacters = JSON.parse(preferences.favoriteCharacters);
        } catch {
          favoriteCharacters = [];
        }
      }

      // 添加到收藏（如果不存在）
      if (!favoriteCharacters.includes(characterId)) {
        favoriteCharacters.push(characterId);
      }

      return ctx.db.userPreference.upsert({
        where: { userId },
        update: {
          favoriteCharacters: JSON.stringify(favoriteCharacters),
        },
        create: {
          userId,
          favoriteCharacters: JSON.stringify(favoriteCharacters),
        },
      });
    }),

  // 移除收藏角色
  removeFavoriteCharacter: protectedProcedure
    .input(z.object({
      characterId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;
      const { characterId } = input;

      const preferences = await ctx.db.userPreference.findUnique({
        where: { userId },
      });

      let favoriteCharacters: string[] = [];
      if (preferences?.favoriteCharacters) {
        try {
          favoriteCharacters = JSON.parse(preferences.favoriteCharacters);
        } catch {
          favoriteCharacters = [];
        }
      }

      // 从收藏中移除
      favoriteCharacters = favoriteCharacters.filter(id => id !== characterId);

      return ctx.db.userPreference.update({
        where: { userId },
        data: {
          favoriteCharacters: JSON.stringify(favoriteCharacters),
        },
      });
    }),

  // 保存自定义风格模板
  saveStyleTemplate: protectedProcedure
    .input(z.object({
      name: z.string().min(1).max(50),
      nameKhmer: z.string().min(1).max(50),
      description: z.string().min(1).max(200),
      prompt: z.string().min(1).max(500),
      recommendedFor: z.array(z.string()).optional(),
      examples: z.array(z.string()).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      const preferences = await ctx.db.userPreference.findUnique({
        where: { userId },
      });

      let styleTemplates: any[] = [];
      if (preferences?.styleTemplates) {
        try {
          styleTemplates = JSON.parse(preferences.styleTemplates);
        } catch {
          styleTemplates = [];
        }
      }

      // 添加新模板
      const newTemplate = {
        id: `custom-${Date.now()}`,
        ...input,
        category: 'custom',
        icon: '⭐',
        createdAt: new Date().toISOString(),
      };

      styleTemplates.push(newTemplate);

      return ctx.db.userPreference.upsert({
        where: { userId },
        update: {
          styleTemplates: JSON.stringify(styleTemplates),
        },
        create: {
          userId,
          styleTemplates: JSON.stringify(styleTemplates),
        },
      });
    }),

  // 删除自定义风格模板
  deleteStyleTemplate: protectedProcedure
    .input(z.object({
      templateId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;
      const { templateId } = input;

      const preferences = await ctx.db.userPreference.findUnique({
        where: { userId },
      });

      let styleTemplates: any[] = [];
      if (preferences?.styleTemplates) {
        try {
          styleTemplates = JSON.parse(preferences.styleTemplates);
        } catch {
          styleTemplates = [];
        }
      }

      // 移除指定模板
      styleTemplates = styleTemplates.filter(template => template.id !== templateId);

      return ctx.db.userPreference.update({
        where: { userId },
        data: {
          styleTemplates: JSON.stringify(styleTemplates),
        },
      });
    }),

  // 更新使用统计
  updateUsageStats: protectedProcedure
    .input(z.object({
      characterCount: z.number().min(0),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;
      const { characterCount } = input;

      return ctx.db.userPreference.upsert({
        where: { userId },
        update: {
          totalGenerations: { increment: 1 },
          totalCharacters: { increment: characterCount },
        },
        create: {
          userId,
          totalGenerations: 1,
          totalCharacters: characterCount,
        },
      });
    }),

  // 获取使用统计
  getUsageStats: protectedProcedure
    .query(async ({ ctx }) => {
      const userId = ctx.session.user.id;
      
      const preferences = await ctx.db.userPreference.findUnique({
        where: { userId },
        select: {
          totalGenerations: true,
          totalCharacters: true,
          createdAt: true,
        },
      });

      return {
        totalGenerations: preferences?.totalGenerations || 0,
        totalCharacters: preferences?.totalCharacters || 0,
        memberSince: preferences?.createdAt || new Date(),
      };
    }),
});
