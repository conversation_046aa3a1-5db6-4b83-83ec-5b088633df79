import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";
// import { CharacterManager } from "~/lib/character-manager";
import { TRPCError } from "@trpc/server";

// 超级管理员权限检查中间件
const superAdminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });

  if (!user || user.role !== 'SUPER_ADMIN') {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Super admin access required',
    });
  }

  return next();
});

export const characterRouter = createTRPCRouter({
  // 获取Gemini语音选项
  getGeminiVoices: protectedProcedure
    .query(async () => {
      // return CharacterManager.getGeminiVoiceOptions();
      return [];
    }),

  // 获取语言选项
  getLanguageOptions: protectedProcedure
    .query(async () => {
      // return CharacterManager.getLanguageOptions();
      return [];
    }),

  // 获取风格选项
  getStyleOptions: protectedProcedure
    .query(async () => {
      // return CharacterManager.getStyleOptions();
      return [];
    }),

  // 获取角色模板
  getCharacterTemplates: protectedProcedure
    .query(async () => {
      // return CharacterManager.getCharacterTemplates();
      return [];
    }),

  // 获取用户的自定义角色
  getUserCustomCharacters: protectedProcedure
    .query(async ({ ctx }) => {
      // const userId = ctx.session.user.id;
      // return await CharacterManager.getUserCustomCharacters(userId);
      return [];
    }),

  // 创建自定义角色
  createCustomCharacter: protectedProcedure
    .input(z.object({
      characterName: z.string().min(1).max(50),
      characterNameEn: z.string().min(1).max(50),
      apiProvider: z.enum(['GEMINI', 'OPENAI', 'KHMER']),
      apiVoiceName: z.string().min(1),
      gender: z.enum(['MALE', 'FEMALE', 'NEUTRAL']),
      description: z.string().min(1).max(200),

      language: z.string().optional(),
      style: z.string().optional(),
      customPrompt: z.string().optional(),
      customOptions: z.object({
        baseVoice: z.string(),
        style: z.string(),
        language: z.string(),
        personality: z.string(),
        speed: z.number().min(0.5).max(2.0),
        pitch: z.number().min(-20).max(20),
        customInstructions: z.string().optional(),
      }).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      try {
        const template = {
          characterName: input.characterName,
          characterNameEn: input.characterNameEn,
          apiProvider: input.apiProvider,
          apiVoiceName: input.apiVoiceName,
          gender: input.gender,
          description: input.description,
          tier: input.tier,
          language: input.language,
          style: input.style,
          customPrompt: input.customPrompt,
        };

        const character = await CharacterManager.createCustomCharacter(
          userId,
          template,
          input.customOptions
        );

        return character;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : 'Failed to create character',
        });
      }
    }),

  // 更新自定义角色
  updateCustomCharacter: protectedProcedure
    .input(z.object({
      characterId: z.string(),
      characterName: z.string().min(1).max(50).optional(),
      characterNameEn: z.string().min(1).max(50).optional(),
      description: z.string().min(1).max(200).optional(),
      language: z.string().optional(),
      style: z.string().optional(),
      customPrompt: z.string().optional(),
      customOptions: z.object({
        baseVoice: z.string(),
        style: z.string(),
        language: z.string(),
        personality: z.string(),
        speed: z.number().min(0.5).max(2.0),
        pitch: z.number().min(-20).max(20),
        customInstructions: z.string().optional(),
      }).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;
      const { characterId, customOptions, ...updates } = input;

      try {
        const character = await CharacterManager.updateCustomCharacter(
          characterId,
          userId,
          updates,
          customOptions
        );

        return character;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : 'Failed to update character',
        });
      }
    }),

  // 删除自定义角色
  deleteCustomCharacter: protectedProcedure
    .input(z.object({
      characterId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      try {
        await CharacterManager.deleteCustomCharacter(input.characterId, userId);
        return { success: true };
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : 'Failed to delete character',
        });
      }
    }),

  // 克隆角色
  cloneCharacter: protectedProcedure
    .input(z.object({
      sourceCharacterId: z.string(),
      newName: z.string().min(1).max(50),
      newNameEn: z.string().min(1).max(50),
      customOptions: z.object({
        baseVoice: z.string(),
        style: z.string(),
        language: z.string(),
        personality: z.string(),
        speed: z.number().min(0.5).max(2.0),
        pitch: z.number().min(-20).max(20),
        customInstructions: z.string().optional(),
      }).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      try {
        const character = await CharacterManager.cloneCharacter(
          input.sourceCharacterId,
          userId,
          input.newName,
          input.newNameEn,
          input.customOptions
        );

        return character;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : 'Failed to clone character',
        });
      }
    }),

  // 获取角色配置
  getCharacterConfig: protectedProcedure
    .input(z.object({
      characterId: z.string(),
    }))
    .query(async ({ input }) => {
      try {
        return await CharacterManager.getCharacterConfig(input.characterId);
      } catch (error) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: error instanceof Error ? error.message : 'Character not found',
        });
      }
    }),

  // 测试自定义角色
  testCustomCharacter: protectedProcedure
    .input(z.object({
      characterId: z.string(),
      testText: z.string().min(1).max(100),
    }))
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      try {
        // 获取角色配置
        const character = await CharacterManager.getCharacterConfig(input.characterId);
        
        // 验证权限
        if (character.isCustom && character.createdBy !== userId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Access denied to this custom character',
          });
        }

        // 这里可以调用TTS服务生成测试音频
        // 暂时返回配置信息
        return {
          character,
          testText: input.testText,
          message: 'Character configuration retrieved successfully',
        };

      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : 'Failed to test character',
        });
      }
    }),

  // ========== 管理员专用功能 ==========

  // 创建新角色（超级管理员）
  createCharacter: superAdminProcedure
    .input(z.object({
      characterName: z.string(),
      characterNameEn: z.string(),
      apiProvider: z.enum(['GEMINI']),
      apiVoiceName: z.string(),
      gender: z.enum(['MALE', 'FEMALE', 'NEUTRAL']),
      description: z.string(),

      customPrompt: z.string().optional(),
      customSettings: z.string().optional(),
      sortOrder: z.number().default(0),
    }))
    .mutation(async ({ ctx, input }) => {
      const character = await ctx.db.voiceCharacter.create({
        data: {
          ...input,
          originalName: input.apiVoiceName, // 使用apiVoiceName作为originalName
          isCustom: false,
          createdBy: ctx.session.user.id,
        },
      });

      return character;
    }),

  // 更新角色（管理员）
  updateCharacter: superAdminProcedure
    .input(z.object({
      id: z.string(),
      characterName: z.string().optional(),
      characterNameEn: z.string().optional(),
      apiVoiceName: z.string().optional(),
      gender: z.enum(['MALE', 'FEMALE', 'NEUTRAL']).optional(),
      description: z.string().optional(),

      customPrompt: z.string().optional(),
      customSettings: z.string().optional(),
      sortOrder: z.number().optional(),
      isActive: z.boolean().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      const character = await ctx.db.voiceCharacter.update({
        where: { id },
        data: updateData,
      });

      return character;
    }),

  // 删除角色（管理员）
  deleteCharacter: superAdminProcedure
    .input(z.object({
      id: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查是否有相关的音频生成记录
      const generationCount = await ctx.db.audioGeneration.count({
        where: { characterId: input.id },
      });

      if (generationCount > 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Cannot delete character with existing audio generations',
        });
      }

      await ctx.db.voiceCharacter.delete({
        where: { id: input.id },
      });

      return { success: true };
    }),

  // 获取所有角色（管理员视图）
  getAllCharactersAdmin: superAdminProcedure
    .input(z.object({
      page: z.number().default(1),
      limit: z.number().default(20),
      search: z.string().optional(),

      isActive: z.boolean().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { page, limit, search, tier, isActive } = input;
      const skip = (page - 1) * limit;

      const where = {
        ...(search && {
          OR: [
            { characterName: { contains: search, mode: 'insensitive' as const } },
            { characterNameEn: { contains: search, mode: 'insensitive' as const } },
            { description: { contains: search, mode: 'insensitive' as const } },
          ],
        }),
        ...(isActive !== undefined && { isActive }),
      };

      const [characters, total] = await Promise.all([
        ctx.db.voiceCharacter.findMany({
          where,
          skip,
          take: limit,
          orderBy: [{ sortOrder: 'asc' }, { createdAt: 'desc' }],
          include: {
            creator: {
              select: {
                name: true,
                email: true,
              },
            },
            _count: {
              select: {
                audioGenerations: true,
              },
            },
          },
        }),
        ctx.db.voiceCharacter.count({ where }),
      ]);

      return {
        characters,
        total,
        pages: Math.ceil(total / limit),
        currentPage: page,
      };
    }),
});
