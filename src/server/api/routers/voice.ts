import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";

export const voiceRouter = createTRPCRouter({
  // 获取支持的语言列表
  getLanguages: publicProcedure
    .query(async ({ ctx }) => {
      try {
        const languages = await ctx.db.language.findMany({
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' },
        });

        console.log(`✅ 从数据库获取到 ${languages.length} 种语言`);
        return languages;
      } catch (error) {
        console.error('Error fetching languages:', error);
        throw new Error('Failed to fetch languages');
      }
    }),

  // 获取启用的语言和角色（用于landing page）
  getActiveLanguagesWithCharacters: publicProcedure
    .input(z.object({
      limit: z.number().min(1).max(20).default(6), // 限制显示的角色数量
    }).optional())
    .query(async ({ ctx, input }) => {
      try {
        const limit = input?.limit || 6;

        // 获取启用的语言
        const activeLanguages = await ctx.db.language.findMany({
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' },
          include: {
            characters: {
              where: {
                isActive: true,
                isCustom: false, // 只显示系统预设角色
              },
              include: {
                template: {
                  select: {
                    id: true,
                    apiProvider: true,
                    apiVoiceName: true,
                    originalName: true,
                    gender: true,
                  },
                },
              },
              orderBy: { sortOrder: 'asc' },
              take: 3, // 每种语言最多显示3个角色
            },
          },
        });

        // 收集所有角色并限制总数
        const allCharacters = [];
        for (const language of activeLanguages) {
          for (const character of language.characters) {
            if (allCharacters.length >= limit) break;

            allCharacters.push({
              id: character.id,
              characterName: character.name,
              characterNameEn: character.template?.originalName || character.name,
              originalName: character.template?.originalName || character.name,
              apiProvider: character.template?.apiProvider || 'GEMINI',
              apiVoiceName: character.template?.apiVoiceName || character.name,
              gender: character.template?.gender || 'NEUTRAL',
              description: character.description,
              style: character.style,
              personality: character.personality,
              bestFor: character.bestFor,
              avatarUrl: character.avatarUrl,
              isActive: character.isActive,
              sortOrder: character.sortOrder,
              templateId: character.templateId,
              languageId: character.languageId,
              language: {
                id: language.id,
                code: language.code,
                name: language.name,
                nativeName: language.nativeName,
                flag: language.flag,
              },
            });
          }
          if (allCharacters.length >= limit) break;
        }

        console.log(`✅ Landing page: 获取到 ${activeLanguages.length} 种语言，${allCharacters.length} 个角色`);

        return {
          languages: activeLanguages.map(lang => ({
            id: lang.id,
            code: lang.code,
            name: lang.name,
            nativeName: lang.nativeName,
            flag: lang.flag,
            characterCount: lang.characters.length,
          })),
          characters: allCharacters,
          totalLanguages: activeLanguages.length,
          totalCharacters: allCharacters.length,
        };
      } catch (error) {
        console.error('Error fetching active languages with characters:', error);
        throw new Error('Failed to fetch active languages with characters');
      }
    }),

  // 获取语音角色列表
  getCharacters: publicProcedure
    .input(z.object({
      languageCode: z.string().optional(),
    }).optional())
    .query(async ({ ctx, input }) => {
      try {
        // 如果提供了语言代码，返回该语言下的角色
        if (input?.languageCode) {
          // 根据语言代码获取对应的语言ID
          const language = await ctx.db.language.findFirst({
            where: { code: input.languageCode },
          });

          if (!language) {
            console.log(`❌ 未找到语言代码: ${input.languageCode}`);
            return [];
          }

          // 获取该语言下的活跃角色
          const languageCharacters = await ctx.db.languageCharacter.findMany({
            where: {
              languageId: language.id,
              isActive: true,
              isCustom: false, // 只返回系统预设角色
            },
            include: {
              template: {
                select: {
                  id: true,
                  apiProvider: true,
                  apiVoiceName: true,
                  originalName: true,
                  gender: true,
                },
              },
            },
            orderBy: [
              { sortOrder: 'asc' },
              { name: 'asc' },
            ],
          });

          // 转换为兼容的格式
          const characters = languageCharacters.map(langChar => ({
            id: langChar.id,
            characterName: langChar.name,
            characterNameEn: langChar.template?.originalName || langChar.name,
            originalName: langChar.template?.originalName || langChar.name,
            apiProvider: langChar.template?.apiProvider || 'GEMINI',
            apiVoiceName: langChar.template?.apiVoiceName || langChar.name,
            gender: langChar.template?.gender || 'NEUTRAL',
            description: langChar.description,
            style: langChar.style,
            personality: langChar.personality,
            bestFor: langChar.bestFor,
            avatarUrl: langChar.avatarUrl,
            isActive: langChar.isActive,
            sortOrder: langChar.sortOrder,
            isCustom: langChar.isCustom,
            // 添加新字段
            templateId: langChar.templateId,
            languageId: langChar.languageId,
          }));

          console.log(`✅ 找到 ${characters.length} 个支持 ${input.languageCode} 的角色`);
          return characters;
        }

        // 如果没有提供语言代码，返回英文语言的角色作为默认
        const englishLanguage = await ctx.db.language.findFirst({
          where: { code: 'en-US' },
        });

        if (!englishLanguage) {
          return [];
        }

        const languageCharacters = await ctx.db.languageCharacter.findMany({
          where: {
            languageId: englishLanguage.id,
            isActive: true,
            isCustom: false,
          },
          include: {
            template: {
              select: {
                id: true,
                apiProvider: true,
                apiVoiceName: true,
                originalName: true,
                gender: true,
              },
            },
          },
          orderBy: [
            { sortOrder: 'asc' },
            { name: 'asc' },
          ],
        });

        const characters = languageCharacters.map(langChar => ({
          id: langChar.id,
          characterName: langChar.name,
          characterNameEn: langChar.template?.originalName || langChar.name,
          originalName: langChar.template?.originalName || langChar.name,
          apiProvider: langChar.template?.apiProvider || 'GEMINI',
          apiVoiceName: langChar.template?.apiVoiceName || langChar.name,
          gender: langChar.template?.gender || 'NEUTRAL',
          description: langChar.description,
          style: langChar.style,
          personality: langChar.personality,
          bestFor: langChar.bestFor,
          avatarUrl: langChar.avatarUrl,
          isActive: langChar.isActive,
          sortOrder: langChar.sortOrder,
          isCustom: langChar.isCustom,
          templateId: langChar.templateId,
          languageId: langChar.languageId,
        }));

        console.log(`✅ 返回默认英文角色: ${characters.length} 个`);
        return characters;
      } catch (error) {
        console.error('Error fetching characters:', error);
        throw new Error('Failed to fetch characters');
      }
    }),

  // 获取用户自定义角色
  getCustomCharacters: protectedProcedure
    .query(async ({ ctx }) => {
      const characters = await ctx.db.customVoiceCharacter.findMany({
        where: {
          isActive: true,
          userId: ctx.session.user.id,
        },
        include: {
          baseTemplate: {
            select: {
              id: true,
              apiProvider: true,
              apiVoiceName: true,
              originalName: true,
              gender: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      return characters;
    }),

  // 获取试听音频
  getVoiceDemos: publicProcedure
    .input(z.object({
      languageCode: z.string(),
      characterId: z.string().optional(), // 这里现在是languageCharacterId
    }).optional())
    .query(async ({ ctx, input }) => {
      // 处理可选输入
      if (!input?.languageCode) {
        return [];
      }

      const where: any = {
        languageCode: input.languageCode,
      };

      // 如果提供了characterId，需要找到对应的templateId
      if (input.characterId) {
        const languageCharacter = await ctx.db.languageCharacter.findUnique({
          where: { id: input.characterId },
          select: { templateId: true },
        });

        if (languageCharacter?.templateId) {
          where.templateId = languageCharacter.templateId;
        } else {
          // 如果找不到对应的模板，返回空数组
          return [];
        }
      }

      const demos = await ctx.db.voiceDemo.findMany({
        where,
        include: {
          template: {
            select: {
              id: true,
              originalName: true,
              apiVoiceName: true,
              gender: true,
              defaultStyle: true,
            },
          },
          language: {
            select: {
              id: true,
              code: true,
              name: true,
              flag: true,
            },
          },
        },
        orderBy: {
          template: {
            sortOrder: 'asc',
          },
        },
      });

      return demos;
    }),

  // 获取特定角色的所有语言试听
  getCharacterDemos: publicProcedure
    .input(z.object({
      characterId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const demos = await ctx.db.voiceDemo.findMany({
        where: {
          voiceCharacterId: input.characterId,
        },
        include: {
          language: {
            select: {
              id: true,
              code: true,
              name: true,
              nativeName: true,
              flag: true,
            },
          },
        },
        orderBy: {
          language: {
            sortOrder: 'asc',
          },
        },
      });
      
      return demos;
    }),

  // 获取语音角色详情
  getCharacterById: publicProcedure
    .input(z.object({
      id: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const character = await ctx.db.voiceCharacter.findUnique({
        where: { id: input.id },
        include: {
          voiceDemos: {
            include: {
              language: true,
            },
            orderBy: {
              language: {
                sortOrder: 'asc',
              },
            },
          },
        },
      });
      
      return character;
    }),

  // 搜索语音角色
  searchCharacters: publicProcedure
    .input(z.object({
      query: z.string().min(1),
      gender: z.enum(['MALE', 'FEMALE', 'NEUTRAL']).optional(),
      style: z.string().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const where: any = {
        isActive: true,
        OR: [
          { characterName: { contains: input.query, mode: 'insensitive' } },
          { description: { contains: input.query, mode: 'insensitive' } },
          { style: { contains: input.query, mode: 'insensitive' } },
        ],
      };

      if (input.gender) {
        where.gender = input.gender;
      }

      if (input.style) {
        where.style = { contains: input.style, mode: 'insensitive' };
      }

      const characters = await ctx.db.voiceCharacter.findMany({
        where,
        orderBy: { sortOrder: 'asc' },
        take: 20, // 限制结果数量
      });
      
      return characters;
    }),

  // 获取语音统计信息
  getVoiceStats: publicProcedure
    .query(async ({ ctx }) => {
      const [
        totalLanguages,
        totalCharacters,
        totalDemos,
        femaleCharacters,
        maleCharacters,
      ] = await Promise.all([
        ctx.db.language.count({ where: { isActive: true } }),
        ctx.db.voiceCharacter.count({ where: { isActive: true, isCustom: false } }),
        ctx.db.voiceDemo.count(),
        ctx.db.voiceCharacter.count({ where: { isActive: true, isCustom: false, gender: 'FEMALE' } }),
        ctx.db.voiceCharacter.count({ where: { isActive: true, isCustom: false, gender: 'MALE' } }),
      ]);

      return {
        totalLanguages,
        totalCharacters,
        totalDemos,
        femaleCharacters,
        maleCharacters,
        coverage: totalDemos / (totalLanguages * totalCharacters) * 100, // 覆盖率百分比
      };
    }),

  // 获取推荐角色（基于用户使用历史）
  getRecommendedCharacters: protectedProcedure
    .input(z.object({
      languageCode: z.string().optional(),
      limit: z.number().min(1).max(10).default(5),
    }))
    .query(async ({ ctx, input }) => {
      // 获取用户最近使用的角色
      const recentGenerations = await ctx.db.audioGeneration.findMany({
        where: {
          userId: ctx.session.user.id,
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 最近30天
          },
        },
        select: {
          templateId: true,
        },
        distinct: ['templateId'],
        take: input.limit,
        orderBy: {
          createdAt: 'desc',
        },
      });

      const recentTemplateIds = recentGenerations.map(g => g.templateId).filter(Boolean);

      // 如果用户没有使用历史，返回热门角色
      if (recentTemplateIds.length === 0) {
        return await ctx.db.languageCharacter.findMany({
          where: {
            isActive: true,
            isCustom: false,
          },
          include: {
            template: true,
            language: true,
          },
          orderBy: {
            sortOrder: 'asc',
          },
          take: input.limit,
        });
      }

      // 返回用户最近使用的模板对应的语言角色
      const characters = await ctx.db.languageCharacter.findMany({
        where: {
          templateId: { in: recentTemplateIds },
          isActive: true,
        },
        include: {
          template: true,
          language: true,
        },
        orderBy: {
          sortOrder: 'asc',
        },
      });

      return characters;

      return characters;
    }),
});
