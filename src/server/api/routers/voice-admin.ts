import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { localizeCharacter } from "~/lib/multilingual-utils";

// 获取默认试听文本的辅助函数
function getDefaultDemoText(languageCode: string): string {
  const demoTexts: Record<string, string> = {
    'en-US': 'Hello, this is a voice demonstration. How are you today?',
    'zh-CN': '你好，这是语音演示。你今天好吗？',
    'ja-JP': 'こんにちは、これは音声デモンストレーションです。今日はいかがですか？',
    'ko-KR': '안녕하세요, 이것은 음성 데모입니다. 오늘 어떠세요?',
    'fr-FR': 'Bonjour, ceci est une démonstration vocale. Comment allez-vous aujourd\'hui?',
    'de-DE': 'Hallo, das ist eine Sprachdemonstration. Wie geht es Ihnen heute?',
    'es-US': '<PERSON><PERSON>, esta es una demostración de voz. ¿Cómo estás hoy?',
    'it-IT': '<PERSON>ia<PERSON>, questa è una dimostrazione vocale. Come stai oggi?',
    'pt-BR': 'Olá, esta é uma demonstração de voz. Como você está hoje?',
    'ru-RU': 'Привет, это голосовая демонстрация. Как дела сегодня?',
    'ar-EG': 'مرحبا، هذا عرض صوتي. كيف حالك اليوم؟',
    'hi-IN': 'नमस्ते, यह एक आवाज़ प्रदर्शन है। आज आप कैसे हैं?',
    'th-TH': 'สวัสดี นี่คือการสาธิตเสียง วันนี้คุณเป็นอย่างไรบ้าง?',
    'vi-VN': 'Xin chào, đây là bản demo giọng nói. Hôm nay bạn thế nào?',
    'id-ID': 'Halo, ini adalah demonstrasi suara. Apa kabar hari ini?',
    'km-KH': 'សួស្តី នេះជាការបង្ហាញសំឡេង។ តើថ្ងៃនេះអ្នកមានសុខភាពយ៉ាងណា?',
    'nl-NL': 'Hallo, dit is een spraakdemonstratie. Hoe gaat het vandaag?',
    'pl-PL': 'Cześć, to jest demonstracja głosu. Jak się masz dzisiaj?',
    'tr-TR': 'Merhaba, bu bir ses gösterimi. Bugün nasılsın?',
    'uk-UA': 'Привіт, це голосова демонстрація. Як справи сьогодні?',
    'ro-RO': 'Salut, aceasta este o demonstrație vocală. Cum te simți astăzi?',
    'bn-BD': 'হ্যালো, এটি একটি ভয়েস ডেমো। আজ আপনি কেমন আছেন?',
    'en-IN': 'Hello, this is a voice demonstration. How are you today?',
    'mr-IN': 'नमस्कार, हे आवाज प्रदर्शन आहे। आज तुम्ही कसे आहात?',
    'ta-IN': 'வணக்கம், இது ஒரு குரல் ஆர்ப்பாட்டம். இன்று நீங்கள் எப்படி இருக்கிறீர்கள்?',
    'te-IN': 'హలో, ఇది వాయిస్ డెమోన్స్ట్రేషన్. ఈరోజు మీరు ఎలా ఉన్నారు?',
  };

  return demoTexts[languageCode] || 'Hello, this is a voice demonstration. How are you today?';
}

// 超级管理员权限检查中间件
const superAdminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });

  if (!user || user.role !== 'SUPER_ADMIN') {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Super admin access required',
    });
  }

  return next({
    ctx: {
      ...ctx,
      user: {
        ...ctx.session.user,
        role: user.role,
      },
    },
  });
});

// 语音角色数据验证模式
const VoiceCharacterSchema = z.object({
  id: z.string().optional(),
  characterName: z.string().min(1).max(100),
  characterNameEn: z.string().min(1).max(100),
  multilingualNames: z.string().optional(), // JSON字符串
  originalName: z.string().min(1).max(100),
  apiProvider: z.enum(['GEMINI']),
  apiVoiceName: z.string().min(1).max(100),
  gender: z.enum(['MALE', 'FEMALE', 'NEUTRAL']),
  description: z.string().min(1),
  style: z.string().optional(),
  personality: z.string().optional(),
  bestFor: z.string().optional(),
  avatarUrl: z.string().url().optional(),
  isActive: z.boolean(),
  sortOrder: z.number().int().min(0),
  isCustom: z.boolean(),
  createdBy: z.string().optional(),
  customSettings: z.string().optional(), // JSON字符串
  customPrompt: z.string().optional(),
});

// 语音试听数据验证模式
const VoiceDemoSchema = z.object({
  id: z.string().optional(),
  voiceCharacterId: z.string(),
  languageId: z.string(),
  languageCode: z.string().optional(),
  demoText: z.string().optional(),
  audioUrl: z.string().optional(),
  audioKey: z.string().optional(),
  duration: z.number().optional(),
  fileSize: z.number().optional(),
  quality: z.string().optional(),
});

export const voiceAdminRouter = createTRPCRouter({
  // 获取所有语音角色（管理员用）
  getAllCharacters: superAdminProcedure
    .input(z.object({
      includeCustom: z.boolean().default(true),
      isActive: z.boolean().optional(),
      languageCode: z.string().optional(), // 添加语言代码参数
    }).optional())
    .query(async ({ ctx, input }) => {
      const where: any = {};

      if (input?.isActive !== undefined) {
        where.isActive = input.isActive;
      }

      if (!input?.includeCustom) {
        where.isCustom = false;
      }

      const characters = await ctx.db.voiceCharacter.findMany({
        where,
        orderBy: [
          { isCustom: 'asc' }, // 系统角色在前
          { sortOrder: 'asc' },
          { characterName: 'asc' },
        ],
        include: {
          _count: {
            select: {
              voiceDemos: true,
            },
          },
          voiceDemos: {
            include: {
              language: {
                select: {
                  id: true,
                  code: true,
                  name: true,
                  flag: true,
                },
              },
            },
          },
        },
      });

      // 如果提供了语言代码，返回本土化的角色信息
      if (input?.languageCode) {
        console.log('🔍 API调试: 接收到语言代码:', input.languageCode);
        const localizedCharacters = characters.map(character => {
          const localized = localizeCharacter(character, input.languageCode!);
          console.log(`🔍 角色 ${character.characterName}: ${character.style} → ${(localized as any).localizedStyle}`);
          return localized;
        });
        console.log('🔍 API调试: 返回本土化角色数量:', localizedCharacters.length);
        return localizedCharacters;
      }

      console.log('🔍 API调试: 没有语言代码，返回原始角色');
      return characters;
    }),

  // 更新角色状态
  updateCharacterStatus: superAdminProcedure
    .input(z.object({
      id: z.string(),
      isActive: z.boolean(),
    }))
    .mutation(async ({ ctx, input }) => {
      const character = await ctx.db.voiceCharacter.update({
        where: { id: input.id },
        data: { isActive: input.isActive },
      });

      return {
        success: true,
        message: `角色 ${character.characterName} 状态更新成功`,
      };
    }),

  // 获取语音角色统计
  getCharacterStats: superAdminProcedure
    .query(async ({ ctx }) => {
      const totalCharacters = await ctx.db.voiceCharacter.count();
      const activeCharacters = await ctx.db.voiceCharacter.count({
        where: { isActive: true },
      });
      const systemCharacters = await ctx.db.voiceCharacter.count({
        where: { isCustom: false },
      });
      const customCharacters = await ctx.db.voiceCharacter.count({
        where: { isCustom: true },
      });

      // 按性别统计
      const genderStats = await ctx.db.voiceCharacter.groupBy({
        by: ['gender'],
        _count: { id: true },
        where: { isActive: true },
      });

      // 按API提供商统计
      const providerStats = await ctx.db.voiceCharacter.groupBy({
        by: ['apiProvider'],
        _count: { id: true },
        where: { isActive: true },
      });

      return {
        totalCharacters,
        activeCharacters,
        systemCharacters,
        customCharacters,
        genderStats,
        providerStats,
      };
    }),

  // 获取单个语音角色详情
  getCharacterById: superAdminProcedure
    .input(z.object({
      id: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const character = await ctx.db.voiceCharacter.findUnique({
        where: { id: input.id },
        include: {
          voiceDemos: {
            include: {
              language: true,
            },
            orderBy: {
              language: {
                sortOrder: 'asc',
              },
            },
          },
        },
      });

      if (!character) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '语音角色不存在',
        });
      }

      return character;
    }),

  // 创建语音角色
  createCharacter: superAdminProcedure
    .input(VoiceCharacterSchema.omit({ id: true }))
    .mutation(async ({ ctx, input }) => {
      // 检查角色名称是否已存在
      const existingCharacter = await ctx.db.voiceCharacter.findFirst({
        where: {
          OR: [
            { characterName: input.characterName },
            { apiVoiceName: input.apiVoiceName },
          ],
        },
      });

      if (existingCharacter) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: '角色名称或API语音名称已存在',
        });
      }

      const character = await ctx.db.voiceCharacter.create({
        data: {
          ...input,
          createdBy: input.createdBy || ctx.session.user.id,
        },
      });

      return {
        success: true,
        character,
        message: `语音角色 ${input.characterName} 创建成功`,
      };
    }),

  // 获取角色的所有语言试听
  getCharacterDemos: superAdminProcedure
    .input(z.object({
      characterId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const demos = await ctx.db.voiceDemo.findMany({
        where: {
          voiceCharacterId: input.characterId,
        },
        include: {
          language: true,
        },
        orderBy: {
          language: {
            sortOrder: 'asc',
          },
        },
      });

      return demos;
    }),

  // 创建或更新语音试听
  upsertVoiceDemo: superAdminProcedure
    .input(VoiceDemoSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...demoData } = input;

      if (id) {
        // 更新现有试听 - 过滤掉空值字段
        const updateData: any = {};
        if (demoData.demoText) updateData.demoText = demoData.demoText;
        if (demoData.quality) updateData.quality = demoData.quality;
        if (demoData.audioUrl) updateData.audioUrl = demoData.audioUrl;
        if (demoData.audioKey) updateData.audioKey = demoData.audioKey;
        if (demoData.duration !== undefined && demoData.duration !== null) updateData.duration = demoData.duration;
        if (demoData.fileSize !== undefined && demoData.fileSize !== null) updateData.fileSize = demoData.fileSize;

        const demo = await ctx.db.voiceDemo.update({
          where: { id },
          data: updateData,
          include: {
            language: true,
            voiceCharacter: true,
          },
        });

        return {
          success: true,
          demo,
          message: '语音试听更新成功',
        };
      } else {
        // 创建新试听
        const demo = await ctx.db.voiceDemo.upsert({
          where: {
            voiceCharacterId_languageId: {
              voiceCharacterId: demoData.voiceCharacterId,
              languageId: demoData.languageId,
            },
          },
          update: demoData,
          create: demoData,
          include: {
            language: true,
            voiceCharacter: true,
          },
        });

        return {
          success: true,
          demo,
          message: '语音试听创建成功',
        };
      }
    }),

  // 生成语音试听
  generateVoiceDemo: superAdminProcedure
    .input(z.object({
      demoId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 获取试听信息
      const demo = await ctx.db.voiceDemo.findUnique({
        where: { id: input.demoId },
        include: {
          language: true,
          voiceCharacter: true,
        },
      });

      if (!demo) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '试听不存在',
        });
      }

      if (!demo.demoText) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '试听文本不能为空',
        });
      }

      try {
        // 使用真实的Gemini TTS生成语音
        const { GeminiTTSService } = await import('~/lib/tts/gemini-tts');
        const { r2Storage } = await import('~/lib/r2-storage');
        const { audioCacheService } = await import('~/lib/cache/audio-cache');

        // 检查缓存
        const cachedAudio = await audioCacheService.get(
          demo.voiceCharacter.characterName,
          demo.language.code,
          demo.demoText
        );

        let audioUrl: string;
        let audioKey: string;
        let duration: number | undefined;
        let fileSize: number;

        if (cachedAudio) {
          // 使用缓存的音频
          audioUrl = cachedAudio.audioUrl;
          audioKey = `audio/${demo.voiceCharacter.characterName.toLowerCase()}-${demo.language.code}-cached.wav`;
          duration = cachedAudio.duration;
          fileSize = cachedAudio.fileSize;

          console.log(`🎯 Using cached audio for ${demo.voiceCharacter.characterName}-${demo.language.code}`);
        } else {
          // 生成新的语音
          const geminiTTS = new GeminiTTSService();

          const result = await geminiTTS.generateSpeech({
            text: demo.demoText,
            voiceName: demo.voiceCharacter.apiVoiceName,
            format: 'wav'
          });

          // 生成存储键和文件名
          const fileName = `${demo.voiceCharacter.characterName.toLowerCase()}-${demo.language.code}-${Date.now()}.wav`;
          audioKey = `voice-demos/${fileName}`;

          // 上传到R2
          audioUrl = await r2Storage.uploadAudio(result.audioData, fileName, 'audio/wav');
          duration = result.duration;
          fileSize = result.audioData.length;

          // 存储到缓存
          await audioCacheService.set(
            demo.voiceCharacter.characterName,
            demo.language.code,
            demo.demoText,
            {
              audioUrl,
              duration,
              fileSize,
              quality: 'standard',
              voiceName: demo.voiceCharacter.apiVoiceName,
              languageCode: demo.language.code,
              createdAt: Date.now(),
              lastAccessed: Date.now(),
              accessCount: 1,
            }
          );
        }

        // 更新试听记录
        const updatedDemo = await ctx.db.voiceDemo.update({
          where: { id: input.demoId },
          data: {
            audioUrl,
            audioKey,
            duration,
            fileSize,
          },
          include: {
            language: true,
            voiceCharacter: true,
          },
        });

        return {
          success: true,
          demo: updatedDemo,
          message: '语音生成成功',
        };
      } catch (error) {
        console.error('语音生成失败:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `语音生成失败: ${error instanceof Error ? error.message : '未知错误'}`,
        });
      }
    }),

  // 删除语音试听
  deleteVoiceDemo: superAdminProcedure
    .input(z.object({
      id: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db.voiceDemo.delete({
        where: { id: input.id },
      });

      return {
        success: true,
        message: '语音试听删除成功',
      };
    }),

  // 批量生成语音试听
  generateVoiceDemos: superAdminProcedure
    .input(z.object({
      characterId: z.string(),
      languageIds: z.array(z.string()),
      demoText: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const character = await ctx.db.voiceCharacter.findUnique({
        where: { id: input.characterId },
      });

      if (!character) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '语音角色不存在',
        });
      }

      const languages = await ctx.db.language.findMany({
        where: {
          id: { in: input.languageIds },
          isActive: true,
        },
      });

      const results = [];
      for (const language of languages) {
        try {
          // 使用默认试听文本或自定义文本
          const demoText = input.demoText || getDefaultDemoText(language.code);

          // 创建试听记录（暂时不生成实际音频）
          const demo = await ctx.db.voiceDemo.upsert({
            where: {
              voiceCharacterId_languageId: {
                voiceCharacterId: character.id,
                languageId: language.id,
              },
            },
            update: {
              demoText,
            },
            create: {
              voiceCharacterId: character.id,
              languageId: language.id,
              languageCode: language.code,
              demoText,
              audioUrl: '', // 待生成
              audioKey: `voice-demos/${character.characterName.toLowerCase()}/${language.code}.wav`,
              quality: 'standard',
            },
          });

          results.push({
            language: language.name,
            success: true,
            demo,
          });
        } catch (error) {
          results.push({
            language: language.name,
            success: false,
            error: error instanceof Error ? error.message : '未知错误',
          });
        }
      }

      return {
        success: true,
        results,
        message: `为角色 ${character.characterName} 生成了 ${results.filter(r => r.success).length} 个语言试听`,
      };
    }),

  // 批量生成音频
  batchGenerateAudio: superAdminProcedure
    .input(z.object({
      characterId: z.string().optional(),
      languageIds: z.array(z.string()).optional(),
      quality: z.enum(['fast', 'high']).default('fast'),
      maxConcurrent: z.number().min(1).max(5).default(3),
    }))
    .mutation(async ({ ctx, input }) => {
      const { GeminiTTSService } = await import('~/lib/tts/gemini-tts');
      const { r2Storage } = await import('~/lib/r2-storage');

      // 构建查询条件 - audioUrl是必填字段，只检查空字符串
      const whereCondition: any = {
        audioUrl: ''
      };

      if (input.characterId) {
        whereCondition.voiceCharacterId = input.characterId;
      }

      if (input.languageIds && input.languageIds.length > 0) {
        whereCondition.languageId = { in: input.languageIds };
      }

      // 获取需要生成音频的试听
      const demosToGenerate = await ctx.db.voiceDemo.findMany({
        where: whereCondition,
        include: {
          voiceCharacter: true,
          language: true,
        },
        orderBy: [
          { voiceCharacter: { sortOrder: 'asc' } },
          { language: { sortOrder: 'asc' } }
        ]
      });

      if (demosToGenerate.length === 0) {
        return {
          success: true,
          results: [],
          message: '没有需要生成音频的试听',
        };
      }

      const geminiTTS = new GeminiTTSService();
      const results = [];
      let successCount = 0;
      let failureCount = 0;

      // 分批处理，避免并发过多
      const batchSize = input.maxConcurrent;
      for (let i = 0; i < demosToGenerate.length; i += batchSize) {
        const batch = demosToGenerate.slice(i, i + batchSize);

        const batchPromises = batch.map(async (demo) => {
          try {
            // 生成语音
            const result = await geminiTTS.generateSpeech({
              text: demo.demoText,
              voiceName: demo.voiceCharacter.apiVoiceName,
              format: 'wav',
              quality: input.quality
            });

            // 生成文件名
            const fileName = `${demo.voiceCharacter.characterName.toLowerCase()}-${demo.language.code}-${Date.now()}.wav`;

            // 上传到R2
            const audioUrl = await r2Storage.uploadAudio(result.audioData, fileName, 'audio/wav');

            // 更新数据库
            await ctx.db.voiceDemo.update({
              where: { id: demo.id },
              data: {
                audioUrl,
                audioKey: `audio/${fileName}`,
                duration: result.duration,
                fileSize: result.audioData.length,
              },
            });

            successCount++;
            return {
              success: true,
              demoId: demo.id,
              character: demo.voiceCharacter.characterName,
              language: demo.language.code,
              audioUrl,
            };
          } catch (error) {
            failureCount++;
            return {
              success: false,
              demoId: demo.id,
              character: demo.voiceCharacter.characterName,
              language: demo.language.code,
              error: error instanceof Error ? error.message : '未知错误',
            };
          }
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);

        // 添加延迟避免API限制
        if (i + batchSize < demosToGenerate.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      return {
        success: true,
        results,
        summary: {
          total: demosToGenerate.length,
          success: successCount,
          failure: failureCount,
          successRate: `${(successCount / demosToGenerate.length * 100).toFixed(1)}%`
        },
        message: `批量生成完成：成功 ${successCount} 个，失败 ${failureCount} 个`,
      };
    }),

  // 记录播放统计
  recordPlayStat: publicProcedure
    .input(z.object({
      demoId: z.string(),
      playDuration: z.number().optional(),
      isCompleted: z.boolean().default(false),
      userAgent: z.string().optional(),
      referrer: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // 获取IP地址（简单哈希处理）
        const ipAddress = ctx.req?.ip ?
          Buffer.from(ctx.req.ip).toString('base64').substring(0, 16) :
          undefined;

        // 记录播放统计
        await ctx.db.voiceDemoPlayStat.create({
          data: {
            voiceDemoId: input.demoId,
            userId: ctx.session?.user?.id,
            playDuration: input.playDuration,
            isCompleted: input.isCompleted,
            userAgent: input.userAgent,
            ipAddress,
            referrer: input.referrer,
          },
        });

        // 更新聚合统计
        await ctx.db.voiceDemoAggregateStats.upsert({
          where: { voiceDemoId: input.demoId },
          create: {
            voiceDemoId: input.demoId,
            totalPlays: 1,
            uniquePlays: 1,
            completedPlays: input.isCompleted ? 1 : 0,
            averageDuration: input.playDuration,
            lastPlayedAt: new Date(),
          },
          update: {
            totalPlays: { increment: 1 },
            completedPlays: input.isCompleted ? { increment: 1 } : undefined,
            lastPlayedAt: new Date(),
            // 更新平均播放时长（简化计算）
            averageDuration: input.playDuration ? {
              set: input.playDuration // 简化处理，实际应该计算真正的平均值
            } : undefined,
          },
        });

        return { success: true };
      } catch (error) {
        console.error('Record play stat error:', error);
        return { success: false };
      }
    }),

  // 获取播放统计
  getPlayStats: superAdminProcedure
    .input(z.object({
      characterId: z.string().optional(),
      languageCode: z.string().optional(),
      startDate: z.date().optional(),
      endDate: z.date().optional(),
      limit: z.number().min(1).max(100).default(20),
    }))
    .query(async ({ ctx, input }) => {
      const whereCondition: any = {};

      if (input.startDate || input.endDate) {
        whereCondition.createdAt = {};
        if (input.startDate) whereCondition.createdAt.gte = input.startDate;
        if (input.endDate) whereCondition.createdAt.lte = input.endDate;
      }

      if (input.characterId || input.languageCode) {
        whereCondition.voiceDemo = {};
        if (input.characterId) whereCondition.voiceDemo.voiceCharacterId = input.characterId;
        if (input.languageCode) whereCondition.voiceDemo.languageCode = input.languageCode;
      }

      const stats = await ctx.db.voiceDemoPlayStat.findMany({
        where: whereCondition,
        include: {
          voiceDemo: {
            include: {
              voiceCharacter: {
                select: { characterName: true, gender: true }
              },
              language: {
                select: { code: true, name: true, flag: true }
              }
            }
          },
          user: {
            select: { id: true, name: true, email: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: input.limit,
      });

      return stats;
    }),

  // 获取聚合统计
  getAggregateStats: superAdminProcedure
    .input(z.object({
      characterId: z.string().optional(),
      languageCode: z.string().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const whereCondition: any = {};

      if (input.characterId || input.languageCode) {
        whereCondition.voiceDemo = {};
        if (input.characterId) whereCondition.voiceDemo.voiceCharacterId = input.characterId;
        if (input.languageCode) whereCondition.voiceDemo.languageCode = input.languageCode;
      }

      const aggregateStats = await ctx.db.voiceDemoAggregateStats.findMany({
        where: whereCondition,
        include: {
          voiceDemo: {
            include: {
              voiceCharacter: {
                select: { characterName: true, gender: true }
              },
              language: {
                select: { code: true, name: true, flag: true }
              }
            }
          }
        },
        orderBy: { totalPlays: 'desc' },
      });

      return aggregateStats;
    }),

  // 更新语音角色
  updateCharacter: superAdminProcedure
    .input(VoiceCharacterSchema.partial().extend({
      id: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      // 检查角色是否存在
      const existingCharacter = await ctx.db.voiceCharacter.findUnique({
        where: { id },
      });

      if (!existingCharacter) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '语音角色不存在',
        });
      }

      // 如果更新了角色名称或API语音名，检查是否冲突
      if (updateData.characterName || updateData.apiVoiceName) {
        const conflictWhere: any = {
          id: { not: id },
          OR: [],
        };

        if (updateData.characterName) {
          conflictWhere.OR.push({ characterName: updateData.characterName });
        }
        if (updateData.apiVoiceName) {
          conflictWhere.OR.push({ apiVoiceName: updateData.apiVoiceName });
        }

        const conflictCharacter = await ctx.db.voiceCharacter.findFirst({
          where: conflictWhere,
        });

        if (conflictCharacter) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: '角色名称或API语音名称已存在',
          });
        }
      }

      const character = await ctx.db.voiceCharacter.update({
        where: { id },
        data: updateData,
      });

      return {
        success: true,
        character,
        message: `语音角色 ${character.characterName} 更新成功`,
      };
    }),

  // 删除语音角色
  deleteCharacter: superAdminProcedure
    .input(z.object({
      id: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查角色是否存在
      const character = await ctx.db.voiceCharacter.findUnique({
        where: { id: input.id },
        include: {
          _count: {
            select: {
              audioGenerations: true,
              voiceDemos: true,
            },
          },
        },
      });

      if (!character) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '语音角色不存在',
        });
      }

      // 检查是否有关联的音频生成记录
      if (character._count.audioGenerations > 0) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: '该角色已被使用，无法删除',
        });
      }

      // 删除角色（会级联删除相关的语音试听）
      await ctx.db.voiceCharacter.delete({
        where: { id: input.id },
      });

      return {
        success: true,
        message: `语音角色 ${character.characterName} 删除成功`,
      };
    }),

  // 批量更新角色状态
  batchUpdateCharacters: superAdminProcedure
    .input(z.object({
      characterIds: z.array(z.string()),
      isActive: z.boolean().optional(),
      sortOrder: z.number().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const updateData: any = {};

      if (input.isActive !== undefined) {
        updateData.isActive = input.isActive;
      }

      if (input.sortOrder !== undefined) {
        updateData.sortOrder = input.sortOrder;
      }

      await ctx.db.voiceCharacter.updateMany({
        where: {
          id: { in: input.characterIds },
        },
        data: updateData,
      });

      return {
        success: true,
        message: `成功更新 ${input.characterIds.length} 个角色`,
      };
    }),

  // 批量更新角色状态
  batchUpdateCharacterStatus: superAdminProcedure
    .input(z.object({
      characterIds: z.array(z.string()),
      isActive: z.boolean(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { characterIds, isActive } = input;

      const result = await ctx.db.voiceCharacter.updateMany({
        where: {
          id: { in: characterIds },
        },
        data: { isActive },
      });

      return {
        success: true,
        updatedCount: result.count,
        message: `已${isActive ? '启用' : '禁用'} ${result.count} 个角色`,
      };
    }),

  // 重新排序角色
  reorderCharacters: superAdminProcedure
    .input(z.object({
      characterOrders: z.array(z.object({
        id: z.string(),
        sortOrder: z.number(),
      })),
    }))
    .mutation(async ({ ctx, input }) => {
      const { characterOrders } = input;

      // 使用事务批量更新排序
      await ctx.db.$transaction(
        characterOrders.map(({ id, sortOrder }) =>
          ctx.db.voiceCharacter.update({
            where: { id },
            data: { sortOrder },
          })
        )
      );

      return {
        success: true,
        message: '角色排序更新成功',
      };
    }),

  // 更新角色多语言名称
  updateCharacterMultilingualName: superAdminProcedure
    .input(z.object({
      characterId: z.string(),
      languageCode: z.string(),
      localizedName: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const character = await ctx.db.voiceCharacter.findUnique({
        where: { id: input.characterId },
      });

      if (!character) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '角色不存在',
        });
      }

      // 解析现有的多语言名称
      let multilingualNames: Record<string, string> = {};
      if (character.multilingualNames) {
        try {
          multilingualNames = JSON.parse(character.multilingualNames);
        } catch (error) {
          console.error('Error parsing multilingual names:', error);
        }
      }

      // 更新指定语言的名称
      multilingualNames[input.languageCode] = input.localizedName;

      // 保存到数据库
      await ctx.db.voiceCharacter.update({
        where: { id: input.characterId },
        data: {
          multilingualNames: JSON.stringify(multilingualNames),
        },
      });

      return {
        success: true,
        message: `角色 ${character.characterName} 的多语言名称更新成功`,
      };
    }),
});
