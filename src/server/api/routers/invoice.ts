import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";
import InvoiceService from "~/lib/invoice";

export const invoiceRouter = createTRPCRouter({
  
  // 获取发票数据
  getInvoice: protectedProcedure
    .input(z.object({
      paymentId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const { paymentId } = input;

      // 验证支付记录所有权
      const payment = await ctx.db.payment.findFirst({
        where: {
          id: paymentId,
          userId: ctx.session.user.id,
          status: 'COMPLETED',
        },
      });

      if (!payment) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Payment not found or not completed',
        });
      }

      const invoiceData = await InvoiceService.getData(paymentId);

      if (!invoiceData) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to generate invoice data',
        });
      }

      return invoiceData;
    }),

  // 生成发票HTML
  generateInvoiceHTML: protectedProcedure
    .input(z.object({
      paymentId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const { paymentId } = input;

      // 验证支付记录所有权
      const payment = await ctx.db.payment.findFirst({
        where: {
          id: paymentId,
          userId: ctx.session.user.id,
          status: 'COMPLETED',
        },
      });

      if (!payment) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Payment not found or not completed',
        });
      }

      const invoiceData = await InvoiceService.getData(paymentId);

      if (!invoiceData) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to generate invoice data',
        });
      }

      const html = InvoiceService.generateHTML(invoiceData);
      return { html };
    }),

  // 创建发票
  createInvoice: protectedProcedure
    .input(z.object({
      paymentId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { paymentId } = input;

      // 验证支付记录所有权
      const payment = await ctx.db.payment.findFirst({
        where: {
          id: paymentId,
          userId: ctx.session.user.id,
          status: 'COMPLETED',
        },
      });

      if (!payment) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Payment not found or not completed',
        });
      }

      const invoiceData = await InvoiceService.create(paymentId);

      if (!invoiceData) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create invoice',
        });
      }

      return invoiceData;
    }),

  // 获取用户所有发票
  getUserInvoices: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(50).default(10),
    }))
    .query(async ({ ctx, input }) => {
      const { page, limit } = input;
      const skip = (page - 1) * limit;

      const [payments, total] = await Promise.all([
        ctx.db.payment.findMany({
          where: {
            userId: ctx.session.user.id,
            status: 'COMPLETED',
          },
          include: {
            creditPurchase: true,
          },
          orderBy: { completedAt: 'desc' },
          skip,
          take: limit,
        }),
        ctx.db.payment.count({
          where: {
            userId: ctx.session.user.id,
            status: 'COMPLETED',
          },
        }),
      ]);

      // 为每个支付记录添加发票信息
      const invoices = payments.map(payment => {
        const metadata = payment.metadata as any;
        const invoiceInfo = metadata?.invoice;

        return {
          paymentId: payment.id,
          invoiceNumber: invoiceInfo?.invoiceNumber || `INV-${payment.id.slice(0, 8)}`,
          amount: payment.amount,
          currency: payment.currency,
          description: payment.description,
          completedAt: payment.completedAt,
          packageName: payment.creditPurchase?.packageName || '积分充值',
          credits: payment.creditPurchase?.credits || 0,
          hasInvoice: !!invoiceInfo,
          issueDate: invoiceInfo?.issueDate ? new Date(invoiceInfo.issueDate) : payment.completedAt,
        };
      });

      return {
        invoices,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    }),
});
