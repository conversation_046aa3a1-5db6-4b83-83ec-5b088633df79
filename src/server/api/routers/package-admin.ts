import { z } from "zod";
import { createTR<PERSON><PERSON>outer, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 超级管理员权限检查
const superAdminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });

  if (user?.role !== 'SUPER_ADMIN') {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: '需要超级管理员权限',
    });
  }

  return next();
});

// API定价配置验证模式
const ApiPricingSchema = z.object({
  standardInputPrice: z.number().min(0),
  standardOutputPrice: z.number().min(0),
  professionalInputPrice: z.number().min(0),
  professionalOutputPrice: z.number().min(0),
  textGenerationInputPrice: z.number().min(0),
  textGenerationOutputPrice: z.number().min(0),
});

// 积分消耗比配置验证模式
const CreditConsumptionRatioSchema = z.object({
  textGenerationRatio: z.number().min(1), // tokens per credit
  standardVoiceRatio: z.number().min(1),  // tokens per credit
  professionalVoiceRatio: z.number().min(1), // tokens per credit
});

// 积分包定价验证模式
const PackagePricingSchema = z.object({
  packageType: z.enum(['basic', 'standard', 'premium']),
  credits: z.number().min(1),
  salePrice: z.number().min(0),
  promoPrice: z.number().min(0).optional(),
});

const FreePackageSchema = z.object({
  enabled: z.boolean(),
  giftType: z.enum(['daily', 'monthly']),
  dailyCredits: z.number().min(0),
  monthlyCredits: z.number().min(0),
});

export const packageAdminRouter = createTRPCRouter({

  // 获取API定价配置
  getApiPricing: superAdminProcedure
    .query(async ({ ctx }) => {
      let config = await ctx.db.apiPricingConfig.findFirst({
        where: { isActive: true },
        orderBy: { createdAt: 'desc' },
      });

      // 如果没有配置，创建默认配置
      if (!config) {
        config = await ctx.db.apiPricingConfig.create({
          data: {
            standardInputPrice: 0.50,
            standardOutputPrice: 10.00,
            professionalInputPrice: 1.00,
            professionalOutputPrice: 20.00,
            textGenerationInputPrice: 0.50,
            textGenerationOutputPrice: 1.50,
            isActive: true,
            description: '默认API定价配置',
          },
        });
      }

      return config;
    }),

  // 更新API定价配置
  updateApiPricing: superAdminProcedure
    .input(ApiPricingSchema)
    .mutation(async ({ ctx, input }) => {
      // 先将现有配置设为非活跃
      await ctx.db.apiPricingConfig.updateMany({
        where: { isActive: true },
        data: { isActive: false },
      });

      // 创建新的配置
      const newConfig = await ctx.db.apiPricingConfig.create({
        data: {
          standardInputPrice: input.standardInputPrice,
          standardOutputPrice: input.standardOutputPrice,
          professionalInputPrice: input.professionalInputPrice,
          professionalOutputPrice: input.professionalOutputPrice,
          textGenerationInputPrice: input.textGenerationInputPrice,
          textGenerationOutputPrice: input.textGenerationOutputPrice,
          isActive: true,
          description: `API定价配置 - ${new Date().toISOString()}`,
        },
      });

      return newConfig;
    }),

  // 获取积分消耗比配置
  getCreditConsumptionRatios: superAdminProcedure
    .query(async ({ ctx }) => {
      let config = await ctx.db.apiPricingConfig.findFirst({
        where: { isActive: true },
        orderBy: { createdAt: 'desc' },
      });

      // 如果没有配置，创建默认配置
      if (!config) {
        config = await ctx.db.apiPricingConfig.create({
          data: {
            standardInputPrice: 1.00,
            standardOutputPrice: 19.90,
            professionalInputPrice: 1.00,
            professionalOutputPrice: 39.80,
            textGenerationInputPrice: 1.00,
            textGenerationOutputPrice: 1.80,
            // 消耗比配置 (tokens per credit)
            textGenerationRatio: 1000,    // 1000 tokens = 1 credit
            standardVoiceRatio: 100,      // 100 tokens = 1 credit
            professionalVoiceRatio: 50,   // 50 tokens = 1 credit
            isActive: true,
            description: '默认积分消耗比配置',
          },
        });
      }

      return {
        textGenerationRatio: config.textGenerationRatio || 1000,
        standardVoiceRatio: config.standardVoiceRatio || 100,
        professionalVoiceRatio: config.professionalVoiceRatio || 50,
      };
    }),

  // 更新积分消耗比配置
  updateCreditConsumptionRatios: superAdminProcedure
    .input(CreditConsumptionRatioSchema)
    .mutation(async ({ ctx, input }) => {
      // 获取当前配置
      const currentConfig = await ctx.db.apiPricingConfig.findFirst({
        where: { isActive: true },
        orderBy: { createdAt: 'desc' },
      });

      if (!currentConfig) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '未找到当前配置',
        });
      }

      // 更新消耗比配置
      const updatedConfig = await ctx.db.apiPricingConfig.update({
        where: { id: currentConfig.id },
        data: {
          textGenerationRatio: input.textGenerationRatio,
          standardVoiceRatio: input.standardVoiceRatio,
          professionalVoiceRatio: input.professionalVoiceRatio,
          description: `积分消耗比配置更新 - ${new Date().toISOString()}`,
        },
      });

      return {
        textGenerationRatio: updatedConfig.textGenerationRatio,
        standardVoiceRatio: updatedConfig.standardVoiceRatio,
        professionalVoiceRatio: updatedConfig.professionalVoiceRatio,
      };
    }),



  // 获取积分包定价
  getPackagePricing: superAdminProcedure
    .query(async ({ ctx }) => {
      const packages = await ctx.db.creditPackagePricing.findMany({
        where: { isActive: true },
        orderBy: { credits: 'asc' },
      });

      // 如果没有积分包，创建默认的
      if (packages.length === 0) {
        const defaultPackages = [
          { packageType: 'basic', credits: 1000, salePrice: 9.99 },
          { packageType: 'standard', credits: 3000, salePrice: 24.99 },
          { packageType: 'premium', credits: 10000, salePrice: 79.99 },
        ];

        const createdPackages = await Promise.all(
          defaultPackages.map(pkg => 
            ctx.db.creditPackagePricing.create({
              data: {
                ...pkg,
                costPrice: 0, // 将在计算时更新
                isActive: true,
              },
            })
          )
        );

        return createdPackages;
      }

      return packages;
    }),

  // 更新积分包定价
  updatePackagePricing: superAdminProcedure
    .input(PackagePricingSchema)
    .mutation(async ({ ctx, input }) => {
      const { packageType, credits, salePrice, promoPrice } = input;

      // 计算成本价
      const apiConfig = await ctx.db.apiPricingConfig.findFirst({
        where: { isActive: true },
        orderBy: { createdAt: 'desc' },
      });

      if (!apiConfig) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '未找到API定价配置',
        });
      }

      // 1积分 = 1000 token，计算成本价
      const tokensPerCredit = 1000;
      const totalTokens = credits * tokensPerCredit;
      const avgTokenCost = (apiConfig.standardInputPrice + apiConfig.standardOutputPrice) / 2;
      const costPrice = (totalTokens / 1000000) * avgTokenCost;

      // 先将现有的同类型包设为非活跃
      await ctx.db.creditPackagePricing.updateMany({
        where: { packageType, isActive: true },
        data: { isActive: false },
      });

      // 创建新的积分包定价
      const newPackage = await ctx.db.creditPackagePricing.create({
        data: {
          packageType,
          credits,
          costPrice: Math.round(costPrice * 100) / 100,
          salePrice,
          promoPrice,
          isActive: true,
        },
      });

      return newPackage;
    }),

  // 计算积分包利润信息
  calculatePackageProfit: superAdminProcedure
    .input(z.object({
      credits: z.number().min(1),
      salePrice: z.number().min(0),
      promoPrice: z.number().min(0).optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { credits, salePrice, promoPrice } = input;

      // 获取API定价配置
      const apiConfig = await ctx.db.apiPricingConfig.findFirst({
        where: { isActive: true },
        orderBy: { createdAt: 'desc' },
      });

      if (!apiConfig) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '未找到API定价配置',
        });
      }

      // 计算成本价
      const tokensPerCredit = 1000;
      const totalTokens = credits * tokensPerCredit;
      const avgTokenCost = (apiConfig.standardInputPrice + apiConfig.standardOutputPrice) / 2;
      const costPrice = (totalTokens / 1000000) * avgTokenCost;

      // 计算利润信息
      const saleProfit = salePrice - costPrice;
      const saleProfitMargin = costPrice > 0 ? (saleProfit / costPrice) * 100 : 0;

      const result: any = {
        costPrice: Math.round(costPrice * 100) / 100,
        salePrice,
        saleProfit: Math.round(saleProfit * 100) / 100,
        saleProfitMargin: Math.round(saleProfitMargin * 100) / 100,
        finalPrice: salePrice,
        hasPromotion: false,
      };

      if (promoPrice && promoPrice > 0 && promoPrice < salePrice) {
        const promoProfit = promoPrice - costPrice;
        const promoProfitMargin = costPrice > 0 ? (promoProfit / costPrice) * 100 : 0;
        const discountPercent = ((salePrice - promoPrice) / salePrice) * 100;

        result.promoPrice = promoPrice;
        result.promoProfit = Math.round(promoProfit * 100) / 100;
        result.promoProfitMargin = Math.round(promoProfitMargin * 100) / 100;
        result.discountPercent = Math.round(discountPercent * 100) / 100;
        result.finalPrice = promoPrice;
        result.hasPromotion = true;
      }

      return result;
    }),

  // 获取积分包统计
  getPackageStats: superAdminProcedure
    .query(async ({ ctx }) => {
      const [totalPackages, totalPurchases, totalRevenue] = await Promise.all([
        ctx.db.creditPackagePricing.count({ where: { isActive: true } }),
        ctx.db.creditPurchase.count(),
        ctx.db.creditPurchase.aggregate({
          _sum: { amount: true },
        }),
      ]);

      return {
        totalPackages,
        totalPurchases,
        totalRevenue: totalRevenue._sum.amount || 0,
        activePurchases: 0, // 可以根据需要添加更复杂的逻辑
      };
    }),

  // 获取免费包配置
  getFreePackageConfig: superAdminProcedure
    .query(async ({ ctx }) => {
      let config = await ctx.db.freePackageConfig.findFirst({
        where: { isActive: true },
        orderBy: { createdAt: 'desc' },
      });

      // 如果没有配置，创建默认配置
      if (!config) {
        config = await ctx.db.freePackageConfig.create({
          data: {
            enabled: true,
            giftType: 'daily',
            dailyCredits: 100,
            monthlyCredits: 0,
            isActive: true,
            description: '默认免费包配置',
          },
        });
      }

      return config;
    }),

  // 更新免费包配置
  updateFreePackageConfig: superAdminProcedure
    .input(FreePackageSchema)
    .mutation(async ({ ctx, input }) => {
      // 先将现有配置设为非活跃
      await ctx.db.freePackageConfig.updateMany({
        where: { isActive: true },
        data: { isActive: false },
      });

      // 创建新的配置
      const newConfig = await ctx.db.freePackageConfig.create({
        data: {
          enabled: input.enabled,
          giftType: input.giftType,
          dailyCredits: input.dailyCredits,
          monthlyCredits: input.monthlyCredits,
          isActive: true,
          description: `免费包配置 - ${input.giftType === 'daily' ? '每日' : '每月'}赠送`,
        },
      });

      return newConfig;
    }),

});
