import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 积分使用记录验证模式
const CreditUsageSchema = z.object({
  serviceType: z.enum(['STANDARD_VOICE', 'PROFESSIONAL_VOICE', 'TEXT_GENERATION']),
  inputTokens: z.number().min(0),
  outputTokens: z.number().min(0),
  totalTokens: z.number().min(0),
  creditsUsed: z.number().min(0),
  description: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

export const creditUsageRouter = createTRPCRouter({

  // 注意：consumeCredits方法已弃用，请使用新的CreditCalculationService
  // 此方法保留仅为向后兼容，建议使用新的积分计算服务

  // 获取积分使用历史
  getUsageHistory: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
      serviceType: z.enum(['STANDARD_VOICE', 'PROFESSIONAL_VOICE', 'TEXT_GENERATION']).optional(),
      startDate: z.date().optional(),
      endDate: z.date().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { limit, offset, serviceType, startDate, endDate } = input;
      const userId = ctx.session.user.id;

      const where: any = { userId };

      if (serviceType) {
        where.serviceType = serviceType;
      }

      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) where.createdAt.gte = startDate;
        if (endDate) where.createdAt.lte = endDate;
      }

      const [usageRecords, total] = await Promise.all([
        ctx.db.creditUsage.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip: offset,
        }),
        ctx.db.creditUsage.count({ where }),
      ]);

      return {
        usageRecords,
        total,
        hasMore: offset + limit < total,
      };
    }),

  // 获取积分使用统计
  getUsageStats: protectedProcedure
    .input(z.object({
      period: z.enum(['today', 'week', 'month', 'all']).default('month'),
    }))
    .query(async ({ ctx, input }) => {
      const { period } = input;
      const userId = ctx.session.user.id;

      let startDate: Date | undefined;
      const now = new Date();

      switch (period) {
        case 'today':
          startDate = new Date(now.setHours(0, 0, 0, 0));
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'all':
          startDate = undefined;
          break;
      }

      const where: any = { userId };
      if (startDate) {
        where.createdAt = { gte: startDate };
      }

      const [totalUsage, serviceStats] = await Promise.all([
        ctx.db.creditUsage.aggregate({
          where,
          _sum: {
            amount: true, // 使用amount字段（积分数）
            totalTokens: true,
          },
          _count: {
            id: true,
          },
        }),
        ctx.db.creditUsage.groupBy({
          by: ['serviceType'],
          where: {
            ...where,
            serviceType: { not: null }, // 只统计有serviceType的记录
          },
          _sum: {
            amount: true, // 使用amount字段（积分数）
            totalTokens: true,
          },
          _count: {
            id: true,
          },
        }),
      ]);

      return {
        period,
        totalCreditsUsed: totalUsage._sum.amount || 0,
        totalTokensProcessed: totalUsage._sum.totalTokens || 0,
        totalRequests: totalUsage._count || 0,
        serviceBreakdown: serviceStats.map(stat => ({
          serviceType: stat.serviceType,
          creditsUsed: stat._sum.amount || 0,
          tokensProcessed: stat._sum.totalTokens || 0,
          requests: stat._count || 0,
        })),
      };
    }),

  // 注意：estimateCredits和checkBalance方法已弃用
  // 请使用新的CreditCalculationService进行积分计算和检查

  // 获取不同服务的积分消耗预估
  getServicePricing: protectedProcedure
    .query(async ({ ctx }) => {
      const { CreditCalculationService } = await import('~/lib/credits/credit-calculation-service');
      const creditService = new CreditCalculationService(ctx.db);

      const estimates = await creditService.estimateCreditsForDisplay();

      return {
        textGeneration: estimates.textGeneration,
        ttsStandard: estimates.ttsStandard,
        ttsProfessional: estimates.ttsProfessional,
        explanation: {
          creditValue: '每积分价值 $0.01 (1美分)',
          calculation: '积分消耗基于实际API成本计算',
          note: '实际消耗可能因token使用量而略有差异',
        },
      };
    }),

});
