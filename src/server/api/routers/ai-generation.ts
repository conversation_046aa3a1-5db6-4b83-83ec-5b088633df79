import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { r2Storage, R2StorageService } from "~/lib/r2-storage";
import sharp from "sharp";

// PCM转WAV格式函数
function convertPcmToWav(pcmBuffer: Buffer, sampleRate: number, channels: number, bitsPerSample: number): Buffer {
  const byteRate = sampleRate * channels * (bitsPerSample / 8);
  const blockAlign = channels * (bitsPerSample / 8);
  const dataSize = pcmBuffer.length;
  const fileSize = 36 + dataSize;

  // 创建WAV头部（44字节）
  const wavHeader = Buffer.alloc(44);

  // RIFF标识符
  wavHeader.write('RIFF', 0);
  wavHeader.writeUInt32LE(fileSize, 4);
  wavHeader.write('WAVE', 8);

  // fmt子块
  wavHeader.write('fmt ', 12);
  wavHeader.writeUInt32LE(16, 16); // fmt子块大小
  wavHeader.writeUInt16LE(1, 20);  // 音频格式（PCM）
  wavHeader.writeUInt16LE(channels, 22);
  wavHeader.writeUInt32LE(sampleRate, 24);
  wavHeader.writeUInt32LE(byteRate, 28);
  wavHeader.writeUInt16LE(blockAlign, 32);
  wavHeader.writeUInt16LE(bitsPerSample, 34);

  // data子块
  wavHeader.write('data', 36);
  wavHeader.writeUInt32LE(dataSize, 40);

  // 合并头部和PCM数据
  return Buffer.concat([wavHeader, pcmBuffer]);
}

// 默认试听文本
const DEFAULT_DEMO_TEXTS = {
  'zh-CN': '你好，我是你的语音助手，很高兴为你服务。',
  'en-US': 'Hello, I am your voice assistant, nice to serve you.',
  'ja-JP': 'こんにちは、私はあなたの音声アシスタントです。',
  'ko-KR': '안녕하세요, 저는 당신의 음성 어시스턴트입니다.',
  'th-TH': 'สวัสดีครับ ผมเป็นผู้ช่วยเสียงของคุณ',
  'vi-VN': 'Xin chào, tôi là trợ lý giọng nói của bạn.',
  'default': 'Hello, this is a voice demo.',
};

// 超级管理员权限检查中间件
const superAdminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });

  if (!user || user.role !== 'SUPER_ADMIN') {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Super admin access required',
    });
  }

  return next();
});

// AI头像生成函数 - 使用Gemini Imagen API
async function generateAvatar(prompt: string, characterId: string): Promise<string> {
  try {
    console.log(`🎨 开始使用Gemini Imagen生成头像: ${prompt}`);

    // 检查Gemini API密钥
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('Gemini API密钥未配置');
    }

    // 检查R2存储是否配置
    if (!r2Storage.isConfigured()) {
      throw new Error('R2存储服务未配置，无法保存头像');
    }

    // 使用Gemini 2.5 Flash Image Preview模型生成头像
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-image-preview:generateContent?key=${process.env.GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: `Generate a professional headshot portrait image of ${prompt}. The image should be:
- Clean, neutral background
- Photorealistic style
- Suitable for voice character avatar
- High quality
- 512x512 resolution
- Professional lighting
- Clear facial features
- Appropriate for business/professional use`
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
        }
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Gemini 2.5 Flash API error:', errorText);
      throw new Error(`Gemini 2.5 Flash API error: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Gemini API response:', JSON.stringify(data, null, 2));

    // 检查响应中是否包含图像数据
    const candidate = data.candidates?.[0];
    const content = candidate?.content;

    // 查找图像数据 - Gemini 2.5 Flash可能在不同位置返回图像
    let imageData = null;

    if (content?.parts) {
      for (const part of content.parts) {
        if (part.inlineData && part.inlineData.data) {
          imageData = part.inlineData.data;
          break;
        }
        if (part.image && part.image.data) {
          imageData = part.image.data;
          break;
        }
      }
    }

    if (!imageData) {
      console.error('No image data found in response:', data);
      throw new Error('Gemini未返回图片数据，可能该模型不支持图像生成');
    }

    console.log(`📥 获取到Gemini 2.5 Flash生成的图片数据`);

    // 将base64数据转换为Buffer
    const imageBuffer = Buffer.from(imageData, 'base64');

    console.log(`🔧 压缩图片: ${imageBuffer.length} bytes -> WebP 512x512`);

    // 使用Sharp压缩并转换为WebP格式，调整为512x512
    const compressedImage = await sharp(imageBuffer)
      .resize(512, 512, {
        fit: 'cover',
        position: 'center'
      })
      .webp({
        quality: 85,
        effort: 6
      })
      .toBuffer();

    console.log(`✅ 图片压缩完成: ${compressedImage.length} bytes`);

    // 生成文件名
    const fileName = R2StorageService.generateAvatarFileName(characterId, undefined, 'webp');

    console.log(`☁️ 上传到R2: ${fileName}`);

    // 上传到R2
    const avatarUrl = await r2Storage.uploadAvatar(
      compressedImage,
      fileName,
      'image/webp'
    );

    console.log(`🎉 头像生成并上传成功: ${avatarUrl}`);

    return avatarUrl;
  } catch (error) {
    console.error('Avatar generation error:', error);
    throw new Error(error instanceof Error ? error.message : '头像生成失败');
  }
}

// AI语音试听生成函数 - 使用Gemini TTS API
async function generateVoiceDemo(text: string, template: any, languageCode: string, quality: 'standard' | 'professional' = 'standard'): Promise<{ audioUrl: string; duration: number; fileSize: number }> {
  try {
    console.log('Template data:', JSON.stringify(template, null, 2));
    console.log('API Provider:', template.apiProvider);
    console.log('Quality:', quality);

    // 直接使用Gemini TTS生成
    console.log(`🎵 使用Gemini TTS生成${quality === 'standard' ? '标准' : '专业'}质量语音`);
    return await generateGeminiVoiceDemo(text, template.apiVoiceName, languageCode, quality);
  } catch (error) {
    console.error('Gemini TTS生成失败:', error);
    throw new Error('语音试听生成失败');
  }
}



// 生成真实的WAV音频文件并保存到R2
async function generateSimulatedVoiceDemo(text: string, voiceName: string, languageCode: string, quality: 'standard' | 'professional' = 'standard'): Promise<{ audioUrl: string; duration: number; fileSize: number }> {
  console.log(`🎵 生成${quality === 'professional' ? '专业质量' : '标准质量'}WAV音频文件: ${voiceName}, 语言: ${languageCode}`);

  try {
    // 检查R2存储是否配置
    if (!r2Storage.isConfigured()) {
      throw new Error('R2存储服务未配置，无法保存音频');
    }

    // 根据质量生成不同参数的WAV音频文件
    const duration = Math.max(2, Math.floor(text.length / 10)); // 最少2秒，基于文本长度
    const sampleRate = quality === 'professional' ? 48000 : 44100; // 专业质量使用更高采样率
    const samples = duration * sampleRate;
    const frequency = quality === 'professional' ? 523.25 : 440; // 专业质量使用C5音符，标准使用A4

    // 创建WAV文件头
    const wavHeader = Buffer.alloc(44);
    const dataSize = samples * 2; // 16位音频
    const fileSize = dataSize + 36;

    // WAV文件头
    wavHeader.write('RIFF', 0);
    wavHeader.writeUInt32LE(fileSize, 4);
    wavHeader.write('WAVE', 8);
    wavHeader.write('fmt ', 12);
    wavHeader.writeUInt32LE(16, 16); // PCM格式大小
    wavHeader.writeUInt16LE(1, 20);  // PCM格式
    wavHeader.writeUInt16LE(1, 22);  // 单声道
    wavHeader.writeUInt32LE(sampleRate, 24);
    wavHeader.writeUInt32LE(sampleRate * 2, 28); // 字节率
    wavHeader.writeUInt16LE(2, 32);  // 块对齐
    wavHeader.writeUInt16LE(16, 34); // 位深度
    wavHeader.write('data', 36);
    wavHeader.writeUInt32LE(dataSize, 40);

    // 生成音频数据（简单的正弦波）
    const audioData = Buffer.alloc(dataSize);
    for (let i = 0; i < samples; i++) {
      const sample = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 0.3; // 降低音量
      const value = Math.round(sample * 32767);
      audioData.writeInt16LE(value, i * 2);
    }

    // 合并头部和数据
    const wavBuffer = Buffer.concat([wavHeader, audioData]);

    // 生成文件名，包含质量信息
    const fileName = R2StorageService.generateFileName(`voice-demo-${quality}-${voiceName}-${Date.now()}`, 'wav');

    console.log(`☁️ 上传WAV音频到R2: ${fileName}, 大小: ${wavBuffer.length} bytes`);

    // 上传到R2
    const audioUrl = await r2Storage.uploadAudio(
      wavBuffer,
      fileName,
      'audio/wav'
    );

    console.log(`🎉 WAV音频生成并上传成功: ${audioUrl}`);

    return {
      audioUrl,
      duration,
      fileSize: wavBuffer.length,
    };
  } catch (error) {
    console.error('WAV音频生成失败:', error);

    // 如果R2上传失败，返回一个占位符URL
    const placeholderUrl = `data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT`;

    return {
      audioUrl: placeholderUrl,
      duration: Math.floor(text.length / 15),
      fileSize: 1024,
    };
  }
}

// Gemini TTS生成函数 - 使用真实的Gemini API
async function generateGeminiVoiceDemo(text: string, voiceName: string, languageCode: string, quality: 'standard' | 'professional' = 'standard'): Promise<{ audioUrl: string; duration: number; fileSize: number }> {
  try {
    console.log(`🎵 开始使用Gemini TTS API生成语音: ${text.substring(0, 50)}...`);
    console.log(`🎤 语音角色: ${voiceName}, 语言: ${languageCode}`);

    // 检查Gemini API密钥
    if (!process.env.GEMINI_API_KEY) {
      console.log('⚠️ Gemini API密钥未配置，使用WAV生成');
      return await generateSimulatedVoiceDemo(text, voiceName, languageCode, quality);
    }

    // 检查R2存储是否配置
    if (!r2Storage.isConfigured()) {
      console.log('⚠️ R2存储未配置，使用WAV生成');
      return await generateSimulatedVoiceDemo(text, voiceName, languageCode, quality);
    }

    // 根据质量选择不同的Gemini TTS模型
    const modelName = quality === 'professional'
      ? 'gemini-2.5-pro-preview-tts'    // 高质量Pro TTS模型
      : 'gemini-2.5-flash-preview-tts'; // 标准Flash TTS模型

    console.log(`🎤 使用Gemini模型: ${modelName} (${quality})`);

    // 使用Gemini TTS API生成语音 - 按照官方文档格式
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${process.env.GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: text
          }]
        }],
        generationConfig: {
          responseModalities: ["AUDIO"],
          speechConfig: {
            voiceConfig: {
              prebuiltVoiceConfig: {
                voiceName: voiceName || "Zephyr"  // 使用文档中的预建语音
              }
            }
          }
        }
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Gemini TTS API错误:', errorText);
      console.log('⚠️ 回退到WAV生成');
      return await generateSimulatedVoiceDemo(text, voiceName, languageCode, quality);
    }

    const data = await response.json();
    console.log('📥 Gemini TTS API响应接收');

    // 检查响应中是否包含音频数据
    const candidate = data.candidates?.[0];
    const content = candidate?.content;

    // 查找音频数据 - TTS API返回格式
    let audioData = null;

    if (content?.parts) {
      for (const part of content.parts) {
        // TTS API返回的音频数据在inlineData中
        if (part.inlineData && part.inlineData.data) {
          audioData = part.inlineData.data;
          console.log('📥 从TTS API获取音频数据，MIME类型:', part.inlineData.mimeType);
          break;
        }
      }
    }

    if (!audioData) {
      console.log('⚠️ Gemini未返回音频数据，使用WAV生成');
      return await generateSimulatedVoiceDemo(text, voiceName, languageCode, quality);
    }

    console.log(`📥 获取到Gemini生成的音频数据`);

    // 将base64数据转换为Buffer
    const pcmBuffer = Buffer.from(audioData, 'base64');

    console.log(`🔧 PCM音频数据大小: ${pcmBuffer.length} bytes`);

    // 将PCM数据转换为WAV格式
    const wavBuffer = convertPcmToWav(pcmBuffer, 24000, 1, 16);

    console.log(`🔧 转换后WAV数据大小: ${wavBuffer.length} bytes`);

    // 生成文件名，包含模型和质量信息
    const modelSuffix = quality === 'professional' ? 'pro' : 'standard';
    const fileName = R2StorageService.generateFileName(`gemini-tts-${modelSuffix}-${voiceName}-${Date.now()}`, 'wav');

    console.log(`☁️ 上传Gemini音频到R2: ${fileName}`);

    // 上传到R2
    const audioUrl = await r2Storage.uploadAudio(
      wavBuffer,
      fileName,
      'audio/wav'
    );

    console.log(`🎉 Gemini语音生成并上传成功: ${audioUrl}`);

    return {
      audioUrl,
      duration: Math.floor(text.length / 15), // 估算时长
      fileSize: wavBuffer.length,
    };
  } catch (error) {
    console.error('Gemini TTS generation error:', error);

    // 出错时回退到模拟生成
    console.log('⚠️ 回退到模拟生成');
    return await generateSimulatedVoiceDemo(text, voiceName, languageCode, quality);
  }
}

export const aiGenerationRouter = createTRPCRouter({
  // 为语言角色生成AI头像
  generateCharacterAvatar: superAdminProcedure
    .input(z.object({
      characterId: z.string(),
      characterName: z.string(),
      gender: z.enum(['MALE', 'FEMALE', 'NEUTRAL']),
      style: z.string().optional(),
      customPrompt: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查角色是否存在
      const character = await ctx.db.languageCharacter.findUnique({
        where: { id: input.characterId },
        include: { language: true, template: true },
      });

      if (!character) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '语言角色不存在',
        });
      }

      // 使用改进的AI头像生成器
      const { aiAvatarGenerator } = await import('~/lib/ai-avatar-generator');
      const { r2Storage, R2StorageService } = await import('~/lib/r2-storage');

      if (!aiAvatarGenerator.isAvailable()) {
        throw new TRPCError({
          code: 'SERVICE_UNAVAILABLE',
          message: 'AI头像生成服务不可用',
        });
      }

      if (!r2Storage.isConfigured()) {
        throw new TRPCError({
          code: 'SERVICE_UNAVAILABLE',
          message: 'R2存储服务未配置',
        });
      }

      // 构建生成选项
      const avatarOptions = {
        characterName: character.name,
        gender: character.template?.gender || input.gender,
        style: character.style || input.style || undefined,
        personality: character.personality || undefined,
        description: character.description || undefined,
        languageCode: character.language?.code,
        culturalContext: character.language?.nativeName,
      };

      try {
        // 生成头像
        const result = await aiAvatarGenerator.generateAvatar(avatarOptions);

        // 生成文件名
        const fileName = R2StorageService.generateAvatarFileName(
          input.characterId,
          character.language?.code,
          'webp'
        );

        // 上传到R2
        const avatarUrl = await r2Storage.uploadAvatar(
          result.imageData,
          fileName,
          'image/webp'
        );

        // 更新角色头像
        await ctx.db.languageCharacter.update({
          where: { id: input.characterId },
          data: { avatarUrl },
        });

        console.log(`✅ 角色 ${character.name} 头像生成并上传成功: ${avatarUrl}`);

        return {
          success: true,
          avatarUrl,
          prompt: result.prompt,
          message: `角色 ${character.name} 的头像生成成功`,
        };
      } catch (error) {
        console.error('❌ 头像生成失败:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '头像生成失败',
        });
      }
    }),

  // 为语言角色生成AI语音试听
  generateVoiceDemo: superAdminProcedure
    .input(z.object({
      characterId: z.string(),
      demoText: z.string().optional(),
      quality: z.enum(['standard', 'professional']).default('standard'),
      regenerate: z.boolean().default(false),
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查角色是否存在
      const character = await ctx.db.languageCharacter.findUnique({
        where: { id: input.characterId },
        include: { 
          language: true, 
          template: true,
        },
      });

      if (!character) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '语言角色不存在',
        });
      }

      if (!character.template) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '该角色没有关联的API模板，无法生成语音',
        });
      }

      // 检查是否已有指定质量的试听文件
      const existingDemo = await ctx.db.voiceDemo.findFirst({
        where: {
          templateId: character.templateId!,
          languageId: character.languageId,
          quality: input.quality,
        },
      });

      if (existingDemo && existingDemo.audioUrl && !input.regenerate) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: `该角色已有${input.quality === 'standard' ? '标准' : '专业'}质量试听文件，如需重新生成请选择"重新生成"`,
        });
      }

      // 获取试听文本
      const demoText = input.demoText || existingDemo?.demoText || getDefaultDemoText(character.language.code);

      try {
        console.log('Character template before generation:', JSON.stringify(character.template, null, 2));

        // 生成语音试听
        const { audioUrl, duration, fileSize } = await generateVoiceDemo(
          demoText,
          character.template,
          character.language.code,
          input.quality
        );

        // 更新或创建试听记录
        const demo = await ctx.db.voiceDemo.upsert({
          where: {
            templateId_languageId_quality: {
              templateId: character.templateId!,
              languageId: character.languageId,
              quality: input.quality,
            },
          },
          update: {
            demoText,
            audioUrl,
            duration,
            fileSize,
            updatedAt: new Date(),
          },
          create: {
            templateId: character.templateId!,
            languageId: character.languageId,
            languageCode: character.language.code,
            demoText,
            audioUrl,
            audioKey: audioUrl,
            duration,
            fileSize,
            quality: input.quality,
          },
        });

        return {
          success: true,
          demo,
          message: `角色 ${character.name} 的语音试听生成成功`,
        };
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '语音试听生成失败',
        });
      }
    }),

  // 批量生成语音试听
  batchGenerateVoiceDemos: superAdminProcedure
    .input(z.object({
      languageId: z.string(),
      characterIds: z.array(z.string()).optional(),
      regenerateExisting: z.boolean().default(false),
    }))
    .mutation(async ({ ctx, input }) => {
      // 获取要处理的角色
      const where: any = { languageId: input.languageId };
      if (input.characterIds?.length) {
        where.id = { in: input.characterIds };
      }

      const characters = await ctx.db.languageCharacter.findMany({
        where,
        include: { 
          language: true, 
          template: true,
        },
      });

      const results = [];
      let successCount = 0;
      let errorCount = 0;

      for (const character of characters) {
        if (!character.template) {
          results.push({
            characterId: character.id,
            characterName: character.name,
            success: false,
            error: '没有关联的API模板',
          });
          errorCount++;
          continue;
        }

        try {
          // 检查是否已有试听文件
          const existingDemo = await ctx.db.voiceDemo.findUnique({
            where: {
              templateId_languageId: {
                templateId: character.templateId!,
                languageId: character.languageId,
              },
            },
          });

          if (existingDemo && existingDemo.audioUrl && !input.regenerateExisting) {
            results.push({
              characterId: character.id,
              characterName: character.name,
              success: false,
              error: '已有试听文件',
            });
            continue;
          }

          const demoText = existingDemo?.demoText || getDefaultDemoText(character.language.code);

          // 生成语音试听
          const { audioUrl, duration, fileSize } = await generateVoiceDemo(
            demoText,
            character.template,
            character.language.code
          );

          // 更新或创建试听记录
          await ctx.db.voiceDemo.upsert({
            where: {
              templateId_languageId: {
                templateId: character.templateId!,
                languageId: character.languageId,
              },
            },
            update: {
              audioUrl,
              duration,
              fileSize,
              updatedAt: new Date(),
            },
            create: {
              templateId: character.templateId!,
              languageId: character.languageId,
              languageCode: character.language.code,
              demoText,
              audioUrl,
              audioKey: `voice-demos/${character.template.apiVoiceName}/${character.language.code}.mp3`,
              duration,
              fileSize,
              quality: 'standard',
            },
          });

          results.push({
            characterId: character.id,
            characterName: character.name,
            success: true,
          });
          successCount++;

          // 添加延迟避免API限制
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (error) {
          results.push({
            characterId: character.id,
            characterName: character.name,
            success: false,
            error: error instanceof Error ? error.message : '生成失败',
          });
          errorCount++;
        }
      }

      return {
        success: true,
        results,
        summary: {
          total: characters.length,
          success: successCount,
          error: errorCount,
        },
        message: `批量生成完成：成功 ${successCount} 个，失败 ${errorCount} 个`,
      };
    }),
});

// 获取默认试听文本的辅助函数
function getDefaultDemoText(languageCode: string): string {
  const demoTexts: Record<string, string> = {
    'en-US': 'Hello, this is a voice demonstration. How are you today?',
    'zh-CN': '你好，这是语音演示。你今天好吗？',
    'ja-JP': 'こんにちは、これは音声デモンストレーションです。今日はいかがですか？',
    'ko-KR': '안녕하세요, 이것은 음성 데모입니다. 오늘 어떠세요?',
    'fr-FR': 'Bonjour, ceci est une démonstration vocale. Comment allez-vous aujourd\'hui?',
    'de-DE': 'Hallo, das ist eine Sprachdemonstration. Wie geht es Ihnen heute?',
    'es-US': 'Hola, esta es una demostración de voz. ¿Cómo estás hoy?',
    'it-IT': 'Ciao, questa è una dimostrazione vocale. Come stai oggi?',
    'pt-BR': 'Olá, esta é uma demonstração de voz. Como você está hoje?',
    'ru-RU': 'Привет, это голосовая демонстрация. Как дела сегодня?',
    'ar-EG': 'مرحبا، هذا عرض صوتي. كيف حالك اليوم؟',
    'hi-IN': 'नमस्ते, यह एक आवाज़ प्रदर्शन है। आज आप कैसे हैं?',
    'th-TH': 'สวัสดี นี่คือการสาธิตเสียง วันนี้คุณเป็นอย่างไรบ้าง?',
    'vi-VN': 'Xin chào, đây là bản demo giọng nói. Hôm nay bạn thế nào?',
    'id-ID': 'Halo, ini adalah demonstrasi suara. Apa kabar hari ini?',
    'km-KH': 'សួស្តី នេះជាការបង្ហាញសំឡេង។ តើថ្ងៃនេះអ្នកមានសុខភាពយ៉ាងណា?',
    'nl-NL': 'Hallo, dit is een spraakdemonstratie. Hoe gaat het vandaag?',
    'pl-PL': 'Cześć, to jest demonstracja głosu. Jak się masz dzisiaj?',
    'tr-TR': 'Merhaba, bu bir ses gösterimi. Bugün nasılsın?',
    'uk-UA': 'Привіт, це голосова демонстрація. Як справи сьогодні?',
    'ro-RO': 'Salut, aceasta este o demonstrație vocală. Cum te simți astăzi?',
    'bn-BD': 'হ্যালো, এটি একটি ভয়েস ডেমো। আজ আপনি কেমন আছেন?',
    'en-IN': 'Hello, this is a voice demonstration. How are you today?',
    'mr-IN': 'नमस्कार, हे आवाज प्रदर्शन आहे। आज तुम्ही कसे आहात?',
    'ta-IN': 'வணக்கம், இது ஒரு குரல் ஆர்ப்பாட்டம். இன்று நீங்கள் எப்படி இருக்கிறீர்கள்?',
    'te-IN': 'హలో, ఇది వాయిస్ డెమోన్స్ట్రేషన్. ఈరోజు మీరు ఎలా ఉన్నారు?',
  };

  return demoTexts[languageCode] || 'Hello, this is a voice demonstration. How are you today?';
}
