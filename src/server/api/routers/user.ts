import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

export const userRouter = createTRPCRouter({
  // 获取用户个人资料
  getProfile: protectedProcedure.query(async ({ ctx }) => {
    const user = await ctx.db.user.findUnique({
      where: { id: ctx.session.user.id },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        bio: true,
        location: true,
        website: true,
        phone: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "用户不存在",
      });
    }

    return user;
  }),

  // 更新用户个人资料
  updateProfile: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1, "姓名不能为空").max(50, "姓名不能超过50个字符").optional(),
        bio: z.string().max(500, "个人简介不能超过500个字符").optional(),
        location: z.string().max(100, "位置不能超过100个字符").optional(),
        website: z.string().url("请输入有效的网址").or(z.literal("")).optional(),
        phone: z.string().max(20, "电话号码不能超过20个字符").optional(),
        image: z.string().url("请输入有效的图片地址").optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const updatedUser = await ctx.db.user.update({
        where: { id: ctx.session.user.id },
        data: {
          ...input,
          updatedAt: new Date(),
        },
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          bio: true,
          location: true,
          website: true,
          phone: true,
          role: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return updatedUser;
    }),

  // 获取用户统计信息
  getStats: protectedProcedure.query(async ({ ctx }) => {
    // 获取语音生成总次数
    const totalGenerations = await ctx.db.audioGeneration.count({
      where: { userId: ctx.session.user.id },
    });

    // 获取本月生成次数
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const monthlyGenerations = await ctx.db.audioGeneration.count({
      where: {
        userId: ctx.session.user.id,
        createdAt: {
          gte: currentMonth,
        },
      },
    });

    // 获取总订单数
    const totalOrders = await ctx.db.order.count({
      where: { userId: ctx.session.user.id },
    });

    // 获取成功订单数
    const successfulOrders = await ctx.db.order.count({
      where: {
        userId: ctx.session.user.id,
        status: "COMPLETED",
      },
    });

    // 获取总消费金额
    const totalSpent = await ctx.db.order.aggregate({
      where: {
        userId: ctx.session.user.id,
        status: "COMPLETED",
      },
      _sum: {
        amount: true,
      },
    });

    // 获取最近的语音生成记录
    const recentGenerations = await ctx.db.audioGeneration.findMany({
      where: { userId: ctx.session.user.id },
      orderBy: { createdAt: "desc" },
      take: 5,
      select: {
        id: true,
        inputText: true,
        outputAudioUrl: true,
        createdAt: true,
        template: {
          select: {
            originalName: true,
          },
        },
      },
    });

    return {
      totalGenerations,
      monthlyGenerations,
      totalOrders,
      successfulOrders,
      totalSpent: totalSpent._sum.amount || 0,
      recentGenerations,
    };
  }),

  // 获取用户活动历史
  getActivity: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(20),
        offset: z.number().min(0).default(0),
      })
    )
    .query(async ({ ctx, input }) => {
      // 获取语音生成历史
      const generations = await ctx.db.audioGeneration.findMany({
        where: { userId: ctx.session.user.id },
        orderBy: { createdAt: "desc" },
        skip: input.offset,
        take: input.limit,
        select: {
          id: true,
          inputText: true,
          outputAudioUrl: true,
          createdAt: true,
          template: {
            select: {
              originalName: true,
              gender: true,
            },
          },
        },
      });

      // 获取总数用于分页
      const totalCount = await ctx.db.audioGeneration.count({
        where: { userId: ctx.session.user.id },
      });

      return {
        generations,
        totalCount,
        hasMore: input.offset + input.limit < totalCount,
      };
    }),

  // 删除历史记录
  deleteHistory: protectedProcedure
    .input(
      z.object({
        ids: z.array(z.string()).min(1, "请选择要删除的记录"),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // 验证所有记录都属于当前用户
      const records = await ctx.db.audioGeneration.findMany({
        where: {
          id: { in: input.ids },
          userId: ctx.session.user.id,
        },
        select: { id: true },
      });

      if (records.length !== input.ids.length) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "无权删除这些记录",
        });
      }

      // 删除记录
      const deletedCount = await ctx.db.audioGeneration.deleteMany({
        where: {
          id: { in: input.ids },
          userId: ctx.session.user.id,
        },
      });

      return {
        deletedCount: deletedCount.count,
        success: true,
      };
    }),

  // 删除用户账户
  deleteAccount: protectedProcedure
    .input(
      z.object({
        confirmPassword: z.string().min(1, "请输入密码确认"),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // 注意：这里应该添加密码验证逻辑
      // 由于使用的是 NextAuth，可能需要不同的验证方式

      // 软删除用户（标记为已删除，而不是真正删除）
      const deletedUser = await ctx.db.user.update({
        where: { id: ctx.session.user.id },
        data: {
          email: `deleted_${ctx.session.user.id}_${Date.now()}@deleted.com`,
          name: "已删除用户",
          image: null,
          bio: null,
          location: null,
          website: null,
          phone: null,
          // 可以添加一个 deletedAt 字段来标记删除时间
        },
      });

      return { success: true };
    }),

  // 更改密码（如果使用自定义认证）
  changePassword: protectedProcedure
    .input(
      z.object({
        currentPassword: z.string().min(1, "请输入当前密码"),
        newPassword: z.string().min(8, "新密码至少需要8个字符"),
        confirmPassword: z.string().min(1, "请确认新密码"),
      })
    )
    .mutation(async ({ ctx, input }) => {
      if (input.newPassword !== input.confirmPassword) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "新密码和确认密码不匹配",
        });
      }

      // 注意：这里需要根据实际的认证方式来实现密码更改逻辑
      // 如果使用 NextAuth 的 OAuth 提供商，可能不需要这个功能
      
      throw new TRPCError({
        code: "NOT_IMPLEMENTED",
        message: "密码更改功能暂未实现",
      });
    }),

  // 获取用户偏好设置
  getPreferences: protectedProcedure.query(async ({ ctx }) => {
    const preferences = await ctx.db.userPreferences.findUnique({
      where: { userId: ctx.session.user.id },
    });

    // 如果没有偏好设置，返回默认值
    if (!preferences) {
      return {
        language: "zh-CN",
        theme: "system",
        emailNotifications: true,
        pushNotifications: true,
        marketingEmails: false,
      };
    }

    return preferences;
  }),

  // 更新用户偏好设置
  updatePreferences: protectedProcedure
    .input(
      z.object({
        language: z.enum(["zh-CN", "en-US"]).optional(),
        theme: z.enum(["light", "dark", "system"]).optional(),
        emailNotifications: z.boolean().optional(),
        pushNotifications: z.boolean().optional(),
        marketingEmails: z.boolean().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const preferences = await ctx.db.userPreferences.upsert({
        where: { userId: ctx.session.user.id },
        update: input,
        create: {
          userId: ctx.session.user.id,
          ...input,
        },
      });

      return preferences;
    }),
});
