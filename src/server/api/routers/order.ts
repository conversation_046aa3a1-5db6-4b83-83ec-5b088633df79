import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 超级管理员权限检查中间件
const superAdminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });

  if (!user || user.role !== 'SUPER_ADMIN') {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Super admin access required',
    });
  }

  return next({
    ctx: {
      ...ctx,
      session: { ...ctx.session, user: { ...ctx.session.user, role: user.role } },
    },
  });
});

export const orderRouter = createTRPCRouter({
  
  // 创建订单
  createOrder: protectedProcedure
    .input(z.object({
      planId: z.string(),
      paymentProvider: z.enum(['PAYPAL', 'KHQR', 'WECHAT', 'STRIPE']),
      billingCycle: z.enum(['monthly', 'yearly']).default('monthly'),
      currency: z.enum(['USD', 'KHR']).default('USD'),
    }))
    .mutation(async ({ ctx, input }) => {
      const { planId, paymentProvider, billingCycle, currency } = input;

      // 获取定价计划
      const plan = await ctx.db.pricingPlan.findUnique({
        where: { id: planId },
      });

      if (!plan) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Pricing plan not found',
        });
      }

      // 检查用户是否已有活跃订阅
      const existingSubscription = await ctx.db.subscription.findFirst({
        where: {
          userId: ctx.session.user.id,
          status: 'ACTIVE',
        },
      });

      if (existingSubscription) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'User already has an active subscription',
        });
      }

      // 计算价格
      let finalPrice = plan.priceUsd;
      let finalPriceKhr = plan.priceKhr || 0;
      
      if (billingCycle === 'yearly') {
        finalPrice = finalPrice * 12 * 0.8; // 年付8折
        finalPriceKhr = finalPriceKhr * 12 * 0.8;
      }

      // 根据货币选择价格
      const amount = currency === 'KHR' ? finalPriceKhr : finalPrice;

      // 计算订阅时间
      const startDate = new Date();
      const endDate = new Date();
      if (billingCycle === 'yearly') {
        endDate.setFullYear(endDate.getFullYear() + 1);
      } else {
        endDate.setMonth(endDate.getMonth() + 1);
      }

      // 生成订单号
      const orderNumber = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;

      // 创建订阅记录
      const subscription = await ctx.db.subscription.create({
        data: {
          userId: ctx.session.user.id,
          planId: planId,
          planType: plan.name as any,
          status: 'PENDING',
          startDate,
          endDate,
          amount: amount,
          currency,
          billingCycle,
          nextBillingDate: endDate,
        },
        include: {
          plan: true,
        },
      });

      // 创建支付记录
      const payment = await ctx.db.payment.create({
        data: {
          userId: ctx.session.user.id,
          subscriptionId: subscription.id,
          amount: amount,
          currency,
          provider: paymentProvider,
          status: 'PENDING',
          description: `${plan.displayName} - ${billingCycle === 'yearly' ? '年付' : '月付'}`,
          metadata: {
            orderNumber,
            planName: plan.name,
            billingCycle,
            originalPriceUsd: plan.priceUsd,
            originalPriceKhr: plan.priceKhr,
            discount: billingCycle === 'yearly' ? 0.2 : 0,
          },
        },
      });

      return {
        orderNumber,
        subscription,
        payment,
        redirectUrl: `/payment/${payment.id}`,
      };
    }),

  // 获取订单详情
  getOrder: protectedProcedure
    .input(z.object({
      paymentId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      // 获取用户角色
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      // 构建查询条件：普通用户只能查看自己的订单，超级管理员可以查看所有订单
      const whereCondition = user?.role === 'SUPER_ADMIN'
        ? { id: input.paymentId }
        : { id: input.paymentId, userId: ctx.session.user.id };

      const payment = await ctx.db.payment.findFirst({
        where: whereCondition,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
            },
          },
          creditPurchase: true,
        },
      });

      if (!payment) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Order not found',
        });
      }

      return payment;
    }),

  // 更新支付状态
  updatePaymentStatus: protectedProcedure
    .input(z.object({
      paymentId: z.string(),
      status: z.enum(['PENDING', 'COMPLETED', 'FAILED', 'CANCELLED']),
      transactionId: z.string().optional(),
      metadata: z.record(z.any()).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { paymentId, status, transactionId, metadata } = input;

      // 验证支付记录所有权
      const payment = await ctx.db.payment.findFirst({
        where: {
          id: paymentId,
          userId: ctx.session.user.id,
        },
        include: {
          subscription: true,
        },
      });

      if (!payment) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Payment not found',
        });
      }

      // 更新支付记录
      const updatedPayment = await ctx.db.payment.update({
        where: { id: paymentId },
        data: {
          status,
          completedAt: status === 'COMPLETED' ? new Date() : null,
          ...(transactionId && {
            [`${payment.provider.toLowerCase()}TransactionId`]: transactionId,
          }),
          ...(metadata && {
            metadata: {
              ...(payment.metadata as object || {}),
              ...metadata,
            },
          }),
        },
      });

      // 如果支付成功，处理积分购买
      if (status === 'COMPLETED' && payment.creditPurchase) {
        // 更新积分购买状态
        await ctx.db.creditPurchase.update({
          where: { id: payment.creditPurchase.id },
          data: {
            status: 'COMPLETED',
            completedAt: new Date(),
          },
        });

        // 给用户增加积分
        await ctx.db.user.update({
          where: { id: ctx.session.user.id },
          data: {
            credits: { increment: payment.creditPurchase.credits },
          },
        });

        // 记录积分赠送历史
        await ctx.db.creditGift.create({
          data: {
            fromUserId: null, // 系统赠送
            toUserId: ctx.session.user.id,
            amount: payment.creditPurchase.credits,
            reason: `购买积分包：${payment.creditPurchase.packageName}`,
            type: 'PROMOTION',
          },
        });
      }

      return updatedPayment;
    }),

  // 获取用户订单历史
  getOrderHistory: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(50).default(10),
      status: z.enum(['PENDING', 'COMPLETED', 'FAILED', 'CANCELLED']).optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { page, limit, status } = input;
      const skip = (page - 1) * limit;

      const where = {
        userId: ctx.session.user.id,
        ...(status && { status }),
      };

      const [payments, total] = await Promise.all([
        ctx.db.payment.findMany({
          where,
          include: {
            creditPurchase: true,
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        ctx.db.payment.count({ where }),
      ]);

      return {
        payments,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    }),

  // 管理员：获取所有订单
  getAllOrders: superAdminProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      status: z.enum(['PENDING', 'COMPLETED', 'FAILED', 'CANCELLED']).optional(),
      provider: z.enum(['PAYPAL', 'KHQR', 'WECHAT', 'STRIPE']).optional(),
      startDate: z.date().optional(),
      endDate: z.date().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { page, limit, status, provider, startDate, endDate } = input;
      const skip = (page - 1) * limit;

      const where: any = {};
      if (status) where.status = status;
      if (provider) where.provider = provider;
      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) where.createdAt.gte = startDate;
        if (endDate) where.createdAt.lte = endDate;
      }

      const [payments, total] = await Promise.all([
        ctx.db.payment.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            creditPurchase: true,
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        ctx.db.payment.count({ where }),
      ]);

      return {
        payments,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    }),

  // 管理员：更新订单状态
  adminUpdateOrder: superAdminProcedure
    .input(z.object({
      paymentId: z.string(),
      status: z.enum(['PENDING', 'COMPLETED', 'FAILED', 'CANCELLED']),
      notes: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { paymentId, status, notes } = input;

      const payment = await ctx.db.payment.findUnique({
        where: { id: paymentId },
        include: {
          subscription: true,
          user: true,
        },
      });

      if (!payment) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Payment not found',
        });
      }

      // 更新支付状态
      const updatedPayment = await ctx.db.payment.update({
        where: { id: paymentId },
        data: {
          status,
          completedAt: status === 'COMPLETED' ? new Date() : null,
          metadata: {
            ...(payment.metadata as object || {}),
            adminNotes: notes,
            adminUpdatedBy: ctx.session.user.id,
            adminUpdatedAt: new Date().toISOString(),
          },
        },
      });

      // 如果支付成功，激活订阅
      if (status === 'COMPLETED' && payment.subscription) {
        await ctx.db.subscription.update({
          where: { id: payment.subscription.id },
          data: { status: 'ACTIVE' },
        });

        // 更新用户订阅计划
        const plan = await ctx.db.pricingPlan.findUnique({
          where: { id: payment.subscription.planId! },
        });

        if (plan) {
          await ctx.db.user.update({
            where: { id: payment.userId },
            data: {
              professionalQuota: plan.monthlyQuota,
              usedProfessionalQuota: 0,
            },
          });
        }
      }

      return updatedPayment;
    }),

  // 获取用户订单列表
  getUserOrders: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(10),
        offset: z.number().min(0).default(0),
      })
    )
    .query(async ({ ctx, input }) => {
      const orders = await ctx.db.order.findMany({
        where: { userId: ctx.session.user.id },
        orderBy: { createdAt: "desc" },
        skip: input.offset,
        take: input.limit,
        select: {
          id: true,
          amount: true,
          currency: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          package: {
            select: {
              name: true,
              credits: true,
            },
          },
        },
      });

      const totalCount = await ctx.db.order.count({
        where: { userId: ctx.session.user.id },
      });

      return {
        orders,
        totalCount,
        hasMore: input.offset + input.limit < totalCount,
      };
    }),
});
