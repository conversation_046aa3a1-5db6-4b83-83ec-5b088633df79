import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 风格类型枚举
const StyleTypeSchema = z.enum(['SINGLE_SPEAKER', 'MULTI_SPEAKER']);
const StyleSourceSchema = z.enum(['OFFICIAL', 'USER_CREATED', 'COMMUNITY']);

// 创建风格的输入验证
const CreateStyleSchema = z.object({
  name: z.string().min(1).max(100),
  nameEn: z.string().optional(),
  nameKhmer: z.string().optional(),
  description: z.string().optional(),
  prompt: z.string().min(1).max(1000),
  type: StyleTypeSchema.default('SINGLE_SPEAKER'),
  categoryId: z.string().optional(),
  parameters: z.record(z.any()).optional(), // JSON参数
  speakerConfig: z.record(z.any()).optional(), // 多人风格配置
  tags: z.array(z.string()).default([]),
  icon: z.string().optional(),
  color: z.string().optional(),
  isPublic: z.boolean().default(false),
});

// 更新风格的输入验证
const UpdateStyleSchema = CreateStyleSchema.partial().extend({
  id: z.string(),
});

// 查询风格的输入验证
const GetStylesSchema = z.object({
  categoryId: z.string().optional(),
  type: StyleTypeSchema.optional(),
  source: StyleSourceSchema.optional(),
  isPublic: z.boolean().optional(),
  userId: z.string().optional(),
  search: z.string().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
});

export const styleManagementRouter = createTRPCRouter({
  // 获取风格分类列表
  getCategories: publicProcedure
    .query(async ({ ctx }) => {
      try {
        const categories = await ctx.db.styleCategory.findMany({
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' },
        });

        console.log(`✅ 获取到 ${categories.length} 个风格分类`);
        return categories;
      } catch (error) {
        console.error('Error fetching style categories:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取风格分类失败',
        });
      }
    }),

  // 获取单人风格分类列表
  getSingleSpeakerCategories: publicProcedure
    .query(async ({ ctx }) => {
      try {
        // 获取只包含单人风格的分类
        const categories = await ctx.db.styleCategory.findMany({
          where: {
            isActive: true,
            name: {
              in: ['情感表达', '语调风格', '专业角色', '应用场景', '节奏控制', '行业专用']
            }
          },
          orderBy: { sortOrder: 'asc' },
        });

        console.log(`✅ 获取到 ${categories.length} 个单人风格分类`);
        return categories;
      } catch (error) {
        console.error('Error fetching single speaker categories:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取单人风格分类失败',
        });
      }
    }),

  // 获取多人对话分类列表
  getMultiSpeakerCategories: publicProcedure
    .query(async ({ ctx }) => {
      try {
        // 获取只包含多人对话风格的分类
        const categories = await ctx.db.styleCategory.findMany({
          where: {
            isActive: true,
            name: {
              in: ['商务对话', '教育互动', '服务咨询', '社交聊天', '媒体访谈', '专业咨询']
            }
          },
          orderBy: { sortOrder: 'asc' },
        });

        console.log(`✅ 获取到 ${categories.length} 个多人对话分类`);
        return categories;
      } catch (error) {
        console.error('Error fetching multi speaker categories:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取多人对话分类失败',
        });
      }
    }),

  // 创建自定义风格
  createStyle: protectedProcedure
    .input(CreateStyleSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;

        // 检查分类是否存在
        if (input.categoryId) {
          const category = await ctx.db.styleCategory.findUnique({
            where: { id: input.categoryId },
          });
          if (!category) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: '指定的风格分类不存在',
            });
          }
        }

        const style = await ctx.db.userStyle.create({
          data: {
            ...input,
            userId,
            source: 'USER_CREATED',
          },
          include: {
            category: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });

        console.log(`✅ 用户 ${userId} 创建了风格: ${style.name}`);
        return style;
      } catch (error) {
        console.error('Error creating style:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '创建风格失败',
        });
      }
    }),

  // 获取风格列表
  getStyles: publicProcedure
    .input(GetStylesSchema)
    .query(async ({ ctx, input }) => {
      try {
        const {
          categoryId,
          type,
          source,
          isPublic,
          userId,
          search,
          limit,
          offset,
        } = input;

        // 构建查询条件
        const where: any = {
          isActive: true,
        };

        if (categoryId) {
          where.categoryId = categoryId;
        }

        if (type) {
          where.type = type;
        }

        if (source) {
          where.source = source;
        }

        if (isPublic !== undefined) {
          where.isPublic = isPublic;
        }

        if (userId) {
          where.userId = userId;
        }

        if (search) {
          where.OR = [
            { name: { contains: search, mode: 'insensitive' } },
            { nameEn: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
            { tags: { has: search } },
          ];
        }

        // 获取风格列表
        const styles = await ctx.db.userStyle.findMany({
          where,
          include: {
            category: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            _count: {
              select: {
                favorites: true,
              },
            },
          },
          orderBy: [
            { favoriteCount: 'desc' },
            { usageCount: 'desc' },
            { createdAt: 'desc' },
          ],
          take: limit,
          skip: offset,
        });

        // 获取总数
        const total = await ctx.db.userStyle.count({ where });

        console.log(`✅ 获取到 ${styles.length} 个风格，总数: ${total}`);
        return {
          styles,
          total,
          hasMore: offset + limit < total,
        };
      } catch (error) {
        console.error('Error fetching styles:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取风格列表失败',
        });
      }
    }),

  // 获取单人风格列表（专用于单人语音生成）
  getSingleSpeakerStyles: publicProcedure
    .input(GetStylesSchema.omit({ type: true }))
    .query(async ({ ctx, input }) => {
      try {
        const {
          categoryId,
          source,
          isPublic,
          userId,
          search,
          limit,
          offset,
        } = input;

        // 构建查询条件 - 只获取单人风格
        const where: any = {
          isActive: true,
          type: 'SINGLE_SPEAKER',
        };

        if (categoryId) {
          where.categoryId = categoryId;
        }

        if (source) {
          where.source = source;
        }

        if (isPublic !== undefined) {
          where.isPublic = isPublic;
        }

        if (userId) {
          where.userId = userId;
        }

        if (search) {
          where.OR = [
            { name: { contains: search, mode: 'insensitive' } },
            { nameEn: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
            { tags: { has: search } },
          ];
        }

        // 获取单人风格列表
        const styles = await ctx.db.userStyle.findMany({
          where,
          include: {
            category: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            _count: {
              select: {
                favorites: true,
              },
            },
          },
          orderBy: [
            { isOfficial: 'desc' }, // 官方风格优先
            { favoriteCount: 'desc' },
            { usageCount: 'desc' },
            { createdAt: 'desc' },
          ],
          take: limit,
          skip: offset,
        });

        // 获取总数
        const total = await ctx.db.userStyle.count({ where });

        console.log(`✅ 获取到 ${styles.length} 个单人风格，总数: ${total}`);
        return {
          styles,
          total,
          hasMore: offset + limit < total,
        };
      } catch (error) {
        console.error('Error fetching single speaker styles:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取单人风格列表失败',
        });
      }
    }),

  // 获取多人对话风格列表（专用于多人对话生成）
  getMultiSpeakerStyles: publicProcedure
    .input(GetStylesSchema.omit({ type: true }))
    .query(async ({ ctx, input }) => {
      try {
        const {
          categoryId,
          source,
          isPublic,
          userId,
          search,
          limit,
          offset,
        } = input;

        // 构建查询条件 - 只获取多人对话风格
        const where: any = {
          isActive: true,
          type: 'MULTI_SPEAKER',
        };

        if (categoryId) {
          where.categoryId = categoryId;
        }

        if (source) {
          where.source = source;
        }

        if (isPublic !== undefined) {
          where.isPublic = isPublic;
        }

        if (userId) {
          where.userId = userId;
        }

        if (search) {
          where.OR = [
            { name: { contains: search, mode: 'insensitive' } },
            { nameEn: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
            { tags: { has: search } },
          ];
        }

        // 获取多人对话风格列表
        const styles = await ctx.db.userStyle.findMany({
          where,
          include: {
            category: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            _count: {
              select: {
                favorites: true,
              },
            },
          },
          orderBy: [
            { isOfficial: 'desc' }, // 官方风格优先
            { favoriteCount: 'desc' },
            { usageCount: 'desc' },
            { createdAt: 'desc' },
          ],
          take: limit,
          skip: offset,
        });

        // 获取总数
        const total = await ctx.db.userStyle.count({ where });

        console.log(`✅ 获取到 ${styles.length} 个多人对话风格，总数: ${total}`);
        return {
          styles,
          total,
          hasMore: offset + limit < total,
        };
      } catch (error) {
        console.error('Error fetching multi speaker styles:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取多人对话风格列表失败',
        });
      }
    }),

  // 获取单个风格详情
  getStyleById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      try {
        const style = await ctx.db.userStyle.findUnique({
          where: { id: input.id },
          include: {
            category: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            _count: {
              select: {
                favorites: true,
              },
            },
          },
        });

        if (!style) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '风格不存在',
          });
        }

        // 增加使用次数
        await ctx.db.userStyle.update({
          where: { id: input.id },
          data: { usageCount: { increment: 1 } },
        });

        console.log(`✅ 获取风格详情: ${style.name}`);
        return style;
      } catch (error) {
        console.error('Error fetching style:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取风格详情失败',
        });
      }
    }),

  // 更新风格
  updateStyle: protectedProcedure
    .input(UpdateStyleSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;
        const { id, ...updateData } = input;

        // 检查风格是否存在且属于当前用户
        const existingStyle = await ctx.db.userStyle.findUnique({
          where: { id },
        });

        if (!existingStyle) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '风格不存在',
          });
        }

        if (existingStyle.userId !== userId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: '无权限修改此风格',
          });
        }

        // 检查分类是否存在
        if (updateData.categoryId) {
          const category = await ctx.db.styleCategory.findUnique({
            where: { id: updateData.categoryId },
          });
          if (!category) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: '指定的风格分类不存在',
            });
          }
        }

        const updatedStyle = await ctx.db.userStyle.update({
          where: { id },
          data: updateData,
          include: {
            category: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });

        console.log(`✅ 用户 ${userId} 更新了风格: ${updatedStyle.name}`);
        return updatedStyle;
      } catch (error) {
        console.error('Error updating style:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '更新风格失败',
        });
      }
    }),

  // 删除风格
  deleteStyle: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;

        // 检查风格是否存在且属于当前用户
        const existingStyle = await ctx.db.userStyle.findUnique({
          where: { id: input.id },
        });

        if (!existingStyle) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '风格不存在',
          });
        }

        if (existingStyle.userId !== userId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: '无权限删除此风格',
          });
        }

        await ctx.db.userStyle.delete({
          where: { id: input.id },
        });

        console.log(`✅ 用户 ${userId} 删除了风格: ${existingStyle.name}`);
        return { success: true };
      } catch (error) {
        console.error('Error deleting style:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '删除风格失败',
        });
      }
    }),

  // 收藏/取消收藏风格
  toggleFavorite: protectedProcedure
    .input(z.object({ styleId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;

        // 检查风格是否存在
        const style = await ctx.db.userStyle.findUnique({
          where: { id: input.styleId },
        });

        if (!style) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '风格不存在',
          });
        }

        // 检查是否已收藏
        const existingFavorite = await ctx.db.styleFavorite.findUnique({
          where: {
            userId_styleId: {
              userId,
              styleId: input.styleId,
            },
          },
        });

        if (existingFavorite) {
          // 取消收藏
          await ctx.db.$transaction([
            ctx.db.styleFavorite.delete({
              where: { id: existingFavorite.id },
            }),
            ctx.db.userStyle.update({
              where: { id: input.styleId },
              data: { favoriteCount: { decrement: 1 } },
            }),
          ]);

          console.log(`✅ 用户 ${userId} 取消收藏风格: ${style.name}`);
          return { isFavorited: false };
        } else {
          // 添加收藏
          await ctx.db.$transaction([
            ctx.db.styleFavorite.create({
              data: {
                userId,
                styleId: input.styleId,
              },
            }),
            ctx.db.userStyle.update({
              where: { id: input.styleId },
              data: { favoriteCount: { increment: 1 } },
            }),
          ]);

          console.log(`✅ 用户 ${userId} 收藏了风格: ${style.name}`);
          return { isFavorited: true };
        }
      } catch (error) {
        console.error('Error toggling favorite:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '收藏操作失败',
        });
      }
    }),

  // 获取用户收藏的风格
  getFavoriteStyles: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;

        const favorites = await ctx.db.styleFavorite.findMany({
          where: { userId },
          include: {
            style: {
              include: {
                category: true,
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
                _count: {
                  select: {
                    favorites: true,
                  },
                },
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          take: input.limit,
          skip: input.offset,
        });

        const total = await ctx.db.styleFavorite.count({
          where: { userId },
        });

        const styles = favorites.map(fav => fav.style);

        console.log(`✅ 获取用户 ${userId} 的收藏风格: ${styles.length} 个`);
        return {
          styles,
          total,
          hasMore: input.offset + input.limit < total,
        };
      } catch (error) {
        console.error('Error fetching favorite styles:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取收藏风格失败',
        });
      }
    }),

  // 获取用户创建的风格
  getMyStyles: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;

        const styles = await ctx.db.userStyle.findMany({
          where: {
            userId,
            isActive: true,
          },
          include: {
            category: true,
            _count: {
              select: {
                favorites: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          take: input.limit,
          skip: input.offset,
        });

        const total = await ctx.db.userStyle.count({
          where: {
            userId,
            isActive: true,
          },
        });

        console.log(`✅ 获取用户 ${userId} 创建的风格: ${styles.length} 个`);
        return {
          styles,
          total,
          hasMore: input.offset + input.limit < total,
        };
      } catch (error) {
        console.error('Error fetching user styles:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取我的风格失败',
        });
      }
    }),

  // 创建分享链接
  createShare: protectedProcedure
    .input(z.object({
      styleId: z.string(),
      expiresAt: z.date(),
      maxUsage: z.number().min(1).max(999).default(10),
      requireAuth: z.boolean().default(false),
      allowCopy: z.boolean().default(true),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;

        // 验证风格所有权
        const style = await ctx.db.userStyle.findFirst({
          where: {
            id: input.styleId,
            userId,
          },
        });

        if (!style) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "风格不存在或无权限",
          });
        }

        // 生成分享码
        const shareCode = `style_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

        const share = await ctx.db.styleShare.create({
          data: {
            styleId: input.styleId,
            sharedBy: userId,
            shareCode,
            expiresAt: input.expiresAt,
            maxUsage: input.maxUsage,
            requireAuth: input.requireAuth,
            allowCopy: input.allowCopy,
          },
        });

        console.log(`✅ 用户 ${userId} 创建了风格分享: ${shareCode}`);
        return share;
      } catch (error) {
        console.error('Error creating share:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '创建分享链接失败',
        });
      }
    }),

  // 获取分享统计
  getShareStats: protectedProcedure
    .input(z.object({
      styleId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      try {
        const userId = ctx.session.user.id;

        const shares = await ctx.db.styleShare.findMany({
          where: {
            styleId: input.styleId,
            sharedBy: userId,
          },
          orderBy: { createdAt: 'desc' },
        });

        console.log(`✅ 获取风格 ${input.styleId} 的分享统计: ${shares.length} 个`);
        return shares;
      } catch (error) {
        console.error('Error fetching share stats:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取分享统计失败',
        });
      }
    }),

  // 通过分享码获取风格
  getSharedStyle: publicProcedure
    .input(z.object({
      shareCode: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      try {
        const share = await ctx.db.styleShare.findUnique({
          where: { shareCode: input.shareCode },
          include: {
            style: {
              include: {
                category: true,
                user: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        });

        if (!share) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "分享链接不存在",
          });
        }

        // 检查是否过期
        if (new Date() > share.expiresAt) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "分享链接已过期",
          });
        }

        // 检查使用次数
        if (share.usageCount >= share.maxUsage) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "分享链接使用次数已达上限",
          });
        }

        // 更新使用次数
        await ctx.db.styleShare.update({
          where: { id: share.id },
          data: { usageCount: { increment: 1 } },
        });

        console.log(`✅ 分享码 ${input.shareCode} 被访问，使用次数: ${share.usageCount + 1}`);
        return {
          style: share.style,
          shareInfo: {
            shareCode: share.shareCode,
            allowCopy: share.allowCopy,
            requireAuth: share.requireAuth,
            usageCount: share.usageCount + 1,
            maxUsage: share.maxUsage,
          },
        };
      } catch (error) {
        console.error('Error fetching shared style:', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '获取分享风格失败',
        });
      }
    }),
});
