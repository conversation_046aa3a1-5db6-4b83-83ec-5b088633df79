import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";
import { TTSManager } from "~/lib/tts/tts-manager";
import { OptimizedTTSManager } from "~/lib/tts/optimized-tts-manager";
import { enhancedAudioCacheService } from "~/lib/cache/enhanced-audio-cache";
import { ttsPerformanceMonitor } from "~/lib/performance/tts-performance-monitor";
import { TRPCError } from "@trpc/server";
import { r2Storage, R2StorageService } from "~/lib/r2-storage";

export const ttsRouter = createTRPCRouter({
  // Get all available voice characters
  getCharacters: publicProcedure
    .input(z.object({
      apiProvider: z.enum(['GEMINI', 'OPENAI', 'OPENROUTER']).optional(),
      includeInactive: z.boolean().optional().default(false),
      languageCode: z.string().optional().default('en-US'),
    }).optional())
    .query(async ({ ctx, input }) => {
      // 处理可选输入
      const languageCode = input?.languageCode || 'en-US';
      const includeInactive = input?.includeInactive || false;
      const apiProvider = input?.apiProvider;

      // 获取语言信息
      const language = await ctx.db.language.findFirst({
        where: { code: languageCode },
      });

      if (!language) {
        return [];
      }

      const whereClause: any = {
        languageId: language.id,
        isCustom: false, // 只返回系统角色
      };

      // 只有明确要求包含非活跃角色时才包含
      if (!includeInactive) {
        whereClause.isActive = true;
      }

      const languageCharacters = await ctx.db.languageCharacter.findMany({
        where: whereClause,
        include: {
          template: {
            select: {
              id: true,
              apiProvider: true,
              apiVoiceName: true,
              originalName: true,
              gender: true,
            },
          },
        },
        orderBy: { sortOrder: 'asc' },
      });

      // 如果指定了API提供商，过滤结果
      let filteredCharacters = languageCharacters;
      if (apiProvider) {
        filteredCharacters = languageCharacters.filter(
          (char: any) => char.template?.apiProvider === apiProvider
        );
      }

      // 转换为兼容格式
      return filteredCharacters.map((langChar: any) => ({
        id: langChar.id,
        characterName: langChar.name,
        characterNameEn: langChar.template?.originalName || langChar.name,
        originalName: langChar.template?.originalName || langChar.name,
        apiProvider: langChar.template?.apiProvider || 'GEMINI',
        apiVoiceName: langChar.template?.apiVoiceName || langChar.name,
        gender: langChar.template?.gender || 'NEUTRAL',
        description: langChar.description,
        style: langChar.style,
        isActive: langChar.isActive,
        sortOrder: langChar.sortOrder,
        isCustom: langChar.isCustom,
      }));
    }),

  // Get character details by ID
  getCharacter: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      // 首先尝试从语言角色中查找
      const languageCharacter = await ctx.db.languageCharacter.findUnique({
        where: { id: input.id },
        include: {
          template: true,
          language: true,
        },
      });

      if (languageCharacter) {
        // 转换为兼容格式
        return {
          id: languageCharacter.id,
          characterName: languageCharacter.name,
          characterNameEn: languageCharacter.template?.originalName || languageCharacter.name,
          originalName: languageCharacter.template?.originalName || languageCharacter.name,
          apiProvider: languageCharacter.template?.apiProvider || 'GEMINI',
          apiVoiceName: languageCharacter.template?.apiVoiceName || languageCharacter.name,
          gender: languageCharacter.template?.gender || 'NEUTRAL',
          description: languageCharacter.description,
          style: languageCharacter.style,
          isActive: languageCharacter.isActive,
          sortOrder: languageCharacter.sortOrder,
          isCustom: languageCharacter.isCustom,
        };
      }

      // 如果没找到，尝试从自定义角色中查找
      const customCharacter = await ctx.db.customVoiceCharacter.findUnique({
        where: { id: input.id },
        include: {
          baseTemplate: true,
        },
      });

      if (customCharacter) {
        return {
          id: customCharacter.id,
          characterName: customCharacter.name,
          characterNameEn: customCharacter.nameEn || customCharacter.name,
          originalName: (customCharacter as any).baseTemplate?.originalName || customCharacter.name,
          apiProvider: (customCharacter as any).baseTemplate?.apiProvider || 'GEMINI',
          apiVoiceName: (customCharacter as any).baseTemplate?.apiVoiceName || customCharacter.name,
          gender: (customCharacter as any).baseTemplate?.gender || 'NEUTRAL',
          description: customCharacter.description || '',
          style: '',
          isActive: customCharacter.isActive,
          sortOrder: 0,
          isCustom: true,
        };
      }

      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Character not found',
      });
    }),

  // Generate speech with optimizations (public - allows demo usage)
  generateSpeechOptimized: publicProcedure
    .input(z.object({
      text: z.string().min(1).max(32000),
      characterId: z.string(),
      speed: z.number().min(0.25).max(4.0).default(1.0),
      pitch: z.number().min(-10).max(10).optional().default(0),
      volumeGainDb: z.number().min(-20).max(20).optional().default(0),
      format: z.enum(['MP3', 'WAV', 'AAC']).default('WAV'),
      quality: z.enum(['fast', 'high']).optional().default('fast'),
      stylePrompt: z.string().max(500).optional(),
      useStreaming: z.boolean().optional().default(false),
    }))
    .mutation(async ({ ctx, input }) => {
      const startTime = Date.now();
      const {
        text,
        characterId,
        speed,
        pitch,
        volumeGainDb,
        format,
        quality,
        stylePrompt,
        useStreaming
      } = input;
      const userId = ctx.session?.user?.id;

      let cacheHit = false;
      let errorOccurred = false;
      let errorMessage: string | undefined;

      try {
        // Validate text
        const textValidation = TTSManager.validateText(text);
        if (!textValidation.isValid) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: textValidation.error,
          });
        }

        // Get character information
        const character = await TTSManager.getCharacterInfo(characterId, ctx.db);

        // Check cache first
        const cacheKey = {
          text,
          voiceName: character.template?.apiVoiceName || character.name,
          quality: quality || 'fast',
          speed: speed || 1.0,
          pitch: pitch || 0,
          volumeGainDb: volumeGainDb || 0,
          stylePrompt,
        };

        const cacheStartTime = Date.now();
        const cachedResult = await enhancedAudioCacheService.get(cacheKey);
        const cacheTime = Date.now() - cacheStartTime;

        if (cachedResult && enhancedAudioCacheService.shouldCache(text)) {
          cacheHit = true;
          console.log('🎯 Using cached audio result');

          // Record performance metrics for cache hit
          ttsPerformanceMonitor.recordMetrics({
            totalTime: Date.now() - startTime,
            apiCallTime: 0,
            dbOperationTime: 0,
            fileUploadTime: 0,
            cacheTime,
            textLength: text.length,
            audioSize: cachedResult.fileSize,
            tokenCount: cachedResult.tokenCount,
            quality: quality || 'fast',
            voiceName: cachedResult.voiceName,
            timestamp: Date.now(),
            userId,
            cacheHit: true,
            errorOccurred: false,
          });

          return {
            audioUrl: cachedResult.audioUrl,
            duration: cachedResult.duration,
            characterCount: text.length,
            cost: cachedResult.cost,
            message: 'Audio generated successfully (cached)',
            fromCache: true,
          };
        }

        // Use optimized TTS manager for new generation
        const optimizedTTSManager = new OptimizedTTSManager(ctx.db);
        const apiStartTime = Date.now();

        const result = await optimizedTTSManager.generateSpeechOptimized({
          text,
          characterId,
          apiProvider: character.template?.apiProvider || 'GEMINI',
          voiceName: character.template?.apiVoiceName || character.name,
          speed,
          pitch,
          volumeGainDb,
          format,
          quality,
          stylePrompt,
          userId,
          useStreaming,
        });

        const apiCallTime = Date.now() - apiStartTime;

        // Cache the result if appropriate
        if (enhancedAudioCacheService.shouldCache(text)) {
          await enhancedAudioCacheService.set(cacheKey, {
            audioUrl: result.audioUrl,
            duration: result.duration,
            fileSize: 0, // Will be updated when we have the actual file size
            characterCount: result.characterCount,
            tokenCount: result.tokenCount,
            cost: result.cost || 0,
            voiceName: result.actualVoiceName,
            quality: quality || 'fast',
          });
        }

        // Record performance metrics
        ttsPerformanceMonitor.recordMetrics({
          totalTime: Date.now() - startTime,
          apiCallTime,
          dbOperationTime: 0, // Handled internally by optimized manager
          fileUploadTime: 0, // Handled internally by optimized manager
          cacheTime,
          textLength: text.length,
          audioSize: 0, // Will be updated when available
          tokenCount: result.tokenCount,
          quality: quality || 'fast',
          voiceName: result.actualVoiceName,
          timestamp: Date.now(),
          userId,
          cacheHit: false,
          errorOccurred: false,
        });

        return {
          audioUrl: result.audioUrl,
          duration: result.duration,
          characterCount: result.characterCount,
          cost: result.cost,
          message: 'Audio generated successfully (optimized)',
          fromCache: false,
        };

      } catch (error) {
        errorOccurred = true;
        errorMessage = error instanceof Error ? error.message : 'Unknown error';

        // Record error metrics
        ttsPerformanceMonitor.recordMetrics({
          totalTime: Date.now() - startTime,
          apiCallTime: 0,
          dbOperationTime: 0,
          fileUploadTime: 0,
          cacheTime: 0,
          textLength: text.length,
          audioSize: 0,
          tokenCount: 0,
          quality: quality || 'fast',
          voiceName: 'unknown',
          timestamp: Date.now(),
          userId,
          cacheHit,
          errorOccurred: true,
          errorMessage,
        });

        console.error('Optimized TTS generation error:', error);
        throw error;
      }
    }),

  // Generate speech (public - allows demo usage)
  generateSpeech: publicProcedure
    .input(z.object({
      text: z.string().min(1).max(32000),
      characterId: z.string(),
      speed: z.number().min(0.25).max(4.0).default(1.0),
      pitch: z.number().min(-10).max(10).optional().default(0),
      volumeGainDb: z.number().min(-20).max(20).optional().default(0),
      format: z.enum(['MP3', 'WAV', 'AAC']).default('WAV'),
      quality: z.enum(['fast', 'high']).optional().default('fast'),
      stylePrompt: z.string().max(500).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const {
        text,
        characterId,
        speed,
        pitch,
        volumeGainDb,
        format,
        quality,
        stylePrompt
      } = input;
      const userId = ctx.session?.user?.id;

      // 不要将stylePrompt合并到文本中，它应该作为单独的指令传递

      try {
        // Validate text
        const textValidation = TTSManager.validateText(text);
        if (!textValidation.isValid) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: textValidation.error,
          });
        }

        // For demo users (not logged in), limit text length
        if (!userId) {
          if (text.length > 500) {
            throw new TRPCError({
              code: 'BAD_REQUEST',
              message: 'Demo users are limited to 500 characters. Please sign in for full access.',
            });
          }
        } else {
          // Check user credits for logged in users using new credit calculation system
          try {
            // 使用新的积分计算服务进行预检查
            const { CreditCalculationService } = await import('~/lib/credits/credit-calculation-service');
            const creditService = new CreditCalculationService(ctx.db);

            // 估算输出token使用量：中文字符约等于1.5个token，英文单词约等于1.3个token
            // 注意：只对输出tokens扣积分，输入tokens成本很低可以忽略
            const estimatedOutputTokens = Math.ceil(text.length * 1.5);
            const estimatedTokenUsage = {
              totalTokens: estimatedOutputTokens, // 只估算输出tokens
            };

            // 根据质量计算积分需求
            const qualityType = quality === 'high' ? 'professional' : 'standard';
            const calculation = await creditService.calculateTTSCredits(estimatedTokenUsage, qualityType);

            // 检查用户积分是否足够
            const creditCheck = await creditService.checkUserCredits(userId, calculation.creditsNeeded);
            if (!creditCheck.isEnough) {
              throw new TRPCError({
                code: 'FORBIDDEN',
                message: `积分不足。需要 ${calculation.creditsNeeded} 积分，可用 ${creditCheck.availableCredits} 积分。请充值积分后再试。`,
              });
            }
          } catch (error) {
            if (error instanceof TRPCError) {
              throw error;
            }
            console.error('积分预检查失败:', error);
            throw new TRPCError({
              code: 'INTERNAL_SERVER_ERROR',
              message: '积分检查失败，请稍后重试',
            });
          }
        }

        // Get character information
        const character = await TTSManager.getCharacterInfo(characterId, ctx.db);

        // Check if user has access to this character tier (only for logged in users)
        let user = null;
        if (userId) {
          user = await ctx.db.user.findUnique({
            where: { id: userId },
          });
        }

        // Check character tier access
        // 移除所有tier检查 - 所有用户都可以使用所有角色

        // Generate speech
        const ttsManager = new TTSManager();
        const result = await ttsManager.generateSpeech({
          text: text, // 只传递用户输入的文本
          characterId,
          apiProvider: character.template?.apiProvider || 'GEMINI',
          voiceName: character.template?.apiVoiceName || character.name,
          speed,
          pitch,
          volumeGainDb,
          format,
          quality,
          stylePrompt, // stylePrompt作为单独的指令传递
        });

        // 如果用户已登录，消耗积分（基于真实token使用量和实际成本）
        if (userId) {
          try {
            // 使用API提供的真实token数据，只计算输出tokens（音频生成），忽略输入tokens
            const outputTokens = result.outputTokens || Math.ceil(text.length * 1.5);
            const inputTokens = result.inputTokens || 0;

            console.log(`🔍 开始积分扣除: 用户=${userId}, 文本长度=${text.length}, 输入tokens=${inputTokens}, 输出tokens=${outputTokens} (只对输出扣积分)`);

            // 使用新的积分计算服务
            const { CreditCalculationService } = await import('~/lib/credits/credit-calculation-service');
            const creditService = new CreditCalculationService(ctx.db);

            // 根据质量计算积分，只对输出tokens扣积分
            const qualityType = quality === 'high' ? 'professional' : 'standard';
            const calculation = await creditService.calculateTTSCredits(
              { totalTokens: outputTokens }, // 只使用输出tokens
              qualityType
            );

            console.log(`💰 计算积分需求: ${calculation.explanation}`);

            // 检查用户积分是否足够
            const creditCheck = await creditService.checkUserCredits(userId, calculation.creditsNeeded);
            if (!creditCheck.isEnough) {
              throw new TRPCError({
                code: 'FORBIDDEN',
                message: `积分不足。需要 ${calculation.creditsNeeded} 积分，可用 ${creditCheck.availableCredits} 积分`,
              });
            }

            // 消耗积分并记录使用历史
            await creditService.consumeCredits(
              userId,
              calculation,
              `语音生成 - ${character.characterName} (${quality === 'high' ? '高质量' : '快速'}模式)`
            );

            // 更新用户已使用积分
            await ctx.db.user.update({
              where: { id: userId },
              data: {
                usedCredits: {
                  increment: calculation.creditsNeeded,
                },
              },
            });

            console.log(`✅ 积分扣除成功: 扣除=${calculation.creditsNeeded}积分, 成本=$${calculation.costBreakdown.actualCost.toFixed(4)}`);

          } catch (creditError) {
            console.error('❌ 积分消耗失败:', creditError);
            // 积分消耗失败不影响语音生成结果，只记录错误
          }
        }

        // Upload audio to R2 storage and save record (only for logged in users)
        let audioGeneration = null;
        let audioUrl = '';

        if (userId) {
          // Create initial record
          audioGeneration = await ctx.db.audioGeneration.create({
            data: {
              userId,
              inputText: text, // 保存原始文本，不包含风格提示
              outputAudioUrl: '', // Will be updated after R2 upload
              templateId: character.templateId || null, // 使用templateId而不是characterId
              apiProvider: character.template?.apiProvider || 'GEMINI',
              actualVoiceName: character.template?.apiVoiceName || character.name,
              audioFormat: format,
              speed,
              duration: result.duration,
              characterCount: text.length, // 使用实际文本长度计算费用
              fileSize: result.audioData.length,
              cost: result.cost,
            },
          });

          // Upload to R2 if configured
          if (r2Storage.isConfigured()) {
            try {
              const fileName = R2StorageService.generateFileName(audioGeneration.id, 'wav');
              audioUrl = await r2Storage.uploadAudio(
                result.audioData,
                fileName,
                'audio/wav' // 统一使用WAV格式
              );

              // Update record with R2 URL
              await ctx.db.audioGeneration.update({
                where: { id: audioGeneration.id },
                data: { outputAudioUrl: audioUrl },
              });

              console.log(`✅ Audio uploaded to R2: ${audioUrl}`);
            } catch (error) {
              console.error('❌ R2 upload failed:', error);
              // Continue without R2 storage - audio will be returned as base64
            }
          } else {
            console.log('⚠️ R2 not configured, audio will be returned as base64');
          }

          // 积分已在上面的新系统中扣除，这里不再重复扣除
        }

        // Update usage statistics (only for logged in users)
        if (userId) {
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          await ctx.db.usage.upsert({
            where: {
              userId_date: {
                userId,
                date: today,
              },
            },
            update: {
              characterCount: {
                increment: text.length,
              },
              audioCount: {
                increment: 1,
              },
            },
            create: {
              userId,
              date: today,
              characterCount: text.length,
              audioCount: 1,
            },
          });

          // 触发统计更新通知（通过WebSocket或其他机制）
          // 这里我们可以添加一个事件发射器来通知前端更新统计
          console.log(`📊 统计已更新: 用户${userId} 生成了${text.length}字符的音频`);
        }

        // Return audio URL if uploaded to R2, otherwise base64 data
        return {
          id: audioGeneration?.id || 'demo',
          audioUrl: audioUrl || undefined, // R2 URL if available
          audioData: audioUrl ? undefined : result.audioData.toString('base64'), // Base64 fallback
          characterCount: text.length, // 返回原始文本长度
          tokenCount: result.tokenCount, // 新增token信息
          duration: result.duration,
          cost: result.cost,
          character: {
            characterName: character.characterName,
            characterNameEn: character.characterNameEn,
          },
        };

      } catch (error) {
        console.error('TTS Generation Error:', error);

        if (error instanceof TRPCError) {
          throw error;
        }

        // 提供友好的错误信息
        let userMessage = '语音生成失败，请稍后再试';

        if (error instanceof Error) {
          if (error.message.includes('rate limit') || error.message.includes('429')) {
            userMessage = '系统繁忙，请稍后再试';
          } else if (error.message.includes('quota') || error.message.includes('RESOURCE_EXHAUSTED')) {
            userMessage = '服务暂时不可用，请稍后再试';
          } else if (error.message.includes('network') || error.message.includes('timeout')) {
            userMessage = '网络连接异常，请检查网络后重试';
          }
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: userMessage,
        });
      }
    }),

  // Get user's generation history
  getHistory: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      const generations = await ctx.db.audioGeneration.findMany({
        where: { userId },
        include: {
          template: {
            select: {
              id: true,
              originalName: true,
              apiProvider: true,
              apiVoiceName: true,
              gender: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: input.limit,
        skip: input.offset,
      });

      const total = await ctx.db.audioGeneration.count({
        where: { userId },
      });

      return {
        generations,
        total,
        hasMore: input.offset + input.limit < total,
      };
    }),

  // Get user usage statistics
  getUsageStats: publicProcedure
    .query(async ({ ctx }) => {
      // If user is not logged in, return default values
      if (!ctx.session?.user?.id) {
        return {
          credits: { total: 1000, used: 0, remaining: 1000 },
          monthlyUsage: 0,
          dailyUsage: 0,
          usagePercentage: 0,
          monthlyStats: {
            charactersUsed: 0,
            audiosGenerated: 0,
          },
        };
      }

      const userId = ctx.session.user.id;

      const user = await ctx.db.user.findUnique({
        where: { id: userId },
        select: {
          credits: true,
          usedCredits: true,
        },
      });

      if (!user) {
        // Return default values if user not found
        return {
          credits: { total: 1000, used: 0, remaining: 1000 },
          monthlyUsage: 0,
          dailyUsage: 0,
          usagePercentage: 0,
          monthlyStats: {
            charactersUsed: 0,
            audiosGenerated: 0,
          },
        };
      }

      // Get this month's usage
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const monthlyUsage = await ctx.db.usage.aggregate({
        where: {
          userId,
          date: {
            gte: startOfMonth,
          },
        },
        _sum: {
          characterCount: true,
          audioCount: true,
        },
      });

      // Calculate usage percentage
      const usagePercentage = user.credits > 0 ? Math.round((user.usedCredits / user.credits) * 100) : 0;

      return {
        // Credits system
        credits: {
          total: user.credits,
          used: user.usedCredits,
          remaining: user.credits - user.usedCredits,
        },
        usagePercentage,
        monthlyStats: {
          charactersUsed: monthlyUsage._sum.characterCount || 0,
          audiosGenerated: monthlyUsage._sum.audioCount || 0,
        },
      };
    }),

  // Get user's audio generation history with R2 support
  getUserAudioHistory: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
      languageCode: z.string().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { limit, offset, languageCode } = input;
      const userId = ctx.session.user.id;

      const audioHistory = await ctx.db.audioGeneration.findMany({
        where: { userId },
        include: {
          template: {
            select: {
              id: true,
              originalName: true,
              apiProvider: true,
              apiVoiceName: true,
              gender: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
      });

      // 为每个音频生成记录查找相关的积分消耗
      const audioHistoryWithCredits = await Promise.all(
        audioHistory.map(async (item) => {
          // 查找在音频生成时间前后2分钟内的积分消耗记录
          const timeWindow = 2 * 60 * 1000; // 2分钟
          const startTime = new Date(item.createdAt.getTime() - timeWindow);
          const endTime = new Date(item.createdAt.getTime() + timeWindow);

          const creditUsage = await ctx.db.creditUsage.findFirst({
            where: {
              userId: userId,
              serviceType: {
                in: ['STANDARD_VOICE', 'PROFESSIONAL_VOICE'],
              },
              createdAt: {
                gte: startTime,
                lte: endTime,
              },
            },
            orderBy: {
              createdAt: 'desc',
            },
          });

          return {
            id: item.id,
            inputText: item.inputText,
            audioUrl: item.outputAudioUrl || null,
            character: {
              id: item.template?.id || '',
              characterName: item.template?.originalName || item.actualVoiceName,
              characterNameEn: item.template?.originalName || item.actualVoiceName,
              apiProvider: item.template?.apiProvider || item.apiProvider,
              gender: item.template?.gender || 'NEUTRAL',
            },
            audioFormat: item.audioFormat,
            speed: item.speed,
            duration: item.duration,
            characterCount: item.characterCount,
            fileSize: item.fileSize,
            cost: item.cost,
            createdAt: item.createdAt,
            status: item.outputAudioUrl ? 'COMPLETED' : 'FAILED',
            creditUsed: creditUsage?.amount || 0,
          };
        })
      );

      return audioHistoryWithCredits;
    }),

  // Performance monitoring endpoints (admin only)
  getPerformanceStats: protectedProcedure
    .input(z.object({
      timeRangeMs: z.number().optional().default(24 * 60 * 60 * 1000), // 24 hours default
    }))
    .query(async ({ ctx, input }) => {
      // Check if user is admin
      if (ctx.session.user.role !== 'SUPER_ADMIN') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Admin access required',
        });
      }

      const stats = ttsPerformanceMonitor.getStats(input.timeRangeMs);
      const trend = ttsPerformanceMonitor.getTrendAnalysis(input.timeRangeMs);
      const cacheStats = enhancedAudioCacheService.getStats();

      return {
        performance: stats,
        trend,
        cache: cacheStats,
      };
    }),

  getPerformanceReport: protectedProcedure
    .query(async ({ ctx }) => {
      // Check if user is admin
      if (ctx.session.user.role !== 'SUPER_ADMIN') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Admin access required',
        });
      }

      const report = ttsPerformanceMonitor.generateReport();
      return { report };
    }),

  clearPerformanceHistory: protectedProcedure
    .mutation(async ({ ctx }) => {
      // Check if user is admin
      if (ctx.session.user.role !== 'SUPER_ADMIN') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Admin access required',
        });
      }

      ttsPerformanceMonitor.clearHistory();
      return { success: true, message: 'Performance history cleared' };
    }),

  clearAudioCache: protectedProcedure
    .mutation(async ({ ctx }) => {
      // Check if user is admin
      if (ctx.session.user.role !== 'SUPER_ADMIN') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Admin access required',
        });
      }

      enhancedAudioCacheService.clear();
      return { success: true, message: 'Audio cache cleared' };
    }),
});
