import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 角色配置 Schema
const CharacterSchema = z.object({
  id: z.string(),
  name: z.string(),
  voiceId: z.string(),
  speed: z.number().min(0.5).max(2.0),
  pitch: z.number().min(-20).max(20),
  volume: z.number().min(0.1).max(2.0),
  emotion: z.string(),
});

// 对话内容 Schema
const DialogueSchema = z.object({
  id: z.string(),
  characterId: z.string(),
  text: z.string().min(1),
  order: z.number(),
});

// 对话生成请求 Schema
const ConversationGenerateSchema = z.object({
  characters: z.array(CharacterSchema).min(1),
  dialogues: z.array(DialogueSchema).min(1),
  sceneDescription: z.string().optional(),
  pauseBetweenLines: z.number().min(0.1).max(3.0).default(1.0),
  backgroundMusic: z.string().optional(),
  exportFormat: z.enum(['mp3', 'wav', 'aac']).default('mp3'),
});

// 对话模板 Schema
const ConversationTemplateSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  languageCode: z.string().default("zh-CN"),
  characters: z.array(CharacterSchema.omit({ id: true })),
  dialogues: z.array(DialogueSchema.omit({ id: true })),
  isPublic: z.boolean().default(false),
});

export const conversationRouter = createTRPCRouter({
  // 生成多人对话
  generate: protectedProcedure
    .input(ConversationGenerateSchema)
    .mutation(async ({ ctx, input }) => {
      const { characters, dialogues, sceneDescription, pauseBetweenLines, exportFormat } = input;

      try {
        // 验证用户权限和配额
        const user = await ctx.db.user.findUnique({
          where: { id: ctx.session.user.id },
          select: {
            id: true,
            standardQuota: true,
            usedStandardQuota: true,
            professionalQuota: true,
            usedProfessionalQuota: true,
            isActive: true,
          },
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // 计算总字符数
        const totalCharacters = dialogues.reduce((sum, dialogue) => sum + dialogue.text.length, 0);

        // 检查字符余额
        const totalQuota = user.standardQuota + user.professionalQuota;
        const totalUsed = user.usedStandardQuota + user.usedProfessionalQuota;
        const remainingCharacters = totalQuota - totalUsed;
        
        if (remainingCharacters < totalCharacters) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Insufficient character balance",
          });
        }

        // 验证所有角色的语音ID是否有效
        const voiceIds = characters.map(c => c.voiceId);
        const validVoices = await ctx.db.voiceCharacter.findMany({
          where: { id: { in: voiceIds } },
        });

        if (validVoices.length !== voiceIds.length) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Invalid voice character IDs",
          });
        }

        // 创建对话生成记录
        const conversation = await ctx.db.conversation.create({
          data: {
            userId: ctx.session.user.id,
            title: `Conversation - ${new Date().toLocaleDateString()}`,
            sceneDescription: sceneDescription || "",
            pauseBetweenLines,
            exportFormat,
            status: "PROCESSING",
            totalCharacters: characters.length,
          },
        });

        // 创建角色记录并建立ID映射
        const characterMap = new Map<string, string>(); // 前端角色ID -> 数据库角色ID
        const createdCharacters = [];

        for (const char of characters) {
          const createdChar = await ctx.db.conversationCharacter.create({
            data: {
              conversationId: conversation.id,
              name: char.name,
              voiceCharacterId: char.voiceId,
              speed: char.speed,
              pitch: char.pitch,
              volume: char.volume,
              emotion: char.emotion,
            },
            include: {
              voiceCharacter: true,
            },
          });

          // 使用前端传来的角色ID作为映射键
          characterMap.set(char.id, createdChar.id);
          createdCharacters.push(createdChar);
        }

        // 创建对话记录，使用正确的角色ID
        const createdDialogues = [];
        for (const dialogue of dialogues) {
          const characterId = characterMap.get(dialogue.characterId);
          if (!characterId) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: `Invalid character ID: ${dialogue.characterId}`,
            });
          }

          const createdDialogue = await ctx.db.conversationDialogue.create({
            data: {
              conversationId: conversation.id,
              characterId,
              text: dialogue.text,
              order: dialogue.order,
            },
          });
          createdDialogues.push(createdDialogue);
        }

        // 获取完整的对话数据
        const fullConversation = {
          ...conversation,
          characters: createdCharacters,
          dialogues: createdDialogues.sort((a, b) => a.order - b.order),
        };

        // 异步生成音频
        void generateConversationAudio(fullConversation.id, fullConversation);

        return {
          conversationId: fullConversation.id,
          status: fullConversation.status,
          estimatedDuration: calculateEstimatedDuration(dialogues, pauseBetweenLines),
        };

      } catch (error) {
        console.error("Conversation generation error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate conversation",
        });
      }
    }),

  // 获取对话状态
  getStatus: protectedProcedure
    .input(z.object({ conversationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const conversation = await ctx.db.conversation.findFirst({
        where: {
          id: input.conversationId,
          userId: ctx.session.user.id,
        },
        include: {
          characters: {
            include: {
              voiceCharacter: true,
            },
          },
          dialogues: {
            orderBy: { order: 'asc' },
          },
        },
      });

      if (!conversation) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Conversation not found",
        });
      }

      return {
        id: conversation.id,
        status: conversation.status,
        audioUrl: conversation.audioUrl,
        duration: conversation.duration,
        errorMessage: conversation.errorMessage,
        createdAt: conversation.createdAt,
        updatedAt: conversation.updatedAt,
      };
    }),

  // 获取用户的对话历史
  getHistory: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(50).default(10),
    }))
    .query(async ({ ctx, input }) => {
      const { page, limit } = input;
      const offset = (page - 1) * limit;

      const [conversations, total] = await Promise.all([
        ctx.db.conversation.findMany({
          where: { userId: ctx.session.user.id },
          include: {
            characters: {
              include: {
                template: true,
              },
            },
            _count: {
              select: { dialogues: true },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip: offset,
          take: limit,
        }),
        ctx.db.conversation.count({
          where: { userId: ctx.session.user.id },
        }),
      ]);

      // 为每个对话查找相关的积分消耗记录
      const conversationsWithCredits = await Promise.all(
        conversations.map(async (conversation) => {
          // 查找在对话创建时间前后5分钟内的积分消耗记录
          const timeWindow = 5 * 60 * 1000; // 5分钟
          const startTime = new Date(conversation.createdAt.getTime() - timeWindow);
          const endTime = new Date(conversation.createdAt.getTime() + timeWindow);

          const creditUsage = await ctx.db.creditUsage.findFirst({
            where: {
              userId: ctx.session.user.id,
              serviceType: 'STANDARD_VOICE', // 对话通常使用标准语音
              createdAt: {
                gte: startTime,
                lte: endTime,
              },
            },
            orderBy: {
              createdAt: 'desc',
            },
          });

          return {
            ...conversation,
            creditUsed: creditUsage?.amount || 0,
          };
        })
      );

      return {
        conversations: conversationsWithCredits,
        total,
        hasMore: offset + conversations.length < total,
      };
    }),

  // 保存对话模板
  saveTemplate: protectedProcedure
    .input(ConversationTemplateSchema)
    .mutation(async ({ ctx, input }) => {
      const template = await ctx.db.conversationTemplate.create({
        data: {
          ...input,
          userId: ctx.session.user.id,
          characters: input.characters as any,
          dialogues: input.dialogues as any,
        },
      });

      return template;
    }),

  // 获取对话模板
  getTemplates: protectedProcedure
    .input(z.object({
      includePublic: z.boolean().default(true),
      search: z.string().optional(),
      languageCode: z.string().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { includePublic, search, languageCode } = input;
      
      const where: any = {
        OR: [
          { userId: ctx.session.user.id },
        ],
      };

      if (includePublic) {
        where.OR.push({ isPublic: true });
      }

      const andConditions = [];

      if (search) {
        andConditions.push({
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
          ],
        });
      }

      if (languageCode) {
        andConditions.push({
          languageCode: languageCode,
        });
      }

      if (andConditions.length > 0) {
        where.AND = andConditions;
      }

      try {
        console.log('查询对话模板，条件:', JSON.stringify(where, null, 2));

        const templates = await ctx.db.conversationTemplate.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: [
            { userId: 'asc' }, // 系统模板优先
            { createdAt: 'desc' },
          ],
        });

        console.log(`✅ 查询到 ${templates.length} 个对话模板`);

        return templates.map(template => ({
          ...template,
          characters: JSON.parse(template.characters),
          dialogues: JSON.parse(template.dialogues),
          isOfficial: template.userId === null,
        }));
      } catch (error) {
        console.error('获取对话模板失败:', error);
        throw new Error('获取对话模板失败');
      }
    }),

  // 获取模板详情
  getTemplate: protectedProcedure
    .input(z.object({ templateId: z.string() }))
    .query(async ({ ctx, input }) => {
      const template = await ctx.db.conversationTemplate.findFirst({
        where: {
          id: input.templateId,
          OR: [
            { userId: ctx.session.user.id },
            { isPublic: true },
          ],
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!template) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Template not found",
        });
      }

      return {
        ...template,
        characters: JSON.parse(template.characters),
        dialogues: JSON.parse(template.dialogues),
        isOfficial: template.userId === null,
      };
    }),

  // 更新模板
  updateTemplate: protectedProcedure
    .input(z.object({
      templateId: z.string(),
      name: z.string().optional(),
      description: z.string().optional(),
      characters: z.array(CharacterSchema.omit({ id: true })).optional(),
      dialogues: z.array(DialogueSchema.omit({ id: true })).optional(),
      isPublic: z.boolean().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { templateId, ...updateData } = input;

      // 验证模板所有权
      const existingTemplate = await ctx.db.conversationTemplate.findFirst({
        where: {
          id: templateId,
          userId: ctx.session.user.id, // 只有创建者可以更新
        },
      });

      if (!existingTemplate) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Template not found or access denied",
        });
      }

      const updatedTemplate = await ctx.db.conversationTemplate.update({
        where: { id: templateId },
        data: {
          ...updateData,
          characters: updateData.characters ? JSON.stringify(updateData.characters) : undefined,
          dialogues: updateData.dialogues ? JSON.stringify(updateData.dialogues) : undefined,
        },
      });

      return updatedTemplate;
    }),

  // 删除模板
  deleteTemplate: protectedProcedure
    .input(z.object({ templateId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 验证模板所有权
      const template = await ctx.db.conversationTemplate.findFirst({
        where: {
          id: input.templateId,
          userId: ctx.session.user.id, // 只有创建者可以删除
        },
      });

      if (!template) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Template not found or access denied",
        });
      }

      await ctx.db.conversationTemplate.delete({
        where: { id: input.templateId },
      });

      return { success: true };
    }),

  // 复制模板（用于复制公开模板到用户账号）
  copyTemplate: protectedProcedure
    .input(z.object({
      templateId: z.string(),
      name: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 获取原模板
      const originalTemplate = await ctx.db.conversationTemplate.findFirst({
        where: {
          id: input.templateId,
          OR: [
            { userId: ctx.session.user.id },
            { isPublic: true },
          ],
        },
      });

      if (!originalTemplate) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Template not found",
        });
      }

      // 创建副本
      const copiedTemplate = await ctx.db.conversationTemplate.create({
        data: {
          userId: ctx.session.user.id,
          name: input.name,
          description: originalTemplate.description,
          characters: originalTemplate.characters,
          dialogues: originalTemplate.dialogues,
          isPublic: false, // 复制的模板默认为私有
        },
      });

      // 增加原模板的使用次数
      await ctx.db.conversationTemplate.update({
        where: { id: input.templateId },
        data: { usageCount: { increment: 1 } },
      });

      return copiedTemplate;
    }),

  // 删除对话
  delete: protectedProcedure
    .input(z.object({ conversationId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const conversation = await ctx.db.conversation.findFirst({
        where: {
          id: input.conversationId,
          userId: ctx.session.user.id,
        },
      });

      if (!conversation) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Conversation not found",
        });
      }

      await ctx.db.conversation.delete({
        where: { id: input.conversationId },
      });

      return { success: true };
    }),
});

// 辅助函数：计算预计时长
function calculateEstimatedDuration(dialogues: any[], pauseBetweenLines: number): number {
  const totalCharacters = dialogues.reduce((sum, dialogue) => sum + dialogue.text.length, 0);
  const estimatedSpeechTime = totalCharacters * 0.1; // 假设每个字符0.1秒
  const totalPauseTime = (dialogues.length - 1) * pauseBetweenLines;
  return Math.round(estimatedSpeechTime + totalPauseTime);
}

// 异步生成对话音频
async function generateConversationAudio(conversationId: string, conversation: any) {
  const { db } = await import("~/server/db");
  const { TTSManager } = await import("~/lib/tts/tts-manager");
  const { r2Storage } = await import("~/lib/r2-storage");
  const { AudioProcessor } = await import("~/lib/audio/audio-processor");

  try {
    console.log(`🎵 开始生成对话音频: ${conversationId}`);

    // 1. 为每个对话生成单独的音频
    const ttsManager = new TTSManager();
    const audioSegments: Buffer[] = [];
    let totalDuration = 0;

    for (const dialogue of conversation.dialogues) {
      const character = conversation.characters.find((c: any) => c.id === dialogue.characterId);
      if (!character) {
        console.warn(`⚠️ 未找到角色 ID: ${dialogue.characterId}`);
        continue;
      }

      console.log(`🎤 生成角色 ${character.name} 的对话: ${dialogue.text.substring(0, 50)}...`);

      try {
        const result = await ttsManager.generateSpeech({
          text: dialogue.text,
          characterId: character.voiceCharacterId,
          apiProvider: character.voiceCharacter.apiProvider,
          voiceName: character.voiceCharacter.apiVoiceName,
          speed: character.speed,
          pitch: character.pitch,
          volumeGainDb: Math.log10(character.volume) * 20, // 转换为dB
          format: 'WAV',
          quality: 'fast',
        });

        // 验证生成的音频
        if (!AudioProcessor.validateWavFile(result.audioData)) {
          throw new Error(`生成的音频格式无效 (角色: ${character.name})`);
        }

        audioSegments.push(result.audioData);

        // 计算实际音频时长
        const actualDuration = AudioProcessor.getWavDuration(result.audioData);
        totalDuration += actualDuration;

        console.log(`✅ 角色 ${character.name} 音频生成成功: ${actualDuration.toFixed(2)}s, ${result.audioData.length} 字节`);

        // 添加停顿时间
        if (dialogue.order < conversation.dialogues.length - 1) {
          const silenceDuration = conversation.pauseBetweenLines;
          console.log(`🔇 添加停顿: ${silenceDuration}s`);

          // 使用第一个音频的参数生成静音
          const firstHeader = AudioProcessor.parseWavHeader(result.audioData);
          const silenceBuffer = AudioProcessor.generateSilenceWav(silenceDuration, {
            sampleRate: firstHeader.sampleRate,
            channels: firstHeader.channels,
            bitsPerSample: firstHeader.bitsPerSample
          });

          audioSegments.push(silenceBuffer);
          totalDuration += silenceDuration;
        }

      } catch (error) {
        console.error(`❌ 生成对话片段失败 (角色: ${character.name}):`, error);
        throw new Error(`角色 ${character.name} 的对话生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    if (audioSegments.length === 0) {
      throw new Error('没有成功生成任何音频片段');
    }

    // 2. 合并音频文件
    console.log(`🔗 合并 ${audioSegments.length} 个音频片段`);
    const finalAudio = AudioProcessor.mergeWavFiles(audioSegments);

    // 验证合并后的音频
    if (!AudioProcessor.validateWavFile(finalAudio)) {
      throw new Error('合并后的音频格式无效');
    }

    const finalDuration = AudioProcessor.getWavDuration(finalAudio);
    console.log(`🎵 音频合并完成: ${finalDuration.toFixed(2)}s, ${finalAudio.length} 字节`);

    // 3. 上传到R2存储
    let audioUrl = '';
    if (r2Storage.isConfigured()) {
      const fileName = `conversation-${conversationId}-${Date.now()}.wav`;
      audioUrl = await r2Storage.uploadAudio(finalAudio, fileName, 'audio/wav');
      console.log(`☁️ 音频已上传到R2: ${audioUrl}`);
    } else {
      // 如果R2未配置，使用本地存储或base64
      audioUrl = `data:audio/wav;base64,${finalAudio.toString('base64')}`;
      console.log(`💾 使用本地存储（R2未配置）`);
    }

    // 4. 更新对话状态
    await db.conversation.update({
      where: { id: conversationId },
      data: {
        status: "COMPLETED",
        audioUrl,
        duration: Math.round(finalDuration),
      },
    });

    console.log(`✅ 对话生成完成: ${conversationId}, 时长: ${finalDuration.toFixed(2)}s`);

  } catch (error) {
    console.error(`❌ 对话生成失败 ${conversationId}:`, error);

    // 更新状态为失败，并记录错误信息
    await db.conversation.update({
      where: { id: conversationId },
      data: {
        status: "FAILED",
        errorMessage: error instanceof Error ? error.message : '未知错误',
      },
    });
  }
}


