import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";
import PayPalService from "~/lib/paypal";

export const paypalRouter = createTRPCRouter({
  
  // 创建PayPal支付
  createPayment: protectedProcedure
    .input(z.object({
      paymentId: z.string(), // 我们系统中的支付ID
    }))
    .mutation(async ({ ctx, input }) => {
      const { paymentId } = input;

      // 获取支付记录
      const payment = await ctx.db.payment.findFirst({
        where: {
          id: paymentId,
          userId: ctx.session.user.id,
          provider: 'PAYPAL',
          status: 'PENDING',
        },
        include: {
          creditPurchase: true,
        },
      });

      if (!payment) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Payment not found or not eligible for PayPal payment',
        });
      }

      const metadata = payment.metadata as any;
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';

      try {
        // 创建PayPal订单
        const description = payment.creditPurchase
          ? `Voctana 积分购买 - ${payment.creditPurchase.packageName}`
          : payment.characterPurchase
          ? `Voctana 字符包购买 - ${payment.characterPurchase.packageName}`
          : payment.description || 'Voctana Subscription';

        const paypalResult = await PayPalService.createOrder({
          amount: payment.amount,
          currency: payment.currency,
          description,
          orderId: metadata?.orderNumber || payment.id,
          returnUrl: `${baseUrl}/payment/${payment.id}/success`,
          cancelUrl: `${baseUrl}/payment/${payment.id}/cancel`,
        });

        if (!paypalResult.success) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: `PayPal order creation failed: ${paypalResult.error}`,
          });
        }

        // 更新支付记录，保存PayPal订单ID
        await ctx.db.payment.update({
          where: { id: payment.id },
          data: {
            paypalOrderId: paypalResult.orderId,
            metadata: {
              ...metadata,
              paypalOrderId: paypalResult.orderId,
              paypalApprovalUrl: paypalResult.approvalUrl,
              createdAt: new Date().toISOString(),
            },
          },
        });

        return {
          success: true,
          paypalOrderId: paypalResult.orderId,
          approvalUrl: paypalResult.approvalUrl,
        };

      } catch (error) {
        console.error('PayPal payment creation error:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : 'Failed to create PayPal payment',
        });
      }
    }),

  // 确认PayPal支付
  confirmPayment: protectedProcedure
    .input(z.object({
      paymentId: z.string(),
      paypalOrderId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { paymentId, paypalOrderId } = input;

      // 验证支付记录
      const payment = await ctx.db.payment.findFirst({
        where: {
          id: paymentId,
          userId: ctx.session.user.id,
          provider: 'PAYPAL',
          paypalOrderId: paypalOrderId,
        },
        include: {
          creditPurchase: true,
        },
      });

      if (!payment) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Payment not found',
        });
      }

      try {
        // 首先检查PayPal订单状态
        const orderDetails = await PayPalService.getOrderDetails(paypalOrderId);

        if (!orderDetails.success) {
          throw new TRPCError({
            code: 'PAYMENT_REQUIRED',
            message: `Failed to get PayPal order details: ${orderDetails.error}`,
          });
        }

        let captureResult;

        // 检查订单是否已经被捕获
        if (orderDetails.status === 'COMPLETED') {
          // 订单已经完成，使用现有的捕获信息
          console.log(`PayPal order ${paypalOrderId} already completed with capture ID: ${orderDetails.captureId}`);
          captureResult = {
            success: true,
            captureId: orderDetails.captureId || paypalOrderId,
            status: 'COMPLETED'
          };
        } else if (orderDetails.status === 'APPROVED') {
          // 订单已批准但未捕获，进行捕获
          captureResult = await PayPalService.captureOrder(paypalOrderId);

          if (!captureResult.success) {
            // 检查是否是重复捕获错误
            if (captureResult.error && captureResult.error.includes('ORDER_ALREADY_CAPTURED')) {
              console.log(`PayPal order ${paypalOrderId} was already captured by another process`);
              captureResult = {
                success: true,
                captureId: paypalOrderId,
                status: 'COMPLETED'
              };
            } else {
              throw new TRPCError({
                code: 'PAYMENT_REQUIRED',
                message: `PayPal payment capture failed: ${captureResult.error}`,
              });
            }
          }
        } else {
          throw new TRPCError({
            code: 'PAYMENT_REQUIRED',
            message: `PayPal order status is ${orderDetails.status}, cannot capture`,
          });
        }

        // 更新支付状态
        const updatedPayment = await ctx.db.payment.update({
          where: { id: payment.id },
          data: {
            status: 'COMPLETED',
            completedAt: new Date(),
            metadata: {
              ...(payment.metadata as object || {}),
              paypalCaptureId: captureResult.captureId,
              paypalStatus: captureResult.status,
              capturedAt: new Date().toISOString(),
              paypalResponse: captureResult.response,
            },
          },
        });

        // 处理积分购买
        if (payment.creditPurchase) {
          // 处理积分购买
          await ctx.db.creditPurchase.update({
            where: { id: payment.creditPurchase.id },
            data: {
              status: 'COMPLETED',
              completedAt: new Date(),
            },
          });

          // 给用户增加积分
          await ctx.db.user.update({
            where: { id: payment.userId },
            data: {
              credits: { increment: payment.creditPurchase.credits },
            },
          });

          // 记录积分赠送历史
          await ctx.db.creditGift.create({
            data: {
              fromUserId: null, // 系统赠送
              toUserId: payment.userId,
              amount: payment.creditPurchase.credits,
              reason: `购买积分包：${payment.creditPurchase.packageName}`,
              type: 'PROMOTION',
            },
          });
        } else if (payment.characterPurchase) {
          // 完成字符包购买
          await ctx.db.characterPurchase.update({
            where: { id: payment.characterPurchase.id },
            data: {
              status: 'COMPLETED',
              completedAt: new Date(),
            },
          });

          // 增加用户字符配额
          await ctx.db.user.update({
            where: { id: ctx.session.user.id },
            data: {
              usageQuota: {
                increment: payment.characterPurchase.characters,
              },
            },
          });
        }

        return {
          success: true,
          payment: updatedPayment,
          captureId: captureResult.captureId,
        };

      } catch (error) {
        console.error('PayPal payment confirmation error:', error);
        
        // 更新支付状态为失败
        await ctx.db.payment.update({
          where: { id: payment.id },
          data: {
            status: 'FAILED',
            metadata: {
              ...(payment.metadata as object || {}),
              failedAt: new Date().toISOString(),
              failureReason: error instanceof Error ? error.message : 'Unknown error',
            },
          },
        });

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : 'Failed to confirm PayPal payment',
        });
      }
    }),

  // 取消PayPal支付
  cancelPayment: protectedProcedure
    .input(z.object({
      paymentId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { paymentId } = input;

      const payment = await ctx.db.payment.findFirst({
        where: {
          id: paymentId,
          userId: ctx.session.user.id,
          provider: 'PAYPAL',
        },
      });

      if (!payment) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Payment not found',
        });
      }

      // 更新支付状态为取消
      const updatedPayment = await ctx.db.payment.update({
        where: { id: payment.id },
        data: {
          status: 'CANCELLED',
          metadata: {
            ...(payment.metadata as object || {}),
            cancelledAt: new Date().toISOString(),
            cancelReason: 'user_cancelled',
          },
        },
      });

      return {
        success: true,
        payment: updatedPayment,
      };
    }),

  // 获取PayPal支付状态
  getPaymentStatus: protectedProcedure
    .input(z.object({
      paymentId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const { paymentId } = input;

      const payment = await ctx.db.payment.findFirst({
        where: {
          id: paymentId,
          userId: ctx.session.user.id,
          provider: 'PAYPAL',
        },
      });

      if (!payment || !payment.paypalOrderId) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'PayPal payment not found',
        });
      }

      try {
        // 从PayPal获取最新状态
        const orderDetails = await PayPalService.getOrderDetails(payment.paypalOrderId);

        if (orderDetails.success) {
          return {
            success: true,
            paymentStatus: payment.status,
            paypalStatus: orderDetails.order.status,
            orderDetails: orderDetails.order,
          };
        } else {
          return {
            success: false,
            error: orderDetails.error,
          };
        }
      } catch (error) {
        console.error('PayPal status check error:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    }),
});
