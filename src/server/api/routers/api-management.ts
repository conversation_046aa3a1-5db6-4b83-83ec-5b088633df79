import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { apiConfigService } from "~/lib/api-config/api-config-service";

// 超级管理员权限检查中间件
const superAdminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });

  if (!user || user.role !== 'SUPER_ADMIN') {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Super admin access required',
    });
  }

  return next({
    ctx: {
      ...ctx,
      session: { ...ctx.session, user: { ...ctx.session.user, role: user.role } },
    },
  });
});

export const apiManagementRouter = createTRPCRouter({
  // 获取所有API配置
  getAllConfigs: superAdminProcedure
    .query(async () => {
      return await apiConfigService.getAllConfigs();
    }),

  // 获取特定API配置
  getConfig: superAdminProcedure
    .input(z.object({
      provider: z.enum(['GEMINI', 'OPENAI']),
    }))
    .query(async ({ input }) => {
      return await apiConfigService.getConfig(input.provider);
    }),

  // 更新API配置
  updateConfig: superAdminProcedure
    .input(z.object({
      provider: z.enum(['GEMINI', 'OPENAI']),
      displayName: z.string().optional(),
      description: z.string().optional(),
      status: z.enum(['ACTIVE', 'INACTIVE', 'MAINTENANCE', 'ERROR']).optional(),
      priority: z.number().optional(),
      isEnabled: z.boolean().optional(),
      config: z.record(z.any()).optional(),
      dailyLimit: z.number().optional(),
      monthlyLimit: z.number().optional(),
      costLimit: z.number().optional(),
    }))
    .mutation(async ({ input }) => {
      const { provider, ...updates } = input;
      await apiConfigService.updateConfig(provider, updates);
      return { success: true };
    }),

  // 测试API连接
  testConnection: superAdminProcedure
    .input(z.object({
      provider: z.enum(['GEMINI', 'OPENAI']),
      config: z.record(z.any()).optional(),
    }))
    .mutation(async ({ input }) => {
      const { provider } = input;

      try {
        // 根据不同的API提供商进行连接测试
        let testResult = false;
        let errorMessage = '';

        switch (provider) {
          case 'GEMINI':
            testResult = await testGeminiConnection();
            if (!testResult) errorMessage = 'GEMINI_API_KEY not configured or invalid';
            break;
          case 'OPENAI':
            testResult = await testOpenAIConnection();
            if (!testResult) errorMessage = 'OPENAI_API_KEY not configured or invalid';
            break;
          default:
            throw new Error('Unsupported provider');
        }

        return {
          success: testResult,
          message: testResult ? 'Connection successful' : 'Connection failed',
          errorMessage: testResult ? undefined : errorMessage,
        };
      } catch (error) {
        return {
          success: false,
          message: 'Connection test failed',
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    }),

  // 获取使用统计
  getUsageStats: superAdminProcedure
    .input(z.object({
      provider: z.enum(['GEMINI', 'OPENAI']).optional(),
      startDate: z.date().optional(),
      endDate: z.date().optional(),
    }))
    .query(async ({ input }) => {
      return await apiConfigService.getUsageStats(
        input.provider,
        input.startDate,
        input.endDate
      );
    }),

  // 获取详细使用日志
  getUsageLogs: superAdminProcedure
    .input(z.object({
      provider: z.enum(['GEMINI', 'OPENAI']).optional(),
      limit: z.number().min(1).max(100).default(50),
      offset: z.number().min(0).default(0),
      startDate: z.date().optional(),
      endDate: z.date().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const where: any = {};
      
      if (input.provider) {
        where.provider = input.provider;
      }
      
      if (input.startDate || input.endDate) {
        where.createdAt = {};
        if (input.startDate) where.createdAt.gte = input.startDate;
        if (input.endDate) where.createdAt.lte = input.endDate;
      }

      const [logs, total] = await Promise.all([
        ctx.db.apiUsageLog.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                email: true,
                name: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          take: input.limit,
          skip: input.offset,
        }),
        ctx.db.apiUsageLog.count({ where }),
      ]);

      return {
        logs,
        total,
        hasMore: input.offset + input.limit < total,
      };
    }),

  // 初始化默认配置
  initializeDefaults: superAdminProcedure
    .mutation(async () => {
      await apiConfigService.initializeDefaultConfigs();
      return { success: true };
    }),

  // 获取API状态概览
  getOverview: superAdminProcedure
    .query(async ({ ctx }) => {
      const configs = await apiConfigService.getAllConfigs();
      
      // 获取今日统计
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const todayStats = await ctx.db.apiUsageLog.groupBy({
        by: ['provider'],
        where: {
          createdAt: { gte: today },
        },
        _count: { id: true },
        _sum: { cost: true },
      });

      // 获取本月统计
      const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      
      const monthStats = await ctx.db.apiUsageLog.groupBy({
        by: ['provider'],
        where: {
          createdAt: { gte: thisMonth },
        },
        _count: { id: true },
        _sum: { cost: true },
      });

      return {
        configs: configs.map(config => ({
          ...config,
          todayRequests: todayStats.find(s => s.provider === config.provider)?._count.id || 0,
          todayCost: todayStats.find(s => s.provider === config.provider)?._sum.cost || 0,
          monthRequests: monthStats.find(s => s.provider === config.provider)?._count.id || 0,
          monthCost: monthStats.find(s => s.provider === config.provider)?._sum.cost || 0,
        })),
        totalTodayRequests: todayStats.reduce((sum, stat) => sum + stat._count.id, 0),
        totalTodayCost: todayStats.reduce((sum, stat) => sum + (stat._sum.cost || 0), 0),
        totalMonthRequests: monthStats.reduce((sum, stat) => sum + stat._count.id, 0),
        totalMonthCost: monthStats.reduce((sum, stat) => sum + (stat._sum.cost || 0), 0),
      };
    }),
});

// 辅助函数：测试Gemini连接
async function testGeminiConnection(): Promise<boolean> {
  try {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      console.log('❌ GEMINI_API_KEY not found');
      return false;
    }

    // 简单的API密钥格式验证
    if (!apiKey.startsWith('AIza')) {
      console.log('❌ GEMINI_API_KEY format invalid');
      return false;
    }

    console.log('✅ GEMINI_API_KEY found and format valid');
    return true;
  } catch (error) {
    console.log('❌ Gemini connection test failed:', error);
    return false;
  }
}



// 辅助函数：测试OpenAI连接
async function testOpenAIConnection(): Promise<boolean> {
  try {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      console.log('❌ OPENAI_API_KEY not found');
      return false;
    }

    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
      },
    });

    console.log('🔗 OpenAI API response status:', response.status);
    return response.ok;
  } catch (error) {
    console.log('❌ OpenAI connection test failed:', error);
    return false;
  }
}
