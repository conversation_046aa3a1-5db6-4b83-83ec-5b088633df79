import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 动态计算积分包价格
async function calculatePackagePrice(db: any, packageId: string, credits: number) {
  // 获取当前API定价配置
  const config = await db.apiPricingConfig.findFirst({
    where: { isActive: true },
    orderBy: { createdAt: 'desc' },
  });

  if (!config) {
    // 使用默认定价
    const defaultPrices = {
      basic: 9.99,
      standard: 24.99,
      premium: 79.99,
    };
    return defaultPrices[packageId as keyof typeof defaultPrices] || 9.99;
  }

  // 1积分 = 1000 token
  const tokensPerCredit = 1000;
  const totalTokens = credits * tokensPerCredit;

  // 计算基础成本（使用标准价格作为基准，假设输入输出各占50%）
  const avgTokenCost = (config.standardInputPrice + config.standardOutputPrice) / 2;
  // 使用固定的markup比例，因为数据库中没有markup字段
  const markup = 300; // 300% markup
  const baseCostPer1M = avgTokenCost * (1 + markup / 100);
  const baseCost = (totalTokens / 1000000) * baseCostPer1M;

  // 获取积分包策略
  let packageStrategy: any;
  switch (packageId) {
    case 'basic':
      packageStrategy = (typeof config.basicPackageStrategy === 'object' && config.basicPackageStrategy !== null)
        ? config.basicPackageStrategy as any
        : { discountType: 'percentage', discountValue: 0, promotionDiscount: 0 };
      break;
    case 'standard':
      packageStrategy = (typeof config.standardPackageStrategy === 'object' && config.standardPackageStrategy !== null)
        ? config.standardPackageStrategy as any
        : { discountType: 'percentage', discountValue: 10, promotionDiscount: 0 };
      break;
    case 'premium':
      packageStrategy = (typeof config.premiumPackageStrategy === 'object' && config.premiumPackageStrategy !== null)
        ? config.premiumPackageStrategy as any
        : { discountType: 'percentage', discountValue: 20, promotionDiscount: 0 };
      break;
    default:
      packageStrategy = { discountType: 'percentage', discountValue: 0, promotionDiscount: 0 };
  }

  // 第一层：内部定价优惠（得到销售价格）
  let salePrice = baseCost;
  if (packageStrategy.discountType === 'amount') {
    salePrice = Math.max(0, baseCost - packageStrategy.discountValue);
  } else {
    salePrice = baseCost * (1 - packageStrategy.discountValue / 100);
  }

  // 第二层：促销优惠（用户可见）
  const finalPrice = salePrice * (1 - packageStrategy.promotionDiscount / 100);

  return Math.round(finalPrice * 100) / 100; // 保留两位小数
}

// 积分包基础配置
const CREDIT_PACKAGES_BASE = {
  basic: {
    id: "basic",
    name: "基础包",
    credits: 1000,
    description: "适合轻度使用",
  },
  standard: {
    id: "standard",
    name: "标准包",
    credits: 3000,
    description: "最受欢迎的选择",
    bonus: 300, // 额外10%积分
  },
  premium: {
    id: "premium",
    name: "高级包",
    credits: 10000,
    description: "适合重度使用",
    bonus: 2000, // 额外20%积分
  },
} as const;

export const creditPurchaseRouter = createTRPCRouter({

  // 获取积分包列表（简化定价）
  getPackages: protectedProcedure
    .query(async ({ ctx }) => {
      // 获取积分包定价
      const packagePricing = await ctx.db.creditPackagePricing.findMany({
        where: { isActive: true },
        orderBy: { credits: 'asc' },
      });

      // 如果没有定价数据，返回默认包
      if (packagePricing.length === 0) {
        return Object.values(CREDIT_PACKAGES_BASE).map(pkg => ({
          ...pkg,
          price: pkg.id === 'basic' ? 9.99 : pkg.id === 'standard' ? 24.99 : 79.99,
          salePrice: pkg.id === 'basic' ? 9.99 : pkg.id === 'standard' ? 24.99 : 79.99,
          hasPromotion: false,
        }));
      }

      // 转换为前端需要的格式
      const packages = packagePricing.map(pricing => {
        const basePackage = Object.values(CREDIT_PACKAGES_BASE).find(
          pkg => pkg.id === pricing.packageType
        );

        if (!basePackage) return null;

        const hasPromotion = pricing.promoPrice && pricing.promoPrice > 0 && pricing.promoPrice < pricing.salePrice;
        const finalPrice = hasPromotion ? pricing.promoPrice! : pricing.salePrice;
        const discountPercent = hasPromotion
          ? Math.round(((pricing.salePrice - pricing.promoPrice!) / pricing.salePrice) * 100)
          : 0;

        return {
          ...basePackage,
          credits: pricing.credits,
          price: finalPrice,
          salePrice: pricing.salePrice,
          promoPrice: pricing.promoPrice,
          hasPromotion,
          discountPercent,
        };
      }).filter(Boolean);

      return packages;
    }),

  // 创建积分购买订单
  createPurchase: protectedProcedure
    .input(z.object({
      packageId: z.enum(['basic', 'standard', 'premium']),
      paymentProvider: z.enum(['PAYPAL', 'KHQR', 'WECHAT', 'STRIPE']).default('PAYPAL'),
      currency: z.enum(['USD', 'KHR']).default('USD'),
    }))
    .mutation(async ({ ctx, input }) => {
      const { packageId, paymentProvider, currency } = input;
      const packageBase = CREDIT_PACKAGES_BASE[packageId];

      if (!packageBase) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '无效的积分包',
        });
      }

      // 动态计算价格
      const price = await calculatePackagePrice(ctx.db, packageId, packageBase.credits);

      const package_ = {
        ...packageBase,
        price,
      };

      // 生成订单号
      const orderNumber = `CP${Date.now()}${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

      // 计算总积分（包含奖励）
      const totalCredits = package_.credits + (package_.bonus || 0);

      // 使用事务创建购买记录和支付记录
      const result = await ctx.db.$transaction(async (tx) => {
        // 创建积分购买记录
        const purchase = await tx.creditPurchase.create({
          data: {
            userId: ctx.session.user.id,
            packageId: package_.id,
            packageName: package_.name,
            credits: totalCredits,
            amount: package_.price,
            currency,
            orderNumber,
            status: 'PENDING',
          },
        });

        // 创建支付记录
        const payment = await tx.payment.create({
          data: {
            userId: ctx.session.user.id,
            creditPurchaseId: purchase.id,
            amount: package_.price,
            currency,
            provider: paymentProvider,
            description: `积分购买 - ${package_.name}`,
            status: 'PENDING',
            metadata: {
              orderNumber,
              packageId,
              packageName: package_.name,
              credits: totalCredits,
            },
          },
        });

        return { purchase, payment };
      });

      return {
        purchase: result.purchase,
        payment: result.payment,
        package: package_,
        totalCredits,
      };
    }),

  // 获取购买历史
  getPurchaseHistory: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ ctx, input }) => {
      const { limit, offset } = input;

      const [purchases, total] = await Promise.all([
        ctx.db.creditPurchase.findMany({
          where: { userId: ctx.session.user.id },
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip: offset,
          include: {
            payments: {
              select: {
                id: true,
                status: true,
                provider: true,
                createdAt: true,
                completedAt: true,
              },
            },
          },
        }),
        ctx.db.creditPurchase.count({
          where: { userId: ctx.session.user.id },
        }),
      ]);

      return {
        purchases,
        total,
        hasMore: offset + limit < total,
      };
    }),

  // 完成购买（支付成功后调用）
  completePurchase: protectedProcedure
    .input(z.object({
      purchaseId: z.string(),
      paymentId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { purchaseId, paymentId } = input;

      // 检查购买记录
      const purchase = await ctx.db.creditPurchase.findUnique({
        where: { id: purchaseId },
        include: {
          payments: true,
        },
      });

      if (!purchase) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '购买记录不存在',
        });
      }

      if (purchase.userId !== ctx.session.user.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '权限不足',
        });
      }

      if (purchase.status === 'COMPLETED') {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '订单已完成',
        });
      }

      // 检查支付记录
      const payment = await ctx.db.payment.findUnique({
        where: { id: paymentId },
      });

      if (!payment || payment.status !== 'COMPLETED') {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '支付未完成',
        });
      }

      // 使用事务完成购买
      const result = await ctx.db.$transaction(async (tx) => {
        // 更新购买状态
        const updatedPurchase = await tx.creditPurchase.update({
          where: { id: purchaseId },
          data: {
            status: 'COMPLETED',
            completedAt: new Date(),
          },
        });

        // 给用户增加积分
        await tx.user.update({
          where: { id: purchase.userId },
          data: {
            credits: { increment: purchase.credits },
          },
        });

        // 记录积分赠送历史（购买记录）
        await tx.creditGift.create({
          data: {
            fromUserId: null, // 系统赠送
            toUserId: purchase.userId,
            amount: purchase.credits,
            reason: `购买积分包：${purchase.packageName}`,
            type: 'PROMOTION',
          },
        });

        return updatedPurchase;
      });

      return {
        success: true,
        purchase: result,
      };
    }),

  // 取消购买
  cancelPurchase: protectedProcedure
    .input(z.object({
      purchaseId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { purchaseId } = input;

      const purchase = await ctx.db.creditPurchase.findUnique({
        where: { id: purchaseId },
      });

      if (!purchase) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '购买记录不存在',
        });
      }

      if (purchase.userId !== ctx.session.user.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '权限不足',
        });
      }

      if (purchase.status !== 'PENDING') {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '只能取消待支付的订单',
        });
      }

      // 更新购买状态
      const updatedPurchase = await ctx.db.creditPurchase.update({
        where: { id: purchaseId },
        data: {
          status: 'CANCELLED',
        },
      });

      return {
        success: true,
        purchase: updatedPurchase,
      };
    }),

  // 获取单个购买记录
  getPurchase: protectedProcedure
    .input(z.object({
      purchaseId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const { purchaseId } = input;

      const purchase = await ctx.db.creditPurchase.findUnique({
        where: { id: purchaseId },
        include: {
          payments: {
            orderBy: { createdAt: 'desc' },
          },
        },
      });

      if (!purchase) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '购买记录不存在',
        });
      }

      if (purchase.userId !== ctx.session.user.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '权限不足',
        });
      }

      return purchase;
    }),

});
