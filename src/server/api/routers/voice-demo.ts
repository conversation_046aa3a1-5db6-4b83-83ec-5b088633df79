import { z } from "zod";
import { createTRPCRouter, publicProcedure, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

export const voiceDemoRouter = createTRPCRouter({
  // 根据语言角色ID获取语音试听
  getByCharacter: publicProcedure
    .input(z.object({
      characterId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      // 首先获取语言角色信息
      const character = await ctx.db.languageCharacter.findUnique({
        where: { id: input.characterId },
        include: {
          template: true,
          language: true,
        },
      });

      if (!character || !character.templateId) {
        return null;
      }

      // 查找对应的语音试听（标准和专业质量）
      const voiceDemos = await ctx.db.voiceDemo.findMany({
        where: {
          templateId: character.templateId,
          languageId: character.languageId,
        },
        include: {
          template: {
            select: {
              id: true,
              originalName: true,
              apiVoiceName: true,
              apiProvider: true,
              gender: true,
            },
          },
          language: {
            select: {
              id: true,
              code: true,
              name: true,
              flag: true,
            },
          },
        },
        orderBy: {
          quality: 'asc', // standard first, then professional
        },
      });

      // 返回按质量分组的试听
      const result = {
        standard: voiceDemos.find(demo => demo.quality === 'standard') || null,
        professional: voiceDemos.find(demo => demo.quality === 'professional') || null,
      };

      return result;
    }),

  // 根据模板ID和语言ID获取语音试听
  getByTemplateAndLanguage: publicProcedure
    .input(z.object({
      templateId: z.string(),
      languageId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const voiceDemo = await ctx.db.voiceDemo.findFirst({
        where: {
          templateId: input.templateId,
          languageId: input.languageId,
        },
        include: {
          template: {
            select: {
              id: true,
              originalName: true,
              apiVoiceName: true,
              apiProvider: true,
              gender: true,
            },
          },
          language: {
            select: {
              id: true,
              code: true,
              name: true,
              flag: true,
            },
          },
        },
      });

      return voiceDemo;
    }),

  // 获取语言的所有语音试听
  getByLanguage: publicProcedure
    .input(z.object({
      languageCode: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const voiceDemos = await ctx.db.voiceDemo.findMany({
        where: {
          languageCode: input.languageCode,
        },
        include: {
          template: {
            select: {
              id: true,
              originalName: true,
              apiVoiceName: true,
              apiProvider: true,
              gender: true,
              sortOrder: true,
            },
          },
          language: {
            select: {
              id: true,
              code: true,
              name: true,
              flag: true,
            },
          },
        },
        orderBy: {
          template: {
            sortOrder: 'asc',
          },
        },
      });

      return voiceDemos;
    }),

  // 记录播放统计
  recordPlay: protectedProcedure
    .input(z.object({
      voiceDemoId: z.string(),
      playDuration: z.number().optional(),
      isCompleted: z.boolean().default(false),
      userAgent: z.string().optional(),
      referrer: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // 创建播放记录
        const playRecord = await ctx.db.voiceDemoPlayStat.create({
          data: {
            voiceDemoId: input.voiceDemoId,
            userId: ctx.session.user.id,
            playDuration: input.playDuration,
            isCompleted: input.isCompleted,
            userAgent: input.userAgent,
            referrer: input.referrer,
          },
        });

        // 更新聚合统计
        await ctx.db.voiceDemoAggregateStats.upsert({
          where: {
            voiceDemoId: input.voiceDemoId,
          },
          update: {
            totalPlays: { increment: 1 },
            completedPlays: input.isCompleted ? { increment: 1 } : undefined,
            lastPlayedAt: new Date(),
          },
          create: {
            voiceDemoId: input.voiceDemoId,
            totalPlays: 1,
            uniquePlays: 1,
            completedPlays: input.isCompleted ? 1 : 0,
            averageDuration: input.playDuration,
            lastPlayedAt: new Date(),
          },
        });

        return {
          success: true,
          playRecord,
        };
      } catch (error) {
        console.error('Failed to record voice demo play:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '记录播放统计失败',
        });
      }
    }),

  // 获取试听统计信息
  getStats: protectedProcedure
    .input(z.object({
      voiceDemoId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const stats = await ctx.db.voiceDemoAggregateStats.findUnique({
        where: {
          voiceDemoId: input.voiceDemoId,
        },
      });

      return stats;
    }),
});
