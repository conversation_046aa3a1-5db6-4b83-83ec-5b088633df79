import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 获取角色在特定语言中的本地化名称
function getLocalizedCharacterName(character: any, languageCode: string): string {
  if (!character.multilingualNames) {
    return character.characterName;
  }

  try {
    const names = JSON.parse(character.multilingualNames);
    return names[languageCode] || character.characterName;
  } catch (error) {
    console.error('Error parsing multilingual names:', error);
    return character.characterName;
  }
}

// 获取默认试听文本的辅助函数
function getDefaultDemoText(languageCode: string): string {
  const demoTexts: Record<string, string> = {
    'en-US': 'Hello, this is a voice demonstration. How are you today?',
    'zh-CN': '你好，这是语音演示。你今天好吗？',
    'ja-JP': 'こんにちは、これは音声デモンストレーションです。今日はいかがですか？',
    'ko-KR': '안녕하세요, 이것은 음성 데모입니다. 오늘 어떠세요?',
    'fr-FR': 'Bonjour, ceci est une démonstration vocale. Comment allez-vous aujourd\'hui?',
    'de-DE': 'Hallo, das ist eine Sprachdemonstration. Wie geht es Ihnen heute?',
    'es-US': 'Hola, esta es una demostración de voz. ¿Cómo estás hoy?',
    'it-IT': 'Ciao, questa è una dimostrazione vocale. Come stai oggi?',
    'pt-BR': 'Olá, esta é uma demonstração de voz. Como você está hoje?',
    'ru-RU': 'Привет, это голосовая демонстрация. Как дела сегодня?',
    'ar-EG': 'مرحبا، هذا عرض صوتي. كيف حالك اليوم؟',
    'hi-IN': 'नमस्ते, यह एक आवाज़ प्रदर्शन है। आज आप कैसे हैं?',
    'th-TH': 'สวัสดี นี่คือการสาธิตเสียง วันนี้คุณเป็นอย่างไรบ้าง?',
    'vi-VN': 'Xin chào, đây là bản demo giọng nói. Hôm nay bạn thế nào?',
    'id-ID': 'Halo, ini adalah demonstrasi suara. Apa kabar hari ini?',
    'km-KH': 'សួស្តី នេះជាការបង្ហាញសំឡេង។ តើថ្ងៃនេះអ្នកមានសុខភាពយ៉ាងណា?',
    'nl-NL': 'Hallo, dit is een spraakdemonstratie. Hoe gaat het vandaag?',
    'pl-PL': 'Cześć, to jest demonstracja głosu. Jak się masz dzisiaj?',
    'tr-TR': 'Merhaba, bu bir ses gösterimi. Bugün nasılsın?',
    'uk-UA': 'Привіт, це голосова демонстрація. Як справи сьогодні?',
    'ro-RO': 'Salut, aceasta este o demonstrație vocală. Cum te simți astăzi?',
    'bn-BD': 'হ্যালো, এটি একটি ভয়েস ডেমো। আজ আপনি কেমন আছেন?',
    'en-IN': 'Hello, this is a voice demonstration. How are you today?',
    'mr-IN': 'नमस्कार, हे आवाज प्रदर्शन आहे। आज तुम्ही कसे आहात?',
    'ta-IN': 'வணக்கம், இது ஒரு குரல் ஆர்ப்பாட்டம். இன்று நீங்கள் எப்படி இருக்கிறீர்கள்?',
    'te-IN': 'హలో, ఇది వాయిస్ డెమోన్స్ట్రేషన్. ఈరోజు మీరు ఎలా ఉన్నారు?',
  };

  return demoTexts[languageCode] || 'Hello, this is a voice demonstration. How are you today?';
}

// 超级管理员权限检查中间件
const superAdminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });

  if (!user || user.role !== 'SUPER_ADMIN') {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Super admin access required',
    });
  }

  return next({
    ctx: {
      ...ctx,
      user: {
        ...ctx.session.user,
        role: user.role,
      },
    },
  });
});

// 语言数据验证模式
const LanguageSchema = z.object({
  id: z.string().optional(),
  code: z.string().min(2).max(10), // 如 'en-US', 'zh-CN'
  name: z.string().min(1).max(100), // 英文名称
  nativeName: z.string().min(1).max(100), // 本地名称
  region: z.string().min(1).max(100), // 地区
  flag: z.string().min(1).max(10), // 旗帜表情符号
  isActive: z.boolean(),
  sortOrder: z.number().int().min(0),
});

export const languageAdminRouter = createTRPCRouter({
  // 获取所有语言（管理员用）
  getAllLanguages: superAdminProcedure
    .query(async ({ ctx }) => {
      const languages = await ctx.db.language.findMany({
        orderBy: [
          { sortOrder: 'asc' },
          { name: 'asc' },
        ],
        include: {
          _count: {
            select: {
              voiceDemos: true,
            },
          },
        },
      });

      return languages;
    }),

  // 获取语言统计
  getLanguageStats: superAdminProcedure
    .query(async ({ ctx }) => {
      const totalLanguages = await ctx.db.language.count();
      const activeLanguages = await ctx.db.language.count({
        where: { isActive: true },
      });

      // 获取有试听音频的语言数量
      const languagesWithDemos = await ctx.db.language.count({
        where: {
          voiceDemos: {
            some: {},
          },
        },
      });

      // 获取总试听音频数量
      const totalDemos = await ctx.db.voiceDemo.count();

      return {
        totalLanguages,
        activeLanguages,
        languagesWithDemos,
        totalDemos,
      };
    }),

  // 创建语言
  createLanguage: superAdminProcedure
    .input(LanguageSchema.omit({ id: true }))
    .mutation(async ({ ctx, input }) => {
      // 检查语言代码是否已存在
      const existingLanguage = await ctx.db.language.findUnique({
        where: { code: input.code },
      });

      if (existingLanguage) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: `语言代码 ${input.code} 已存在`,
        });
      }

      const language = await ctx.db.language.create({
        data: input,
      });

      return {
        success: true,
        language,
        message: `语言 ${input.name} 创建成功`,
      };
    }),

  // 更新语言
  updateLanguage: superAdminProcedure
    .input(LanguageSchema.required({ id: true }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      // 检查语言是否存在
      const existingLanguage = await ctx.db.language.findUnique({
        where: { id },
      });

      if (!existingLanguage) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '语言不存在',
        });
      }

      // 如果更改了语言代码，检查新代码是否已被使用
      if (data.code !== existingLanguage.code) {
        const codeExists = await ctx.db.language.findUnique({
          where: { code: data.code },
        });

        if (codeExists) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: `语言代码 ${data.code} 已被使用`,
          });
        }
      }

      const updatedLanguage = await ctx.db.language.update({
        where: { id },
        data,
      });

      return {
        success: true,
        language: updatedLanguage,
        message: `语言 ${data.name} 更新成功`,
      };
    }),

  // 删除语言
  deleteLanguage: superAdminProcedure
    .input(z.object({
      id: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 检查是否有关联的试听音频
      const demosCount = await ctx.db.voiceDemo.count({
        where: { languageId: input.id },
      });

      if (demosCount > 0) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: `无法删除语言，还有 ${demosCount} 个关联的试听音频`,
        });
      }

      const deletedLanguage = await ctx.db.language.delete({
        where: { id: input.id },
      });

      return {
        success: true,
        message: `语言 ${deletedLanguage.name} 删除成功`,
      };
    }),

  // 批量更新语言状态
  batchUpdateLanguageStatus: superAdminProcedure
    .input(z.object({
      languageIds: z.array(z.string()),
      isActive: z.boolean(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { languageIds, isActive } = input;

      const result = await ctx.db.language.updateMany({
        where: {
          id: { in: languageIds },
        },
        data: { isActive },
      });

      return {
        success: true,
        updatedCount: result.count,
        message: `已${isActive ? '启用' : '禁用'} ${result.count} 种语言`,
      };
    }),

  // 重新排序语言
  reorderLanguages: superAdminProcedure
    .input(z.object({
      languageOrders: z.array(z.object({
        id: z.string(),
        sortOrder: z.number(),
      })),
    }))
    .mutation(async ({ ctx, input }) => {
      const { languageOrders } = input;

      // 使用事务批量更新排序
      await ctx.db.$transaction(
        languageOrders.map(({ id, sortOrder }) =>
          ctx.db.language.update({
            where: { id },
            data: { sortOrder },
          })
        )
      );

      return {
        success: true,
        message: '语言排序更新成功',
      };
    }),

  // 根据ID获取单个语言
  getLanguageById: superAdminProcedure
    .input(z.object({
      id: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const language = await ctx.db.language.findUnique({
        where: { id: input.id },
        include: {
          _count: {
            select: {
              voiceDemos: true,
            },
          },
        },
      });

      if (!language) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '语言不存在',
        });
      }

      return language;
    }),

  // 获取语言下的所有角色
  getLanguageCharacters: superAdminProcedure
    .input(z.object({
      languageId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const voiceDemos = await ctx.db.voiceDemo.findMany({
        where: {
          languageId: input.languageId,
        },
        include: {
          voiceCharacter: {
            include: {
              _count: {
                select: {
                  voiceDemos: true,
                },
              },
            },
          },
          language: true,
        },
        orderBy: {
          voiceCharacter: {
            sortOrder: 'asc',
          },
        },
      });

      // 转换为语言角色格式
      return voiceDemos.map(demo => ({
        id: demo.id,
        characterId: demo.voiceCharacterId,
        languageId: demo.languageId,
        localizedName: getLocalizedCharacterName(demo.voiceCharacter, demo.language.code),
        character: demo.voiceCharacter,
        language: demo.language,
        voiceDemo: demo,
      }));
    }),

  // 添加角色到语言
  addCharactersToLanguage: superAdminProcedure
    .input(z.object({
      languageId: z.string(),
      characterIds: z.array(z.string()),
    }))
    .mutation(async ({ ctx, input }) => {
      const language = await ctx.db.language.findUnique({
        where: { id: input.languageId },
      });

      if (!language) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '语言不存在',
        });
      }

      const characters = await ctx.db.voiceCharacter.findMany({
        where: {
          id: { in: input.characterIds },
        },
      });

      if (characters.length !== input.characterIds.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '部分角色不存在',
        });
      }

      // 创建语音试听记录
      const results = [];
      for (const character of characters) {
        try {
          const demoText = getDefaultDemoText(language.code);

          const demo = await ctx.db.voiceDemo.upsert({
            where: {
              voiceCharacterId_languageId: {
                voiceCharacterId: character.id,
                languageId: language.id,
              },
            },
            update: {
              demoText,
            },
            create: {
              voiceCharacterId: character.id,
              languageId: language.id,
              languageCode: language.code,
              demoText,
              audioUrl: '',
              audioKey: `voice-demos/${character.characterName.toLowerCase()}/${language.code}.wav`,
              quality: 'standard',
            },
          });

          results.push({
            character: character.characterName,
            success: true,
            demo,
          });
        } catch (error) {
          results.push({
            character: character.characterName,
            success: false,
            error: error instanceof Error ? error.message : '未知错误',
          });
        }
      }

      return {
        success: true,
        results,
        message: `成功为语言 ${language.name} 添加了 ${results.filter(r => r.success).length} 个角色`,
      };
    }),

  // 更新角色在语言中的信息
  updateCharacterInLanguage: superAdminProcedure
    .input(z.object({
      languageId: z.string(),
      characterId: z.string(),
      localizedName: z.string().optional(),
      localizedAvatar: z.string().optional(),
      demoText: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const demo = await ctx.db.voiceDemo.findUnique({
        where: {
          voiceCharacterId_languageId: {
            voiceCharacterId: input.characterId,
            languageId: input.languageId,
          },
        },
        include: {
          voiceCharacter: true,
          language: true,
        },
      });

      if (!demo) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '角色在该语言下的配置不存在',
        });
      }

      // 更新试听文本
      if (input.demoText) {
        await ctx.db.voiceDemo.update({
          where: { id: demo.id },
          data: {
            demoText: input.demoText,
          },
        });
      }

      // 更新角色的多语言名称
      if (input.localizedName) {
        const character = demo.voiceCharacter;
        let multilingualNames: Record<string, string> = {};

        if (character.multilingualNames) {
          try {
            multilingualNames = JSON.parse(character.multilingualNames);
          } catch (error) {
            console.error('Error parsing multilingual names:', error);
          }
        }

        multilingualNames[demo.language.code] = input.localizedName;

        await ctx.db.voiceCharacter.update({
          where: { id: character.id },
          data: {
            multilingualNames: JSON.stringify(multilingualNames),
          },
        });
      }

      return {
        success: true,
        message: `角色 ${demo.voiceCharacter.characterName} 在 ${demo.language.name} 中的信息更新成功`,
      };
    }),

  // 更新语言状态
  updateLanguageStatus: superAdminProcedure
    .input(z.object({
      id: z.string(),
      isActive: z.boolean(),
    }))
    .mutation(async ({ ctx, input }) => {
      const language = await ctx.db.language.update({
        where: { id: input.id },
        data: { isActive: input.isActive },
      });

      return {
        success: true,
        message: `语言 ${language.name} 状态更新成功`,
      };
    }),

  // 删除语言
  deleteLanguage: superAdminProcedure
    .input(z.object({
      id: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 先删除相关的语音试听
      await ctx.db.voiceDemo.deleteMany({
        where: { languageId: input.id },
      });

      // 删除语言
      const language = await ctx.db.language.delete({
        where: { id: input.id },
      });

      return {
        success: true,
        message: `语言 ${language.name} 删除成功`,
      };
    }),
});
