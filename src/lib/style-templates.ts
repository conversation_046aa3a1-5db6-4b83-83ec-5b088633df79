export interface StyleTemplate {
  id: string;
  name: string;
  nameKhmer: string;
  description: string;
  prompt: string;
  category: 'emotion' | 'tone' | 'pace' | 'character' | 'scenario';
  icon: string;
  recommendedFor: string[];
  examples: string[];
}

export const STYLE_TEMPLATES: StyleTemplate[] = [
  // 情感类
  {
    id: 'warm-friendly',
    name: 'Warm & Friendly',
    nameKhmer: 'កក់ក្តៅ និងរាក់ទាក់',
    description: '温暖友好的语调，适合日常对话',
    prompt: 'in a warm, friendly, and welcoming tone, as if talking to a close friend',
    category: 'emotion',
    icon: '😊',
    recommendedFor: ['casual', 'story', 'education'],
    examples: ['日常对话', '故事叙述', '客户服务']
  },
  {
    id: 'professional-confident',
    name: 'Professional & Confident',
    nameKhmer: 'វិជ្ជាជីវៈ និងជឿជាក់',
    description: '专业自信的语调，适合商务场合',
    prompt: 'with confidence and professionalism, clear and authoritative',
    category: 'tone',
    icon: '💼',
    recommendedFor: ['business', 'news', 'formal'],
    examples: ['商务演讲', '新闻播报', '产品介绍']
  },
  {
    id: 'calm-soothing',
    name: 'Calm & Soothing',
    nameKhmer: 'ស្ងប់ស្ងាត់ និងលួងលោម',
    description: '平静舒缓的语调，适合放松内容',
    prompt: 'Speak in a calm, soothing, and peaceful manner, like a gentle meditation guide',
    category: 'emotion',
    icon: '🧘',
    recommendedFor: ['meditation', 'education', 'story'],
    examples: ['冥想指导', '睡前故事', '放松音频']
  },
  {
    id: 'energetic-enthusiastic',
    name: 'Energetic & Enthusiastic',
    nameKhmer: 'មានថាមពល និងរំភើប',
    description: '充满活力和热情的语调',
    prompt: 'Speak with high energy and enthusiasm, exciting and motivating',
    category: 'emotion',
    icon: '⚡',
    recommendedFor: ['sports', 'entertainment', 'motivation'],
    examples: ['体育解说', '娱乐节目', '激励演讲']
  },
  {
    id: 'storyteller',
    name: 'Storyteller',
    nameKhmer: 'អ្នកនិទានរឿង',
    description: '讲故事的语调，富有表现力',
    prompt: 'like an engaging storyteller, with dramatic pauses and expressive intonation',
    category: 'character',
    icon: '📚',
    recommendedFor: ['story', 'entertainment', 'education'],
    examples: ['童话故事', '历史叙述', '小说朗读']
  },
  {
    id: 'news-anchor',
    name: 'News Anchor',
    nameKhmer: 'អ្នកសម្តែងព័ត៌មាន',
    description: '新闻主播的专业语调',
    prompt: 'like a professional news anchor, clear, neutral, and informative',
    category: 'character',
    icon: '📺',
    recommendedFor: ['news', 'formal', 'business'],
    examples: ['新闻播报', '公告宣读', '正式通知']
  },
  {
    id: 'teacher',
    name: 'Teacher',
    nameKhmer: 'គ្រូបង្រៀន',
    description: '教师的耐心教学语调',
    prompt: 'Speak like a patient and encouraging teacher, clear and educational',
    category: 'character',
    icon: '👩‍🏫',
    recommendedFor: ['education', 'tutorial', 'explanation'],
    examples: ['课程讲解', '教程指导', '知识分享']
  },
  {
    id: 'conversational',
    name: 'Conversational',
    nameKhmer: 'ការសន្ទនា',
    description: '自然对话的语调',
    prompt: 'Speak in a natural, conversational way, as if having a casual chat',
    category: 'tone',
    icon: '💬',
    recommendedFor: ['casual', 'interview', 'discussion'],
    examples: ['访谈节目', '日常对话', '讨论交流']
  },
  {
    id: 'slow-clear',
    name: 'Slow & Clear',
    nameKhmer: 'យឺត និងច្បាស់',
    description: '缓慢清晰的语调，便于理解',
    prompt: 'Speak slowly and clearly, emphasizing each word for better understanding',
    category: 'pace',
    icon: '🐌',
    recommendedFor: ['education', 'tutorial', 'accessibility'],
    examples: ['语言学习', '操作指南', '重要说明']
  },
  {
    id: 'fast-dynamic',
    name: 'Fast & Dynamic',
    nameKhmer: 'លឿន និងមានថាមពល',
    description: '快速动态的语调，充满活力',
    prompt: 'Speak with a fast, dynamic pace, energetic and engaging',
    category: 'pace',
    icon: '🚀',
    recommendedFor: ['sports', 'entertainment', 'advertisement'],
    examples: ['广告配音', '快节奏解说', '紧急通知']
  },
  {
    id: 'mysterious',
    name: 'Mysterious',
    nameKhmer: 'អាថ៌កំបាំង',
    description: '神秘的语调，营造悬疑氛围',
    prompt: 'in a mysterious and intriguing tone, creating suspense',
    category: 'emotion',
    icon: '🔮',
    recommendedFor: ['story', 'entertainment', 'thriller'],
    examples: ['悬疑故事', '神秘小说', '恐怖音频']
  },
  {
    id: 'cheerful',
    name: 'Cheerful',
    nameKhmer: 'រីករាយ',
    description: '开朗愉快的语调',
    prompt: 'in a cheerful and upbeat tone, spreading joy and positivity',
    category: 'emotion',
    icon: '🌟',
    recommendedFor: ['entertainment', 'children', 'celebration'],
    examples: ['儿童节目', '庆祝活动', '欢乐内容']
  },
  {
    id: 'formal-ceremonial',
    name: 'Formal & Ceremonial',
    nameKhmer: 'ផ្លូវការ និងពិធីការ',
    description: '正式庄重的仪式语调',
    prompt: 'Speak with formal dignity and ceremonial gravitas, respectful and solemn',
    category: 'tone',
    icon: '🎩',
    recommendedFor: ['ceremony', 'formal', 'official'],
    examples: ['正式仪式', '庄重场合', '官方声明']
  },
  {
    id: 'whisper-intimate',
    name: 'Whisper & Intimate',
    nameKhmer: 'ខ្សឹប និងស្និទ្ធស្នាល',
    description: '轻声细语的亲密语调',
    prompt: 'Speak in a soft whisper, intimate and gentle, very close and personal',
    category: 'tone',
    icon: '🤫',
    recommendedFor: ['romance', 'meditation', 'bedtime'],
    examples: ['睡前故事', '冥想引导', '私密对话']
  },
  {
    id: 'robotic-ai',
    name: 'Robotic & AI',
    nameKhmer: 'រ៉ូបូត និង AI',
    description: '机器人AI的语调',
    prompt: 'Speak like an artificial intelligence or robot, precise and mechanical',
    category: 'character',
    icon: '🤖',
    recommendedFor: ['sci-fi', 'technology', 'futuristic'],
    examples: ['科幻内容', '技术说明', 'AI助手']
  },
  // 新增：符合官方文档示例的风格
  {
    id: 'spooky-whisper',
    name: 'Spooky Whisper',
    nameKhmer: 'ខ្សឹបគួរឱ្យភ័យ',
    description: '恐怖低语，营造诡异氛围',
    prompt: 'in a spooky whisper',
    category: 'emotion',
    icon: '👻',
    recommendedFor: ['horror', 'thriller', 'mystery'],
    examples: ['恐怖故事', '悬疑音频', '万圣节内容']
  },
  {
    id: 'excited-happy',
    name: 'Excited & Happy',
    nameKhmer: 'រំភើប និងរីករាយ',
    description: '兴奋快乐的语调',
    prompt: 'excited and happy',
    category: 'emotion',
    icon: '🎉',
    recommendedFor: ['celebration', 'children', 'entertainment'],
    examples: ['庆祝活动', '儿童节目', '游戏解说']
  },
  {
    id: 'tired-bored',
    name: 'Tired & Bored',
    nameKhmer: 'ហត់នឿយ និងធុញ',
    description: '疲惫无聊的语调',
    prompt: 'tired and bored',
    category: 'emotion',
    icon: '😴',
    recommendedFor: ['comedy', 'character', 'drama'],
    examples: ['喜剧角色', '戏剧表演', '角色扮演']
  }
];

export class StyleTemplateService {
  
  /**
   * 获取所有风格模板
   */
  getAllTemplates(): StyleTemplate[] {
    return STYLE_TEMPLATES;
  }

  /**
   * 根据类别获取模板
   */
  getTemplatesByCategory(category: StyleTemplate['category']): StyleTemplate[] {
    return STYLE_TEMPLATES.filter(template => template.category === category);
  }

  /**
   * 根据推荐用途获取模板
   */
  getTemplatesForContent(contentType: string): StyleTemplate[] {
    return STYLE_TEMPLATES.filter(template => 
      template.recommendedFor.includes(contentType)
    );
  }

  /**
   * 根据ID获取模板
   */
  getTemplateById(id: string): StyleTemplate | undefined {
    return STYLE_TEMPLATES.find(template => template.id === id);
  }

  /**
   * 搜索模板
   */
  searchTemplates(query: string): StyleTemplate[] {
    const lowerQuery = query.toLowerCase();
    return STYLE_TEMPLATES.filter(template =>
      template.name.toLowerCase().includes(lowerQuery) ||
      template.nameKhmer.includes(query) ||
      template.description.toLowerCase().includes(lowerQuery) ||
      template.examples.some(example => example.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * 获取用户自定义模板（从用户偏好中）
   */
  getUserCustomTemplates(userTemplatesJson: string | null): StyleTemplate[] {
    if (!userTemplatesJson) return [];
    
    try {
      const customTemplates = JSON.parse(userTemplatesJson);
      return customTemplates.map((template: any) => ({
        ...template,
        category: 'custom' as const,
        icon: '⭐'
      }));
    } catch {
      return [];
    }
  }

  /**
   * 保存用户自定义模板
   */
  saveUserCustomTemplate(
    existingTemplatesJson: string | null,
    newTemplate: Omit<StyleTemplate, 'id' | 'category' | 'icon'>
  ): string {
    const existingTemplates = this.getUserCustomTemplates(existingTemplatesJson);
    const customTemplate: StyleTemplate = {
      ...newTemplate,
      id: `custom-${Date.now()}`,
      category: 'custom',
      icon: '⭐'
    };
    
    const updatedTemplates = [...existingTemplates, customTemplate];
    return JSON.stringify(updatedTemplates);
  }
}

export const styleTemplateService = new StyleTemplateService();
