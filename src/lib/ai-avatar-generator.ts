import { GoogleGenerativeAI } from '@google/generative-ai';

export interface AvatarGenerationOptions {
  characterName: string;
  gender: 'MALE' | 'FEMALE' | 'NEUTRAL';
  style?: string;
  personality?: string;
  description?: string;
  languageCode?: string;
  culturalContext?: string;
}

export interface AvatarGenerationResult {
  imageData: Buffer;
  prompt: string;
  metadata: {
    characterName: string;
    gender: string;
    style?: string;
    languageCode?: string;
    generatedAt: string;
  };
}

export class AIAvatarGenerator {
  private genAI: GoogleGenerativeAI;

  constructor() {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY is required for AI avatar generation');
    }
    this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
  }

  /**
   * 生成角色头像
   */
  async generateAvatar(options: AvatarGenerationOptions): Promise<AvatarGenerationResult> {
    try {
      // 构建提示词
      const prompt = this.buildPrompt(options);
      
      // 使用Gemini 2.5 Flash Image Preview模型
      const model = this.genAI.getGenerativeModel({ 
        model: "gemini-2.5-flash-image-preview" 
      });

      console.log(`🎨 开始为角色 ${options.characterName} 生成头像...`);
      console.log(`📝 提示词: ${prompt}`);

      // 生成图像
      const result = await model.generateContent([prompt]);
      
      if (!result.response) {
        throw new Error('No response from Gemini model');
      }

      // 获取生成的图像数据
      const imageData = await this.extractImageData(result.response);

      const metadata = {
        characterName: options.characterName,
        gender: options.gender,
        style: options.style,
        languageCode: options.languageCode,
        generatedAt: new Date().toISOString(),
      };

      console.log(`✅ 头像生成成功: ${imageData.length} 字节`);

      return {
        imageData,
        prompt,
        metadata,
      };

    } catch (error) {
      console.error('❌ AI头像生成失败:', error);
      throw new Error(`Failed to generate avatar: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 构建AI生成提示词
   */
  private buildPrompt(options: AvatarGenerationOptions): string {
    const {
      characterName,
      gender,
      style,
      personality,
      description,
      languageCode,
      culturalContext
    } = options;

    // 基础提示词 - 更具体和个性化
    let prompt = `Create a professional voice character portrait for "${characterName}". `;

    // 性别和年龄描述 - 更详细
    const genderMap = {
      'MALE': 'adult male',
      'FEMALE': 'adult female',
      'NEUTRAL': 'person with neutral gender presentation'
    };
    prompt += `This is a ${genderMap[gender]} voice actor. `;

    // 文化背景和外观特征 - 大幅增强
    if (languageCode && culturalContext) {
      prompt += `The character represents ${culturalContext} culture (${languageCode}). `;
    } else if (languageCode) {
      const detailedCultureMap: Record<string, { ethnicity: string; features: string; attire: string }> = {
        'zh-CN': {
          ethnicity: 'East Asian (Chinese)',
          features: 'with typical Chinese facial features, dark hair, warm brown eyes',
          attire: 'wearing modern professional attire with subtle traditional elements'
        },
        'en-US': {
          ethnicity: 'American',
          features: 'with diverse American features, could be any ethnicity common in the US',
          attire: 'wearing contemporary American business casual attire'
        },
        'ja-JP': {
          ethnicity: 'Japanese',
          features: 'with Japanese facial features, neat dark hair, gentle expression',
          attire: 'wearing clean, minimalist professional clothing'
        },
        'ko-KR': {
          ethnicity: 'Korean',
          features: 'with Korean facial features, well-groomed appearance',
          attire: 'wearing stylish modern Korean fashion'
        },
        'th-TH': {
          ethnicity: 'Thai',
          features: 'with Southeast Asian Thai features, warm smile',
          attire: 'wearing elegant Thai-inspired professional attire'
        },
        'vi-VN': {
          ethnicity: 'Vietnamese',
          features: 'with Vietnamese facial features, graceful appearance',
          attire: 'wearing modern Vietnamese professional style'
        },
        'km-KH': {
          ethnicity: 'Cambodian (Khmer)',
          features: 'with Cambodian facial features, kind eyes, traditional beauty',
          attire: 'wearing professional attire with subtle Khmer cultural touches'
        },
        'fr-FR': {
          ethnicity: 'French',
          features: 'with European French features, sophisticated appearance',
          attire: 'wearing chic French professional fashion'
        },
        'de-DE': {
          ethnicity: 'German',
          features: 'with Central European German features, confident expression',
          attire: 'wearing precise, well-tailored German business attire'
        },
        'es-ES': {
          ethnicity: 'Spanish',
          features: 'with Mediterranean Spanish features, expressive eyes',
          attire: 'wearing elegant Spanish professional style'
        },
        'es-US': {
          ethnicity: 'Hispanic American',
          features: 'with Latino/Hispanic features, warm expression',
          attire: 'wearing modern American professional attire with Hispanic cultural touches'
        },
        'it-IT': {
          ethnicity: 'Italian',
          features: 'with Mediterranean Italian features, warm expression',
          attire: 'wearing stylish Italian fashion'
        },
        'pt-BR': {
          ethnicity: 'Brazilian',
          features: 'with diverse Brazilian features reflecting the country\'s multicultural heritage',
          attire: 'wearing vibrant yet professional Brazilian style'
        },
        'ru-RU': {
          ethnicity: 'Russian',
          features: 'with Slavic Russian features, strong bone structure',
          attire: 'wearing formal Russian professional attire'
        },
        'ar-SA': {
          ethnicity: 'Arabic',
          features: 'with Middle Eastern Arabic features, dignified appearance',
          attire: 'wearing modern professional attire respecting cultural modesty'
        },
        'ar-EG': {
          ethnicity: 'Egyptian Arabic',
          features: 'with Middle Eastern Egyptian features, warm and expressive eyes',
          attire: 'wearing modern professional attire with Egyptian elegance'
        },
        'hi-IN': {
          ethnicity: 'Indian',
          features: 'with South Asian Indian features, expressive eyes',
          attire: 'wearing modern Indian professional attire with cultural elements'
        },
      };

      // 通用语言族群映射（用于处理变体）
      const languageFamilyMap: Record<string, string> = {
        'ar': 'Arabic',
        'zh': 'Chinese',
        'en': 'English',
        'es': 'Spanish',
        'fr': 'French',
        'de': 'German',
        'it': 'Italian',
        'pt': 'Portuguese',
        'ru': 'Russian',
        'ja': 'Japanese',
        'ko': 'Korean',
        'th': 'Thai',
        'vi': 'Vietnamese',
        'km': 'Khmer',
        'hi': 'Hindi',
      };

      let cultureInfo = detailedCultureMap[languageCode];

      // 如果没有找到精确匹配，尝试语言族群匹配
      if (!cultureInfo) {
        const languageFamily = languageCode.split('-')[0];
        const familyName = languageFamilyMap[languageFamily];
        if (familyName) {
          cultureInfo = {
            ethnicity: familyName,
            features: `with ${familyName} cultural features`,
            attire: 'wearing modern professional attire appropriate for their cultural background'
          };
        }
      }

      if (cultureInfo) {
        prompt += `The character is ${cultureInfo.ethnicity} ${cultureInfo.features}. ${cultureInfo.attire}. `;
      } else {
        prompt += `The character has international features suitable for global audiences. `;
      }
    }

    // 角色描述和个性 - 更好地整合
    if (description) {
      // 从描述中提取关键特征
      const descLower = description.toLowerCase();
      if (descLower.includes('温暖') || descLower.includes('warm')) {
        prompt += `The character has a warm, nurturing appearance with gentle eyes and a kind smile. `;
      }
      if (descLower.includes('专业') || descLower.includes('professional')) {
        prompt += `The character looks highly professional and competent. `;
      }
      if (descLower.includes('年轻') || descLower.includes('young')) {
        prompt += `The character appears youthful and energetic. `;
      }
      if (descLower.includes('成熟') || descLower.includes('mature')) {
        prompt += `The character has a mature, experienced appearance. `;
      }
      if (descLower.includes('友好') || descLower.includes('friendly')) {
        prompt += `The character has a very friendly and approachable demeanor. `;
      }

      prompt += `Character background: ${description}. `;
    }

    // 风格描述 - 更具体
    if (style) {
      const styleMap: Record<string, string> = {
        'professional': 'highly professional business appearance with confident posture',
        'friendly': 'warm and approachable with a genuine smile',
        'authoritative': 'commanding presence with strong, confident features',
        'gentle': 'soft, kind features with a gentle expression',
        'energetic': 'vibrant and lively appearance with bright eyes',
        'calm': 'serene and peaceful expression with relaxed features',
        'sophisticated': 'refined and elegant appearance with polished style',
        'casual': 'relaxed and natural appearance with comfortable styling'
      };

      const styleDesc = styleMap[style.toLowerCase()] || style;
      prompt += `Character style: ${styleDesc}. `;
    }

    // 个性特征 - 转化为视觉特征
    if (personality) {
      try {
        const personalityTraits = JSON.parse(personality);
        if (Array.isArray(personalityTraits) && personalityTraits.length > 0) {
          const visualTraits = personalityTraits.map(trait => {
            const traitMap: Record<string, string> = {
              'confident': 'confident posture and direct gaze',
              'friendly': 'warm smile and open expression',
              'intelligent': 'thoughtful eyes and composed demeanor',
              'creative': 'expressive features and artistic flair',
              'calm': 'serene expression and relaxed posture',
              'energetic': 'bright eyes and dynamic presence',
              'professional': 'polished appearance and business-like demeanor',
              'caring': 'kind eyes and nurturing expression'
            };
            return traitMap[trait.toLowerCase()] || `${trait} personality reflected in facial expression`;
          });
          prompt += `Personality visual traits: ${visualTraits.join(', ')}. `;
        }
      } catch {
        prompt += `Personality: ${personality} reflected in the character's expression and demeanor. `;
      }
    }

    // 技术要求 - 更详细和专业
    prompt += `

TECHNICAL REQUIREMENTS:
- Professional headshot portrait style
- High-resolution, photorealistic rendering
- Clean, modern aesthetic with excellent lighting
- Genuine, natural facial expression appropriate for voice work
- Clear, detailed facial features that convey personality
- Professional attire suitable for the character's cultural background
- Neutral to warm color palette
- Sharp focus on face with subtle background
- 512x512 pixel dimensions
- Suitable for use as a voice character avatar
- Conveys trustworthiness and professionalism

VISUAL STYLE:
- Photorealistic human portrait
- Professional photography lighting setup
- Soft, even lighting that flatters the subject
- Natural skin tones and textures
- Authentic cultural representation
- Modern, timeless appearance
- Appropriate for international audiences`;

    // 避免的内容 - 更全面
    prompt += `

AVOID:
- Cartoon, anime, or illustrated styles
- Overly dramatic or exaggerated expressions
- Inappropriate, casual, or unprofessional clothing
- Cultural stereotypes or caricatures
- Low quality, blurry, or pixelated images
- Dark, moody, or dramatic lighting
- Extreme close-ups or unusual angles
- Distracting backgrounds or elements
- Overly edited or artificial appearance
- Generic stock photo appearance`;

    return prompt.trim();
  }

  /**
   * 从Gemini响应中提取图像数据
   */
  private async extractImageData(response: any): Promise<Buffer> {
    try {
      // 检查响应中是否包含图像数据
      if (response.candidates && response.candidates[0]) {
        const candidate = response.candidates[0];
        
        // 检查是否有内联数据
        if (candidate.content && candidate.content.parts) {
          for (const part of candidate.content.parts) {
            if (part.inlineData && part.inlineData.data) {
              // 解码base64图像数据
              return Buffer.from(part.inlineData.data, 'base64');
            }
          }
        }
      }

      // 如果没有找到图像数据，生成默认头像
      console.warn('⚠️ 未找到生成的图像数据，使用默认头像');
      return this.generateDefaultAvatar(response.text || 'Character');

    } catch (error) {
      console.error('❌ 提取图像数据失败:', error);
      throw new Error('Failed to extract image data from response');
    }
  }

  /**
   * 生成默认头像（SVG转Buffer）
   */
  private generateDefaultAvatar(characterName: string): Buffer {
    const initial = characterName.charAt(0).toUpperCase();
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    
    // 基于角色名称选择颜色
    const colorIndex = characterName.length % colors.length;
    const bgColor = colors[colorIndex];

    const svg = `
      <svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
        <circle cx="256" cy="256" r="256" fill="${bgColor}"/>
        <text x="256" y="320" font-family="Arial, sans-serif" font-size="200" 
              font-weight="bold" text-anchor="middle" fill="white">${initial}</text>
      </svg>
    `;

    return Buffer.from(svg, 'utf-8');
  }

  /**
   * 验证生成的图像
   */
  private validateImage(imageData: Buffer): boolean {
    // 检查文件大小（应该在合理范围内）
    if (imageData.length < 1000 || imageData.length > 10 * 1024 * 1024) {
      return false;
    }

    // 检查是否是有效的图像格式（简单检查）
    const header = imageData.slice(0, 10);
    const isPNG = header[0] === 0x89 && header[1] === 0x50;
    const isJPEG = header[0] === 0xFF && header[1] === 0xD8;
    const isWebP = header.includes(Buffer.from('WEBP'));

    return isPNG || isJPEG || isWebP;
  }

  /**
   * 检查服务是否可用
   */
  isAvailable(): boolean {
    return !!process.env.GEMINI_API_KEY;
  }
}

// 导出单例实例
export const aiAvatarGenerator = new AIAvatarGenerator();
