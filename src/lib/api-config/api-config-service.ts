import { type ApiProvider } from '@prisma/client';

// 临时定义 ApiStatus，直到 Prisma 客户端更新
export type ApiStatus = 'ACTIVE' | 'INACTIVE' | 'MAINTENANCE' | 'ERROR';

export interface ApiConfig {
  provider: ApiProvider;
  displayName: string;
  description?: string;
  status: ApiStatus;
  priority: number;
  isEnabled: boolean;
  config: Record<string, any>;
  dailyLimit?: number;
  monthlyLimit?: number;
  costLimit?: number;
  totalRequests: number;
  successRequests: number;
  failedRequests: number;
  totalCost: number;
  lastUsedAt?: Date;
}

export interface ApiUsageStats {
  provider: ApiProvider;
  requestType: string;
  inputSize: number;
  outputSize?: number;
  success: boolean;
  responseTime: number;
  errorMessage?: string;
  cost?: number;
  userId?: string;
  sessionId?: string;
}

class ApiConfigService {
  private configState: Map<string, { isEnabled: boolean }> = new Map([
    ['GEMINI', { isEnabled: true }],
    ['OPENROUTER', { isEnabled: true }],
    ['OPENAI', { isEnabled: true }],
  ]);

  // 获取所有API配置（基于环境变量的简化版本）
  async getAllConfigs(): Promise<ApiConfig[]> {
    const configs: ApiConfig[] = [];

    // Gemini 配置
    const geminiEnabled = this.configState.get('GEMINI')?.isEnabled ?? true;
    const hasGeminiKey = !!process.env.GEMINI_API_KEY;
    configs.push({
      provider: 'GEMINI',
      displayName: 'Google Gemini',
      description: 'Google Gemini TTS API - 高质量多语言语音合成',
      status: hasGeminiKey ? 'ACTIVE' : 'INACTIVE',
      priority: 1,
      isEnabled: geminiEnabled,
      config: {
        apiKey: hasGeminiKey ? '***' : '',
        models: {
          fast: 'gemini-2.5-flash-preview-tts',
          high: 'gemini-2.5-pro-preview-tts',
        },
      },
      dailyLimit: 10000,
      monthlyLimit: 300000,
      costLimit: 100,
      totalRequests: 0,
      successRequests: 0,
      failedRequests: 0,
      totalCost: 0,
    });



    // OpenAI 配置
    const openaiEnabled = this.configState.get('OPENAI')?.isEnabled ?? true;
    const hasOpenAIKey = !!process.env.OPENAI_API_KEY;
    configs.push({
      provider: 'OPENAI',
      displayName: 'OpenAI',
      description: 'OpenAI TTS API - 高质量英语语音合成',
      status: hasOpenAIKey ? 'ACTIVE' : 'INACTIVE',
      priority: 3,
      isEnabled: openaiEnabled,
      config: {
        apiKey: hasOpenAIKey ? '***' : '',
        models: {
          fast: 'tts-1',
          high: 'tts-1-hd',
        },
      },
      dailyLimit: 1000,
      monthlyLimit: 30000,
      costLimit: 30,
      totalRequests: 0,
      successRequests: 0,
      failedRequests: 0,
      totalCost: 0,
    });

    return configs;
  }

  // 获取特定API配置
  async getConfig(provider: ApiProvider): Promise<ApiConfig | null> {
    const configs = await this.getAllConfigs();
    return configs.find(config => config.provider === provider) || null;
  }

  // 获取启用的API配置（按优先级排序）
  async getEnabledConfigs(): Promise<ApiConfig[]> {
    const configs = await this.getAllConfigs();
    return configs
      .filter(config => config.isEnabled && config.status === 'ACTIVE')
      .sort((a, b) => a.priority - b.priority);
  }

  // 获取最优API配置
  async getBestAvailableConfig(): Promise<ApiConfig | null> {
    const enabledConfigs = await this.getEnabledConfigs();
    return enabledConfigs[0] || null;
  }

  // 更新API配置（简化版本）
  async updateConfig(provider: ApiProvider, updates: Partial<ApiConfig>): Promise<void> {
    console.log(`Updating config for ${provider}:`, updates);

    // 更新内存中的配置状态
    if (updates.isEnabled !== undefined) {
      const currentState = this.configState.get(provider) || { isEnabled: true };
      this.configState.set(provider, {
        ...currentState,
        isEnabled: updates.isEnabled
      });
      console.log(`${provider} isEnabled set to:`, updates.isEnabled);
    }
  }

  // 记录API使用（简化版本）
  async logUsage(stats: ApiUsageStats): Promise<void> {
    try {
      console.log('API Usage:', stats);
      // TODO: 实现实际的使用日志记录
    } catch (error) {
      console.error('Failed to log API usage:', error);
    }
  }

  // 获取使用统计（简化版本）
  async getUsageStats(
    provider?: ApiProvider,
    startDate?: Date,
    endDate?: Date
  ): Promise<any> {
    return {
      totalRequests: 0,
      successRequests: 0,
      failedRequests: 0,
      successRate: 0,
      totalCost: 0,
      avgResponseTime: 0,
    };
  }

  // 初始化默认配置（简化版本）
  async initializeDefaultConfigs(): Promise<void> {
    console.log('Initializing default API configurations...');
    // TODO: 实现实际的配置初始化
  }
}

// 单例实例
export const apiConfigService = new ApiConfigService();
