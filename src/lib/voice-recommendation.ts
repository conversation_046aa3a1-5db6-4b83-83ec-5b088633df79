import { type VoiceCharacter } from '@prisma/client';

export interface VoiceRecommendation {
  character: VoiceCharacter;
  score: number;
  reasons: string[];
}

export interface TextAnalysis {
  language: 'khmer' | 'english' | 'mixed';
  contentType: 'news' | 'story' | 'education' | 'business' | 'casual' | 'formal';
  emotion: 'neutral' | 'happy' | 'serious' | 'calm' | 'energetic';
  length: 'short' | 'medium' | 'long';
  complexity: 'simple' | 'moderate' | 'complex';
}

export class VoiceRecommendationService {
  
  /**
   * 分析文本内容特征
   */
  analyzeText(text: string): TextAnalysis {
    const analysis: TextAnalysis = {
      language: this.detectLanguage(text),
      contentType: this.detectContentType(text),
      emotion: this.detectEmotion(text),
      length: this.detectLength(text),
      complexity: this.detectComplexity(text),
    };

    return analysis;
  }

  /**
   * 推荐最适合的语音角色
   */
  recommendVoices(
    text: string, 
    availableCharacters: VoiceCharacter[], 
    limit: number = 3
  ): VoiceRecommendation[] {
    const analysis = this.analyzeText(text);
    const recommendations: VoiceRecommendation[] = [];

    for (const character of availableCharacters) {
      const score = this.calculateScore(character, analysis);
      const reasons = this.generateReasons(character, analysis);
      
      recommendations.push({
        character,
        score,
        reasons,
      });
    }

    // 按分数排序并返回前N个
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  private detectLanguage(text: string): 'khmer' | 'english' | 'mixed' {
    const khmerRegex = /[\u1780-\u17FF]/;
    const englishRegex = /[a-zA-Z]/;
    
    const hasKhmer = khmerRegex.test(text);
    const hasEnglish = englishRegex.test(text);
    
    if (hasKhmer && hasEnglish) return 'mixed';
    if (hasKhmer) return 'khmer';
    return 'english';
  }

  private detectContentType(text: string): TextAnalysis['contentType'] {
    const lowerText = text.toLowerCase();
    
    // 新闻关键词
    if (this.containsKeywords(lowerText, ['ព័ត៌មាន', 'ប្រកាស', 'រាយការណ៍', 'news', 'report', 'announce'])) {
      return 'news';
    }
    
    // 故事关键词
    if (this.containsKeywords(lowerText, ['រឿង', 'កាលពី', 'មានម្តង', 'story', 'once upon', 'tale'])) {
      return 'story';
    }
    
    // 教育关键词
    if (this.containsKeywords(lowerText, ['សិក្សា', 'បង្រៀន', 'ការអប់រំ', 'learn', 'education', 'teach', 'lesson'])) {
      return 'education';
    }
    
    // 商务关键词
    if (this.containsKeywords(lowerText, ['អាជីវកម្ម', 'ការងារ', 'ប្រជុំ', 'business', 'meeting', 'professional'])) {
      return 'business';
    }
    
    // 正式文档关键词
    if (this.containsKeywords(lowerText, ['លិខិត', 'ឯកសារ', 'ការប្រកាស', 'document', 'official', 'formal'])) {
      return 'formal';
    }
    
    return 'casual';
  }

  private detectEmotion(text: string): TextAnalysis['emotion'] {
    const lowerText = text.toLowerCase();
    
    // 快乐情绪
    if (this.containsKeywords(lowerText, ['រីករាយ', 'សប្បាយ', 'ល្អ', 'happy', 'joy', 'great', '!', '😊'])) {
      return 'happy';
    }
    
    // 严肃情绪
    if (this.containsKeywords(lowerText, ['សំខាន់', 'ធ្ងន់ធ្ងរ', 'serious', 'important', 'critical'])) {
      return 'serious';
    }
    
    // 平静情绪
    if (this.containsKeywords(lowerText, ['ស្ងប់', 'ស្ងាត់', 'calm', 'peaceful', 'quiet'])) {
      return 'calm';
    }
    
    // 充满活力
    if (this.containsKeywords(lowerText, ['ថាមពល', 'រំភើប', 'energy', 'exciting', 'dynamic'])) {
      return 'energetic';
    }
    
    return 'neutral';
  }

  private detectLength(text: string): TextAnalysis['length'] {
    const charCount = text.length;
    if (charCount < 100) return 'short';
    if (charCount < 500) return 'medium';
    return 'long';
  }

  private detectComplexity(text: string): TextAnalysis['complexity'] {
    // 简单指标：句子长度、标点符号、专业词汇等
    const sentences = text.split(/[។.!?]/).filter(s => s.trim().length > 0);
    const avgSentenceLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;
    
    if (avgSentenceLength < 50) return 'simple';
    if (avgSentenceLength < 150) return 'moderate';
    return 'complex';
  }

  private containsKeywords(text: string, keywords: string[]): boolean {
    return keywords.some(keyword => text.includes(keyword));
  }

  private calculateScore(character: VoiceCharacter, analysis: TextAnalysis): number {
    let score = 50; // 基础分数
    
    // 根据性别调整分数
    if (analysis.contentType === 'news' || analysis.contentType === 'business') {
      if (character.gender === 'MALE') score += 15;
      if (character.gender === 'FEMALE') score += 10;
    } else if (analysis.contentType === 'story') {
      if (character.gender === 'FEMALE') score += 15;
      if (character.gender === 'NEUTRAL') score += 10;
    }
    
    // 根据情绪调整分数
    if (analysis.emotion === 'happy') {
      if (character.characterNameEn.includes('Puck') || 
          character.characterNameEn.includes('Aoede')) score += 20;
    } else if (analysis.emotion === 'serious') {
      if (character.characterNameEn.includes('Charon') || 
          character.characterNameEn.includes('Zeus')) score += 20;
    } else if (analysis.emotion === 'calm') {
      if (character.characterNameEn.includes('Kore') || 
          character.characterNameEn.includes('Sage')) score += 20;
    }
    
    // 根据内容类型调整分数
    if (analysis.contentType === 'education') {
      if (character.characterNameEn.includes('Charon') || 
          character.characterNameEn.includes('Kore')) score += 15;
    } else if (analysis.contentType === 'business') {
      if (character.gender === 'MALE' && 
          (character.characterNameEn.includes('Atlas') || 
           character.characterNameEn.includes('Zeus'))) score += 15;
    }
    
    // 根据文本长度调整分数
    if (analysis.length === 'long') {
      // 长文本适合更稳定的声音
      if (character.characterNameEn.includes('Charon') || 
          character.characterNameEn.includes('Kore')) score += 10;
    }
    
    // 等级加分
    if (character.tier === 'FREE') score += 5; // 免费角色稍微加分
    
    return Math.min(100, Math.max(0, score));
  }

  private generateReasons(character: VoiceCharacter, analysis: TextAnalysis): string[] {
    const reasons: string[] = [];
    
    // 基于性别的推荐理由
    if (analysis.contentType === 'news' && character.gender === 'MALE') {
      reasons.push('男性声音适合新闻播报');
    } else if (analysis.contentType === 'story' && character.gender === 'FEMALE') {
      reasons.push('女性声音适合故事叙述');
    }
    
    // 基于角色特点的推荐理由
    if (character.characterNameEn.includes('Puck') && analysis.emotion === 'happy') {
      reasons.push('Puck的欢快风格适合轻松内容');
    } else if (character.characterNameEn.includes('Charon') && analysis.contentType === 'education') {
      reasons.push('Charon的权威声音适合教育内容');
    } else if (character.characterNameEn.includes('Kore') && analysis.emotion === 'serious') {
      reasons.push('Kore的坚定声音适合严肃内容');
    }
    
    // 基于文本特征的推荐理由
    if (analysis.language === 'khmer') {
      reasons.push('优化了高棉语发音');
    }
    
    if (analysis.length === 'long') {
      reasons.push('适合长文本朗读');
    }
    
    if (character.tier === 'FREE') {
      reasons.push('免费使用');
    }
    
    // 如果没有特定理由，添加通用理由
    if (reasons.length === 0) {
      reasons.push('声音特质与内容匹配');
    }
    
    return reasons;
  }
}

export const voiceRecommendationService = new VoiceRecommendationService();
