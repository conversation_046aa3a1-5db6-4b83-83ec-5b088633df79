import nodemailer from 'nodemailer';
import { db } from '~/server/db';

// 邮件配置
const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });
};

// 邮件模板
const emailTemplates = {
  paymentSuccess: {
    subject: '🎉 支付成功 - Voctana订阅已激活',
    html: (data: any) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">支付成功！</h1>
          <p style="color: white; margin: 10px 0 0 0;">您的Voctana订阅已激活</p>
        </div>
        
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">订单详情</h2>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;"><strong>订单号:</strong></td>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;">${data.orderNumber}</td>
            </tr>
            <tr>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;"><strong>订阅计划:</strong></td>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;">${data.planName}</td>
            </tr>
            <tr>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;"><strong>支付金额:</strong></td>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;">${data.currency === 'KHR' ? '៛' : '$'}${data.amount}</td>
            </tr>
            <tr>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;"><strong>支付方式:</strong></td>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;">${data.provider}</td>
            </tr>
            <tr>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;"><strong>支付时间:</strong></td>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;">${new Date(data.completedAt).toLocaleString('zh-CN')}</td>
            </tr>
          </table>
          
          <div style="margin: 30px 0; padding: 20px; background: #e8f5e8; border-radius: 8px;">
            <h3 style="color: #2d5a2d; margin: 0 0 10px 0;">🎯 您现在可以享受:</h3>
            <ul style="color: #2d5a2d; margin: 0; padding-left: 20px;">
              <li>每月 ${data.monthlyQuota?.toLocaleString()} 字符配额</li>
              <li>高质量AI语音生成</li>
              <li>优先技术支持</li>
              <li>所有高级功能</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXTAUTH_URL}/dashboard" 
               style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              前往控制台
            </a>
          </div>
          
          <p style="color: #666; font-size: 14px; margin-top: 30px;">
            如有任何问题，请联系我们的客服团队。<br>
            感谢您选择Voctana！
          </p>
        </div>
      </div>
    `,
  },
  
  paymentFailure: {
    subject: '❌ 支付失败 - Voctana订阅',
    html: (data: any) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">支付失败</h1>
          <p style="color: white; margin: 10px 0 0 0;">您的订阅支付未能完成</p>
        </div>
        
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">订单信息</h2>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;"><strong>订单号:</strong></td>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;">${data.orderNumber}</td>
            </tr>
            <tr>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;"><strong>订阅计划:</strong></td>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;">${data.planName}</td>
            </tr>
            <tr>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;"><strong>支付金额:</strong></td>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;">${data.currency === 'KHR' ? '៛' : '$'}${data.amount}</td>
            </tr>
            <tr>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;"><strong>失败原因:</strong></td>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;">${data.failureReason}</td>
            </tr>
          </table>
          
          <div style="margin: 30px 0; padding: 20px; background: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107;">
            <h3 style="color: #856404; margin: 0 0 10px 0;">💡 解决方案:</h3>
            <ul style="color: #856404; margin: 0; padding-left: 20px;">
              <li>检查您的支付方式是否有效</li>
              <li>确认账户余额充足</li>
              <li>尝试使用其他支付方式</li>
              <li>联系您的银行或支付提供商</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXTAUTH_URL}/payment/${data.paymentId}" 
               style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              重新支付
            </a>
          </div>
          
          <p style="color: #666; font-size: 14px; margin-top: 30px;">
            如需帮助，请联系我们的客服团队。<br>
            我们将竭诚为您解决支付问题。
          </p>
        </div>
      </div>
    `,
  },
  
  subscriptionExpiring: {
    subject: '⏰ 订阅即将到期 - Voctana',
    html: (data: any) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0;">订阅即将到期</h1>
          <p style="color: white; margin: 10px 0 0 0;">请及时续费以继续享受服务</p>
        </div>
        
        <div style="padding: 30px; background: #f9f9f9;">
          <h2 style="color: #333;">订阅信息</h2>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;"><strong>当前计划:</strong></td>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;">${data.planName}</td>
            </tr>
            <tr>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;"><strong>到期时间:</strong></td>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;">${new Date(data.endDate).toLocaleString('zh-CN')}</td>
            </tr>
            <tr>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;"><strong>剩余天数:</strong></td>
              <td style="padding: 10px; border-bottom: 1px solid #ddd;">${data.daysRemaining} 天</td>
            </tr>
          </table>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXTAUTH_URL}/pricing" 
               style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              立即续费
            </a>
          </div>
          
          <p style="color: #666; font-size: 14px; margin-top: 30px;">
            为避免服务中断，请在到期前完成续费。<br>
            感谢您对Voctana的支持！
          </p>
        </div>
      </div>
    `,
  },
};

// 发送邮件通知
export async function sendEmailNotification(
  to: string,
  template: keyof typeof emailTemplates,
  data: any
) {
  try {
    const transporter = createTransporter();
    const emailTemplate = emailTemplates[template];
    
    if (!emailTemplate) {
      throw new Error(`Email template '${template}' not found`);
    }

    const mailOptions = {
      from: `"Voctana" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
      to,
      subject: emailTemplate.subject,
      html: emailTemplate.html(data),
    };

    const result = await transporter.sendMail(mailOptions);
    console.log(`Email sent successfully to ${to}:`, result.messageId);
    
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('Email sending failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// 发送支付成功通知
export async function sendPaymentSuccessNotification(payment: any) {
  try {
    const user = payment.user || await db.user.findUnique({
      where: { id: payment.userId },
    });

    if (!user?.email) {
      console.error('User email not found for payment notification');
      return;
    }

    const metadata = payment.metadata as any;
    const subscription = payment.subscription || await db.subscription.findUnique({
      where: { id: payment.subscriptionId },
      include: { plan: true },
    });

    const data = {
      orderNumber: metadata?.orderNumber || payment.id,
      planName: subscription?.plan?.displayName || 'Unknown Plan',
      amount: payment.amount.toLocaleString(),
      currency: payment.currency,
      provider: payment.provider,
      completedAt: payment.completedAt,
      monthlyQuota: subscription?.plan?.monthlyQuota,
    };

    await sendEmailNotification(user.email, 'paymentSuccess', data);
    
    // 记录通知日志
    await db.payment.update({
      where: { id: payment.id },
      data: {
        metadata: {
          ...(payment.metadata as object || {}),
          notificationSent: true,
          notificationSentAt: new Date().toISOString(),
        },
      },
    });

  } catch (error) {
    console.error('Error sending payment success notification:', error);
  }
}

// 发送支付失败通知
export async function sendPaymentFailureNotification(payment: any, failureReason: string) {
  try {
    const user = payment.user || await db.user.findUnique({
      where: { id: payment.userId },
    });

    if (!user?.email) {
      console.error('User email not found for payment failure notification');
      return;
    }

    const metadata = payment.metadata as any;
    const subscription = payment.subscription || await db.subscription.findUnique({
      where: { id: payment.subscriptionId },
      include: { plan: true },
    });

    const data = {
      orderNumber: metadata?.orderNumber || payment.id,
      planName: subscription?.plan?.displayName || 'Unknown Plan',
      amount: payment.amount.toLocaleString(),
      currency: payment.currency,
      failureReason,
      paymentId: payment.id,
    };

    await sendEmailNotification(user.email, 'paymentFailure', data);

  } catch (error) {
    console.error('Error sending payment failure notification:', error);
  }
}

// 发送订阅即将到期通知
export async function sendSubscriptionExpiringNotification(subscription: any) {
  try {
    const user = await db.user.findUnique({
      where: { id: subscription.userId },
    });

    if (!user?.email) {
      console.error('User email not found for subscription expiring notification');
      return;
    }

    const plan = subscription.plan || await db.pricingPlan.findUnique({
      where: { id: subscription.planId },
    });

    const endDate = new Date(subscription.endDate);
    const now = new Date();
    const daysRemaining = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    const data = {
      planName: plan?.displayName || 'Unknown Plan',
      endDate: subscription.endDate,
      daysRemaining,
    };

    await sendEmailNotification(user.email, 'subscriptionExpiring', data);

  } catch (error) {
    console.error('Error sending subscription expiring notification:', error);
  }
}

export default {
  sendEmail: sendEmailNotification,
  sendPaymentSuccess: sendPaymentSuccessNotification,
  sendPaymentFailure: sendPaymentFailureNotification,
  sendSubscriptionExpiring: sendSubscriptionExpiringNotification,
};
