import { PrismaClient } from '@prisma/client';

export interface TokenUsage {
  totalTokens: number;
}

export interface CreditCalculationResult {
  creditsNeeded: number;
  costBreakdown: {
    consumptionRatio: number; // tokens per credit
    actualCost: number; // USD cost for reference
  };
  tokenUsage: TokenUsage;
  serviceType: string;
  explanation: string;
}

export class CreditCalculationService {
  private prisma: PrismaClient;
  private creditValue = 0.01; // 1积分 = $0.01

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * 计算文本生成所需积分 (使用消耗比)
   */
  async calculateTextGenerationCredits(tokenUsage: TokenUsage): Promise<CreditCalculationResult> {
    const apiConfig = await this.getApiPricingConfig();

    // 使用消耗比计算：积分 = ceil(tokens / 消耗比)
    const creditsNeeded = Math.ceil(tokenUsage.totalTokens / apiConfig.textGenerationRatio);

    // 计算实际成本（仅供参考）
    const actualCost = creditsNeeded * this.creditValue;

    return {
      creditsNeeded,
      costBreakdown: {
        consumptionRatio: apiConfig.textGenerationRatio,
        actualCost,
      },
      tokenUsage,
      serviceType: 'TEXT_GENERATION',
      explanation: `文本生成: ${tokenUsage.totalTokens} tokens ÷ ${apiConfig.textGenerationRatio} = ${creditsNeeded}积分`,
    };
  }

  /**
   * 计算TTS语音生成所需积分 (使用消耗比)
   */
  async calculateTTSCredits(
    tokenUsage: TokenUsage,
    quality: 'standard' | 'professional'
  ): Promise<CreditCalculationResult> {
    const apiConfig = await this.getApiPricingConfig();

    // 根据质量选择消耗比
    const consumptionRatio = quality === 'professional'
      ? apiConfig.professionalVoiceRatio
      : apiConfig.standardVoiceRatio;

    // 使用消耗比计算：积分 = ceil(tokens / 消耗比)
    const creditsNeeded = Math.ceil(tokenUsage.totalTokens / consumptionRatio);

    // 计算实际成本（仅供参考）
    const actualCost = creditsNeeded * this.creditValue;

    const serviceType = quality === 'professional' ? 'PROFESSIONAL_VOICE' : 'STANDARD_VOICE';

    return {
      creditsNeeded,
      costBreakdown: {
        consumptionRatio,
        actualCost,
      },
      tokenUsage,
      serviceType,
      explanation: `TTS${quality === 'professional' ? '高质量' : '标准'}: ${tokenUsage.totalTokens} tokens ÷ ${consumptionRatio} = ${creditsNeeded}积分`,
    };
  }

  /**
   * 获取API定价配置
   */
  private async getApiPricingConfig() {
    const config = await this.prisma.apiPricingConfig.findFirst({
      where: { isActive: true },
      orderBy: { createdAt: 'desc' },
    });

    if (!config) {
      // 返回默认配置
      return {
        textGenerationRatio: 1000,    // 1000 tokens = 1 credit
        standardVoiceRatio: 100,      // 100 tokens = 1 credit
        professionalVoiceRatio: 50,   // 50 tokens = 1 credit
      };
    }

    return {
      textGenerationRatio: config.textGenerationRatio || 1000,
      standardVoiceRatio: config.standardVoiceRatio || 100,
      professionalVoiceRatio: config.professionalVoiceRatio || 50,
    };
  }

  /**
   * 预估不同服务的积分消耗（用于前端显示）
   */
  async estimateCreditsForDisplay() {
    const apiConfig = await this.getApiPricingConfig();

    return {
      textGeneration: {
        tokensPerCredit: apiConfig.textGenerationRatio,
        creditsPerThousandTokens: Math.ceil(1000 / apiConfig.textGenerationRatio),
        description: `文本生成: ${apiConfig.textGenerationRatio} tokens = 1积分`,
      },
      ttsStandard: {
        tokensPerCredit: apiConfig.standardVoiceRatio,
        creditsPerThousandTokens: Math.ceil(1000 / apiConfig.standardVoiceRatio),
        description: `TTS标准: ${apiConfig.standardVoiceRatio} tokens = 1积分`,
      },
      ttsProfessional: {
        tokensPerCredit: apiConfig.professionalVoiceRatio,
        creditsPerThousandTokens: Math.ceil(1000 / apiConfig.professionalVoiceRatio),
        description: `TTS高质量: ${apiConfig.professionalVoiceRatio} tokens = 1积分`,
      },
    };
  }

  /**
   * 检查用户积分是否足够
   */
  async checkUserCredits(userId: string, creditsNeeded: number): Promise<{
    isEnough: boolean;
    availableCredits: number;
    shortfall: number;
  }> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { credits: true, usedCredits: true },
    });

    if (!user) {
      throw new Error('用户不存在');
    }

    const availableCredits = user.credits - user.usedCredits;
    const isEnough = availableCredits >= creditsNeeded;
    const shortfall = isEnough ? 0 : creditsNeeded - availableCredits;

    return {
      isEnough,
      availableCredits,
      shortfall,
    };
  }

  /**
   * 消耗用户积分并记录使用历史
   */
  async consumeCredits(
    userId: string, 
    calculation: CreditCalculationResult,
    description?: string
  ): Promise<void> {
    await this.prisma.$transaction(async (tx) => {
      // 更新用户已使用积分
      await tx.user.update({
        where: { id: userId },
        data: {
          usedCredits: {
            increment: calculation.creditsNeeded,
          },
        },
      });

      // 记录积分使用历史
      await tx.creditUsage.create({
        data: {
          userId,
          amount: calculation.creditsNeeded,
          serviceType: calculation.serviceType as any,
          inputTokens: 0, // 新系统不区分输入输出tokens
          outputTokens: 0,
          totalTokens: calculation.tokenUsage.totalTokens,
          description: description || calculation.explanation,
          metadata: {
            costBreakdown: calculation.costBreakdown,
            explanation: calculation.explanation,
            creditCalculationVersion: '3.0', // 标记新版本计算方式（消耗比）
          },
        },
      });
    });
  }
}
