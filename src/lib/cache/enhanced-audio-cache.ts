import { createHash } from 'crypto';

export interface EnhancedAudioCacheEntry {
  audioUrl: string;
  duration?: number;
  fileSize: number;
  characterCount: number;
  tokenCount: number;
  cost: number;
  voiceName: string;
  quality: 'fast' | 'high';
  createdAt: number;
  lastAccessed: number;
  accessCount: number;
  expiresAt: number;
}

export interface CacheKey {
  text: string;
  voiceName: string;
  quality: 'fast' | 'high';
  speed: number;
  pitch: number;
  volumeGainDb: number;
  stylePrompt?: string;
}

export interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  evictions: number;
  totalSize: number;
  avgAccessTime: number;
}

// 内存缓存存储
const memoryCache = new Map<string, EnhancedAudioCacheEntry>();
const cacheStats: CacheStats = {
  hits: 0,
  misses: 0,
  sets: 0,
  evictions: 0,
  totalSize: 0,
  avgAccessTime: 0,
};

export class EnhancedAudioCacheService {
  private readonly keyPrefix = 'enhanced_audio_cache:';
  private readonly maxCacheSize = 1000; // 最大缓存条目数
  private readonly defaultTTL = 7 * 24 * 60 * 60 * 1000; // 7天过期
  private readonly cleanupInterval = 60 * 60 * 1000; // 1小时清理一次

  constructor() {
    console.log('Enhanced Audio Cache Service initialized');
    this.startCleanupTimer();
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(cacheKey: CacheKey): string {
    // 创建一个包含所有参数的字符串
    const keyString = JSON.stringify({
      text: cacheKey.text.trim().toLowerCase(),
      voiceName: cacheKey.voiceName,
      quality: cacheKey.quality,
      speed: Math.round(cacheKey.speed * 100) / 100, // 保留2位小数
      pitch: Math.round(cacheKey.pitch * 100) / 100,
      volumeGainDb: Math.round(cacheKey.volumeGainDb * 100) / 100,
      stylePrompt: cacheKey.stylePrompt?.trim().toLowerCase() || '',
    });

    // 使用SHA-256生成稳定的哈希
    const hash = createHash('sha256').update(keyString).digest('hex').substring(0, 16);
    return `${this.keyPrefix}${hash}`;
  }

  /**
   * 检查缓存中是否存在音频
   */
  async get(cacheKey: CacheKey): Promise<EnhancedAudioCacheEntry | null> {
    const startTime = Date.now();
    
    try {
      const key = this.generateCacheKey(cacheKey);
      const cached = memoryCache.get(key);
      
      if (!cached) {
        cacheStats.misses++;
        return null;
      }

      // 检查是否过期
      if (Date.now() > cached.expiresAt) {
        memoryCache.delete(key);
        cacheStats.evictions++;
        cacheStats.misses++;
        return null;
      }

      // 更新访问统计
      cached.lastAccessed = Date.now();
      cached.accessCount++;
      memoryCache.set(key, cached);
      
      cacheStats.hits++;
      const accessTime = Date.now() - startTime;
      cacheStats.avgAccessTime = (cacheStats.avgAccessTime + accessTime) / 2;
      
      console.log(`🎯 Enhanced cache hit for ${cacheKey.voiceName}-${cacheKey.quality} (${accessTime}ms)`);
      return cached;
    } catch (error) {
      console.error('Enhanced cache get error:', error);
      cacheStats.misses++;
      return null;
    }
  }

  /**
   * 将音频信息存储到缓存
   */
  async set(cacheKey: CacheKey, audioData: Omit<EnhancedAudioCacheEntry, 'createdAt' | 'lastAccessed' | 'accessCount' | 'expiresAt'>): Promise<void> {
    try {
      const key = this.generateCacheKey(cacheKey);
      const now = Date.now();
      
      const entry: EnhancedAudioCacheEntry = {
        ...audioData,
        createdAt: now,
        lastAccessed: now,
        accessCount: 1,
        expiresAt: now + this.defaultTTL,
      };

      // 检查缓存大小限制
      if (memoryCache.size >= this.maxCacheSize) {
        this.evictLeastRecentlyUsed();
      }

      memoryCache.set(key, entry);
      cacheStats.sets++;
      cacheStats.totalSize = memoryCache.size;
      
      console.log(`💾 Enhanced cached audio for ${cacheKey.voiceName}-${cacheKey.quality} (${audioData.characterCount} chars)`);
    } catch (error) {
      console.error('Enhanced cache set error:', error);
    }
  }

  /**
   * 智能缓存预热
   */
  async preWarmCache(commonTexts: string[], voiceNames: string[], qualities: ('fast' | 'high')[]): Promise<void> {
    console.log('🔥 Starting cache pre-warming...');
    
    const preWarmTasks = [];
    
    for (const text of commonTexts) {
      for (const voiceName of voiceNames) {
        for (const quality of qualities) {
          const cacheKey: CacheKey = {
            text,
            voiceName,
            quality,
            speed: 1.0,
            pitch: 0,
            volumeGainDb: 0,
          };
          
          // 检查是否已缓存
          const existing = await this.get(cacheKey);
          if (!existing) {
            // 这里可以添加预生成逻辑
            console.log(`📋 Would pre-generate: ${text.substring(0, 50)}... with ${voiceName}`);
          }
        }
      }
    }
    
    console.log(`🔥 Cache pre-warming completed`);
  }

  /**
   * 清理过期缓存
   */
  private cleanup(): void {
    const now = Date.now();
    let evicted = 0;
    
    for (const [key, entry] of memoryCache.entries()) {
      if (now > entry.expiresAt) {
        memoryCache.delete(key);
        evicted++;
      }
    }
    
    if (evicted > 0) {
      cacheStats.evictions += evicted;
      cacheStats.totalSize = memoryCache.size;
      console.log(`🧹 Cleaned up ${evicted} expired cache entries`);
    }
  }

  /**
   * 驱逐最少使用的缓存项
   */
  private evictLeastRecentlyUsed(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();
    
    for (const [key, entry] of memoryCache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }
    
    if (oldestKey) {
      memoryCache.delete(oldestKey);
      cacheStats.evictions++;
      console.log(`🗑️ Evicted LRU cache entry`);
    }
  }

  /**
   * 启动定期清理
   */
  private startCleanupTimer(): void {
    setInterval(() => {
      this.cleanup();
    }, this.cleanupInterval);
  }

  /**
   * 获取缓存统计
   */
  getStats(): CacheStats & { hitRate: number; memoryUsage: number } {
    const totalRequests = cacheStats.hits + cacheStats.misses;
    const hitRate = totalRequests > 0 ? (cacheStats.hits / totalRequests) * 100 : 0;
    
    // 估算内存使用（简化计算）
    const avgEntrySize = 1024; // 假设每个缓存条目约1KB
    const memoryUsage = memoryCache.size * avgEntrySize;
    
    return {
      ...cacheStats,
      hitRate: Math.round(hitRate * 100) / 100,
      memoryUsage,
    };
  }

  /**
   * 清空缓存
   */
  clear(): void {
    memoryCache.clear();
    cacheStats.totalSize = 0;
    console.log('🧹 Cache cleared');
  }

  /**
   * 获取缓存大小
   */
  size(): number {
    return memoryCache.size;
  }

  /**
   * 检查特定文本是否可能被缓存
   */
  shouldCache(text: string): boolean {
    // 缓存策略：
    // 1. 文本长度适中（10-1000字符）
    // 2. 不包含时间敏感信息
    // 3. 不包含个人信息
    
    if (text.length < 10 || text.length > 1000) {
      return false;
    }
    
    // 检查是否包含时间相关词汇
    const timeKeywords = ['今天', '昨天', '明天', 'today', 'yesterday', 'tomorrow', '现在', 'now'];
    const hasTimeKeywords = timeKeywords.some(keyword => 
      text.toLowerCase().includes(keyword.toLowerCase())
    );
    
    if (hasTimeKeywords) {
      return false;
    }
    
    // 检查是否包含个人信息模式
    const personalPatterns = [
      /\b\d{11}\b/, // 手机号
      /\b\d{4}-\d{2}-\d{2}\b/, // 日期
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, // 邮箱
    ];
    
    const hasPersonalInfo = personalPatterns.some(pattern => pattern.test(text));
    
    return !hasPersonalInfo;
  }
}

// 导出单例实例
export const enhancedAudioCacheService = new EnhancedAudioCacheService();
