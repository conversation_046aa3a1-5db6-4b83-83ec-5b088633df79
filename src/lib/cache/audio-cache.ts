// 简化的音频缓存服务（不使用Redis）
export interface AudioCacheEntry {
  audioUrl: string;
  duration?: number;
  fileSize: number;
  quality: string;
  voiceName: string;
  languageCode: string;
  createdAt: number;
  lastAccessed: number;
  accessCount: number;
}

export interface AudioCacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  mostAccessed: Array<{
    key: string;
    accessCount: number;
    lastAccessed: number;
  }>;
}

// 内存缓存（仅用于开发环境）
const memoryCache = new Map<string, AudioCacheEntry>();
let cacheStats = { hits: 0, misses: 0, sets: 0 };

export class AudioCacheService {
  private readonly keyPrefix = 'audio_cache:';

  constructor() {
    console.log('AudioCacheService initialized with memory cache (Redis disabled)');
  }

  /**
   * 生成缓存键
   */
  private getCacheKey(characterName: string, languageCode: string, textHash: string): string {
    return `${this.keyPrefix}${characterName}:${languageCode}:${textHash}`;
  }

  /**
   * 生成文本哈希
   */
  private hashText(text: string): string {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 检查缓存中是否存在音频
   */
  async get(characterName: string, languageCode: string, text: string): Promise<AudioCacheEntry | null> {
    try {
      const textHash = this.hashText(text);
      const key = this.getCacheKey(characterName, languageCode, textHash);
      
      const cached = memoryCache.get(key);
      if (!cached) {
        cacheStats.misses++;
        return null;
      }

      // 更新访问统计
      cached.lastAccessed = Date.now();
      cached.accessCount++;
      memoryCache.set(key, cached);
      
      cacheStats.hits++;
      console.log(`🎯 Cache hit for ${characterName}-${languageCode}`);
      return cached;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  /**
   * 将音频信息存储到缓存
   */
  async set(
    characterName: string, 
    languageCode: string, 
    text: string, 
    audioData: AudioCacheEntry
  ): Promise<void> {
    try {
      const textHash = this.hashText(text);
      const key = this.getCacheKey(characterName, languageCode, textHash);
      
      const entry: AudioCacheEntry = {
        ...audioData,
        createdAt: Date.now(),
        lastAccessed: Date.now(),
        accessCount: 1,
      };

      memoryCache.set(key, entry);
      cacheStats.sets++;
      
      console.log(`💾 Cached audio for ${characterName}-${languageCode}`);
    } catch (error) {
      console.error('Cache set error:', error);
    }
  }

  /**
   * 删除特定缓存
   */
  async delete(characterName: string, languageCode: string, text: string): Promise<void> {
    try {
      const textHash = this.hashText(text);
      const key = this.getCacheKey(characterName, languageCode, textHash);
      
      memoryCache.delete(key);
      console.log(`🗑️  Deleted cache for ${characterName}-${languageCode}`);
    } catch (error) {
      console.error('Cache delete error:', error);
    }
  }

  /**
   * 清除所有缓存
   */
  async clear(): Promise<void> {
    try {
      const size = memoryCache.size;
      memoryCache.clear();
      cacheStats = { hits: 0, misses: 0, sets: 0 };
      console.log(`🧹 Cleared ${size} cache entries`);
    } catch (error) {
      console.error('Cache clear error:', error);
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getStats(): Promise<AudioCacheStats> {
    try {
      let totalSize = 0;
      const accessData: Array<{ key: string; accessCount: number; lastAccessed: number }> = [];

      for (const [key, entry] of memoryCache.entries()) {
        totalSize += entry.fileSize;
        accessData.push({
          key: key.replace(this.keyPrefix, ''),
          accessCount: entry.accessCount,
          lastAccessed: entry.lastAccessed,
        });
      }

      const mostAccessed = accessData
        .sort((a, b) => b.accessCount - a.accessCount)
        .slice(0, 10);

      const { hits, misses } = cacheStats;
      const hitRate = hits + misses > 0 ? hits / (hits + misses) : 0;

      return {
        totalEntries: memoryCache.size,
        totalSize,
        hitRate,
        mostAccessed,
      };
    } catch (error) {
      console.error('Cache stats error:', error);
      return {
        totalEntries: 0,
        totalSize: 0,
        hitRate: 0,
        mostAccessed: [],
      };
    }
  }

  /**
   * 清理过期缓存
   */
  async cleanup(): Promise<void> {
    try {
      const now = Date.now();
      const maxAge = 30 * 24 * 60 * 60 * 1000; // 30天
      let cleanedCount = 0;

      for (const [key, entry] of memoryCache.entries()) {
        if (now - entry.lastAccessed > maxAge) {
          memoryCache.delete(key);
          cleanedCount++;
        }
      }

      console.log(`🧹 Cleaned up ${cleanedCount} expired cache entries`);
    } catch (error) {
      console.error('Cache cleanup error:', error);
    }
  }

  /**
   * 关闭连接（内存缓存无需关闭）
   */
  async disconnect(): Promise<void> {
    // 内存缓存无需关闭连接
  }
}

// 单例实例
export const audioCacheService = new AudioCacheService();
