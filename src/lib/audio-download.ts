/**
 * Utility functions for downloading audio files with CORS handling
 */

export interface DownloadOptions {
  filename?: string;
  onSuccess?: (filename: string) => void;
  onError?: (error: Error) => void;
  showToast?: boolean;
}

/**
 * Download audio file with automatic CORS handling
 * @param audioUrl - The URL of the audio file to download
 * @param options - Download options
 */
export async function downloadAudio(audioUrl: string, options: DownloadOptions = {}) {
  const {
    filename = `audio-${Date.now()}.wav`,
    onSuccess,
    onError,
    showToast = false
  } = options;

  if (!audioUrl) {
    const error = new Error('音频文件不存在');
    onError?.(error);
    return;
  }

  try {
    // 检查是否为data URL
    if (audioUrl.startsWith('data:audio/')) {
      // 对于data URL，直接创建下载链接
      const link = document.createElement('a');
      link.href = audioUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      onSuccess?.(filename);
      return;
    }

    // 方法1: 使用代理API下载（适用于R2存储的文件）
    const proxyUrl = `/api/audio/download?url=${encodeURIComponent(audioUrl)}`;
    const response = await fetch(proxyUrl);
    
    if (response.ok) {
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // 尝试从响应头获取文件名
      const contentDisposition = response.headers.get('content-disposition');
      let finalFilename = filename;
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          finalFilename = filenameMatch[1] || filename;
        }
      }
      
      link.download = finalFilename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      onSuccess?.(finalFilename);
      return;
    }
  } catch (error) {
    console.warn('代理下载失败，尝试直接下载:', error);
  }

  try {
    // 方法2: 直接下载（适用于大多数情况）
    const link = document.createElement('a');
    link.href = audioUrl;
    link.download = filename;
    link.target = '_blank';
    link.rel = 'noopener noreferrer';

    // 添加到DOM并点击
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    onSuccess?.(filename);
  } catch (error) {
    console.error('直接下载失败:', error);

    // 方法3: 在新窗口打开（最后的回退方案）
    try {
      window.open(audioUrl, '_blank');
      onSuccess?.(filename);
    } catch (finalError) {
      console.error('所有下载方法都失败:', finalError);
      const error = new Error('下载失败，请检查网络连接或联系管理员');
      onError?.(error);
    }
  }
}

/**
 * Check if an audio URL is accessible (for CORS testing)
 * @param audioUrl - The URL to test
 * @returns Promise<boolean> - Whether the URL is accessible
 */
export async function isAudioUrlAccessible(audioUrl: string): Promise<boolean> {
  try {
    if (audioUrl.startsWith('data:audio/')) {
      return true; // Data URLs are always accessible
    }

    const response = await fetch(audioUrl, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.warn('Audio URL accessibility check failed:', error);
    return false;
  }
}

/**
 * Get audio file info without downloading
 * @param audioUrl - The URL of the audio file
 * @returns Promise with file info or null if failed
 */
export async function getAudioFileInfo(audioUrl: string): Promise<{
  size?: number;
  type?: string;
  accessible: boolean;
} | null> {
  try {
    if (audioUrl.startsWith('data:audio/')) {
      // For data URLs, we can't get size easily, but they're accessible
      const mimeMatch = audioUrl.match(/^data:([^;]+)/);
      return {
        type: mimeMatch?.[1] || 'audio/wav',
        accessible: true
      };
    }

    const response = await fetch(audioUrl, { method: 'HEAD' });
    
    if (!response.ok) {
      return { accessible: false };
    }

    const size = response.headers.get('content-length');
    const type = response.headers.get('content-type');

    return {
      size: size ? parseInt(size, 10) : undefined,
      type: type || undefined,
      accessible: true
    };
  } catch (error) {
    console.warn('Failed to get audio file info:', error);
    return { accessible: false };
  }
}

/**
 * Format file size for display
 * @param bytes - File size in bytes
 * @returns Formatted string
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
