/**
 * 音频处理工具类
 * 专门处理 WAV 音频文件的解析、合并和生成
 */

export interface WavHeader {
  sampleRate: number;
  channels: number;
  bitsPerSample: number;
  dataSize: number;
}

export interface AudioSegment {
  header: WavHeader;
  data: Buffer;
}

export class AudioProcessor {
  /**
   * 解析 WAV 文件头部信息
   */
  static parseWavHeader(buffer: Buffer): WavHeader {
    if (buffer.length < 44) {
      throw new Error('Invalid WAV file: too short');
    }

    // 检查 RIFF 标识
    if (buffer.toString('ascii', 0, 4) !== 'RIFF') {
      throw new Error('Invalid WAV file: missing RIFF header');
    }

    // 检查 WAVE 标识
    if (buffer.toString('ascii', 8, 12) !== 'WAVE') {
      throw new Error('Invalid WAV file: missing WAVE header');
    }

    // 检查 fmt 标识
    if (buffer.toString('ascii', 12, 16) !== 'fmt ') {
      throw new Error('Invalid WAV file: missing fmt header');
    }

    // 读取音频参数
    const channels = buffer.readUInt16LE(22);
    const sampleRate = buffer.readUInt32LE(24);
    const bitsPerSample = buffer.readUInt16LE(34);

    // 查找 data 块
    let dataOffset = 36;
    while (dataOffset < buffer.length - 8) {
      const chunkId = buffer.toString('ascii', dataOffset, dataOffset + 4);
      const chunkSize = buffer.readUInt32LE(dataOffset + 4);
      
      if (chunkId === 'data') {
        return {
          sampleRate,
          channels,
          bitsPerSample,
          dataSize: chunkSize
        };
      }
      
      dataOffset += 8 + chunkSize;
    }

    throw new Error('Invalid WAV file: missing data chunk');
  }

  /**
   * 提取 WAV 文件的音频数据（不包含头部）
   */
  static extractWavData(buffer: Buffer): Buffer {
    if (buffer.length < 44) {
      throw new Error('Invalid WAV file: too short');
    }

    // 查找 data 块
    let dataOffset = 36;
    while (dataOffset < buffer.length - 8) {
      const chunkId = buffer.toString('ascii', dataOffset, dataOffset + 4);
      const chunkSize = buffer.readUInt32LE(dataOffset + 4);
      
      if (chunkId === 'data') {
        const dataStart = dataOffset + 8;
        return buffer.slice(dataStart, dataStart + chunkSize);
      }
      
      dataOffset += 8 + chunkSize;
    }

    throw new Error('Invalid WAV file: missing data chunk');
  }

  /**
   * 创建 WAV 文件头部
   */
  static createWavHeader(dataSize: number, options: {
    sampleRate: number;
    channels: number;
    bitsPerSample: number;
  }): Buffer {
    const { sampleRate, channels, bitsPerSample } = options;
    const byteRate = sampleRate * channels * bitsPerSample / 8;
    const blockAlign = channels * bitsPerSample / 8;
    const header = Buffer.alloc(44);

    // RIFF 头部
    header.write('RIFF', 0);
    header.writeUInt32LE(36 + dataSize, 4); // 文件大小 - 8
    header.write('WAVE', 8);

    // fmt 块
    header.write('fmt ', 12);
    header.writeUInt32LE(16, 16); // fmt 块大小
    header.writeUInt16LE(1, 20);  // 音频格式 (PCM)
    header.writeUInt16LE(channels, 22);
    header.writeUInt32LE(sampleRate, 24);
    header.writeUInt32LE(byteRate, 28);
    header.writeUInt16LE(blockAlign, 32);
    header.writeUInt16LE(bitsPerSample, 34);

    // data 块头部
    header.write('data', 36);
    header.writeUInt32LE(dataSize, 40);

    return header;
  }

  /**
   * 生成静音 WAV 音频
   */
  static generateSilenceWav(durationSeconds: number, options: {
    sampleRate?: number;
    channels?: number;
    bitsPerSample?: number;
  } = {}): Buffer {
    const {
      sampleRate = 24000,
      channels = 1,
      bitsPerSample = 16
    } = options;

    const bytesPerSample = bitsPerSample / 8;
    const numSamples = Math.floor(sampleRate * durationSeconds);
    const dataSize = numSamples * channels * bytesPerSample;

    // 创建静音数据（全零）
    const silenceData = Buffer.alloc(dataSize, 0);

    // 创建 WAV 头部
    const header = this.createWavHeader(dataSize, {
      sampleRate,
      channels,
      bitsPerSample
    });

    return Buffer.concat([header, silenceData]);
  }

  /**
   * 合并多个 WAV 音频文件
   */
  static mergeWavFiles(wavBuffers: Buffer[]): Buffer {
    if (wavBuffers.length === 0) {
      throw new Error('No audio buffers to merge');
    }

    if (wavBuffers.length === 1) {
      return wavBuffers[0];
    }

    console.log(`🔗 开始合并 ${wavBuffers.length} 个 WAV 文件`);

    // 解析第一个文件的参数作为标准
    const firstHeader = this.parseWavHeader(wavBuffers[0]);
    console.log(`📊 音频参数: ${firstHeader.sampleRate}Hz, ${firstHeader.channels}ch, ${firstHeader.bitsPerSample}bit`);

    const audioDataSegments: Buffer[] = [];
    let totalDataSize = 0;

    // 提取所有音频数据并验证格式一致性
    for (let i = 0; i < wavBuffers.length; i++) {
      try {
        const header = this.parseWavHeader(wavBuffers[i]);
        
        // 验证音频参数一致性
        if (header.sampleRate !== firstHeader.sampleRate ||
            header.channels !== firstHeader.channels ||
            header.bitsPerSample !== firstHeader.bitsPerSample) {
          console.warn(`⚠️ 音频参数不一致 (片段 ${i + 1}): ${header.sampleRate}Hz, ${header.channels}ch, ${header.bitsPerSample}bit`);
          // 这里可以添加重采样逻辑，暂时跳过不一致的片段
          continue;
        }

        const audioData = this.extractWavData(wavBuffers[i]);
        audioDataSegments.push(audioData);
        totalDataSize += audioData.length;
        
        console.log(`📦 片段 ${i + 1}: ${audioData.length} 字节`);
      } catch (error) {
        console.error(`❌ 解析音频片段 ${i + 1} 失败:`, error);
        // 跳过损坏的片段
        continue;
      }
    }

    if (audioDataSegments.length === 0) {
      throw new Error('No valid audio segments found');
    }

    // 合并音频数据
    const mergedAudioData = Buffer.concat(audioDataSegments);
    console.log(`✅ 合并完成，总大小: ${mergedAudioData.length} 字节`);

    // 创建新的 WAV 头部
    const newHeader = this.createWavHeader(mergedAudioData.length, {
      sampleRate: firstHeader.sampleRate,
      channels: firstHeader.channels,
      bitsPerSample: firstHeader.bitsPerSample
    });

    return Buffer.concat([newHeader, mergedAudioData]);
  }

  /**
   * 验证 WAV 文件的完整性
   */
  static validateWavFile(buffer: Buffer): boolean {
    try {
      this.parseWavHeader(buffer);
      return true;
    } catch (error) {
      console.error('WAV 文件验证失败:', error);
      return false;
    }
  }

  /**
   * 获取 WAV 文件的时长（秒）
   */
  static getWavDuration(buffer: Buffer): number {
    try {
      const header = this.parseWavHeader(buffer);
      const bytesPerSample = header.bitsPerSample / 8;
      const bytesPerSecond = header.sampleRate * header.channels * bytesPerSample;
      return header.dataSize / bytesPerSecond;
    } catch (error) {
      console.error('计算音频时长失败:', error);
      return 0;
    }
  }
}
