/**
 * 多语言工具函数
 * 用于处理角色信息的多语言支持
 */

// 支持的语言代码类型
export type LanguageCode = 'en-US' | 'zh-CN' | 'km-KH' | string;

// 多语言内容接口
export interface MultilingualContent {
  [languageCode: string]: string;
}

// 角色多语言字段接口
export interface CharacterMultilingualFields {
  multilingualNames?: string | null;
  multilingualDescriptions?: string | null;
  multilingualStyles?: string | null;
  multilingualPersonalities?: string | null;
  multilingualBestFor?: string | null;
}

/**
 * 从JSON字符串中获取指定语言的内容
 * @param jsonString - 存储多语言内容的JSON字符串
 * @param languageCode - 目标语言代码
 * @param fallback - 如果找不到对应语言内容时的回退值
 * @returns 本土化的内容
 */
export function getLocalizedContent(
  jsonString: string | null | undefined,
  languageCode: string,
  fallback: string = ''
): string {
  if (!jsonString) {
    return fallback;
  }

  try {
    const content: MultilingualContent = JSON.parse(jsonString);
    
    // 首先尝试精确匹配
    if (content[languageCode]) {
      return content[languageCode];
    }
    
    // 尝试语言前缀匹配（如 en-US -> en）
    const languagePrefix = languageCode.split('-')[0];
    const matchingKey = Object.keys(content).find(key => 
      key.startsWith(languagePrefix + '-')
    );
    
    if (matchingKey && content[matchingKey]) {
      return content[matchingKey];
    }
    
    // 回退到默认语言（中文）
    if (content['zh-CN']) {
      return content['zh-CN'];
    }
    
    // 回退到英文
    if (content['en-US']) {
      return content['en-US'];
    }
    
    // 返回第一个可用的值
    const firstValue = Object.values(content)[0];
    if (firstValue) {
      return firstValue;
    }
    
    return fallback;
  } catch (error) {
    console.error('Error parsing multilingual content:', error);
    return fallback;
  }
}

/**
 * 设置多语言内容
 * @param existingJsonString - 现有的JSON字符串
 * @param languageCode - 语言代码
 * @param content - 要设置的内容
 * @returns 更新后的JSON字符串
 */
export function setLocalizedContent(
  existingJsonString: string | null | undefined,
  languageCode: string,
  content: string
): string {
  let existingContent: MultilingualContent = {};
  
  if (existingJsonString) {
    try {
      existingContent = JSON.parse(existingJsonString);
    } catch (error) {
      console.error('Error parsing existing multilingual content:', error);
    }
  }
  
  existingContent[languageCode] = content;
  return JSON.stringify(existingContent);
}

/**
 * 获取角色的本土化名称
 */
export function getLocalizedCharacterName(
  character: { characterName: string; characterNameEn?: string; multilingualNames?: string | null },
  languageCode: string
): string {
  return getLocalizedContent(
    character.multilingualNames,
    languageCode,
    character.characterName
  );
}

/**
 * 获取角色的本土化描述
 */
export function getLocalizedDescription(
  character: { description: string; multilingualDescriptions?: string | null },
  languageCode: string
): string {
  return getLocalizedContent(
    character.multilingualDescriptions,
    languageCode,
    character.description
  );
}

/**
 * 获取角色的本土化风格
 */
export function getLocalizedStyle(
  character: { style?: string | null; multilingualStyles?: string | null },
  languageCode: string
): string {
  return getLocalizedContent(
    character.multilingualStyles,
    languageCode,
    character.style || ''
  );
}

/**
 * 获取角色的本土化个性
 */
export function getLocalizedPersonality(
  character: { personality?: string | null; multilingualPersonalities?: string | null },
  languageCode: string
): string {
  return getLocalizedContent(
    character.multilingualPersonalities,
    languageCode,
    character.personality || ''
  );
}

/**
 * 获取角色的本土化最适合用途
 */
export function getLocalizedBestFor(
  character: { bestFor?: string | null; multilingualBestFor?: string | null },
  languageCode: string
): string {
  return getLocalizedContent(
    character.multilingualBestFor,
    languageCode,
    character.bestFor || ''
  );
}

/**
 * 获取性别的本土化显示
 */
export function getLocalizedGender(gender: string, languageCode: string): string {
  const genderMap: Record<string, MultilingualContent> = {
    FEMALE: {
      'zh-CN': '女性',
      'en-US': 'Female',
      'km-KH': 'ស្រី'
    },
    MALE: {
      'zh-CN': '男性',
      'en-US': 'Male',
      'km-KH': 'ប្រុស'
    },
    NEUTRAL: {
      'zh-CN': '中性',
      'en-US': 'Neutral',
      'km-KH': 'អព្យាកៃ'
    }
  };
  
  const content = genderMap[gender];
  if (!content) {
    return gender;
  }
  
  return getLocalizedContent(JSON.stringify(content), languageCode, gender);
}

/**
 * 获取API提供商的本土化显示
 */
export function getLocalizedApiProvider(provider: string, languageCode: string): string {
  const providerMap: Record<string, MultilingualContent> = {
    GEMINI: {
      'zh-CN': 'Gemini',
      'en-US': 'Gemini',
      'km-KH': 'Gemini'
    },
    OPENAI: {
      'zh-CN': 'OpenAI',
      'en-US': 'OpenAI',
      'km-KH': 'OpenAI'
    },
    AZURE: {
      'zh-CN': 'Azure',
      'en-US': 'Azure',
      'km-KH': 'Azure'
    },
    ELEVENLABS: {
      'zh-CN': 'ElevenLabs',
      'en-US': 'ElevenLabs',
      'km-KH': 'ElevenLabs'
    }
  };
  
  const content = providerMap[provider];
  if (!content) {
    return provider;
  }
  
  return getLocalizedContent(JSON.stringify(content), languageCode, provider);
}

/**
 * 批量本土化角色信息
 */
export function localizeCharacter<T extends CharacterMultilingualFields & {
  characterName: string;
  description: string;
  style?: string | null;
  personality?: string | null;
  bestFor?: string | null;
  gender: string;
  apiProvider: string;
}>(character: T, languageCode: string): T & {
  localizedName: string;
  localizedDescription: string;
  localizedStyle: string;
  localizedPersonality: string;
  localizedBestFor: string;
  localizedGender: string;
  localizedApiProvider: string;
} {
  return {
    ...character,
    localizedName: getLocalizedCharacterName(character, languageCode),
    localizedDescription: getLocalizedDescription(character, languageCode),
    localizedStyle: getLocalizedStyle(character, languageCode),
    localizedPersonality: getLocalizedPersonality(character, languageCode),
    localizedBestFor: getLocalizedBestFor(character, languageCode),
    localizedGender: getLocalizedGender(character.gender, languageCode),
    localizedApiProvider: getLocalizedApiProvider(character.apiProvider, languageCode),
  };
}
