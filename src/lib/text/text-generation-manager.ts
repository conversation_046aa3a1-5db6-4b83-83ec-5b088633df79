import { 
  GeminiTextGenerator, 
  type SingleTextOptions, 
  type ConversationOptions,
  type TextGenerationResult 
} from './gemini-text-generator';
import type { PrismaClient } from '@prisma/client';

export interface TextGenerationManagerOptions {
  userId: string;
  type: 'SINGLE' | 'CONVERSATION';
  options: SingleTextOptions | ConversationOptions;
  languageCode?: string;
}

export interface TextGenerationRecord {
  id: string;
  text: string;
  tokenCount: number;
  cost: number;
  createdAt: Date;
}

export class TextGenerationManager {
  private geminiGenerator: GeminiTextGenerator;

  constructor() {
    this.geminiGenerator = new GeminiTextGenerator();
  }

  async generateText(
    params: TextGenerationManagerOptions,
    prisma: PrismaClient
  ): Promise<TextGenerationRecord> {
    const { userId, type, options, languageCode } = params;

    // 验证参数
    if (type === 'SINGLE') {
      const validation = GeminiTextGenerator.validateSingleTextOptions(options as SingleTextOptions);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }
    } else {
      const validation = GeminiTextGenerator.validateConversationOptions(options as ConversationOptions);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }
    }

    // 估算token使用量
    const estimatedTokens = this.estimateTokenUsage(type, options);
    
    // 检查用户积分
    await this.checkUserCredits(userId, estimatedTokens, prisma);

    // 生成文本
    let result: TextGenerationResult;
    try {
      if (type === 'SINGLE') {
        result = await this.geminiGenerator.generateSingleText(options as SingleTextOptions);
      } else {
        result = await this.geminiGenerator.generateConversation(options as ConversationOptions);
      }
    } catch (error) {
      console.error('Text generation failed:', error);
      throw new Error('文本生成失败，请稍后重试');
    }

    // 保存生成记录
    const textGeneration = await prisma.textGeneration.create({
      data: {
        userId,
        type,
        prompt: this.buildPromptSummary(type, options),
        generatedText: result.text,
        parameters: options as any,
        tokenCount: result.tokenCount,
        cost: result.cost,
        languageCode,
      },
    });

    // 消耗用户积分
    await this.consumeUserCredits(userId, result.tokenCount, prisma);

    // 更新每日使用统计
    await this.updateDailyUsage(userId, result.tokenCount, prisma);

    return {
      id: textGeneration.id,
      text: result.text,
      tokenCount: result.tokenCount,
      cost: result.cost,
      createdAt: textGeneration.createdAt,
    };
  }

  private estimateTokenUsage(type: 'SINGLE' | 'CONVERSATION', options: SingleTextOptions | ConversationOptions): number {
    if (type === 'SINGLE') {
      const singleOptions = options as SingleTextOptions;
      const lengthMap = { short: 800, medium: 1200, long: 1800 };
      return lengthMap[singleOptions.length];
    } else {
      const convOptions = options as ConversationOptions;
      const lengthMap = { short: 600, medium: 1000, long: 1500 };
      return lengthMap[convOptions.length] * convOptions.participantCount * 0.5;
    }
  }

  private async checkUserCredits(userId: string, estimatedTokens: number, prisma: PrismaClient): Promise<void> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        credits: true,
        usedCredits: true,
      },
    });

    if (!user) {
      throw new Error('用户不存在');
    }

    // 文本生成积分消耗：1积分 = 100 tokens
    const creditsPerToken = 0.01;
    const requiredCredits = Math.ceil(estimatedTokens * creditsPerToken);
    const availableCredits = user.credits - user.usedCredits;

    if (availableCredits < requiredCredits) {
      throw new Error(`积分不足。需要: ${requiredCredits} 积分，剩余: ${availableCredits} 积分`);
    }
  }

  private async consumeUserCredits(userId: string, actualTokens: number, prisma: PrismaClient): Promise<void> {
    // 文本生成积分消耗：1积分 = 100 tokens
    const creditsPerToken = 0.01;
    const requiredCredits = Math.ceil(actualTokens * creditsPerToken);

    // 使用事务确保数据一致性
    await prisma.$transaction(async (tx) => {
      // 更新用户已使用积分
      await tx.user.update({
        where: { id: userId },
        data: {
          usedCredits: {
            increment: requiredCredits,
          },
        },
      });

      // 记录积分使用历史
      await tx.creditUsage.create({
        data: {
          userId,
          amount: requiredCredits,
          type: 'TEXT_GENERATION',
          description: `AI文本生成，${actualTokens} tokens`,
          metadata: {
            tokens: actualTokens,
            creditsPerToken,
          },
        },
      });
    });
  }

  private async updateDailyUsage(userId: string, tokenCount: number, prisma: PrismaClient): Promise<void> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    await prisma.textGenerationUsage.upsert({
      where: {
        userId_date: {
          userId,
          date: today,
        },
      },
      update: {
        tokenCount: {
          increment: tokenCount,
        },
        generationCount: {
          increment: 1,
        },
      },
      create: {
        userId,
        date: today,
        tokenCount,
        generationCount: 1,
      },
    });
  }

  private buildPromptSummary(type: 'SINGLE' | 'CONVERSATION', options: SingleTextOptions | ConversationOptions): string {
    if (type === 'SINGLE') {
      const singleOptions = options as SingleTextOptions;
      return `类型: ${singleOptions.type}, 主题: ${singleOptions.topic}, 长度: ${singleOptions.length}, 风格: ${singleOptions.style}`;
    } else {
      const convOptions = options as ConversationOptions;
      return `场景: ${convOptions.scenario}, 人数: ${convOptions.participantCount}, 类型: ${convOptions.conversationType}, 长度: ${convOptions.length}`;
    }
  }

  // 获取用户文本生成历史
  async getUserTextGenerations(
    userId: string,
    limit: number = 10,
    offset: number = 0,
    prisma: PrismaClient
  ) {
    return prisma.textGeneration.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
      select: {
        id: true,
        type: true,
        prompt: true,
        generatedText: true,
        tokenCount: true,
        cost: true,
        languageCode: true,
        createdAt: true,
      },
    });
  }

  // 获取用户文本生成统计
  async getUserTextGenerationStats(userId: string, prisma: PrismaClient) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        credits: true,
        usedCredits: true,
      },
    });

    if (!user) {
      throw new Error('用户不存在');
    }

    // 获取本月统计
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const monthlyStats = await prisma.textGenerationUsage.aggregate({
      where: {
        userId,
        date: {
          gte: startOfMonth,
        },
      },
      _sum: {
        tokenCount: true,
        generationCount: true,
      },
    });

    // 获取今日统计
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todayStats = await prisma.textGenerationUsage.findUnique({
      where: {
        userId_date: {
          userId,
          date: today,
        },
      },
      select: {
        tokenCount: true,
        generationCount: true,
      },
    });

    return {
      quota: {
        total: user.textGenerationQuota,
        used: user.usedTextGenerationQuota,
        remaining: user.textGenerationQuota - user.usedTextGenerationQuota,
      },
      monthlyStats: {
        tokensUsed: monthlyStats._sum.tokenCount || 0,
        generationsCount: monthlyStats._sum.generationCount || 0,
      },
      todayStats: {
        tokensUsed: todayStats?.tokenCount || 0,
        generationsCount: todayStats?.generationCount || 0,
      },
    };
  }

  // 检查用户是否有足够配额
  static async checkQuota(userId: string, estimatedTokens: number, prisma: PrismaClient): Promise<boolean> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        textGenerationQuota: true,
        usedTextGenerationQuota: true,
      },
    });

    if (!user) {
      return false;
    }

    const remainingQuota = user.textGenerationQuota - user.usedTextGenerationQuota;
    return remainingQuota >= estimatedTokens;
  }
}
