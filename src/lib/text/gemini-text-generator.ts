import { GoogleGenerativeAI } from '@google/generative-ai';
import { env } from '~/env';

export interface SingleTextOptions {
  type: 'speech' | 'story' | 'news' | 'product' | 'announcement' | 'tutorial';
  topic: string;
  language: string;
  length: 'short' | 'medium' | 'long';
  style: 'formal' | 'casual' | 'humorous' | 'professional' | 'friendly';
}

export interface ConversationOptions {
  scenario: string;
  participantCount: number;
  conversationType: 'business' | 'casual' | 'customer_service' | 'interview' | 'education';
  language: string;
  length: 'short' | 'medium' | 'long';
}

export interface TextGenerationResult {
  text: string;
  tokenCount: number;
  cost: number;
  inputTokens?: number;
  outputTokens?: number;
}

export class GeminiTextGenerator {
  private client: GoogleGenerativeAI;
  private lastRequestTime: number = 0;
  private minRequestInterval: number = 1000; // 1秒间隔

  constructor() {
    console.log('🔧 Initializing Gemini Text Generator...');
    this.client = new GoogleGenerativeAI(env.GEMINI_API_KEY);
  }

  private async waitForRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.minRequestInterval) {
      const waitTime = this.minRequestInterval - timeSinceLastRequest;
      console.log(`⏳ Rate limiting: waiting ${waitTime}ms...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.lastRequestTime = Date.now();
  }

  async generateSimpleText(prompt: string): Promise<TextGenerationResult> {
    const fullPrompt = this.buildSimpleTextPrompt(prompt);
    return this.generateText(fullPrompt);
  }

  async generateSimpleConversation(prompt: string): Promise<TextGenerationResult> {
    const fullPrompt = this.buildSimpleConversationPrompt(prompt);
    return this.generateText(fullPrompt);
  }

  async generateSingleText(options: SingleTextOptions): Promise<TextGenerationResult> {
    const prompt = this.buildSingleTextPrompt(options);
    return this.generateText(prompt);
  }

  async generateConversation(options: ConversationOptions): Promise<TextGenerationResult> {
    const prompt = this.buildConversationPrompt(options);
    return this.generateText(prompt);
  }

  private async generateText(prompt: string): Promise<TextGenerationResult> {
    await this.waitForRateLimit();

    try {
      console.log('🤖 Generating text with Gemini 2.5 Flash...');
      
      const model = this.client.getGenerativeModel({
        model: 'gemini-2.5-flash',
        generationConfig: {
          temperature: 0.7,
          topP: 0.8,
          topK: 40,
          maxOutputTokens: 4000,
        }
      });

      const result = await model.generateContent(prompt);
      const response = result.response;
      const text = response.text();

      if (!text) {
        throw new Error('No text generated');
      }

      // 使用API提供的真实token使用信息
      const usageMetadata = response.usageMetadata;
      const promptTokenCount = usageMetadata?.promptTokenCount || 0;
      const candidatesTokenCount = usageMetadata?.candidatesTokenCount || 0;
      const totalTokenCount = usageMetadata?.totalTokenCount || 0;

      // 如果API没有提供token信息，则回退到估算
      const tokenCount = totalTokenCount > 0 ? totalTokenCount : this.estimateTokens(text);
      const cost = this.calculateCost(tokenCount);

      console.log(`✅ Text generation successful: ${tokenCount} tokens (input: ${promptTokenCount}, output: ${candidatesTokenCount}), $${cost.toFixed(6)}`);

      return {
        text: text.trim(),
        tokenCount,
        cost,
        inputTokens: promptTokenCount,
        outputTokens: candidatesTokenCount,
      };
    } catch (error) {
      console.error('❌ Text generation failed:', error);
      throw new Error('文本生成失败，请稍后重试');
    }
  }

  private buildSimpleTextPrompt(userPrompt: string): string {
    return `你是一个专业的文本创作助手。请根据用户的描述生成适合语音转换的文本内容。

用户描述：${userPrompt}

要求：
1. 生成300-600字的中文文本
2. 语言自然流畅，适合朗读
3. 内容结构清晰，逻辑连贯
4. 语调友好亲切
5. 直接生成文本内容，不要包含标题或额外说明

请开始生成：`;
  }

  private buildSimpleConversationPrompt(userPrompt: string): string {
    return `你是一个专业的对话脚本创作助手。请根据用户的描述生成自然的多人对话内容。

用户描述：${userPrompt}

要求：
1. 生成2-3人的对话，8-15轮对话
2. 对话自然真实，符合日常交流习惯
3. 每个说话者有明确的身份和性格
4. 使用"姓名：对话内容"的格式
5. 对话内容适合语音转换
6. 直接生成对话脚本，不要包含额外说明

请开始生成对话：`;
  }

  private buildSingleTextPrompt(options: SingleTextOptions): string {
    const { type, topic, language, length, style } = options;
    
    const lengthMap = {
      short: '300-500字',
      medium: '500-800字', 
      long: '800-1200字'
    };

    const typeMap = {
      speech: '演讲稿',
      story: '故事',
      news: '新闻稿',
      product: '产品介绍',
      announcement: '公告',
      tutorial: '教程'
    };

    const styleMap = {
      formal: '正式',
      casual: '轻松',
      humorous: '幽默',
      professional: '专业',
      friendly: '友好'
    };

    return `你是一个专业的${typeMap[type]}撰写助手。请根据以下要求创作一篇${typeMap[type]}：

主题：${topic}
语言：${language}
长度：${lengthMap[length]}
风格：${styleMap[style]}

要求：
1. 内容要有逻辑性和连贯性
2. 语言要符合指定风格
3. 适合语音朗读，避免过于复杂的句式
4. 内容积极正面，避免敏感话题
5. 如果是演讲稿，要有开头、主体、结尾的结构
6. 如果是故事，要有完整的情节发展
7. 如果是新闻稿，要有标题和正文结构

请直接生成${typeMap[type]}内容，不要包含额外的说明文字：`;
  }

  private buildConversationPrompt(options: ConversationOptions): string {
    const { scenario, participantCount, conversationType, language, length } = options;
    
    const lengthMap = {
      short: '8-12轮',
      medium: '12-20轮',
      long: '20-30轮'
    };

    const typeMap = {
      business: '商务会议',
      casual: '朋友聊天',
      customer_service: '客服对话',
      interview: '面试对话',
      education: '教学对话'
    };

    return `你是一个专业的对话脚本创作助手。请根据以下要求生成多人对话：

场景：${scenario}
参与人数：${participantCount}人
对话类型：${typeMap[conversationType]}
语言：${language}
对话长度：约${lengthMap[length]}对话

要求：
1. 每个说话者要有明确的身份和特点
2. 对话自然流畅，符合场景设定
3. 严格使用格式：说话者名称：对话内容
4. 每次发言控制在1-2句话，避免过长独白
5. 体现不同说话者的语言风格差异
6. 对话要有明确的开始、发展和结束
7. 内容积极正面，避免争吵或冲突
8. 说话者名称要简洁明了（如：张经理、李客户、小王等）

请直接生成对话脚本，不要包含额外的说明文字：`;
  }

  private estimateTokens(text: string): number {
    // 简单的token估算：中文字符约1.5个token，英文单词约1个token
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    const otherChars = text.length - chineseChars - englishWords;
    
    return Math.ceil(chineseChars * 1.5 + englishWords + otherChars * 0.5);
  }

  private calculateCost(tokenCount: number): number {
    // Gemini 2.5 Flash定价：每100万token约$0.075
    const pricePerToken = 0.000000075;
    return tokenCount * pricePerToken;
  }

  // 验证生成参数
  static validateSingleTextOptions(options: SingleTextOptions): { isValid: boolean; error?: string } {
    if (!options.topic || options.topic.trim().length === 0) {
      return { isValid: false, error: '主题不能为空' };
    }

    if (options.topic.length > 200) {
      return { isValid: false, error: '主题长度不能超过200字符' };
    }

    const validTypes = ['speech', 'story', 'news', 'product', 'announcement', 'tutorial'];
    if (!validTypes.includes(options.type)) {
      return { isValid: false, error: '无效的文本类型' };
    }

    const validLengths = ['short', 'medium', 'long'];
    if (!validLengths.includes(options.length)) {
      return { isValid: false, error: '无效的长度选项' };
    }

    const validStyles = ['formal', 'casual', 'humorous', 'professional', 'friendly'];
    if (!validStyles.includes(options.style)) {
      return { isValid: false, error: '无效的风格选项' };
    }

    return { isValid: true };
  }

  static validateConversationOptions(options: ConversationOptions): { isValid: boolean; error?: string } {
    if (!options.scenario || options.scenario.trim().length === 0) {
      return { isValid: false, error: '场景描述不能为空' };
    }

    if (options.scenario.length > 300) {
      return { isValid: false, error: '场景描述长度不能超过300字符' };
    }

    if (options.participantCount < 2 || options.participantCount > 6) {
      return { isValid: false, error: '参与人数必须在2-6人之间' };
    }

    const validTypes = ['business', 'casual', 'customer_service', 'interview', 'education'];
    if (!validTypes.includes(options.conversationType)) {
      return { isValid: false, error: '无效的对话类型' };
    }

    const validLengths = ['short', 'medium', 'long'];
    if (!validLengths.includes(options.length)) {
      return { isValid: false, error: '无效的长度选项' };
    }

    return { isValid: true };
  }
}
