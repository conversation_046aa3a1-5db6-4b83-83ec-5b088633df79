import { db } from '~/server/db';
import { GEMINI_VOICES } from './tts/gemini-tts';
import type { ApiProvider, Gender, Tier } from '@prisma/client';

export interface CharacterTemplate {
  characterName: string;
  characterNameEn: string;
  apiProvider: ApiProvider;
  apiVoiceName: string;
  gender: Gender;
  description: string;
  tier: Tier;
  language?: string;
  style?: string;
  customPrompt?: string;
}

export interface CustomCharacterOptions {
  baseVoice: string;
  style: string;
  language: string;
  personality: string;
  speed: number;
  pitch: number;
  customInstructions?: string;
}

export class CharacterManager {
  
  // 获取所有可用的Gemini语音选项
  static getGeminiVoiceOptions() {
    return GEMINI_VOICES.map(voice => ({
      value: voice.name,
      label: `${voice.name} - ${voice.description}`,
      description: voice.description,
      gender: voice.gender,
    }));
  }

  // 获取语言选项
  static getLanguageOptions() {
    return [
      { code: 'km-KH', name: '高棉语', nativeName: 'ភាសាខ្មែរ' },
      { code: 'en-US', name: '英语', nativeName: 'English' },
      { code: 'zh-CN', name: '中文', nativeName: '中文' },
      { code: 'ja-JP', name: '日语', nativeName: '日本語' },
      { code: 'ko-KR', name: '韩语', nativeName: '한국어' },
      { code: 'th-TH', name: '泰语', nativeName: 'ไทย' },
      { code: 'vi-VN', name: '越南语', nativeName: 'Tiếng Việt' },
      { code: 'fr-FR', name: '法语', nativeName: 'Français' },
      { code: 'de-DE', name: '德语', nativeName: 'Deutsch' },
      { code: 'es-US', name: '西班牙语', nativeName: 'Español' },
    ];
  }

  // 获取风格选项
  static getStyleOptions() {
    return [
      { value: 'natural', label: '自然', description: '自然流畅的语调' },
      { value: 'cheerful', label: '欢快', description: '说话时带有愉快的语调' },
      { value: 'calm', label: '平静', description: '平静温和的语调' },
      { value: 'authoritative', label: '权威', description: '严肃权威的语调' },
      { value: 'friendly', label: '友好', description: '友好亲切的语调' },
      { value: 'professional', label: '专业', description: '专业正式的语调' },
      { value: 'storytelling', label: '叙述', description: '适合讲故事的语调' },
      { value: 'news', label: '新闻', description: '新闻播报的语调' },
      { value: 'conversational', label: '对话', description: '日常对话的语调' },
      { value: 'dramatic', label: '戏剧', description: '戏剧化的语调' },
    ];
  }

  // 创建自定义角色
  static async createCustomCharacter(
    userId: string,
    template: CharacterTemplate,
    customOptions?: CustomCharacterOptions
  ) {
    try {
      // 构建自定义描述
      let description = template.description;
      if (customOptions) {
        description += ` | 风格: ${customOptions.style} | 语言: ${customOptions.language}`;
        if (customOptions.personality) {
          description += ` | 个性: ${customOptions.personality}`;
        }
      }

      // 构建自定义提示
      let customPrompt = template.customPrompt || '';
      if (customOptions?.customInstructions) {
        customPrompt += ` ${customOptions.customInstructions}`;
      }

      const character = await db.voiceCharacter.create({
        data: {
          characterName: template.characterName,
          characterNameEn: template.characterNameEn,
          apiProvider: template.apiProvider,
          apiVoiceName: template.apiVoiceName,
          gender: template.gender,
          description,
          tier: template.tier,
          sortOrder: 999, // 自定义角色排在最后
          isCustom: true,
          createdBy: userId,
          customSettings: customOptions ? JSON.stringify(customOptions) : null,
          customPrompt,
        },
      });

      console.log('✅ Custom character created:', character.characterName);
      return character;

    } catch (error) {
      console.error('❌ Failed to create custom character:', error);
      throw new Error(`Failed to create custom character: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // 获取用户的自定义角色
  static async getUserCustomCharacters(userId: string) {
    try {
      const characters = await db.voiceCharacter.findMany({
        where: {
          createdBy: userId,
          isCustom: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return characters.map(char => ({
        ...char,
        // customSettings: char.customSettings ? JSON.parse(char.customSettings) : null,
      }));

    } catch (error) {
      console.error('❌ Failed to get user custom characters:', error);
      throw new Error(`Failed to get custom characters: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // 更新自定义角色
  static async updateCustomCharacter(
    characterId: string,
    userId: string,
    updates: Partial<CharacterTemplate>,
    customOptions?: CustomCharacterOptions
  ) {
    try {
      // 验证角色所有权
      const existingCharacter = await db.voiceCharacter.findFirst({
        where: {
          id: characterId,
          createdBy: userId,
          isCustom: true,
        },
      });

      if (!existingCharacter) {
        throw new Error('Character not found or access denied');
      }

      // 构建更新数据
      const updateData: any = { ...updates };
      
      if (customOptions) {
        updateData.customSettings = JSON.stringify(customOptions);
        
        // 更新描述
        if (updates.description) {
          updateData.description = `${updates.description} | 风格: ${customOptions.style} | 语言: ${customOptions.language}`;
          if (customOptions.personality) {
            updateData.description += ` | 个性: ${customOptions.personality}`;
          }
        }
      }

      const updatedCharacter = await db.voiceCharacter.update({
        where: { id: characterId },
        data: updateData,
      });

      console.log('✅ Custom character updated:', updatedCharacter.characterName);
      return updatedCharacter;

    } catch (error) {
      console.error('❌ Failed to update custom character:', error);
      throw new Error(`Failed to update custom character: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // 删除自定义角色
  static async deleteCustomCharacter(characterId: string, userId: string) {
    try {
      const deletedCharacter = await db.voiceCharacter.deleteMany({
        where: {
          id: characterId,
          createdBy: userId,
          isCustom: true,
        },
      });

      if (deletedCharacter.count === 0) {
        throw new Error('Character not found or access denied');
      }

      console.log('✅ Custom character deleted');
      return true;

    } catch (error) {
      console.error('❌ Failed to delete custom character:', error);
      throw new Error(`Failed to delete custom character: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // 克隆现有角色为自定义角色
  static async cloneCharacter(
    sourceCharacterId: string,
    userId: string,
    newName: string,
    newNameEn: string,
    customOptions?: CustomCharacterOptions
  ) {
    try {
      const sourceCharacter = await db.voiceCharacter.findUnique({
        where: { id: sourceCharacterId },
      });

      if (!sourceCharacter) {
        throw new Error('Source character not found');
      }

      const template: CharacterTemplate = {
        characterName: newName,
        characterNameEn: newNameEn,
        apiProvider: sourceCharacter.apiProvider,
        apiVoiceName: sourceCharacter.apiVoiceName,
        gender: sourceCharacter.gender,
        description: `基于 ${sourceCharacter.characterName} 的自定义角色`,
        tier: 'BASIC',
        customPrompt: sourceCharacter.customPrompt || undefined,
      };

      return await this.createCustomCharacter(userId, template, customOptions);

    } catch (error) {
      console.error('❌ Failed to clone character:', error);
      throw new Error(`Failed to clone character: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // 获取角色的完整配置（包括自定义设置）
  static async getCharacterConfig(characterId: string) {
    try {
      const character = await db.voiceCharacter.findUnique({
        where: { id: characterId },
      });

      if (!character) {
        throw new Error('Character not found');
      }

      return {
        ...character,
        customSettings: character.customSettings ? JSON.parse(character.customSettings) : null,
      };

    } catch (error) {
      console.error('❌ Failed to get character config:', error);
      throw new Error(`Failed to get character config: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // 生成角色预设模板
  static getCharacterTemplates(): CharacterTemplate[] {
    return [
      {
        characterName: '新闻播音员',
        characterNameEn: 'News Anchor',
        apiProvider: 'GEMINI',
        apiVoiceName: 'Charon',
        gender: 'MALE',
        description: '专业的新闻播音员，语调清晰权威',
        tier: 'BASIC',
        style: 'professional',
        customPrompt: 'Read in a clear, authoritative news anchor style',
      },
      {
        characterName: '故事讲述者',
        characterNameEn: 'Storyteller',
        apiProvider: 'GEMINI',
        apiVoiceName: 'Aoede',
        gender: 'FEMALE',
        description: '温暖的故事讲述者，适合儿童故事',
        tier: 'BASIC',
        style: 'storytelling',
        customPrompt: 'Read in a warm, engaging storytelling voice',
      },
      {
        characterName: '高棉语导师',
        characterNameEn: 'Khmer Tutor',
        apiProvider: 'GEMINI',
        apiVoiceName: 'Kore',
        gender: 'FEMALE',
        description: '专业的高棉语教学导师',
        tier: 'PRO',
        language: 'km-KH',
        style: 'educational',
        customPrompt: 'Read Khmer text slowly and clearly for language learning',
      },
      {
        characterName: '客服助手',
        characterNameEn: 'Customer Service',
        apiProvider: 'GEMINI',
        apiVoiceName: 'Achird',
        gender: 'FEMALE',
        description: '友好的客服助手，语调亲切专业',
        tier: 'BASIC',
        style: 'friendly',
        customPrompt: 'Speak in a helpful, friendly customer service tone',
      },
    ];
  }
}
