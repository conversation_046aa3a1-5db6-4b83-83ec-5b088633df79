import { GeminiTTSService, type GeminiTTSOptions } from './gemini-tts';
import { StreamingGeminiTTSService, type StreamingGeminiTTSOptions } from './streaming-gemini-tts';
import { type ApiProvider, type AudioFormat } from '@prisma/client';
import { r2Storage } from '~/lib/r2-storage';
import { type PrismaClient } from '@prisma/client';

export interface OptimizedTTSGenerationOptions {
  text: string;
  characterId: string;
  apiProvider: ApiProvider;
  voiceName: string;
  speed?: number;
  pitch?: number;
  volumeGainDb?: number;
  format?: AudioFormat;
  quality?: 'fast' | 'high';
  stylePrompt?: string;
  userId?: string;
  useStreaming?: boolean; // 新增：是否使用流式处理
}

export interface OptimizedTTSGenerationResult {
  audioUrl: string;
  duration?: number;
  characterCount: number;
  tokenCount: number;
  inputTokens?: number;
  outputTokens?: number;
  cost?: number;
  apiProvider: ApiProvider;
  actualVoiceName: string;
  audioGenerationId?: string;
}

export class OptimizedTTSManager {
  private geminiService: GeminiTTSService;
  private streamingGeminiService: StreamingGeminiTTSService;
  private db: PrismaClient;

  constructor(db: PrismaClient) {
    this.geminiService = new GeminiTTSService();
    this.streamingGeminiService = new StreamingGeminiTTSService();
    this.db = db;
    console.log('✅ Optimized TTS Manager initialized with streaming support');
  }

  async generateSpeechOptimized(options: OptimizedTTSGenerationOptions): Promise<OptimizedTTSGenerationResult> {
    const startTime = Date.now();
    console.log('🚀 Starting optimized speech generation...');

    const {
      text,
      characterId,
      apiProvider,
      voiceName,
      speed = 1.0,
      pitch = 0,
      volumeGainDb = 0,
      quality = 'fast',
      stylePrompt,
      userId,
      useStreaming = false
    } = options;

    try {
      // 1. 并行执行：音频生成 + 数据库预处理
      const [ttsResult, dbPreparation] = await Promise.all([
        // 音频生成（主要耗时操作）
        this.generateAudioAsync(text, voiceName, quality, speed, pitch, volumeGainDb, stylePrompt, useStreaming),
        
        // 数据库预处理（并行执行）
        this.prepareDatabaseOperations(userId, text, characterId, apiProvider, voiceName, quality)
      ]);

      console.log(`⚡ Audio generation completed in ${Date.now() - startTime}ms`);

      // 2. 并行执行：文件上传 + 积分计算
      const uploadStartTime = Date.now();
      const [audioUrl, creditCalculation] = await Promise.all([
        // 文件上传
        this.uploadAudioAsync(ttsResult.audioData, dbPreparation.audioGenerationId),
        
        // 积分计算
        this.calculateCreditsAsync(userId, text.length, ttsResult.outputTokens || 0, quality)
      ]);

      console.log(`⚡ Upload completed in ${Date.now() - uploadStartTime}ms`);

      // 3. 批量数据库更新（单次事务）
      const dbUpdateStartTime = Date.now();
      await this.batchUpdateDatabase({
        audioGenerationId: dbPreparation.audioGenerationId,
        audioUrl,
        duration: ttsResult.duration,
        fileSize: ttsResult.audioData.length,
        cost: creditCalculation.cost,
        userId,
        creditsToDeduct: creditCalculation.creditsRequired,
        characterCount: text.length,
        quality
      });

      console.log(`⚡ Database update completed in ${Date.now() - dbUpdateStartTime}ms`);
      console.log(`🎉 Total optimized generation time: ${Date.now() - startTime}ms`);

      return {
        audioUrl,
        duration: ttsResult.duration,
        characterCount: text.length,
        tokenCount: ttsResult.tokenCount,
        inputTokens: ttsResult.inputTokens,
        outputTokens: ttsResult.outputTokens,
        cost: creditCalculation.cost,
        apiProvider,
        actualVoiceName: voiceName,
        audioGenerationId: dbPreparation.audioGenerationId,
      };

    } catch (error) {
      console.error(`❌ Optimized TTS Generation Error:`, error);
      throw error;
    }
  }

  private async generateAudioAsync(
    text: string,
    voiceName: string,
    quality: 'fast' | 'high',
    speed: number,
    pitch: number,
    volumeGainDb: number,
    stylePrompt?: string,
    useStreaming: boolean = false
  ) {
    const model = quality === 'high'
      ? 'gemini-2.5-pro-preview-tts'
      : 'gemini-2.5-flash-preview-tts';

    if (useStreaming) {
      console.log('🌊 Using streaming TTS service for better performance');
      const streamingOptions: StreamingGeminiTTSOptions = {
        text,
        voiceName,
        model,
        quality,
        speed,
        pitch,
        volumeGainDb,
        format: 'wav',
        prompt: stylePrompt,
      };

      return await this.streamingGeminiService.generateSpeech(streamingOptions);
    } else {
      const geminiOptions: GeminiTTSOptions = {
        text,
        voiceName,
        model,
        quality,
        speed,
        pitch,
        volumeGainDb,
        format: 'wav',
        prompt: stylePrompt,
      };

      return await this.geminiService.generateSpeech(geminiOptions);
    }
  }

  private async prepareDatabaseOperations(
    userId: string | undefined,
    text: string,
    characterId: string,
    apiProvider: ApiProvider,
    voiceName: string,
    quality: 'fast' | 'high'
  ) {
    if (!userId) {
      return { audioGenerationId: `guest-${Date.now()}` };
    }

    // 预创建音频生成记录（不包含URL，后续更新）
    const audioGeneration = await this.db.audioGeneration.create({
      data: {
        userId,
        inputText: text,
        outputAudioUrl: '', // 稍后更新
        templateId: characterId,
        apiProvider,
        actualVoiceName: voiceName,
        audioFormat: 'WAV',
        speed: 1.0,
        characterCount: text.length,
        fileSize: 0, // 稍后更新
        cost: 0, // 稍后更新
      },
    });

    return { audioGenerationId: audioGeneration.id };
  }

  private async uploadAudioAsync(audioData: Buffer, audioGenerationId: string): Promise<string> {
    const fileName = `${audioGenerationId}-${Date.now()}.wav`;
    return await r2Storage.uploadAudio(audioData, fileName, 'audio/wav');
  }

  private async calculateCreditsAsync(
    userId: string | undefined,
    textLength: number,
    outputTokens: number,
    quality: 'fast' | 'high'
  ) {
    if (!userId) {
      return { creditsRequired: 0, cost: 0 };
    }

    // 获取定价配置
    const pricingConfig = await this.db.apiPricingConfig.findFirst({
      where: { isActive: true },
      orderBy: { createdAt: 'desc' },
    });

    if (!pricingConfig) {
      throw new Error('未找到有效的定价配置');
    }

    // 使用输出token计算积分（更准确）
    const tokensToUse = outputTokens > 0 ? outputTokens : Math.ceil(textLength * 1.5);
    const creditsRequired = Math.ceil(tokensToUse / 100);
    const cost = creditsRequired * 0.01; // 1积分 = $0.01

    return { creditsRequired, cost };
  }

  private async batchUpdateDatabase(params: {
    audioGenerationId: string;
    audioUrl: string;
    duration?: number;
    fileSize: number;
    cost: number;
    userId?: string;
    creditsToDeduct: number;
    characterCount: number;
    quality: 'fast' | 'high';
  }) {
    const {
      audioGenerationId,
      audioUrl,
      duration,
      fileSize,
      cost,
      userId,
      creditsToDeduct,
      characterCount,
      quality
    } = params;

    if (!userId) {
      return; // 访客用户不需要数据库更新
    }

    // 使用单个事务批量更新所有数据
    await this.db.$transaction(async (tx) => {
      // 1. 更新音频生成记录
      await tx.audioGeneration.update({
        where: { id: audioGenerationId },
        data: {
          outputAudioUrl: audioUrl,
          duration,
          fileSize,
          cost,
        },
      });

      // 2. 扣除用户积分
      if (creditsToDeduct > 0) {
        await tx.user.update({
          where: { id: userId },
          data: {
            usedCredits: { increment: creditsToDeduct }
          },
        });

        // 3. 记录积分使用
        await tx.creditUsage.create({
          data: {
            userId,
            amount: creditsToDeduct,
            type: quality === 'fast' ? 'VOICE_GENERATION_FAST' : 'VOICE_GENERATION_HIGH',
            description: `语音生成 - ${quality === 'fast' ? '快速' : '高质量'}模式，${characterCount}字符`,
            serviceType: quality === 'fast' ? 'STANDARD_VOICE' : 'PROFESSIONAL_VOICE',
            inputTokens: 0,
            outputTokens: Math.ceil(characterCount * 1.5),
            totalTokens: Math.ceil(characterCount * 1.5),
            metadata: {
              textLength: characterCount,
              quality,
              creditsPerCharacter: creditsToDeduct / characterCount,
            },
          },
        });
      }

      // 4. 更新使用统计
      const today = new Date().toISOString().split('T')[0];
      if (today) {
        await tx.usage.upsert({
          where: {
            userId_date: {
              userId,
              date: new Date(today),
            },
          },
          update: {
            characterCount: { increment: characterCount },
            audioCount: { increment: 1 },
          },
          create: {
            userId,
            date: new Date(today),
            characterCount,
            audioCount: 1,
          },
        });
      }
    });

    console.log(`✅ Batch database update completed for user ${userId}`);
  }

  // 估算成本的辅助方法
  private estimateGeminiCost(characterCount: number, quality: 'fast' | 'high'): number {
    // 基于字符数估算成本
    const baseRate = quality === 'high' ? 0.002 : 0.001; // 每字符成本
    return characterCount * baseRate;
  }
}
