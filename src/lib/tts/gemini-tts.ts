import { GoogleGenAI } from '@google/genai';
import { env } from '~/env';

export interface GeminiTTSOptions {
  text: string;
  voiceName?: string;
  model?: 'gemini-2.5-flash-preview-tts' | 'gemini-2.5-pro-preview-tts';
  speed?: number;
  pitch?: number;
  volumeGainDb?: number;
  format?: 'wav' | 'pcm';
  language?: string;
  style?: string;
  prompt?: string; // 用于风格控制的自然语言提示
  quality?: 'fast' | 'high'; // 质量设置：fast使用flash模型，high使用pro模型
  multiSpeaker?: {
    speakers: Array<{
      speaker: string;
      voiceName: string;
    }>;
  };
}

export interface GeminiTTSResult {
  audioData: Buffer;
  duration?: number;
  characterCount: number;
  tokenCount: number; // 新增token计数
  inputTokens?: number; // API提供的输入token数
  outputTokens?: number; // API提供的输出token数
  language?: string;
  voiceName: string;
}

interface WavConversionOptions {
  numChannels: number;
  sampleRate: number;
  bitsPerSample: number;
}

// 根据Google Cloud官方文档的Gemini TTS语音列表
export const GEMINI_VOICES = [
  // 女性语音 (Female)
  { name: 'Achernar', description: '温柔优雅', gender: 'female' },
  { name: 'Aoede', description: '柔美动听', gender: 'female' },
  { name: 'Autonoe', description: '清新自然', gender: 'female' },
  { name: 'Callirrhoe', description: '温暖亲切', gender: 'female' },
  { name: 'Despina', description: '活泼开朗', gender: 'female' },
  { name: 'Erinome', description: '神秘魅力', gender: 'female' },
  { name: 'Gacrux', description: '专业严谨', gender: 'female' },
  { name: 'Kore', description: '坚定自信', gender: 'female' },
  { name: 'Laomedeia', description: '青春活力', gender: 'female' },
  { name: 'Leda', description: '沉稳可靠', gender: 'female' },
  { name: 'Pulcherrima', description: '优雅迷人', gender: 'female' },
  { name: 'Sulafat', description: '柔美动听', gender: 'female' },
  { name: 'Vindemiatrix', description: '现代时尚', gender: 'female' },
  { name: 'Zephyr', description: '清晰明亮', gender: 'female' },

  // 男性语音 (Male)
  { name: 'Achird', description: '深沉磁性', gender: 'male' },
  { name: 'Algenib', description: '阳光开朗', gender: 'male' },
  { name: 'Algieba', description: '威严庄重', gender: 'male' },
  { name: 'Alnilam', description: '机智敏捷', gender: 'male' },
  { name: 'Charon', description: '信息丰富', gender: 'male' },
  { name: 'Enceladus', description: '稳重可靠', gender: 'male' },
  { name: 'Fenrir', description: '英勇果敢', gender: 'male' },
  { name: 'Iapetus', description: '强劲有力', gender: 'male' },
  { name: 'Orus', description: '温和友善', gender: 'male' },
  { name: 'Puck', description: '欢快俏皮', gender: 'male' },
  { name: 'Rasalgethi', description: '专业权威', gender: 'male' },
  { name: 'Sadachbia', description: '沉着冷静', gender: 'male' },
  { name: 'Sadaltager', description: '温暖亲和', gender: 'male' },
  { name: 'Schedar', description: '庄重威严', gender: 'male' },
  { name: 'Umbriel', description: '深邃神秘', gender: 'male' },
  { name: 'Zubenelgenubi', description: '和谐平衡', gender: 'male' },
] as const;

export class GeminiTTSService {
  private client: GoogleGenAI;
  private lastRequestTime: number = 0;
  private minRequestInterval: number = 3000;

  constructor() {
    console.log('🔧 Initializing Gemini TTS Service...');
    this.client = new GoogleGenAI({
      apiKey: env.GEMINI_API_KEY,
    });
  }

  private async waitForRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.minRequestInterval) {
      const waitTime = this.minRequestInterval - timeSinceLastRequest;
      console.log(`⏳ Rate limiting: waiting ${waitTime}ms...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.lastRequestTime = Date.now();
  }

  async generateSpeech(options: GeminiTTSOptions): Promise<GeminiTTSResult> {
    const {
      text,
      voiceName: inputVoiceName = 'Puck',
      model: inputModel,
      quality = 'fast',
      speed = 1.0,
      pitch = 0,
      volumeGainDb = 0,
      format = 'wav',
      prompt: stylePrompt,
      multiSpeaker
    } = options;

    // 现在所有参数都会被使用

    // 优先使用传入的model参数，如果没有则根据质量设置选择模型
    const model = inputModel || (quality === 'high' ? 'gemini-2.5-pro-preview-tts' : 'gemini-2.5-flash-preview-tts');



    // 验证语音名称是否有效
    const validVoices = GEMINI_VOICES.map(v => v.name) as string[];
    const voiceName = validVoices.includes(inputVoiceName) ? inputVoiceName : 'Puck';
    if (!validVoices.includes(inputVoiceName)) {
      console.warn(`⚠️ Invalid voice name: ${inputVoiceName}, using default: Puck`);
    }

    console.log(`🎤 Generating speech with ${model}, voice: ${voiceName}`);
    await this.waitForRateLimit();

    try {
      // 预处理文本，避免可能的问题
      let processedText = text.trim();

      // 检查文本长度，太短的文本可能被拒绝
      if (processedText.length < 5) {
        console.warn(`⚠️ Text too short (${processedText.length} chars), adding prefix`);
        processedText = `请朗读：${processedText}`;
      }

      // 检查是否包含可能有问题的内容
      if (this.containsProblematicContent(processedText)) {
        console.warn('⚠️ Text contains potentially problematic content, sanitizing...');
        processedText = this.sanitizeText(processedText);
      }

      // 将数值参数转换为自然语言风格提示
      // Gemini TTS不支持直接的数值参数，需要转换为自然语言描述
      const parameterPrompts: string[] = [];

      /*
       * 精细参数映射表（支持更细致的调节）：
       *
       * 语速 (speed) - 8个级别:
       * - >= 1.8: "speak very quickly and energetically"
       * - >= 1.5: "speak quickly and energetically"
       * - >= 1.3: "speak at a faster pace"
       * - >= 1.1: "speak slightly faster"
       * - <= 0.5: "speak extremely slowly and deliberately"
       * - <= 0.7: "speak very slowly and deliberately"
       * - <= 0.85: "speak slowly and calmly"
       * - <= 0.95: "speak slightly slower"
       *
       * 音调 (pitch) - 10个级别:
       * - >= 8: "with a very high-pitched, excited voice"
       * - >= 5: "with a high-pitched voice"
       * - >= 3: "with a moderately higher pitch"
       * - >= 1: "with a slightly higher pitch"
       * - >= 0.5: "with a barely higher pitch"
       * - <= -8: "with a very deep, low voice"
       * - <= -5: "with a deep, low voice"
       * - <= -3: "with a moderately lower pitch"
       * - <= -1: "with a slightly lower pitch"
       * - <= -0.5: "with a barely lower pitch"
       *
       * 音量 (volumeGainDb) - 8个级别:
       * - >= 12: "speak very loudly and boldly"
       * - >= 8: "speak loudly and boldly"
       * - >= 4: "speak with a strong, clear voice"
       * - >= 1: "speak with a slightly stronger voice"
       * - <= -12: "speak in a very soft whisper"
       * - <= -8: "speak in a soft whisper"
       * - <= -4: "speak quietly and gently"
       * - <= -1: "speak slightly more quietly"
       */

      // 语速转换为风格提示（精细级别）
      if (speed !== 1.0) {
        if (speed >= 1.8) {
          parameterPrompts.push('speak very quickly and energetically');
        } else if (speed >= 1.5) {
          parameterPrompts.push('speak quickly and energetically');
        } else if (speed >= 1.3) {
          parameterPrompts.push('speak at a faster pace');
        } else if (speed >= 1.1) {
          parameterPrompts.push('speak slightly faster');
        } else if (speed <= 0.5) {
          parameterPrompts.push('speak extremely slowly and deliberately');
        } else if (speed <= 0.7) {
          parameterPrompts.push('speak very slowly and deliberately');
        } else if (speed <= 0.85) {
          parameterPrompts.push('speak slowly and calmly');
        } else if (speed <= 0.95) {
          parameterPrompts.push('speak slightly slower');
        }
      }

      // 音调转换为风格提示（精细级别）
      if (pitch !== 0) {
        if (pitch >= 8) {
          parameterPrompts.push('with a very high-pitched, excited voice');
        } else if (pitch >= 5) {
          parameterPrompts.push('with a high-pitched voice');
        } else if (pitch >= 3) {
          parameterPrompts.push('with a moderately higher pitch');
        } else if (pitch >= 1) {
          parameterPrompts.push('with a slightly higher pitch');
        } else if (pitch >= 0.5) {
          parameterPrompts.push('with a barely higher pitch');
        } else if (pitch <= -8) {
          parameterPrompts.push('with a very deep, low voice');
        } else if (pitch <= -5) {
          parameterPrompts.push('with a deep, low voice');
        } else if (pitch <= -3) {
          parameterPrompts.push('with a moderately lower pitch');
        } else if (pitch <= -1) {
          parameterPrompts.push('with a slightly lower pitch');
        } else if (pitch <= -0.5) {
          parameterPrompts.push('with a barely lower pitch');
        }
      }

      // 音量转换为风格提示（精细级别）
      if (volumeGainDb !== 0) {
        if (volumeGainDb >= 12) {
          parameterPrompts.push('speak very loudly and boldly');
        } else if (volumeGainDb >= 8) {
          parameterPrompts.push('speak loudly and boldly');
        } else if (volumeGainDb >= 4) {
          parameterPrompts.push('speak with a strong, clear voice');
        } else if (volumeGainDb >= 1) {
          parameterPrompts.push('speak with a slightly stronger voice');
        } else if (volumeGainDb <= -12) {
          parameterPrompts.push('speak in a very soft whisper');
        } else if (volumeGainDb <= -8) {
          parameterPrompts.push('speak in a soft whisper');
        } else if (volumeGainDb <= -4) {
          parameterPrompts.push('speak quietly and gently');
        } else if (volumeGainDb <= -1) {
          parameterPrompts.push('speak slightly more quietly');
        }
      }

      // 组合所有风格提示
      const allPrompts: string[] = [];

      // 添加用户自定义的风格提示
      if (stylePrompt && stylePrompt.trim()) {
        allPrompts.push(stylePrompt.trim());
      }

      // 添加参数转换的风格提示
      if (parameterPrompts.length > 0) {
        allPrompts.push(parameterPrompts.join(', '));
      }

      // 应用组合的风格提示
      if (allPrompts.length > 0) {
        const combinedPrompt = allPrompts.join(', ');

        // 检查用户风格提示是否已经包含"Say"或"Make"前缀
        const hasPrefix = stylePrompt && (stylePrompt.startsWith('Say ') || stylePrompt.startsWith('Make '));

        if (hasPrefix) {
          // 如果用户提示已有前缀，直接使用，参数提示作为补充
          if (parameterPrompts.length > 0) {
            processedText = `${stylePrompt}, ${parameterPrompts.join(', ')}: "${processedText}"`;
          } else {
            processedText = `${stylePrompt}: "${processedText}"`;
          }
        } else {
          // 标准格式：Say [所有提示]: "文本"
          processedText = `Say ${combinedPrompt}: "${processedText}"`;
        }

        console.log(`🎭 Applied combined style prompt: "${combinedPrompt}"`);
      }

      console.log(`📝 Final text to speak: ${processedText.substring(0, 200)}...`);

      // 记录语音参数
      console.log(`🎛️ Voice parameters: speed=${speed}, pitch=${pitch}, volume=${volumeGainDb}dB`);

      if (speed !== 1.0) {
        console.log(`🏃 Speed adjustment: ${speed}x`);
      }

      if (pitch !== 0) {
        console.log(`🎵 Pitch adjustment: ${pitch > 0 ? '+' : ''}${pitch}`);
      }

      if (volumeGainDb !== 0) {
        console.log(`🔊 Volume adjustment: ${volumeGainDb > 0 ? '+' : ''}${volumeGainDb}dB`);
      }

      // 根据官方 API 文档配置
      const config = {
        responseModalities: ['AUDIO'], // 必须大写
        speechConfig: {
          voiceConfig: {
            prebuiltVoiceConfig: {
              voiceName: voiceName
            }
          }
        },
      };

      console.log('🔧 API Config:', JSON.stringify(config, null, 2));
      console.log('🔧 Model:', model);
      console.log('🔧 Voice:', voiceName);

      // 构建语音配置
      let speechConfig: any;

      if (multiSpeaker && multiSpeaker.speakers.length > 0) {
        // 多人对话配置
        console.log('🎭 Using multi-speaker configuration');
        speechConfig = {
          multiSpeakerVoiceConfig: {
            speakerVoiceConfigs: multiSpeaker.speakers.map(speaker => ({
              speaker: speaker.speaker,
              voiceConfig: {
                prebuiltVoiceConfig: {
                  voiceName: speaker.voiceName
                }
              }
            }))
          }
        };
      } else {
        // 单人语音配置
        speechConfig = {
          voiceConfig: {
            prebuiltVoiceConfig: {
              voiceName: voiceName
            }
          }
        };
      }

      // 正确的TTS API调用格式
      const response = await this.client.models.generateContent({
        model: model,
        contents: [{ parts: [{ text: processedText }] }],
        config: {
          responseModalities: ['AUDIO'],
          speechConfig
        }
      } as any);

      // 检查API响应中的真实token使用信息
      const usageMetadata = response.usageMetadata;
      const promptTokenCount = usageMetadata?.promptTokenCount || 0;
      const candidatesTokenCount = usageMetadata?.candidatesTokenCount || 0;
      const totalTokenCount = usageMetadata?.totalTokenCount || 0;

      console.log('🔍 API Response structure:', JSON.stringify({
        candidates: response.candidates?.length || 0,
        usageMetadata: {
          promptTokenCount,
          candidatesTokenCount,
          totalTokenCount
        },
        firstCandidate: response.candidates?.[0] ? {
          content: !!response.candidates[0].content,
          parts: response.candidates[0].content?.parts?.length || 0,
          firstPart: response.candidates[0].content?.parts?.[0] ? {
            hasInlineData: !!response.candidates[0].content.parts[0].inlineData,
            hasText: !!response.candidates[0].content.parts[0].text,
          } : null
        } : null
      }, null, 2));

      // 处理非流式响应
      if (!response.candidates?.[0]?.content?.parts?.[0]?.inlineData?.data) {
        console.error('❌ No audio data in response. Full response:', JSON.stringify(response, null, 2));

        // 检查是否是内容被拒绝
        const finishReason = response.candidates?.[0]?.finishReason;
        if (finishReason === 'OTHER' || finishReason === 'SAFETY') {
          console.warn('⚠️ Content may have been filtered by Gemini TTS. Trying with sanitized text...');

          // 尝试清理文本并重试
          const sanitizedText = this.sanitizeText(processedText);
          console.log(`🧹 Retrying with sanitized text: "${sanitizedText}"`);
          return this.generateSpeech({
            ...options,
            text: sanitizedText
          });
        }

        throw new Error('No audio data received from Gemini TTS API');
      }

      const inlineData = response.candidates[0].content.parts[0].inlineData;
      if (!inlineData.data) {
        throw new Error('No audio data in response');
      }

      let finalAudioBuffer = Buffer.from(inlineData.data, 'base64');

      console.log(`📦 Received audio data: ${finalAudioBuffer.length} bytes, mimeType: ${inlineData.mimeType || 'unknown'}`);

      // 如果请求WAV格式但收到的不是WAV，进行转换
      if (format === 'wav' && inlineData.mimeType && !inlineData.mimeType.includes('wav')) {
        console.log('🔄 Converting to WAV format');
        finalAudioBuffer = Buffer.from(this.convertToWav(inlineData.data, inlineData.mimeType));
      }
      console.log('✅ Gemini TTS generation successful');

      // 使用API提供的真实token数据，如果没有则回退到估算
      const actualTokenCount = totalTokenCount > 0 ? totalTokenCount : this.estimateTokens(text);

      console.log(`📊 Token usage: ${actualTokenCount} tokens (input: ${promptTokenCount}, output: ${candidatesTokenCount})`);

      return {
        audioData: finalAudioBuffer,
        characterCount: text.length,
        tokenCount: actualTokenCount,
        inputTokens: promptTokenCount,
        outputTokens: candidatesTokenCount,
        voiceName,
        language: this.detectLanguage(text),
      };

    } catch (error) {
      console.error('❌ Gemini TTS Error:', error);

      if (error instanceof Error) {
        if (error.message.includes('429') || error.message.includes('rate limit')) {
          this.minRequestInterval = Math.min(this.minRequestInterval * 2, 10000);
          throw new Error(`Gemini API rate limit exceeded. Please wait ${this.minRequestInterval/1000} seconds.`);
        }

        // 如果是API配置问题，提供更详细的错误信息
        if (error.message.includes('No audio data received')) {
          console.error('🔍 Debugging info:');
          console.error('- Model:', model);
          console.error('- Voice:', voiceName);
          console.error('- Text length:', text.length);
          console.error('- API Key present:', !!env.GEMINI_API_KEY);

          throw new Error(`Gemini TTS API did not return audio data. This might be due to:
1. Invalid model name (${model})
2. Invalid voice name (${voiceName})
3. API key issues
4. Text content issues
5. API service temporarily unavailable

Please check the console logs for more details.`);
        }
      }

      throw new Error(`Gemini TTS failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private isKhmerText(text: string): boolean {
    return /[\u1780-\u17FF]/.test(text);
  }

  private detectLanguage(text: string): string {
    return this.isKhmerText(text) ? 'km-KH' : 'auto';
  }

  // Estimate tokens for text (similar to text generation)
  private estimateTokens(text: string): number {
    // 简化的token估算：
    // 中文字符：1.5 tokens per character
    // 英文单词：1 token per word
    // 其他字符：0.5 tokens per character

    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    const otherChars = text.length - chineseChars - englishWords;

    return Math.ceil(chineseChars * 1.5 + englishWords + otherChars * 0.5);
  }

  private containsProblematicContent(text: string): boolean {
    // 检查可能导致API拒绝的内容
    const problematicPatterns = [
      /["""'']/,  // 特殊引号
      /[<>{}[\]]/,  // 特殊符号
      /^\s*[?!.。？！]+\s*$/,  // 只有标点符号
      /^.{1,20}$/,  // 文本太短（20字符以下）
      /[""][^""]*[""]/, // 包含引号的短句
    ];

    return problematicPatterns.some(pattern => pattern.test(text));
  }

  private sanitizeText(text: string): string {
    // 移除可能导致问题的字符和内容
    let sanitized = text
      // 移除所有类型的引号
      .replace(/["""''「」『』]/g, '')
      // 移除特殊符号
      .replace(/[<>{}[\]]/g, '')
      // 移除多余的空格
      .replace(/\s+/g, ' ')
      .trim();

    // 如果文本太短或包含敏感内容，添加上下文
    if (sanitized.length < 15) {
      sanitized = `请朗读以下内容：${sanitized}`;
    }

    // 如果仍然太短，添加更多上下文
    if (sanitized.length < 20) {
      sanitized = `现在为您朗读：${sanitized}，谢谢。`;
    }

    return sanitized;
  }

  private convertToWav(rawData: string, _mimeType: string): Buffer {
    const options = this.parseMimeType();
    const buffer = Buffer.from(rawData, 'base64');
    const wavHeader = this.createWavHeader(buffer.length, options);
    return Buffer.concat([wavHeader, buffer]);
  }

  private parseMimeType(): WavConversionOptions {
    return {
      numChannels: 1,
      sampleRate: 24000,
      bitsPerSample: 16,
    };
  }

  private createWavHeader(dataLength: number, options: WavConversionOptions): Buffer {
    const { numChannels, sampleRate, bitsPerSample } = options;
    const byteRate = sampleRate * numChannels * bitsPerSample / 8;
    const blockAlign = numChannels * bitsPerSample / 8;
    const buffer = Buffer.alloc(44);

    buffer.write('RIFF', 0);
    buffer.writeUInt32LE(36 + dataLength, 4);
    buffer.write('WAVE', 8);
    buffer.write('fmt ', 12);
    buffer.writeUInt32LE(16, 16);
    buffer.writeUInt16LE(1, 20);
    buffer.writeUInt16LE(numChannels, 22);
    buffer.writeUInt32LE(sampleRate, 24);
    buffer.writeUInt32LE(byteRate, 28);
    buffer.writeUInt16LE(blockAlign, 32);
    buffer.writeUInt16LE(bitsPerSample, 34);
    buffer.write('data', 36);
    buffer.writeUInt32LE(dataLength, 40);

    return buffer;
  }

  getAvailableVoices() {
    return GEMINI_VOICES;
  }
}