
import { GeminiTTSService, type GeminiTTSOptions } from './gemini-tts';
import { type ApiProvider, type AudioFormat } from '@prisma/client';

export interface TTSGenerationOptions {
  text: string;
  characterId: string;
  apiProvider: ApiProvider;
  voiceName: string;
  speed?: number;
  pitch?: number;
  volumeGainDb?: number;
  format?: AudioFormat;
  quality?: 'fast' | 'high'; // 对应flash和pro模型
  stylePrompt?: string;
}

export interface TTSGenerationResult {
  audioData: Buffer;
  duration?: number;
  characterCount: number;
  tokenCount: number; // 新增token计数
  inputTokens?: number; // API提供的输入token数
  outputTokens?: number; // API提供的输出token数
  cost?: number;
  apiProvider: ApiProvider;
  actualVoiceName: string;
}

export class TTSManager {
  private geminiService: GeminiTTSService;

  constructor() {
    this.geminiService = new GeminiTTSService();
    console.log('✅ Gemini TTS Service initialized');
  }

  async generateSpeech(options: TTSGenerationOptions): Promise<TTSGenerationResult> {
    const {
      text,
      apiProvider,
      voiceName,
      speed = 1.0,
      pitch = 0,
      volumeGainDb = 0,
      quality = 'fast',
      stylePrompt
    } = options;

    try {
      console.log('🔄 Using Gemini TTS Service...');
      console.log(`🎯 Quality setting: ${quality}`);

      // 根据质量选择模型
      const model = quality === 'high'
        ? 'gemini-2.5-pro-preview-tts'
        : 'gemini-2.5-flash-preview-tts';

      console.log(`🤖 Selected model: ${model}`);

      const geminiOptions: GeminiTTSOptions = {
        text,
        voiceName,
        model,
        quality, // 传递quality参数
        speed,
        pitch,
        volumeGainDb,
        format: 'wav', // 强制使用 WAV 格式
        prompt: stylePrompt,
      };

      const result = await this.geminiService.generateSpeech(geminiOptions);
      const cost = this.estimateGeminiCost(text.length, quality);

      return {
        ...result,
        cost,
        apiProvider,
        actualVoiceName: voiceName,
      };
    } catch (error) {
      console.error(`TTS Generation Error (${apiProvider}):`, error);
      throw error;
    }
  }

  private estimateGeminiCost(characterCount: number, quality: 'fast' | 'high'): number {
    // Gemini TTS 定价估算
    // Flash模型: 每100万字符 $10 (更便宜)
    // Pro模型: 每100万字符 $20 (更贵但质量更高)
    const baseRate = quality === 'high' ? 20.00 : 10.00;
    return (characterCount / 1_000_000) * baseRate;
  }

  // Estimate tokens for text (similar to text generation)
  private estimateTokens(text: string): number {
    // 简化的token估算：
    // 中文字符：1.5 tokens per character
    // 英文单词：1 token per word
    // 其他字符：0.5 tokens per character

    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    const otherChars = text.length - chineseChars - englishWords;

    return Math.ceil(chineseChars * 1.5 + englishWords + otherChars * 0.5);
  }

  // Validate if text is suitable for TTS
  static validateText(text: string): { isValid: boolean; error?: string } {
    if (!text || text.trim().length === 0) {
      return { isValid: false, error: 'Text cannot be empty' };
    }

    if (text.length > 32000) {
      return { isValid: false, error: 'Text is too long (maximum 32,000 characters)' };
    }

    // Check for Khmer Unicode range (basic validation)
    const khmerRegex = /[\u1780-\u17FF]/;
    if (!khmerRegex.test(text)) {
      console.warn('Text does not contain Khmer characters');
    }

    return { isValid: true };
  }

  // Get character information by ID
  static async getCharacterInfo(characterId: string, prisma: any) {
    const character = await prisma.languageCharacter.findUnique({
      where: { id: characterId },
      include: {
        template: true,
        language: true,
      },
    });

    if (!character) {
      throw new Error('Character not found');
    }

    if (!character.isActive) {
      throw new Error('Character is not active');
    }

    return character;
  }

  // Check user credits and calculate cost
  static async checkUserCredits(userId: string, textLength: number, quality: 'fast' | 'high', prisma: any) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        credits: true,
        usedCredits: true,
      },
    });

    if (!user) {
      throw new Error('User not found. Please sign out and sign in again.');
    }

    // 计算积分消耗：
    // 快速模式：1积分 = 10字符
    // 高质量模式：1积分 = 5字符
    const creditsPerCharacter = quality === 'fast' ? 0.1 : 0.2;
    const requiredCredits = Math.ceil(textLength * creditsPerCharacter);

    const availableCredits = user.credits - user.usedCredits;

    if (availableCredits < requiredCredits) {
      throw new Error(`积分不足。需要: ${requiredCredits} 积分，剩余: ${availableCredits} 积分`);
    }

    return {
      user,
      requiredCredits,
      availableCredits,
    };
  }

  // Legacy quota check for backward compatibility
  static async checkUserQuotaLegacy(userId: string, textLength: number, prisma: any) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new Error('User not found. Please sign out and sign in again.');
    }

    // 使用标准配额作为默认
    const remainingQuota = user.standardQuota - user.usedStandardQuota;
    if (remainingQuota < textLength) {
      throw new Error(`Insufficient quota. Remaining: ${remainingQuota} characters`);
    }

    return user;
  }

  // Consume user credits based on usage
  static async consumeUserCredits(userId: string, textLength: number, quality: 'fast' | 'high', prisma: any) {
    // 计算积分消耗：
    // 快速模式：1积分 = 10字符
    // 高质量模式：1积分 = 5字符
    const creditsPerCharacter = quality === 'fast' ? 0.1 : 0.2;
    const requiredCredits = Math.ceil(textLength * creditsPerCharacter);

    // 使用事务确保数据一致性
    return await prisma.$transaction(async (tx: any) => {
      // 更新用户已使用积分
      const updatedUser = await tx.user.update({
        where: { id: userId },
        data: {
          usedCredits: { increment: requiredCredits }
        },
        select: {
          credits: true,
          usedCredits: true,
        },
      });

      // 记录积分使用历史
      await tx.creditUsage.create({
        data: {
          userId,
          amount: requiredCredits,
          type: quality === 'fast' ? 'VOICE_GENERATION_FAST' : 'VOICE_GENERATION_HIGH',
          description: `语音生成 - ${quality === 'fast' ? '快速' : '高质量'}模式，${textLength}字符`,
          metadata: {
            textLength,
            quality,
            creditsPerCharacter,
          },
        },
      });

      return updatedUser;
    });
  }
}
