import { GoogleGenAI } from '@google/genai';
import { env } from '~/env';

export interface StreamingGeminiTTSOptions {
  text: string;
  voiceName?: string;
  model?: 'gemini-2.5-flash-preview-tts' | 'gemini-2.5-pro-preview-tts';
  speed?: number;
  pitch?: number;
  volumeGainDb?: number;
  format?: 'wav' | 'pcm';
  language?: string;
  style?: string;
  prompt?: string;
  quality?: 'fast' | 'high';
  temperature?: number;
}

export interface StreamingGeminiTTSResult {
  audioData: Buffer;
  duration?: number;
  characterCount: number;
  tokenCount: number;
  inputTokens?: number;
  outputTokens?: number;
  language?: string;
  voiceName: string;
  chunks: number; // 流式处理的块数
  streamingTime: number; // 流式处理总时间
}

interface WavConversionOptions {
  numChannels: number;
  sampleRate: number;
  bitsPerSample: number;
}

export class StreamingGeminiTTSService {
  private client: GoogleGenAI;
  private lastRequestTime: number = 0;
  private minRequestInterval: number = 1000; // 减少到1秒，因为流式处理更高效

  constructor() {
    console.log('🌊 Initializing Streaming Gemini TTS Service...');
    this.client = new GoogleGenAI({
      apiKey: env.GEMINI_API_KEY,
    });
  }

  private async waitForRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.minRequestInterval) {
      const waitTime = this.minRequestInterval - timeSinceLastRequest;
      console.log(`⏳ Rate limiting: waiting ${waitTime}ms...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.lastRequestTime = Date.now();
  }

  async generateSpeech(options: StreamingGeminiTTSOptions): Promise<StreamingGeminiTTSResult> {
    const {
      text,
      voiceName: inputVoiceName = 'Zephyr',
      model: inputModel,
      quality = 'fast',
      speed = 1.0,
      pitch = 0,
      volumeGainDb = 0,
      format = 'wav',
      prompt: stylePrompt,
      temperature = 1.0
    } = options;

    // 根据质量选择模型
    const model = inputModel || (quality === 'high' ? 'gemini-2.5-pro-preview-tts' : 'gemini-2.5-flash-preview-tts');
    const voiceName = inputVoiceName;

    console.log(`🌊 Starting streaming speech generation with ${model}, voice: ${voiceName}`);
    await this.waitForRateLimit();

    const streamingStartTime = Date.now();
    let chunkCount = 0;
    const audioChunks: Buffer[] = [];

    try {
      // 预处理文本
      let processedText = text.trim();

      // 应用风格提示和参数
      if (stylePrompt || speed !== 1.0 || pitch !== 0 || volumeGainDb !== 0) {
        const parameterPrompts: string[] = [];

        // 语速调整
        if (speed !== 1.0) {
          if (speed >= 1.5) {
            parameterPrompts.push('speak quickly and energetically');
          } else if (speed >= 1.1) {
            parameterPrompts.push('speak slightly faster');
          } else if (speed <= 0.7) {
            parameterPrompts.push('speak slowly and deliberately');
          } else if (speed <= 0.9) {
            parameterPrompts.push('speak slightly slower');
          }
        }

        // 音调调整
        if (pitch !== 0) {
          if (pitch >= 3) {
            parameterPrompts.push('with a higher pitch');
          } else if (pitch >= 1) {
            parameterPrompts.push('with a slightly higher pitch');
          } else if (pitch <= -3) {
            parameterPrompts.push('with a lower pitch');
          } else if (pitch <= -1) {
            parameterPrompts.push('with a slightly lower pitch');
          }
        }

        // 音量调整
        if (volumeGainDb !== 0) {
          if (volumeGainDb >= 5) {
            parameterPrompts.push('speak loudly and clearly');
          } else if (volumeGainDb >= 2) {
            parameterPrompts.push('speak with a strong voice');
          } else if (volumeGainDb <= -5) {
            parameterPrompts.push('speak softly');
          } else if (volumeGainDb <= -2) {
            parameterPrompts.push('speak quietly');
          }
        }

        // 组合提示
        const allPrompts: string[] = [];
        if (stylePrompt && stylePrompt.trim()) {
          allPrompts.push(stylePrompt.trim());
        }
        if (parameterPrompts.length > 0) {
          allPrompts.push(parameterPrompts.join(', '));
        }

        if (allPrompts.length > 0) {
          const combinedPrompt = allPrompts.join(', ');
          processedText = `Say ${combinedPrompt}: "${processedText}"`;
        }
      }

      console.log(`📝 Final text for streaming: ${processedText.substring(0, 200)}...`);

      // 使用官方示例的配置格式
      const config = {
        temperature,
        responseModalities: ['audio'], // 使用小写，如官方示例
        speechConfig: {
          voiceConfig: {
            prebuiltVoiceConfig: {
              voiceName: voiceName
            }
          }
        },
      };

      const contents = [
        {
          role: 'user' as const,
          parts: [
            {
              text: processedText,
            },
          ],
        },
      ];

      console.log('🌊 Starting streaming API call...');
      console.log('🔧 Streaming Config:', JSON.stringify(config, null, 2));

      // 使用流式处理
      const response = await this.client.models.generateContentStream({
        model,
        config,
        contents,
      });

      console.log('🌊 Processing streaming response...');

      // 处理流式响应
      for await (const chunk of response) {
        chunkCount++;
        
        if (!chunk.candidates || !chunk.candidates[0]?.content || !chunk.candidates[0].content.parts) {
          continue;
        }

        const part = chunk.candidates[0].content.parts[0];
        
        if (part?.inlineData?.data) {
          console.log(`📦 Received chunk ${chunkCount}: ${part.inlineData.data.length} chars, mimeType: ${part.inlineData.mimeType || 'unknown'}`);
          
          let chunkBuffer = Buffer.from(part.inlineData.data, 'base64');
          
          // 如果需要转换为WAV格式
          if (format === 'wav' && part.inlineData.mimeType && !part.inlineData.mimeType.includes('wav')) {
            chunkBuffer = Buffer.from(this.convertToWav(part.inlineData.data, part.inlineData.mimeType));
          }
          
          audioChunks.push(chunkBuffer);
        } else if (part?.text) {
          console.log('📝 Text chunk:', part.text);
        }
      }

      const streamingTime = Date.now() - streamingStartTime;
      console.log(`🌊 Streaming completed: ${chunkCount} chunks in ${streamingTime}ms`);

      if (audioChunks.length === 0) {
        throw new Error('No audio data received from streaming response');
      }

      // 合并所有音频块
      const finalAudioBuffer = Buffer.concat(audioChunks);
      console.log(`✅ Streaming TTS generation successful: ${finalAudioBuffer.length} bytes from ${chunkCount} chunks`);

      // 估算token使用量
      const estimatedTokenCount = this.estimateTokens(text);

      return {
        audioData: finalAudioBuffer,
        characterCount: text.length,
        tokenCount: estimatedTokenCount,
        inputTokens: Math.ceil(text.length * 0.75), // 估算
        outputTokens: Math.ceil(text.length * 1.5), // 估算
        voiceName,
        language: this.detectLanguage(text),
        chunks: chunkCount,
        streamingTime,
      };

    } catch (error) {
      console.error('❌ Streaming Gemini TTS Error:', error);

      if (error instanceof Error) {
        if (error.message.includes('429') || error.message.includes('rate limit')) {
          this.minRequestInterval = Math.min(this.minRequestInterval * 2, 5000);
          throw new Error(`Gemini API rate limit exceeded. Please wait ${this.minRequestInterval/1000} seconds.`);
        }
      }

      throw error;
    }
  }

  private convertToWav(rawData: string, mimeType: string): Buffer {
    const options = this.parseMimeType(mimeType);
    const wavHeader = this.createWavHeader(Buffer.from(rawData, 'base64').length, options);
    const buffer = Buffer.from(rawData, 'base64');

    return Buffer.concat([wavHeader, buffer]);
  }

  private parseMimeType(mimeType: string): WavConversionOptions {
    const [fileType, ...params] = mimeType.split(';').map(s => s.trim());
    const [_, format] = fileType?.split('/') || [];

    const options: Partial<WavConversionOptions> = {
      numChannels: 1,
      sampleRate: 24000, // 默认采样率
      bitsPerSample: 16, // 默认位深
    };

    if (format && format.startsWith('L')) {
      const bits = parseInt(format.slice(1), 10);
      if (!isNaN(bits)) {
        options.bitsPerSample = bits;
      }
    }

    for (const param of params) {
      const [key, value] = param.split('=').map(s => s.trim());
      if (key === 'rate' && value) {
        options.sampleRate = parseInt(value, 10);
      }
    }

    return options as WavConversionOptions;
  }

  private createWavHeader(dataLength: number, options: WavConversionOptions): Buffer {
    const { numChannels, sampleRate, bitsPerSample } = options;

    const byteRate = sampleRate * numChannels * bitsPerSample / 8;
    const blockAlign = numChannels * bitsPerSample / 8;
    const buffer = Buffer.alloc(44);

    buffer.write('RIFF', 0);                      // ChunkID
    buffer.writeUInt32LE(36 + dataLength, 4);     // ChunkSize
    buffer.write('WAVE', 8);                      // Format
    buffer.write('fmt ', 12);                     // Subchunk1ID
    buffer.writeUInt32LE(16, 16);                 // Subchunk1Size (PCM)
    buffer.writeUInt16LE(1, 20);                  // AudioFormat (1 = PCM)
    buffer.writeUInt16LE(numChannels, 22);        // NumChannels
    buffer.writeUInt32LE(sampleRate, 24);         // SampleRate
    buffer.writeUInt32LE(byteRate, 28);           // ByteRate
    buffer.writeUInt16LE(blockAlign, 32);         // BlockAlign
    buffer.writeUInt16LE(bitsPerSample, 34);      // BitsPerSample
    buffer.write('data', 36);                     // Subchunk2ID
    buffer.writeUInt32LE(dataLength, 40);         // Subchunk2Size

    return buffer;
  }

  private estimateTokens(text: string): number {
    // 简单的token估算：中文字符约1.5个token，英文单词约1.3个token
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    const otherChars = text.length - chineseChars - englishWords;
    
    return Math.ceil(chineseChars * 1.5 + englishWords * 1.3 + otherChars * 0.5);
  }

  private detectLanguage(text: string): string {
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const totalChars = text.length;
    
    if (chineseChars / totalChars > 0.3) {
      return 'zh-CN';
    }
    return 'en-US';
  }
}
