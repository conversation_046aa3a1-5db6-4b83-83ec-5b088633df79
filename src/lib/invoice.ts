import { db } from '~/server/db';

// 发票数据接口
export interface InvoiceData {
  invoiceNumber: string;
  issueDate: Date;
  dueDate: Date;
  payment: {
    id: string;
    amount: number;
    currency: string;
    description: string;
    completedAt: Date;
    provider: string;
    creditPurchase?: {
      packageName: string;
      credits: number;
    };
  };
  user: {
    name: string;
    email: string;
  };
  company: {
    name: string;
    address: string;
    email: string;
    phone: string;
    website: string;
  };
}

// 生成发票号
export function generateInvoiceNumber(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const timestamp = now.getTime().toString().slice(-6);
  return `INV-${year}${month}-${timestamp}`;
}

// 创建发票记录
export async function createInvoice(paymentId: string): Promise<InvoiceData | null> {
  try {
    // 获取支付信息
    const payment = await db.payment.findUnique({
      where: { id: paymentId },
      include: {
        user: true,
        creditPurchase: true,
      },
    });

    if (!payment || payment.status !== 'COMPLETED') {
      throw new Error('Payment not found or not completed');
    }

    const invoiceNumber = generateInvoiceNumber();
    const issueDate = new Date();
    const dueDate = new Date(issueDate);
    dueDate.setDate(dueDate.getDate() + 30); // 30天付款期限

    // 公司信息
    const company = {
      name: 'Voctana',
      address: 'Phnom Penh, Cambodia',
      email: '<EMAIL>',
      phone: '+855 12 345 678',
      website: 'https://voctana.com',
    };

    const invoiceData: InvoiceData = {
      invoiceNumber,
      issueDate,
      dueDate,
      payment: {
        id: payment.id,
        amount: payment.amount,
        currency: payment.currency,
        description: payment.description || 'Voctana 积分充值',
        completedAt: payment.completedAt!,
        provider: payment.provider,
        creditPurchase: payment.creditPurchase ? {
          packageName: payment.creditPurchase.packageName,
          credits: payment.creditPurchase.credits,
        } : undefined,
      },
      user: {
        name: payment.user.name || 'Unknown User',
        email: payment.user.email || '',
      },
      company,
    };

    // 保存发票信息到支付记录的metadata中
    await db.payment.update({
      where: { id: paymentId },
      data: {
        metadata: {
          ...(payment.metadata as object || {}),
          invoice: {
            invoiceNumber,
            issueDate: issueDate.toISOString(),
            dueDate: dueDate.toISOString(),
            generated: true,
            generatedAt: new Date().toISOString(),
          },
        },
      },
    });

    return invoiceData;

  } catch (error) {
    console.error('Error creating invoice:', error);
    return null;
  }
}

// 生成发票HTML
export function generateInvoiceHTML(invoice: InvoiceData): string {
  const formatCurrency = (amount: number, currency: string) => {
    const symbol = currency === 'KHR' ? '៛' : '$';
    return `${symbol}${amount.toLocaleString()}`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('zh-CN');
  };

  return `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>发票 - ${invoice.invoiceNumber}</title>
      <style>
        body {
          font-family: 'Arial', sans-serif;
          margin: 0;
          padding: 20px;
          background-color: #f5f5f5;
        }
        .invoice-container {
          max-width: 800px;
          margin: 0 auto;
          background: white;
          padding: 40px;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 40px;
          border-bottom: 2px solid #667eea;
          padding-bottom: 20px;
        }
        .company-info h1 {
          color: #667eea;
          margin: 0;
          font-size: 28px;
        }
        .company-info p {
          margin: 5px 0;
          color: #666;
        }
        .invoice-info {
          text-align: right;
        }
        .invoice-info h2 {
          color: #333;
          margin: 0 0 10px 0;
          font-size: 24px;
        }
        .invoice-info p {
          margin: 5px 0;
          color: #666;
        }
        .billing-info {
          display: flex;
          justify-content: space-between;
          margin: 40px 0;
        }
        .billing-section h3 {
          color: #333;
          margin: 0 0 15px 0;
          font-size: 16px;
          text-transform: uppercase;
          letter-spacing: 1px;
        }
        .billing-section p {
          margin: 5px 0;
          color: #666;
        }
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin: 40px 0;
        }
        .items-table th {
          background: #667eea;
          color: white;
          padding: 15px;
          text-align: left;
          font-weight: bold;
        }
        .items-table td {
          padding: 15px;
          border-bottom: 1px solid #eee;
        }
        .items-table tr:nth-child(even) {
          background: #f9f9f9;
        }
        .total-section {
          text-align: right;
          margin-top: 30px;
        }
        .total-row {
          display: flex;
          justify-content: flex-end;
          margin: 10px 0;
        }
        .total-label {
          width: 150px;
          text-align: right;
          padding-right: 20px;
          font-weight: bold;
        }
        .total-amount {
          width: 120px;
          text-align: right;
          font-weight: bold;
        }
        .grand-total {
          border-top: 2px solid #667eea;
          padding-top: 10px;
          font-size: 18px;
          color: #667eea;
        }
        .payment-info {
          background: #f0f8ff;
          padding: 20px;
          border-radius: 8px;
          margin: 30px 0;
          border-left: 4px solid #667eea;
        }
        .payment-info h3 {
          color: #667eea;
          margin: 0 0 10px 0;
        }
        .footer {
          text-align: center;
          margin-top: 40px;
          padding-top: 20px;
          border-top: 1px solid #eee;
          color: #666;
          font-size: 14px;
        }
        @media print {
          body { background: white; }
          .invoice-container { box-shadow: none; }
        }
      </style>
    </head>
    <body>
      <div class="invoice-container">
        <!-- Header -->
        <div class="header">
          <div class="company-info">
            <h1>${invoice.company.name}</h1>
            <p>${invoice.company.address}</p>
            <p>邮箱: ${invoice.company.email}</p>
            <p>电话: ${invoice.company.phone}</p>
            <p>网站: ${invoice.company.website}</p>
          </div>
          <div class="invoice-info">
            <h2>发票</h2>
            <p><strong>发票号:</strong> ${invoice.invoiceNumber}</p>
            <p><strong>开票日期:</strong> ${formatDate(invoice.issueDate)}</p>
            <p><strong>付款期限:</strong> ${formatDate(invoice.dueDate)}</p>
          </div>
        </div>

        <!-- Billing Information -->
        <div class="billing-info">
          <div class="billing-section">
            <h3>账单地址</h3>
            <p><strong>${invoice.user.name}</strong></p>
            <p>${invoice.user.email}</p>
          </div>
          <div class="billing-section">
            <h3>积分包信息</h3>
            <p><strong>积分包:</strong> ${invoice.payment.creditPurchase?.packageName || '积分充值'}</p>
            <p><strong>积分数量:</strong> ${invoice.payment.creditPurchase?.credits?.toLocaleString() || 0} 积分</p>
            <p><strong>购买时间:</strong> ${formatDate(invoice.payment.completedAt)}</p>
          </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
          <thead>
            <tr>
              <th>服务描述</th>
              <th>积分数量</th>
              <th>数量</th>
              <th>单价</th>
              <th>总计</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <strong>${invoice.payment.creditPurchase?.packageName || '积分充值'}</strong><br>
                <small>语音生成积分包</small>
              </td>
              <td>${invoice.payment.creditPurchase?.credits?.toLocaleString() || 0} 积分</td>
              <td>1</td>
              <td>${formatCurrency(invoice.payment.amount, invoice.payment.currency)}</td>
              <td>${formatCurrency(invoice.payment.amount, invoice.payment.currency)}</td>
            </tr>
          </tbody>
        </table>

        <!-- Total Section -->
        <div class="total-section">
          <div class="total-row">
            <div class="total-label">小计:</div>
            <div class="total-amount">${formatCurrency(invoice.payment.amount, invoice.payment.currency)}</div>
          </div>
          <div class="total-row">
            <div class="total-label">税费:</div>
            <div class="total-amount">${formatCurrency(0, invoice.payment.currency)}</div>
          </div>
          <div class="total-row grand-total">
            <div class="total-label">总计:</div>
            <div class="total-amount">${formatCurrency(invoice.payment.amount, invoice.payment.currency)}</div>
          </div>
        </div>

        <!-- Payment Information -->
        <div class="payment-info">
          <h3>支付信息</h3>
          <p><strong>支付方式:</strong> ${invoice.payment.provider}</p>
          <p><strong>支付时间:</strong> ${formatDate(invoice.payment.completedAt)}</p>
          <p><strong>交易ID:</strong> ${invoice.payment.id}</p>
          <p><strong>状态:</strong> <span style="color: green;">已支付</span></p>
        </div>

        <!-- Footer -->
        <div class="footer">
          <p>感谢您选择 ${invoice.company.name}！</p>
          <p>如有任何问题，请联系我们的客服团队。</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

// 获取发票数据
export async function getInvoiceData(paymentId: string): Promise<InvoiceData | null> {
  try {
    const payment = await db.payment.findUnique({
      where: { id: paymentId },
      include: {
        user: true,
        creditPurchase: true,
      },
    });

    if (!payment) {
      return null;
    }

    const metadata = payment.metadata as any;
    const invoiceInfo = metadata?.invoice;

    if (!invoiceInfo) {
      // 如果没有发票信息，创建一个
      return await createInvoice(paymentId);
    }

    // 构建发票数据
    const company = {
      name: 'Voctana',
      address: 'Phnom Penh, Cambodia',
      email: '<EMAIL>',
      phone: '+855 12 345 678',
      website: 'https://voctana.com',
    };

    return {
      invoiceNumber: invoiceInfo.invoiceNumber,
      issueDate: new Date(invoiceInfo.issueDate),
      dueDate: new Date(invoiceInfo.dueDate),
      payment: {
        id: payment.id,
        amount: payment.amount,
        currency: payment.currency,
        description: payment.description || 'Voctana 积分充值',
        completedAt: payment.completedAt!,
        provider: payment.provider,
        creditPurchase: payment.creditPurchase ? {
          packageName: payment.creditPurchase.packageName,
          credits: payment.creditPurchase.credits,
        } : undefined,
      },
      user: {
        name: payment.user.name || 'Unknown User',
        email: payment.user.email || '',
      },
      company,
    };

  } catch (error) {
    console.error('Error getting invoice data:', error);
    return null;
  }
}

export const InvoiceService = {
  create: createInvoice,
  generateHTML: generateInvoiceHTML,
  getData: getInvoiceData,
  generateNumber: generateInvoiceNumber,
};

export default InvoiceService;
