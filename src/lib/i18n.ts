// Simple i18n implementation for the application
export type Locale = 'en' | 'zh';

export const locales: Locale[] = ['en', 'zh'];
export const defaultLocale: Locale = 'en';

export const languages = {
  en: { name: 'English', nativeName: 'English', flag: '🇺🇸' },
  zh: { name: 'Chinese', nativeName: '中文', flag: '🇨🇳' },
};

// Translation keys and values
export const translations = {
  en: {
    site: {
      title: "Voctana - AI-Powered Khmer Text-to-Speech Platform",
      description: "High-quality Khmer AI voice synthesis platform with multiple character voices for education, media, and business applications.",
      keywords: "Khmer TTS, Cambodia voice synthesis, AI voice, text-to-speech"
    },
    navigation: {
      home: "Home",
      characters: "Characters",
      pricing: "Pricing",
      demo: "Demo",
      about: "About",
      login: "Sign In",
      signup: "Sign Up",
      dashboard: "Dashboard",
      generate: "Generate",
      history: "History",
      profile: "Profile",
      admin: "Admin",
      settings: "Settings"
    },
    sidebar: {
      voice_generation: "Voice Generation",
      text_to_speech: "Text to Speech",
      batch_generation: "Batch Generation",
      history: "History",
      generation_history: "Generation History",
      download_management: "Download Management",
      profile: "Profile",
      personal_info: "Personal Info",
      character_packages: "Character Packages",
      usage_stats: "Usage Statistics",
      subscription: "Subscription",
      admin: "Admin",
      user_management: "User Management",
      voice_management: "Voice Management",
      order_management: "Order Management"
    },
    dashboard: {
      welcome_back: "Welcome back, {{name}}!",
      start_journey: "Start your voice creation journey",
      free_plan: "Free Plan",
      voice_generation: "Voice Generation",
      start_creating: "Start Creating",
      convert_text: "Convert text to high-quality speech",
      available_characters: "Available Characters",
      history: "History",
      generation_records: "Generation Records",
      usage_stats: "Usage Statistics",
      characters_used: "Characters Used",
      recent_generations: "Recent Generations",
      recent_activity: "Your recent voice generation records",
      quick_start: "Quick Start",
      common_functions: "Quick access to common functions",
      new_generation: "New Voice Generation",
      view_history: "View Generation History",
      upgrade_subscription: "Upgrade Subscription",
      completed: "Completed"
    },
    generate: {
      title: "Voice Generation",
      subtitle: "Convert your text to high-quality Khmer speech",
      input_text: "Input Text",
      input_placeholder: "Enter the Khmer text you want to convert to speech...",
      character_count: "{{count}}/1000 characters",
      estimated_cost: "Estimated cost: {{count}} characters",
      select_character: "Select Voice Character",
      select_character_desc: "Choose a voice character for your speech generation",
      search_characters: "Search characters...",
      voice_style: "Voice Style",
      voice_style_desc: "Adjust the style and tone of the voice",
      generate_button: "Generate Speech",
      generating: "Generating...",
      download: "Download",
      play: "Play",
      pause: "Pause",
      error_occurred: "An error occurred",
      generation_failed: "Speech generation failed",
      try_again: "Try Again",
      recent_generations: "Recent Generations",
      loading: "Loading...",
      completed: "Completed",
      failed: "Failed",
      character_quota: "Character Quota",
      remaining: "Remaining: {{count}} characters",
      monthly_characters: "Monthly Characters",
      monthly_audio: "Monthly Audio",
      current_plan: "Current Plan",
      free_plan: "Free",
      basic_plan: "Basic",
      pro_plan: "Pro",
      enterprise_plan: "Enterprise",
      load_failed: "Load Failed",
      voice_settings: "Voice Settings",
      speed: "Speed",
      pitch: "Pitch",
      volume: "Volume",
      quality: "Quality",
      fast_quality: "Standard",
      high_quality: "Professional",
      standard_quality_desc: "Lower cost, good quality",
      professional_quality_desc: "Higher cost, premium quality",
      style_prompt: "Define Style",
      style_prompt_placeholder: "Describe the voice style you want...",
      smart_recommendations: "Smart Recommendations",
      style_templates: "Style Templates",
      select_style: "Select Style",
      recommended_for_you: "Recommended for you",
      apply_template: "Apply Template",
      validating: "Validating...",
      uploading: "Uploading...",
      complete: "Complete",
      error: "Error",
      generation_progress: "Generation Progress",
      audio_preview: "Audio Preview",
      character_info: "Character: {{name}}",
      duration_info: "Duration: {{duration}}s",
      cost_info: "Cost: {{cost}} characters",
      download_audio: "Download Audio",
      play_audio: "Play Audio",
      pause_audio: "Pause Audio"
    },
    history: {
      title: "Generation History",
      subtitle: "View and manage your voice generation history",
      search_placeholder: "Search by text content or character name...",
      filter_by_format: "Filter by Format",
      all_formats: "All Formats",
      mp3_format: "MP3",
      wav_format: "WAV",
      no_results: "No generation records found",
      no_results_desc: "You haven't generated any voice content yet",
      start_generating: "Start Generating",
      login_required: "Login Required",
      login_required_desc: "You need to login to view history",
      login_button: "Login",
      character_name: "Character: {{name}}",
      generated_at: "Generated: {{date}}",
      duration: "Duration: {{duration}}s",
      characters_used: "Characters: {{count}}",
      download_audio: "Download Audio",
      delete_record: "Delete Record",
      play_audio: "Play Audio",
      pause_audio: "Pause Audio",
      status_completed: "Completed",
      status_failed: "Failed",
      status_processing: "Processing",
      confirm_delete: "Are you sure you want to delete this record?",
      delete_success: "Record deleted successfully",
      delete_error: "Failed to delete record",
      loading: "Loading...",
      total_records: "Total: {{count}} records",
      this_month: "This Month",
      last_month: "Last Month",
      older: "Older"
    },
    profile: {
      title: "Profile",
      subtitle: "Manage your account settings and preferences",
      personal_info: "Personal Information",
      account_settings: "Account Settings",
      subscription: "Subscription",
      usage_stats: "Usage Statistics",
      name: "Name",
      email: "Email",
      phone: "Phone Number",
      avatar: "Avatar",
      change_avatar: "Change Avatar",
      upload_avatar: "Upload Avatar",
      remove_avatar: "Remove Avatar",
      current_plan: "Current Plan",
      plan_status: "Status",
      next_billing: "Next Billing",
      upgrade_plan: "Upgrade Plan",
      cancel_subscription: "Cancel Subscription",
      billing_history: "Billing History",
      usage_this_month: "Usage This Month",
      characters_used: "Characters Used",
      audio_generated: "Audio Generated",
      quota_remaining: "Quota Remaining",
      reset_date: "Reset Date",
      change_password: "Change Password",
      current_password: "Current Password",
      new_password: "New Password",
      confirm_password: "Confirm Password",
      save_changes: "Save Changes",
      cancel: "Cancel",
      update_success: "Profile updated successfully",
      update_error: "Failed to update profile",
      password_changed: "Password changed successfully",
      password_error: "Failed to change password",
      invalid_password: "Invalid current password",
      password_mismatch: "Passwords do not match",
      required_field: "This field is required",
      invalid_email: "Invalid email address",
      account_info: "Account Information",
      member_since: "Member Since",
      last_login: "Last Login",
      account_status: "Account Status",
      active: "Active",
      inactive: "Inactive",
      suspended: "Suspended",
      delete_account: "Delete Account",
      delete_account_warning: "This action cannot be undone",
      confirm_delete: "Are you sure you want to delete your account?",
      notifications: "Notifications",
      email_notifications: "Email Notifications",
      marketing_emails: "Marketing Emails",
      security_alerts: "Security Alerts",
      product_updates: "Product Updates",
      privacy_settings: "Privacy Settings",
      data_export: "Export Data",
      download_data: "Download My Data"
    },
    settings: {
      title: "Settings",
      subtitle: "Configure your application preferences",
      general: "General",
      appearance: "Appearance",
      language: "Language",
      theme: "Theme",
      light_theme: "Light",
      dark_theme: "Dark",
      system_theme: "System",
      audio: "Audio Settings",
      default_quality: "Default Quality",
      auto_play: "Auto Play",
      download_format: "Download Format",
      api: "API Settings",
      api_key: "API Key",
      api_endpoint: "API Endpoint",
      generate_key: "Generate New Key",
      revoke_key: "Revoke Key",
      api_usage: "API Usage",
      rate_limit: "Rate Limit",
      requests_per_minute: "Requests per minute",
      security: "Security",
      two_factor: "Two-Factor Authentication",
      enable_2fa: "Enable 2FA",
      disable_2fa: "Disable 2FA",
      backup_codes: "Backup Codes",
      generate_codes: "Generate Backup Codes",
      login_history: "Login History",
      active_sessions: "Active Sessions",
      revoke_session: "Revoke Session",
      privacy: "Privacy",
      data_collection: "Data Collection",
      analytics: "Analytics",
      crash_reports: "Crash Reports",
      performance_data: "Performance Data",
      integrations: "Integrations",
      webhooks: "Webhooks",
      add_webhook: "Add Webhook",
      webhook_url: "Webhook URL",
      webhook_events: "Events",
      test_webhook: "Test Webhook",
      advanced: "Advanced",
      developer_mode: "Developer Mode",
      debug_logs: "Debug Logs",
      export_settings: "Export Settings",
      import_settings: "Import Settings",
      reset_settings: "Reset to Default",
      confirm_reset: "Are you sure you want to reset all settings?",
      settings_saved: "Settings saved successfully",
      settings_error: "Failed to save settings",
      invalid_url: "Invalid URL format",
      webhook_added: "Webhook added successfully",
      webhook_error: "Failed to add webhook"
    },
    admin: {
      title: "Admin Dashboard",
      subtitle: "Manage users, content, and system settings",
      overview: "Overview",
      users: "User Management",
      voices: "Voice Management",
      orders: "Order Management",
      analytics: "Analytics",
      system: "System",
      total_users: "Total Users",
      active_users: "Active Users",
      new_users_today: "New Users Today",
      total_generations: "Total Generations",
      revenue_this_month: "Revenue This Month",
      system_status: "System Status",
      online: "Online",
      maintenance: "Maintenance",
      offline: "Offline",
      user_list: "User List",
      user_details: "User Details",
      user_id: "User ID",
      username: "Username",
      email: "Email",
      role: "Role",
      status: "Status",
      created_at: "Created",
      last_active: "Last Active",
      actions: "Actions",
      edit_user: "Edit User",
      delete_user: "Delete User",
      ban_user: "Ban User",
      unban_user: "Unban User",
      reset_password: "Reset Password",
      view_activity: "View Activity",
      user_admin: "Admin",
      user_user: "User",
      user_moderator: "Moderator",
      voice_characters: "Voice Characters",
      add_character: "Add Character",
      character_name: "Character Name",
      character_name_en: "English Name",
      gender: "Gender",
      description: "Description",
      preview_audio: "Preview Audio",
      api_provider: "API Provider",
      api_voice_name: "API Voice Name",
      tier: "Tier",
      is_active: "Active",
      sort_order: "Sort Order",
      upload_avatar: "Upload Avatar",
      upload_preview: "Upload Preview",
      save_character: "Save Character",
      character_saved: "Character saved successfully",
      character_error: "Failed to save character",
      delete_character: "Delete Character",
      confirm_delete_character: "Are you sure you want to delete this character?",
      order_list: "Order List",
      order_id: "Order ID",
      customer: "Customer",
      amount: "Amount",
      payment_status: "Payment Status",
      order_status: "Order Status",
      order_date: "Order Date",
      view_order: "View Order",
      refund_order: "Refund Order",
      paid: "Paid",
      pending: "Pending",
      failed: "Failed",
      refunded: "Refunded",
      processing: "Processing",
      completed: "Completed",
      cancelled: "Cancelled",
      analytics_overview: "Analytics Overview",
      daily_stats: "Daily Statistics",
      monthly_stats: "Monthly Statistics",
      user_growth: "User Growth",
      revenue_chart: "Revenue Chart",
      popular_voices: "Popular Voices",
      usage_patterns: "Usage Patterns",
      system_settings: "System Settings",
      maintenance_mode: "Maintenance Mode",
      enable_maintenance: "Enable Maintenance Mode",
      disable_maintenance: "Disable Maintenance Mode",
      system_logs: "System Logs",
      error_logs: "Error Logs",
      access_logs: "Access Logs",
      performance_metrics: "Performance Metrics",
      database_status: "Database Status",
      cache_status: "Cache Status",
      storage_usage: "Storage Usage",
      backup_system: "Backup System",
      create_backup: "Create Backup",
      restore_backup: "Restore Backup",
      backup_history: "Backup History",
      search_users: "Search users...",
      search_orders: "Search orders...",
      filter_by_role: "Filter by Role",
      filter_by_status: "Filter by Status",
      all_roles: "All Roles",
      all_statuses: "All Statuses",
      export_data: "Export Data",
      import_data: "Import Data",
      bulk_actions: "Bulk Actions",
      select_all: "Select All",
      delete_selected: "Delete Selected",
      ban_selected: "Ban Selected",
      export_selected: "Export Selected"
    },
    conversation: {
      title: "Multi-Character Conversation",
      subtitle: "Create conversations with multiple voice characters",
      add_character: "Add Character",
      remove_character: "Remove Character",
      character_name: "Character Name",
      select_voice: "Select Voice",
      dialogue_text: "Dialogue Text",
      add_dialogue: "Add Dialogue",
      remove_dialogue: "Remove Dialogue",
      generate_conversation: "Generate Conversation",
      preview_conversation: "Preview Conversation",
      download_conversation: "Download Conversation",
      conversation_settings: "Conversation Settings",
      pause_between_lines: "Pause Between Lines",
      background_music: "Background Music",
      export_format: "Export Format",
      scene_description: "Scene Description",
      character_count: "Characters: {{count}}",
      dialogue_count: "Dialogues: {{count}}",
      estimated_duration: "Estimated Duration: {{duration}}",
      character_required: "At least one character is required",
      dialogue_required: "At least one dialogue is required",
      voice_required: "Please select a voice for all characters",
      text_required: "Please enter text for all dialogues",
      generating_conversation: "Generating conversation...",
      conversation_generated: "Conversation generated successfully",
      conversation_error: "Failed to generate conversation",
      play_conversation: "Play Conversation",
      pause_conversation: "Pause Conversation",
      stop_conversation: "Stop Conversation",
      conversation_timeline: "Conversation Timeline",
      character_settings: "Character Settings",
      voice_settings: "Voice Settings",
      speed: "Speed",
      pitch: "Pitch",
      volume: "Volume",
      emotion: "Emotion",
      neutral: "Neutral",
      happy: "Happy",
      sad: "Sad",
      angry: "Angry",
      excited: "Excited",
      calm: "Calm",
      dramatic: "Dramatic",
      whisper: "Whisper",
      shout: "Shout",
      conversation_template: "Conversation Template",
      load_template: "Load Template",
      save_template: "Save Template",
      template_name: "Template Name",
      interview_template: "Interview",
      dialogue_template: "Dialogue",
      story_template: "Story Narration",
      meeting_template: "Meeting",
      custom_template: "Custom",
      character_a: "Character A",
      character_b: "Character B",
      narrator: "Narrator",
      interviewer: "Interviewer",
      interviewee: "Interviewee",
      host: "Host",
      guest: "Guest"
    },
    hero: {
      title: "Transform Text into Natural Khmer Speech",
      subtitle: "Professional AI-powered text-to-speech platform designed specifically for the Khmer language",
      description: "Experience high-quality voice synthesis with multiple character options, perfect for education, content creation, and business applications.",
      cta_primary: "Try Free Demo",
      cta_secondary: "View Pricing",
      trusted_by: "Trusted by educators, content creators, and businesses across Cambodia"
    },
    features: {
      title: "Why Choose Voctana?",
      subtitle: "Advanced AI technology meets Khmer language expertise",
      items: {
        natural_voice: {
          title: "Natural Voice Quality",
          description: "State-of-the-art AI generates human-like Khmer speech with proper pronunciation and intonation"
        },
        multiple_characters: {
          title: "Multiple Voice Characters",
          description: "Choose from diverse male and female voices, each with unique personality and speaking style"
        },
        fast_generation: {
          title: "Lightning Fast",
          description: "Generate high-quality audio in seconds with our optimized processing pipeline"
        },
        easy_integration: {
          title: "Easy Integration",
          description: "Simple API and web interface make it easy to integrate into your applications and workflows"
        },
        affordable_pricing: {
          title: "Affordable Pricing",
          description: "Flexible pricing plans to suit individuals, small businesses, and enterprises"
        },
        khmer_optimized: {
          title: "Khmer Language Optimized",
          description: "Specifically trained and optimized for Khmer language nuances and cultural context"
        }
      }
    },
    characters: {
      title: "Voice Characters",
      subtitle: "Choose from our diverse collection of AI voice characters",
      preview_audio: "Preview Audio",
      fast_quality: "Fast Quality",
      high_quality: "High Quality",
      male: "Male",
      female: "Female",
      neutral: "Neutral",
      free_tier: "Free",
      basic_tier: "Basic",
      pro_tier: "Pro",
      filter_by_gender: "Filter by Gender",
      filter_by_tier: "Filter by Tier",
      filter_by_language: "Filter by Language",
      all_characters: "All Characters",
      play: "Play",
      pause: "Pause",
      loading: "Loading...",
      error_loading: "Error loading audio",
      languages: "Languages",
      voice_characters: "Voice Characters",
      select_language: "Select Language",
      all_languages: "All Languages",
      no_characters: "No characters available",
      playing: "Playing...",
      play_demo: "Play Demo",
      cta_description: "Experience the power of AI voice generation with our diverse character collection",
      try_now: "Try Now",
      showing_characters: "Showing {{count}} of {{total}} characters",
      explore_more_languages: "Explore More Languages",
      explore_description: "Discover voice characters in different languages and experience diverse cultural expressions",
      total_languages_available: "{{count}} languages available in total"
    },
    pricing: {
      title: "Simple, Transparent Pricing",
      subtitle: "Choose the plan that fits your needs",
      monthly: "Monthly",
      yearly: "Yearly",
      save_percent: "Save {{percent}}%",
      most_popular: "Most Popular",
      get_started: "Get Started",
      contact_sales: "Contact Sales",
      free_trial: "Start Free Trial",
      currency_usd: "USD",
      currency_khr: "KHR",
      per_month: "/month",
      per_year: "/year",
      features: {
        characters: "{{count}} characters/month",
        unlimited_characters: "Unlimited characters",
        flash_tts: "Flash TTS",
        pro_tts: "Pro TTS",
        basic_voices: "Basic voice characters",
        all_voices: "All voice characters",
        custom_voices: "Custom voice characters",
        api_access: "API access",
        priority_support: "Priority support",
        commercial_license: "Commercial license"
      },
      plans: {
        free: {
          name: "Free",
          description: "Perfect for trying out our service"
        },
        starter: {
          name: "Starter",
          description: "Great for individuals and small projects"
        },
        professional: {
          name: "Professional",
          description: "Perfect for businesses and content creators"
        },
        enterprise: {
          name: "Enterprise",
          description: "Custom solutions for large organizations"
        }
      }
    },
    common: {
      loading: "Loading...",
      error: "Error",
      success: "Success",
      try_again: "Try Again",
      learn_more: "Learn More",
      get_started: "Get Started",
      contact_us: "Contact Us",
      language: "Language"
    }
  },
  zh: {
    site: {
      title: "Voctana - AI高棉语语音合成平台",
      description: "高质量的高棉语AI语音合成平台，提供多种角色声音，适用于教育、媒体和商业应用。",
      keywords: "高棉语TTS,柬埔寨语音合成,AI语音,文字转语音"
    },
    navigation: {
      home: "首页",
      characters: "角色",
      pricing: "定价",
      demo: "演示",
      about: "关于",
      login: "登录",
      signup: "注册",
      dashboard: "控制台",
      generate: "生成",
      history: "历史",
      profile: "个人",
      admin: "管理",
      settings: "设置"
    },
    sidebar: {
      voice_generation: "语音生成",
      text_to_speech: "文本转语音",
      batch_generation: "批量生成",
      history: "历史记录",
      generation_history: "生成历史",
      download_management: "下载管理",
      profile: "个人中心",
      personal_info: "个人信息",
      character_packages: "字符包购买",
      usage_stats: "使用统计",
      subscription: "订阅管理",
      admin: "管理员",
      user_management: "用户管理",
      voice_management: "语音管理",
      order_management: "订单管理"
    },
    dashboard: {
      welcome_back: "欢迎回来，{{name}}！",
      start_journey: "开始您的语音创作之旅",
      free_plan: "免费版",
      voice_generation: "语音生成",
      start_creating: "开始创作",
      convert_text: "将文本转换为高质量语音",
      available_characters: "可用语音角色",
      history: "历史记录",
      generation_records: "生成记录",
      usage_stats: "使用统计",
      characters_used: "已用字符数",
      recent_generations: "最近生成",
      recent_activity: "您最近的语音生成记录",
      quick_start: "快速开始",
      common_functions: "常用功能快速访问",
      new_generation: "新建语音生成",
      view_history: "查看生成历史",
      upgrade_subscription: "升级订阅",
      completed: "完成"
    },
    generate: {
      title: "语音生成",
      subtitle: "将您的文本转换为高质量的高棉语语音",
      input_text: "输入文本",
      input_placeholder: "请输入您想要转换为语音的高棉语文本...",
      character_count: "{{count}}/1000 字符",
      estimated_cost: "预计消耗: {{count}} 字符",
      select_character: "选择语音角色",
      select_character_desc: "为您的语音生成选择一个语音角色",
      search_characters: "搜索角色...",
      voice_style: "语音风格",
      voice_style_desc: "调整语音的风格和语调",
      generate_button: "生成语音",
      generating: "生成中...",
      download: "下载",
      play: "播放",
      pause: "暂停",
      error_occurred: "发生错误",
      generation_failed: "语音生成失败",
      try_again: "重试",
      recent_generations: "最近生成",
      loading: "加载中...",
      completed: "完成",
      failed: "失败",
      character_quota: "字符配额",
      remaining: "剩余: {{count}} 字符",
      monthly_characters: "本月字符",
      monthly_audio: "本月音频",
      current_plan: "当前计划",
      free_plan: "免费版",
      basic_plan: "基础版",
      pro_plan: "专业版",
      enterprise_plan: "企业版",
      load_failed: "加载失败",
      voice_settings: "语音设置",
      speed: "语速",
      pitch: "音调",
      volume: "音量",
      quality: "质量",
      fast_quality: "标准",
      high_quality: "专业",
      standard_quality_desc: "消耗积分少，质量良好",
      professional_quality_desc: "消耗积分多，质量优秀",
      style_prompt: "定义风格",
      style_prompt_placeholder: "描述您想要的语音风格...",
      smart_recommendations: "智能推荐",
      style_templates: "风格模板",
      select_style: "选择风格",
      recommended_for_you: "为您推荐",
      apply_template: "应用模板",
      validating: "验证中...",
      uploading: "上传中...",
      complete: "完成",
      error: "错误",
      generation_progress: "生成进度",
      audio_preview: "音频预览",
      character_info: "角色: {{name}}",
      duration_info: "时长: {{duration}}秒",
      cost_info: "消耗: {{cost}} 字符",
      download_audio: "下载音频",
      play_audio: "播放音频",
      pause_audio: "暂停音频"
    },
    history: {
      title: "生成历史",
      subtitle: "查看和管理您的语音生成历史记录",
      search_placeholder: "按文本内容或角色名称搜索...",
      filter_by_format: "按格式筛选",
      all_formats: "所有格式",
      mp3_format: "MP3",
      wav_format: "WAV",
      no_results: "未找到生成记录",
      no_results_desc: "您还没有生成任何语音内容",
      start_generating: "开始生成",
      login_required: "需要登录",
      login_required_desc: "您需要登录才能查看历史记录",
      login_button: "登录",
      character_name: "角色: {{name}}",
      generated_at: "生成时间: {{date}}",
      duration: "时长: {{duration}}秒",
      characters_used: "字符数: {{count}}",
      download_audio: "下载音频",
      delete_record: "删除记录",
      play_audio: "播放音频",
      pause_audio: "暂停音频",
      status_completed: "完成",
      status_failed: "失败",
      status_processing: "处理中",
      confirm_delete: "确定要删除这条记录吗？",
      delete_success: "记录删除成功",
      delete_error: "删除记录失败",
      loading: "加载中...",
      total_records: "共 {{count}} 条记录",
      this_month: "本月",
      last_month: "上月",
      older: "更早"
    },
    profile: {
      title: "个人资料",
      subtitle: "管理您的账户设置和偏好",
      personal_info: "个人信息",
      account_settings: "账户设置",
      subscription: "订阅",
      usage_stats: "使用统计",
      name: "姓名",
      email: "邮箱",
      phone: "电话号码",
      avatar: "头像",
      change_avatar: "更换头像",
      upload_avatar: "上传头像",
      remove_avatar: "移除头像",
      current_plan: "当前计划",
      plan_status: "状态",
      next_billing: "下次计费",
      upgrade_plan: "升级计划",
      cancel_subscription: "取消订阅",
      billing_history: "账单历史",
      usage_this_month: "本月使用情况",
      characters_used: "已用字符数",
      audio_generated: "生成音频数",
      quota_remaining: "剩余配额",
      reset_date: "重置日期",
      change_password: "修改密码",
      current_password: "当前密码",
      new_password: "新密码",
      confirm_password: "确认密码",
      save_changes: "保存更改",
      cancel: "取消",
      update_success: "资料更新成功",
      update_error: "资料更新失败",
      password_changed: "密码修改成功",
      password_error: "密码修改失败",
      invalid_password: "当前密码错误",
      password_mismatch: "密码不匹配",
      required_field: "此字段为必填项",
      invalid_email: "邮箱格式无效",
      account_info: "账户信息",
      member_since: "注册时间",
      last_login: "最后登录",
      account_status: "账户状态",
      active: "活跃",
      inactive: "非活跃",
      suspended: "已暂停",
      delete_account: "删除账户",
      delete_account_warning: "此操作无法撤销",
      confirm_delete: "确定要删除您的账户吗？",
      notifications: "通知",
      email_notifications: "邮件通知",
      marketing_emails: "营销邮件",
      security_alerts: "安全警报",
      product_updates: "产品更新",
      privacy_settings: "隐私设置",
      data_export: "导出数据",
      download_data: "下载我的数据"
    },
    settings: {
      title: "设置",
      subtitle: "配置您的应用程序偏好",
      general: "通用",
      appearance: "外观",
      language: "语言",
      theme: "主题",
      light_theme: "浅色",
      dark_theme: "深色",
      system_theme: "跟随系统",
      audio: "音频设置",
      default_quality: "默认质量",
      auto_play: "自动播放",
      download_format: "下载格式",
      api: "API设置",
      api_key: "API密钥",
      api_endpoint: "API端点",
      generate_key: "生成新密钥",
      revoke_key: "撤销密钥",
      api_usage: "API使用情况",
      rate_limit: "速率限制",
      requests_per_minute: "每分钟请求数",
      security: "安全",
      two_factor: "双因素认证",
      enable_2fa: "启用2FA",
      disable_2fa: "禁用2FA",
      backup_codes: "备份代码",
      generate_codes: "生成备份代码",
      login_history: "登录历史",
      active_sessions: "活跃会话",
      revoke_session: "撤销会话",
      privacy: "隐私",
      data_collection: "数据收集",
      analytics: "分析",
      crash_reports: "崩溃报告",
      performance_data: "性能数据",
      integrations: "集成",
      webhooks: "Webhooks",
      add_webhook: "添加Webhook",
      webhook_url: "Webhook URL",
      webhook_events: "事件",
      test_webhook: "测试Webhook",
      advanced: "高级",
      developer_mode: "开发者模式",
      debug_logs: "调试日志",
      export_settings: "导出设置",
      import_settings: "导入设置",
      reset_settings: "重置为默认",
      confirm_reset: "确定要重置所有设置吗？",
      settings_saved: "设置保存成功",
      settings_error: "设置保存失败",
      invalid_url: "URL格式无效",
      webhook_added: "Webhook添加成功",
      webhook_error: "Webhook添加失败"
    },
    admin: {
      title: "管理员控制台",
      subtitle: "管理用户、内容和系统设置",
      overview: "概览",
      users: "用户管理",
      voices: "语音管理",
      orders: "订单管理",
      analytics: "分析",
      system: "系统",
      total_users: "总用户数",
      active_users: "活跃用户",
      new_users_today: "今日新用户",
      total_generations: "总生成数",
      revenue_this_month: "本月收入",
      system_status: "系统状态",
      online: "在线",
      maintenance: "维护中",
      offline: "离线",
      user_list: "用户列表",
      user_details: "用户详情",
      user_id: "用户ID",
      username: "用户名",
      email: "邮箱",
      role: "角色",
      status: "状态",
      created_at: "创建时间",
      last_active: "最后活跃",
      actions: "操作",
      edit_user: "编辑用户",
      delete_user: "删除用户",
      ban_user: "封禁用户",
      unban_user: "解封用户",
      reset_password: "重置密码",
      view_activity: "查看活动",
      user_admin: "管理员",
      user_user: "用户",
      user_moderator: "版主",
      voice_characters: "语音角色",
      add_character: "添加角色",
      character_name: "角色名称",
      character_name_en: "英文名称",
      gender: "性别",
      description: "描述",
      preview_audio: "预览音频",
      api_provider: "API提供商",
      api_voice_name: "API语音名称",
      tier: "层级",
      is_active: "启用",
      sort_order: "排序",
      upload_avatar: "上传头像",
      upload_preview: "上传预览",
      save_character: "保存角色",
      character_saved: "角色保存成功",
      character_error: "角色保存失败",
      delete_character: "删除角色",
      confirm_delete_character: "确定要删除这个角色吗？",
      order_list: "订单列表",
      order_id: "订单ID",
      customer: "客户",
      amount: "金额",
      payment_status: "支付状态",
      order_status: "订单状态",
      order_date: "订单日期",
      view_order: "查看订单",
      refund_order: "退款订单",
      paid: "已支付",
      pending: "待处理",
      failed: "失败",
      refunded: "已退款",
      processing: "处理中",
      completed: "已完成",
      cancelled: "已取消",
      analytics_overview: "分析概览",
      daily_stats: "日统计",
      monthly_stats: "月统计",
      user_growth: "用户增长",
      revenue_chart: "收入图表",
      popular_voices: "热门语音",
      usage_patterns: "使用模式",
      system_settings: "系统设置",
      maintenance_mode: "维护模式",
      enable_maintenance: "启用维护模式",
      disable_maintenance: "禁用维护模式",
      system_logs: "系统日志",
      error_logs: "错误日志",
      access_logs: "访问日志",
      performance_metrics: "性能指标",
      database_status: "数据库状态",
      cache_status: "缓存状态",
      storage_usage: "存储使用",
      backup_system: "备份系统",
      create_backup: "创建备份",
      restore_backup: "恢复备份",
      backup_history: "备份历史",
      search_users: "搜索用户...",
      search_orders: "搜索订单...",
      filter_by_role: "按角色筛选",
      filter_by_status: "按状态筛选",
      all_roles: "所有角色",
      all_statuses: "所有状态",
      export_data: "导出数据",
      import_data: "导入数据",
      bulk_actions: "批量操作",
      select_all: "全选",
      delete_selected: "删除选中",
      ban_selected: "封禁选中",
      export_selected: "导出选中"
    },
    conversation: {
      title: "多角色对话",
      subtitle: "创建多个语音角色的对话场景",
      add_character: "添加角色",
      remove_character: "移除角色",
      character_name: "角色名称",
      select_voice: "选择语音",
      dialogue_text: "对话文本",
      add_dialogue: "添加对话",
      remove_dialogue: "移除对话",
      generate_conversation: "生成对话",
      preview_conversation: "预览对话",
      download_conversation: "下载对话",
      conversation_settings: "对话设置",
      pause_between_lines: "对话间隔",
      background_music: "背景音乐",
      export_format: "导出格式",
      scene_description: "场景描述",
      character_count: "角色数: {{count}}",
      dialogue_count: "对话数: {{count}}",
      estimated_duration: "预计时长: {{duration}}",
      character_required: "至少需要一个角色",
      dialogue_required: "至少需要一段对话",
      voice_required: "请为所有角色选择语音",
      text_required: "请为所有对话输入文本",
      generating_conversation: "正在生成对话...",
      conversation_generated: "对话生成成功",
      conversation_error: "对话生成失败",
      play_conversation: "播放对话",
      pause_conversation: "暂停对话",
      stop_conversation: "停止对话",
      conversation_timeline: "对话时间轴",
      character_settings: "角色设置",
      voice_settings: "语音设置",
      speed: "语速",
      pitch: "音调",
      volume: "音量",
      emotion: "情感",
      neutral: "中性",
      happy: "开心",
      sad: "悲伤",
      angry: "愤怒",
      excited: "兴奋",
      calm: "平静",
      dramatic: "戏剧化",
      whisper: "耳语",
      shout: "大喊",
      conversation_template: "对话模板",
      load_template: "加载模板",
      save_template: "保存模板",
      template_name: "模板名称",
      interview_template: "访谈",
      dialogue_template: "对话",
      story_template: "故事叙述",
      meeting_template: "会议",
      custom_template: "自定义",
      character_a: "角色A",
      character_b: "角色B",
      narrator: "旁白",
      interviewer: "采访者",
      interviewee: "受访者",
      host: "主持人",
      guest: "嘉宾"
    },
    hero: {
      title: "将文字转换为自然的高棉语语音",
      subtitle: "专为高棉语设计的专业AI语音合成平台",
      description: "体验高质量的语音合成，提供多种角色选择，完美适用于教育、内容创作和商业应用。",
      cta_primary: "免费试用",
      cta_secondary: "查看定价",
      trusted_by: "受到柬埔寨教育工作者、内容创作者和企业的信赖"
    },
    features: {
      title: "为什么选择 Voctana？",
      subtitle: "先进的AI技术与高棉语专业知识相结合",
      items: {
        natural_voice: {
          title: "自然语音质量",
          description: "最先进的AI技术生成类人的高棉语语音，发音和语调准确自然"
        },
        multiple_characters: {
          title: "多种语音角色",
          description: "从多样化的男女声音中选择，每个角色都有独特的个性和说话风格"
        },
        fast_generation: {
          title: "闪电般快速",
          description: "通过我们优化的处理管道，在几秒钟内生成高质量音频"
        },
        easy_integration: {
          title: "易于集成",
          description: "简单的API和网页界面，轻松集成到您的应用程序和工作流程中"
        },
        affordable_pricing: {
          title: "实惠定价",
          description: "灵活的定价方案，适合个人、小企业和大型企业"
        },
        khmer_optimized: {
          title: "高棉语优化",
          description: "专门针对高棉语的语言细节和文化背景进行训练和优化"
        }
      }
    },
    characters: {
      title: "语音角色",
      subtitle: "从我们多样化的AI语音角色集合中选择",
      preview_audio: "预览音频",
      fast_quality: "快速质量",
      high_quality: "高质量",
      male: "男性",
      female: "女性",
      neutral: "中性",
      free_tier: "免费",
      basic_tier: "基础",
      pro_tier: "专业",
      filter_by_gender: "按性别筛选",
      filter_by_tier: "按层级筛选",
      filter_by_language: "按语言筛选",
      all_characters: "所有角色",
      play: "播放",
      pause: "暂停",
      loading: "加载中...",
      error_loading: "音频加载错误",
      languages: "种语言",
      voice_characters: "个语音角色",
      select_language: "选择语言",
      all_languages: "所有语言",
      no_characters: "暂无可用角色",
      playing: "播放中...",
      play_demo: "试听",
      cta_description: "体验我们多样化角色集合的AI语音生成能力",
      try_now: "立即体验",
      showing_characters: "显示 {{count}} / {{total}} 个角色",
      explore_more_languages: "探索更多语言",
      explore_description: "发现不同语言的语音角色，体验多元文化表达",
      total_languages_available: "共支持 {{count}} 种语言"
    },
    pricing: {
      title: "简单透明的定价",
      subtitle: "选择适合您需求的方案",
      monthly: "月付",
      yearly: "年付",
      save_percent: "节省 {{percent}}%",
      most_popular: "最受欢迎",
      get_started: "开始使用",
      contact_sales: "联系销售",
      free_trial: "开始免费试用",
      currency_usd: "美元",
      currency_khr: "瑞尔",
      per_month: "/月",
      per_year: "/年",
      features: {
        characters: "{{count}} 字符/月",
        unlimited_characters: "无限字符",
        flash_tts: "快速TTS",
        pro_tts: "专业TTS",
        basic_voices: "基础语音角色",
        all_voices: "所有语音角色",
        custom_voices: "定制语音角色",
        api_access: "API访问",
        priority_support: "优先支持",
        commercial_license: "商业许可"
      },
      plans: {
        free: {
          name: "免费版",
          description: "完美的服务试用体验"
        },
        starter: {
          name: "基础版",
          description: "适合个人和小型项目"
        },
        professional: {
          name: "专业版",
          description: "完美适合企业和内容创作者"
        },
        enterprise: {
          name: "企业版",
          description: "为大型组织提供定制解决方案"
        }
      }
    },
    common: {
      loading: "加载中...",
      error: "错误",
      success: "成功",
      try_again: "重试",
      learn_more: "了解更多",
      get_started: "开始使用",
      contact_us: "联系我们",
      language: "语言"
    }
  }
};

// Simple translation function
export function t(key: string, locale: Locale = 'en', params?: Record<string, string | number>): string {
  const keys = key.split('.');
  let value: any = translations[locale];

  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      // Fallback to English if key not found
      value = translations.en;
      for (const fallbackKey of keys) {
        if (value && typeof value === 'object' && fallbackKey in value) {
          value = value[fallbackKey];
        } else {
          return key; // Return key if not found
        }
      }
      break;
    }
  }

  if (typeof value === 'string') {
    // Simple parameter replacement
    if (params) {
      return value.replace(/\{\{(\w+)\}\}/g, (match, paramKey) => {
        return params[paramKey]?.toString() || match;
      });
    }
    return value;
  }

  return key; // Return key if value is not a string
}
