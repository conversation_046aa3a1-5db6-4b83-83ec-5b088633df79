import checkoutNodeJssdk from '@paypal/checkout-server-sdk';

// PayPal环境配置
function environment() {
  const clientId = process.env.PAYPAL_CLIENT_ID;
  const clientSecret = process.env.PAYPAL_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    throw new Error('PayPal credentials not configured');
  }

  // 根据环境选择沙盒或生产环境
  if (process.env.NODE_ENV === 'production') {
    return new checkoutNodeJssdk.core.LiveEnvironment(clientId, clientSecret);
  } else {
    return new checkoutNodeJssdk.core.SandboxEnvironment(clientId, clientSecret);
  }
}

// PayPal客户端
function client() {
  return new checkoutNodeJssdk.core.PayPalHttpClient(environment());
}

// 创建PayPal订单
export async function createPayPalOrder(orderData: {
  amount: number;
  currency: string;
  description: string;
  orderId: string;
  returnUrl: string;
  cancelUrl: string;
}) {
  const request = new checkoutNodeJssdk.orders.OrdersCreateRequest();
  request.prefer("return=representation");
  request.requestBody({
    intent: 'CAPTURE',
    application_context: {
      brand_name: 'Voctana',
      landing_page: 'BILLING',
      user_action: 'PAY_NOW',
      return_url: orderData.returnUrl,
      cancel_url: orderData.cancelUrl,
    },
    purchase_units: [{
      reference_id: orderData.orderId,
      description: orderData.description,
      amount: {
        currency_code: orderData.currency,
        value: orderData.amount.toFixed(2),
      },
    }],
  });

  try {
    const response = await client().execute(request);
    return {
      success: true,
      orderId: response.result.id,
      approvalUrl: response.result.links?.find((link: any) => link.rel === 'approve')?.href,
      response: response.result,
    };
  } catch (error) {
    console.error('PayPal order creation failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// 捕获PayPal支付
export async function capturePayPalOrder(paypalOrderId: string) {
  const request = new checkoutNodeJssdk.orders.OrdersCaptureRequest(paypalOrderId);
  request.requestBody({});

  try {
    const response = await client().execute(request);
    return {
      success: true,
      captureId: response.result.purchase_units[0]?.payments?.captures?.[0]?.id,
      status: response.result.status,
      response: response.result,
    };
  } catch (error) {
    console.error('PayPal order capture failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// 获取PayPal订单详情
export async function getPayPalOrderDetails(paypalOrderId: string) {
  const request = new checkoutNodeJssdk.orders.OrdersGetRequest(paypalOrderId);

  try {
    const response = await client().execute(request);
    const order = response.result;

    // 提取捕获ID（如果存在）
    let captureId = null;
    if (order.purchase_units && order.purchase_units[0] && order.purchase_units[0].payments && order.purchase_units[0].payments.captures) {
      const captures = order.purchase_units[0].payments.captures;
      if (captures.length > 0) {
        captureId = captures[0].id;
      }
    }

    return {
      success: true,
      order: order,
      status: order.status,
      captureId: captureId,
    };
  } catch (error) {
    console.error('PayPal order details fetch failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// 退款PayPal支付
export async function refundPayPalPayment(captureId: string, amount?: number, currency = 'USD') {
  const request = new checkoutNodeJssdk.payments.CapturesRefundRequest(captureId);
  
  if (amount) {
    request.requestBody({
      amount: {
        value: amount.toFixed(2),
        currency_code: currency,
      },
    });
  }

  try {
    const response = await client().execute(request);
    return {
      success: true,
      refundId: response.result.id,
      status: response.result.status,
      response: response.result,
    };
  } catch (error) {
    console.error('PayPal refund failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// PayPal Webhook验证
export function verifyPayPalWebhook(
  headers: Record<string, string>,
  body: string,
  webhookId: string
): boolean {
  // 这里应该实现PayPal webhook签名验证
  // 为了简化，我们暂时返回true
  // 在生产环境中，必须实现真正的签名验证
  return true;
}

export default {
  createOrder: createPayPalOrder,
  captureOrder: capturePayPalOrder,
  getOrderDetails: getPayPalOrderDetails,
  refundPayment: refundPayPalPayment,
  verifyWebhook: verifyPayPalWebhook,
};
