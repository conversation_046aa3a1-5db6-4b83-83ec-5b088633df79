export interface PerformanceMetrics {
  totalTime: number;
  apiCallTime: number;
  dbOperationTime: number;
  fileUploadTime: number;
  cacheTime: number;
  textLength: number;
  audioSize: number;
  tokenCount: number;
  quality: 'fast' | 'high';
  voiceName: string;
  timestamp: number;
  userId?: string;
  cacheHit: boolean;
  errorOccurred: boolean;
  errorMessage?: string;
}

export interface PerformanceStats {
  avgTotalTime: number;
  avgApiCallTime: number;
  avgDbOperationTime: number;
  avgFileUploadTime: number;
  cacheHitRate: number;
  errorRate: number;
  throughputPerMinute: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  slowestRequests: PerformanceMetrics[];
  fastestRequests: PerformanceMetrics[];
}

class TTSPerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private readonly maxMetricsHistory = 1000;
  private readonly performanceThresholds = {
    totalTime: 30000, // 30秒警告阈值
    apiCallTime: 20000, // 20秒API调用警告
    dbOperationTime: 5000, // 5秒数据库操作警告
    fileUploadTime: 10000, // 10秒文件上传警告
  };

  /**
   * 记录性能指标
   */
  recordMetrics(metrics: PerformanceMetrics): void {
    metrics.timestamp = Date.now();
    this.metrics.push(metrics);

    // 保持历史记录在限制范围内
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics = this.metrics.slice(-this.maxMetricsHistory);
    }

    // 检查性能警告
    this.checkPerformanceWarnings(metrics);

    console.log(`📊 Performance recorded: ${metrics.totalTime}ms total (API: ${metrics.apiCallTime}ms, DB: ${metrics.dbOperationTime}ms, Upload: ${metrics.fileUploadTime}ms)`);
  }

  /**
   * 检查性能警告
   */
  private checkPerformanceWarnings(metrics: PerformanceMetrics): void {
    const warnings: string[] = [];

    if (metrics.totalTime > this.performanceThresholds.totalTime) {
      warnings.push(`Total time exceeded threshold: ${metrics.totalTime}ms > ${this.performanceThresholds.totalTime}ms`);
    }

    if (metrics.apiCallTime > this.performanceThresholds.apiCallTime) {
      warnings.push(`API call time exceeded threshold: ${metrics.apiCallTime}ms > ${this.performanceThresholds.apiCallTime}ms`);
    }

    if (metrics.dbOperationTime > this.performanceThresholds.dbOperationTime) {
      warnings.push(`DB operation time exceeded threshold: ${metrics.dbOperationTime}ms > ${this.performanceThresholds.dbOperationTime}ms`);
    }

    if (metrics.fileUploadTime > this.performanceThresholds.fileUploadTime) {
      warnings.push(`File upload time exceeded threshold: ${metrics.fileUploadTime}ms > ${this.performanceThresholds.fileUploadTime}ms`);
    }

    if (warnings.length > 0) {
      console.warn('⚠️ Performance warnings:', warnings);
    }
  }

  /**
   * 获取性能统计
   */
  getStats(timeRangeMs?: number): PerformanceStats {
    const now = Date.now();
    const relevantMetrics = timeRangeMs
      ? this.metrics.filter(m => now - m.timestamp <= timeRangeMs)
      : this.metrics;

    if (relevantMetrics.length === 0) {
      return this.getEmptyStats();
    }

    const totalTimes = relevantMetrics.map(m => m.totalTime).sort((a, b) => a - b);
    const apiCallTimes = relevantMetrics.map(m => m.apiCallTime);
    const dbOperationTimes = relevantMetrics.map(m => m.dbOperationTime);
    const fileUploadTimes = relevantMetrics.map(m => m.fileUploadTime);
    const cacheHits = relevantMetrics.filter(m => m.cacheHit).length;
    const errors = relevantMetrics.filter(m => m.errorOccurred).length;

    // 计算百分位数
    const p95Index = Math.floor(totalTimes.length * 0.95);
    const p99Index = Math.floor(totalTimes.length * 0.99);

    // 计算吞吐量（每分钟请求数）
    const timeSpanMs = timeRangeMs || (relevantMetrics.length > 1
      ? relevantMetrics[relevantMetrics.length - 1].timestamp - relevantMetrics[0].timestamp
      : 60000);
    const throughputPerMinute = (relevantMetrics.length / timeSpanMs) * 60000;

    return {
      avgTotalTime: this.average(totalTimes),
      avgApiCallTime: this.average(apiCallTimes),
      avgDbOperationTime: this.average(dbOperationTimes),
      avgFileUploadTime: this.average(fileUploadTimes),
      cacheHitRate: (cacheHits / relevantMetrics.length) * 100,
      errorRate: (errors / relevantMetrics.length) * 100,
      throughputPerMinute: Math.round(throughputPerMinute * 100) / 100,
      p95ResponseTime: totalTimes[p95Index] || 0,
      p99ResponseTime: totalTimes[p99Index] || 0,
      slowestRequests: relevantMetrics
        .sort((a, b) => b.totalTime - a.totalTime)
        .slice(0, 5),
      fastestRequests: relevantMetrics
        .sort((a, b) => a.totalTime - b.totalTime)
        .slice(0, 5),
    };
  }

  /**
   * 获取性能趋势分析
   */
  getTrendAnalysis(timeRangeMs: number = 24 * 60 * 60 * 1000): {
    trend: 'improving' | 'degrading' | 'stable';
    changePercentage: number;
    recommendation: string;
  } {
    const now = Date.now();
    const recentMetrics = this.metrics.filter(m => now - m.timestamp <= timeRangeMs);

    if (recentMetrics.length < 10) {
      return {
        trend: 'stable',
        changePercentage: 0,
        recommendation: '数据不足，需要更多样本进行趋势分析'
      };
    }

    // 分为前半段和后半段比较
    const midPoint = Math.floor(recentMetrics.length / 2);
    const firstHalf = recentMetrics.slice(0, midPoint);
    const secondHalf = recentMetrics.slice(midPoint);

    const firstHalfAvg = this.average(firstHalf.map(m => m.totalTime));
    const secondHalfAvg = this.average(secondHalf.map(m => m.totalTime));

    const changePercentage = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100;

    let trend: 'improving' | 'degrading' | 'stable';
    let recommendation: string;

    if (changePercentage < -10) {
      trend = 'improving';
      recommendation = '性能正在改善，继续保持当前优化策略';
    } else if (changePercentage > 10) {
      trend = 'degrading';
      recommendation = '性能正在下降，建议检查系统负载和优化缓存策略';
    } else {
      trend = 'stable';
      recommendation = '性能保持稳定，可以考虑进一步优化以提升用户体验';
    }

    return {
      trend,
      changePercentage: Math.round(changePercentage * 100) / 100,
      recommendation
    };
  }

  /**
   * 生成性能报告
   */
  generateReport(): string {
    const stats = this.getStats();
    const trend = this.getTrendAnalysis();

    return `
📊 TTS 性能报告
================

🕐 响应时间统计:
- 平均总时间: ${Math.round(stats.avgTotalTime)}ms
- P95 响应时间: ${stats.p95ResponseTime}ms
- P99 响应时间: ${stats.p99ResponseTime}ms

⚡ 各环节耗时:
- API 调用: ${Math.round(stats.avgApiCallTime)}ms
- 数据库操作: ${Math.round(stats.avgDbOperationTime)}ms
- 文件上传: ${Math.round(stats.avgFileUploadTime)}ms

📈 系统指标:
- 缓存命中率: ${Math.round(stats.cacheHitRate)}%
- 错误率: ${Math.round(stats.errorRate)}%
- 吞吐量: ${stats.throughputPerMinute} 请求/分钟

📊 性能趋势:
- 趋势: ${trend.trend === 'improving' ? '改善' : trend.trend === 'degrading' ? '下降' : '稳定'}
- 变化: ${trend.changePercentage > 0 ? '+' : ''}${trend.changePercentage}%
- 建议: ${trend.recommendation}

🐌 最慢请求:
${stats.slowestRequests.map((req, i) =>
  `${i + 1}. ${req.totalTime}ms - ${req.textLength}字符 (${req.voiceName}, ${req.quality})`
).join('\n')}

⚡ 最快请求:
${stats.fastestRequests.map((req, i) =>
  `${i + 1}. ${req.totalTime}ms - ${req.textLength}字符 (${req.voiceName}, ${req.quality})`
).join('\n')}
`;
  }

  private average(numbers: number[]): number {
    return numbers.length > 0 ? numbers.reduce((a, b) => a + b, 0) / numbers.length : 0;
  }

  private getEmptyStats(): PerformanceStats {
    return {
      avgTotalTime: 0,
      avgApiCallTime: 0,
      avgDbOperationTime: 0,
      avgFileUploadTime: 0,
      cacheHitRate: 0,
      errorRate: 0,
      throughputPerMinute: 0,
      p95ResponseTime: 0,
      p99ResponseTime: 0,
      slowestRequests: [],
      fastestRequests: [],
    };
  }

  /**
   * 清除历史数据
   */
  clearHistory(): void {
    this.metrics = [];
    console.log('🧹 Performance history cleared');
  }

  /**
   * 导出性能数据
   */
  exportData(): PerformanceMetrics[] {
    return [...this.metrics];
  }
}

// 导出单例实例
export const ttsPerformanceMonitor = new TTSPerformanceMonitor();