import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

export class R2StorageService {
  private client: S3Client;
  private bucketName: string;
  private publicUrl: string;

  constructor() {
    // Cloudflare R2 配置
    this.client = new S3Client({
      region: 'auto', // R2 使用 'auto' 作为 region
      endpoint: process.env.R2_ENDPOINT, // 例如: https://account-id.r2.cloudflarestorage.com
      credentials: {
        accessKeyId: process.env.R2_ACCESS_KEY_ID!,
        secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
      },
    });

    this.bucketName = process.env.R2_BUCKET_NAME!;
    this.publicUrl = process.env.R2_PUBLIC_URL!; // 例如: https://your-domain.com 或 R2 public URL
  }

  /**
   * 上传音频文件到R2
   */
  async uploadAudio(
    audioData: Buffer,
    fileName: string,
    contentType: string = 'audio/wav'
  ): Promise<string> {
    try {
      const key = `audio/${fileName}`;

      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: audioData,
        ContentType: contentType,
        // 设置缓存控制
        CacheControl: 'public, max-age=********', // 1年缓存
        // 设置元数据
        Metadata: {
          'uploaded-at': new Date().toISOString(),
          'service': 'voctana-tts',
        },
      });

      await this.client.send(command);

      // 返回公共访问URL
      return `${this.publicUrl}/${key}`;
    } catch (error) {
      console.error('R2 upload error:', error);
      throw new Error(`Failed to upload audio to R2: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 上传头像图片到R2
   */
  async uploadAvatar(
    imageData: Buffer,
    fileName: string,
    contentType: string = 'image/webp'
  ): Promise<string> {
    try {
      const key = `avatars/${fileName}`;

      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: imageData,
        ContentType: contentType,
        // 设置缓存控制
        CacheControl: 'public, max-age=********', // 1年缓存
        // 设置元数据
        Metadata: {
          'uploaded-at': new Date().toISOString(),
          'service': 'voctana-avatar',
        },
      });

      await this.client.send(command);

      // 返回公共访问URL
      return `${this.publicUrl}/${key}`;
    } catch (error) {
      console.error('R2 avatar upload error:', error);
      throw new Error(`Failed to upload avatar to R2: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 生成预签名下载URL（用于私有文件）
   */
  async getSignedDownloadUrl(fileName: string, expiresIn: number = 3600): Promise<string> {
    try {
      const key = `audio/${fileName}`;
      
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      return await getSignedUrl(this.client, command, { expiresIn });
    } catch (error) {
      console.error('R2 signed URL error:', error);
      throw new Error(`Failed to generate signed URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 删除音频文件
   */
  async deleteAudio(fileName: string): Promise<void> {
    try {
      const key = `audio/${fileName}`;

      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.client.send(command);
    } catch (error) {
      console.error('R2 delete error:', error);
      throw new Error(`Failed to delete audio from R2: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 删除头像文件
   */
  async deleteAvatar(fileName: string): Promise<void> {
    try {
      const key = `avatars/${fileName}`;

      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.client.send(command);
    } catch (error) {
      console.error('R2 avatar delete error:', error);
      throw new Error(`Failed to delete avatar from R2: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 检查服务配置是否正确
   */
  isConfigured(): boolean {
    return !!(
      process.env.R2_ENDPOINT &&
      process.env.R2_ACCESS_KEY_ID &&
      process.env.R2_SECRET_ACCESS_KEY &&
      process.env.R2_BUCKET_NAME &&
      process.env.R2_PUBLIC_URL
    );
  }

  /**
   * 生成唯一的文件名
   */
  static generateFileName(audioGenerationId: string, format: string = 'mp3'): string {
    const timestamp = Date.now();
    return `${audioGenerationId}-${timestamp}.${format.toLowerCase()}`;
  }

  /**
   * 生成头像文件名
   */
  static generateAvatarFileName(characterId: string, languageCode?: string, format: string = 'webp'): string {
    const timestamp = Date.now();
    const suffix = languageCode ? `-${languageCode}` : '';
    return `character-${characterId}${suffix}-${timestamp}.${format.toLowerCase()}`;
  }

  /**
   * 从URL提取文件名
   */
  static extractFileNameFromUrl(url: string): string | null {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      return pathParts[pathParts.length - 1] || null;
    } catch {
      return null;
    }
  }
}

// 导出单例实例
export const r2Storage = new R2StorageService();
