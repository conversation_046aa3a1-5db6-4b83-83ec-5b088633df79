generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Conversation {
  id                String                  @id @default(cuid())
  userId            String
  title             String
  sceneDescription  String?
  pauseBetweenLines Float                   @default(1.0)
  exportFormat      String                  @default("mp3")
  status            ConversationStatus      @default(PROCESSING)
  audioUrl          String?
  duration          Int?
  totalCharacters   Int                     @default(0)
  errorMessage      String?
  createdAt         DateTime                @default(now())
  updatedAt         DateTime                @default(now()) @updatedAt
  user              User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  characters        ConversationCharacter[]
  dialogues         ConversationDialogue[]

  @@index([userId])
  @@index([status])
  @@index([createdAt])
}

model ConversationCharacter {
  id             String                 @id @default(cuid())
  conversationId String
  name           String
  speed          Float                  @default(1.0)
  pitch          Int                    @default(0)
  volume         Float                  @default(1.0)
  emotion        String                 @default("neutral")
  createdAt      DateTime               @default(now())
  templateId     String
  conversation   Conversation           @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  template       ApiCharacterTemplate   @relation(fields: [templateId], references: [id])
  dialogues      ConversationDialogue[]

  @@index([conversationId])
  @@index([templateId])
}

model ConversationDialogue {
  id             String                @id @default(cuid())
  conversationId String
  characterId    String
  text           String
  order          Int
  audioUrl       String?
  duration       Int?
  createdAt      DateTime              @default(now())
  character      ConversationCharacter @relation(fields: [characterId], references: [id], onDelete: Cascade)
  conversation   Conversation          @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@index([conversationId])
  @@index([characterId])
  @@index([order])
}

model ConversationTemplate {
  id           String   @id @default(cuid())
  userId       String?
  name         String
  description  String?
  characters   String
  dialogues    String
  isPublic     Boolean  @default(false)
  usageCount   Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @default(now()) @updatedAt
  languageCode String   @default("zh-CN")
  user         User?    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([isPublic])
  @@index([languageCode])
  @@index([createdAt])
}

model Account {
  id                       String  @id @default(cuid())
  userId                   String
  type                     String
  provider                 String
  providerAccountId        String
  refresh_token            String?
  access_token             String?
  expires_at               Int?
  token_type               String?
  scope                    String?
  id_token                 String?
  session_state            String?
  refresh_token_expires_in Int?
  user                     User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id                    String                    @id @default(cuid())
  name                  String?
  email                 String?                   @unique
  emailVerified         DateTime?
  image                 String?
  bio                   String?
  location              String?
  website               String?
  phone                 String?
  password              String?
  role                  UserRole                  @default(USER)
  isActive              Boolean                   @default(true)
  lastActiveAt          DateTime?
  createdAt             DateTime                  @default(now())
  updatedAt             DateTime                  @updatedAt
  credits               Int                       @default(1000)
  usedCredits           Int                       @default(0)
  accounts              Account[]
  apiUsageLogs          ApiUsageLog[]
  audioGenerations      AudioGeneration[]
  avatarGenerations     AvatarGenerationHistory[]
  conversations         Conversation[]
  conversationTemplates ConversationTemplate[]
  quotaAdjustments      QuotaAdjustment[]
  sessions              Session[]
  textGenerations       TextGeneration[]
  textGenerationUsage   TextGenerationUsage[]
  usageRecords          Usage[]
  preferences           UserPreference?
  characterFavorites    CharacterFavorite[]
  characterSharedBy     CharacterShare[]          @relation("CharacterSharedBy")
  characterSharedTo     CharacterShare[]          @relation("CharacterSharedTo")
  sentGifts             CreditGift[]              @relation("GiftSender")
  receivedGifts         CreditGift[]              @relation("GiftReceiver")
  creditPurchases       CreditPurchase[]
  creditUsages          CreditUsage[]
  customCharacters      CustomVoiceCharacter[]
  languageCharacters    LanguageCharacter[]
  payments              Payment[]
  styleFavorites        StyleFavorite[]
  styleSharedBy         StyleShare[]              @relation("StyleSharedBy")
  styleSharedTo         StyleShare[]              @relation("StyleSharedTo")
  userStyles            UserStyle[]
  voiceDemoPlays        VoiceDemoPlayStat[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model ApiCharacterTemplate {
  id                     String                    @id @default(cuid())
  apiProvider            ApiProvider
  apiVoiceName           String                    @unique
  originalName           String
  gender                 Gender
  defaultStyle           String?
  defaultDescription     String?
  isActive               Boolean                   @default(true)
  sortOrder              Int                       @default(0)
  createdAt              DateTime                  @default(now())
  updatedAt              DateTime                  @default(now()) @updatedAt
  audioGenerations       AudioGeneration[]
  avatarHistory          AvatarGenerationHistory[]
  conversationCharacters ConversationCharacter[]
  languageAvatars        LanguageCharacterAvatar[]
  customCharacters       CustomVoiceCharacter[]
  languageCharacters     LanguageCharacter[]
  voiceDemos             VoiceDemo[]

  @@index([apiProvider])
  @@index([isActive])
  @@index([apiVoiceName])
  @@map("api_character_templates")
}

model LanguageCharacter {
  id             String                @id @default(cuid())
  languageId     String
  templateId     String?
  name           String
  description    String
  gender         Gender?               // 新增性别字段，可为空（从模板继承）
  style          String?
  personality    String?
  bestFor        String?
  avatarUrl      String?
  isActive       Boolean               @default(true)
  sortOrder      Int                   @default(0)
  isCustom       Boolean               @default(false)
  createdBy      String?
  customSettings String?
  customPrompt   String?
  createdAt      DateTime              @default(now())
  updatedAt      DateTime              @default(now()) @updatedAt
  creator        User?                 @relation(fields: [createdBy], references: [id])
  language       Language              @relation(fields: [languageId], references: [id], onDelete: Cascade)
  template       ApiCharacterTemplate? @relation(fields: [templateId], references: [id])

  @@unique([languageId, templateId])
  @@index([languageId])
  @@index([templateId])
  @@index([isActive])
  @@index([isCustom])
  @@index([createdBy])
  @@index([gender])
  @@map("language_characters")
}

model AudioGeneration {
  id               String                     @id @default(cuid())
  userId           String
  inputText        String
  outputAudioUrl   String
  apiProvider      ApiProvider
  actualVoiceName  String
  audioFormat      AudioFormat                @default(MP3)
  speed            Float                      @default(1.0)
  duration         Float?
  characterCount   Int
  fileSize         Int?
  cost             Decimal?
  createdAt        DateTime                   @default(now())
  languageCode     String?
  templateId       String?
  template         ApiCharacterTemplate?      @relation(fields: [templateId], references: [id])
  user             User                       @relation(fields: [userId], references: [id], onDelete: Cascade)
  customCharacters AudioGenerationCharacter[]
  styles           AudioGenerationStyle[]

  @@index([userId])
  @@index([templateId])
  @@index([apiProvider])
  @@index([createdAt])
}

model ApiProviderConfig {
  id              String        @id @default(cuid())
  provider        ApiProvider   @unique
  displayName     String
  description     String?
  status          ApiStatus     @default(ACTIVE)
  priority        Int           @default(0)
  isEnabled       Boolean       @default(true)
  config          Json
  dailyLimit      Int?
  monthlyLimit    Int?
  costLimit       Float?
  totalRequests   Int           @default(0)
  successRequests Int           @default(0)
  failedRequests  Int           @default(0)
  totalCost       Float         @default(0)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  lastUsedAt      DateTime?
  usageLogs       ApiUsageLog[]

  @@index([provider])
  @@index([status])
  @@index([priority])
}

model ApiUsageLog {
  id           String            @id @default(cuid())
  provider     ApiProvider
  requestType  String
  inputSize    Int
  outputSize   Int?
  success      Boolean
  responseTime Int
  errorMessage String?
  cost         Float?
  userId       String?
  sessionId    String?
  createdAt    DateTime          @default(now())
  config       ApiProviderConfig @relation(fields: [provider], references: [provider])
  user         User?             @relation(fields: [userId], references: [id])

  @@index([provider])
  @@index([userId])
  @@index([createdAt])
  @@index([success])
}

model UserPreference {
  id                    String   @id @default(cuid())
  userId                String   @unique
  defaultCharacterId    String?
  defaultQuality        String?
  defaultSpeed          Float?
  defaultPitch          Int?
  defaultVolumeGainDb   Float?
  favoriteCharacters    String?
  styleTemplates        String?
  totalGenerations      Int      @default(0)
  totalCharacters       Int      @default(0)
  enableRecommendations Boolean  @default(true)
  autoSelectQuality     Boolean  @default(false)
  language              String   @default("zh-CN")
  theme                 String   @default("system")
  emailNotifications    Boolean  @default(true)
  pushNotifications     Boolean  @default(true)
  marketingEmails       Boolean  @default(false)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @default(now()) @updatedAt
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model Usage {
  id             String   @id @default(cuid())
  userId         String
  date           DateTime @db.Date
  characterCount Int      @default(0)
  audioCount     Int      @default(0)
  createdAt      DateTime @default(now())
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, date])
  @@index([userId])
  @@index([date])
}

model QuotaAdjustment {
  id        String    @id @default(cuid())
  userId    String
  quotaType QuotaType
  amount    Int
  reason    String
  adminId   String?
  createdAt DateTime  @default(now())
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("QuotaAdjustment")
}

model Payment {
  id                String          @id @default(cuid())
  userId            String
  amount            Float
  currency          String          @default("USD")
  provider          PaymentProvider
  status            PaymentStatus   @default(PENDING)
  paypalOrderId     String?
  khqrTransactionId String?
  wechatOrderId     String?
  stripePaymentId   String?
  description       String?
  metadata          Json?
  createdAt         DateTime        @default(now())
  completedAt       DateTime?
  creditPurchaseId  String?
  creditPurchase    CreditPurchase? @relation(fields: [creditPurchaseId], references: [id])
  user              User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("payments")
}

model CreditUsage {
  id           String           @id @default(cuid())
  userId       String
  amount       Int
  type         CreditUsageType?
  description  String?
  metadata     Json?
  createdAt    DateTime         @default(now())
  inputTokens  Int              @default(0)
  outputTokens Int              @default(0)
  serviceType  ServiceType?
  totalTokens  Int              @default(0)
  updatedAt    DateTime         @default(now()) @updatedAt
  user         User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([serviceType])
  @@index([createdAt])
  @@map("credit_usage")
}

model CreditPurchase {
  id          String               @id @default(cuid())
  userId      String
  packageId   String
  packageName String
  credits     Int
  amount      Float
  currency    String               @default("USD")
  status      CreditPurchaseStatus @default(PENDING)
  orderNumber String               @unique
  createdAt   DateTime             @default(now())
  completedAt DateTime?
  user        User                 @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments    Payment[]

  @@map("credit_purchases")
}

model CreditGift {
  id         String   @id @default(cuid())
  fromUserId String?
  toUserId   String
  amount     Int
  reason     String
  type       GiftType
  createdAt  DateTime @default(now())
  fromUser   User?    @relation("GiftSender", fields: [fromUserId], references: [id])
  toUser     User     @relation("GiftReceiver", fields: [toUserId], references: [id])

  @@map("credit_gifts")
}

model ApiPricingConfig {
  id                        String   @id @default(cuid())
  standardInputPrice        Float
  standardOutputPrice       Float
  professionalInputPrice    Float
  professionalOutputPrice   Float
  isActive                  Boolean  @default(true)
  description               String?
  createdAt                 DateTime @default(now())
  updatedAt                 DateTime @updatedAt
  textGenerationInputPrice  Float    @default(0.50)
  textGenerationOutputPrice Float    @default(1.50)
  textInputMultiplier       Float    @default(1.0)
  textOutputMultiplier      Float    @default(3.0)
  ttsInputMultiplier        Float    @default(1.0)
  ttsOutputMultiplier       Float    @default(40.0)
  ttsProfessionalMultiplier Float    @default(80.0)
  professionalVoiceRatio    Int      @default(50)
  standardVoiceRatio        Int      @default(100)
  textGenerationRatio       Int      @default(1000)

  @@map("api_pricing_configs")
}

model CreditPackagePricing {
  id          String   @id @default(cuid())
  packageType String
  credits     Int
  costPrice   Float
  salePrice   Float
  promoPrice  Float?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([packageType, isActive])
  @@map("credit_package_pricing")
}

model FreePackageConfig {
  id             String   @id @default(cuid())
  enabled        Boolean  @default(true)
  giftType       String
  dailyCredits   Int      @default(0)
  monthlyCredits Int      @default(0)
  isActive       Boolean  @default(true)
  description    String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@map("free_package_configs")
}

model Language {
  id               String                    @id @default(cuid())
  code             String                    @unique
  name             String
  nativeName       String
  region           String
  flag             String
  isActive         Boolean                   @default(true)
  sortOrder        Int                       @default(0)
  createdAt        DateTime                  @default(now())
  updatedAt        DateTime                  @updatedAt
  characterAvatars LanguageCharacterAvatar[]
  characters       LanguageCharacter[]
  voiceDemos       VoiceDemo[]

  @@index([isActive])
  @@index([sortOrder])
  @@map("languages")
}

model VoiceDemo {
  id             String                   @id @default(cuid())
  languageId     String
  languageCode   String
  demoText       String
  audioUrl       String
  audioKey       String
  duration       Float?
  fileSize       Int?
  quality        String                   @default("standard")
  createdAt      DateTime                 @default(now())
  updatedAt      DateTime                 @updatedAt
  templateId     String?
  aggregateStats VoiceDemoAggregateStats?
  playStats      VoiceDemoPlayStat[]
  language       Language                 @relation(fields: [languageId], references: [id], onDelete: Cascade)
  template       ApiCharacterTemplate?    @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@unique([templateId, languageId, quality])
  @@index([languageCode])
  @@index([templateId])
  @@index([quality])
  @@map("voice_demos")
}

model VoiceDemoPlayStat {
  id           String    @id @default(cuid())
  voiceDemoId  String
  userId       String?
  userAgent    String?
  ipAddress    String?
  referrer     String?
  playDuration Float?
  isCompleted  Boolean   @default(false)
  createdAt    DateTime  @default(now())
  user         User?     @relation(fields: [userId], references: [id])
  voiceDemo    VoiceDemo @relation(fields: [voiceDemoId], references: [id], onDelete: Cascade)

  @@index([voiceDemoId])
  @@index([userId])
  @@index([createdAt])
  @@map("voice_demo_play_stats")
}

model VoiceDemoAggregateStats {
  id              String    @id @default(cuid())
  voiceDemoId     String    @unique
  totalPlays      Int       @default(0)
  uniquePlays     Int       @default(0)
  completedPlays  Int       @default(0)
  averageDuration Float?
  lastPlayedAt    DateTime?
  updatedAt       DateTime  @updatedAt
  voiceDemo       VoiceDemo @relation(fields: [voiceDemoId], references: [id], onDelete: Cascade)

  @@index([totalPlays])
  @@index([lastPlayedAt])
  @@map("voice_demo_aggregate_stats")
}

model StyleCategory {
  id          String      @id @default(cuid())
  name        String      @unique
  nameEn      String
  nameKhmer   String?
  description String?
  icon        String?
  sortOrder   Int         @default(0)
  isActive    Boolean     @default(true)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  styles      UserStyle[]

  @@index([sortOrder])
  @@index([isActive])
  @@map("style_categories")
}

model UserStyle {
  id               String                 @id @default(cuid())
  userId           String
  name             String
  nameEn           String?
  nameKhmer        String?
  description      String?
  prompt           String
  type             StyleType              @default(SINGLE_SPEAKER)
  source           StyleSource            @default(USER_CREATED)
  categoryId       String?
  parameters       Json?
  speakerConfig    Json?
  tags             String[]
  icon             String?
  color            String?
  usageCount       Int                    @default(0)
  favoriteCount    Int                    @default(0)
  isPublic         Boolean                @default(false)
  isOfficial       Boolean                @default(false)
  isActive         Boolean                @default(true)
  createdAt        DateTime               @default(now())
  updatedAt        DateTime               @updatedAt
  audioGenerations AudioGenerationStyle[]
  customCharacters CustomVoiceCharacter[] @relation("DefaultStyle")
  favorites        StyleFavorite[]
  shares           StyleShare[]
  category         StyleCategory?         @relation(fields: [categoryId], references: [id])
  user             User                   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([categoryId])
  @@index([type])
  @@index([source])
  @@index([isPublic])
  @@index([isOfficial])
  @@index([usageCount])
  @@index([favoriteCount])
  @@index([createdAt])
  @@map("user_styles")
}

model StyleFavorite {
  id        String    @id @default(cuid())
  userId    String
  styleId   String
  createdAt DateTime  @default(now())
  style     UserStyle @relation(fields: [styleId], references: [id], onDelete: Cascade)
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, styleId])
  @@index([userId])
  @@index([styleId])
  @@index([createdAt])
  @@map("style_favorites")
}

model StyleShare {
  id         String    @id @default(cuid())
  styleId    String
  sharedBy   String
  sharedTo   String?
  shareCode  String?   @unique
  expiresAt  DateTime?
  usageCount Int       @default(0)
  maxUsage   Int?
  isActive   Boolean   @default(true)
  createdAt  DateTime  @default(now())
  sharer     User      @relation("StyleSharedBy", fields: [sharedBy], references: [id], onDelete: Cascade)
  receiver   User?     @relation("StyleSharedTo", fields: [sharedTo], references: [id], onDelete: Cascade)
  style      UserStyle @relation(fields: [styleId], references: [id], onDelete: Cascade)

  @@index([styleId])
  @@index([sharedBy])
  @@index([sharedTo])
  @@index([shareCode])
  @@index([expiresAt])
  @@index([createdAt])
  @@map("style_shares")
}

model CustomVoiceCharacter {
  id               String                     @id @default(cuid())
  userId           String
  name             String
  nameEn           String?
  nameKhmer        String?
  description      String?
  speed            Float                      @default(1.0)
  pitch            Float                      @default(0.0)
  volume           Float                      @default(0.0)
  defaultStyleId   String?
  avatarUrl        String?
  personality      String[]
  bestFor          String[]
  tags             String[]
  isPublic         Boolean                    @default(false)
  isOfficial       Boolean                    @default(false)
  usageCount       Int                        @default(0)
  favoriteCount    Int                        @default(0)
  isActive         Boolean                    @default(true)
  createdAt        DateTime                   @default(now())
  updatedAt        DateTime                   @updatedAt
  baseTemplateId   String
  audioGenerations AudioGenerationCharacter[]
  favorites        CharacterFavorite[]
  shares           CharacterShare[]
  baseTemplate     ApiCharacterTemplate       @relation(fields: [baseTemplateId], references: [id], onDelete: Cascade)
  defaultStyle     UserStyle?                 @relation("DefaultStyle", fields: [defaultStyleId], references: [id])
  user             User                       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([baseTemplateId])
  @@index([defaultStyleId])
  @@index([isPublic])
  @@index([isOfficial])
  @@index([usageCount])
  @@index([favoriteCount])
  @@index([createdAt])
  @@map("custom_voice_characters")
}

model CharacterFavorite {
  id          String               @id @default(cuid())
  userId      String
  characterId String
  createdAt   DateTime             @default(now())
  character   CustomVoiceCharacter @relation(fields: [characterId], references: [id], onDelete: Cascade)
  user        User                 @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, characterId])
  @@index([userId])
  @@index([characterId])
  @@index([createdAt])
  @@map("character_favorites")
}

model CharacterShare {
  id          String               @id @default(cuid())
  characterId String
  sharedBy    String
  sharedTo    String?
  shareCode   String?              @unique
  expiresAt   DateTime?
  usageCount  Int                  @default(0)
  maxUsage    Int?
  isActive    Boolean              @default(true)
  createdAt   DateTime             @default(now())
  character   CustomVoiceCharacter @relation(fields: [characterId], references: [id], onDelete: Cascade)
  sharer      User                 @relation("CharacterSharedBy", fields: [sharedBy], references: [id], onDelete: Cascade)
  receiver    User?                @relation("CharacterSharedTo", fields: [sharedTo], references: [id], onDelete: Cascade)

  @@index([characterId])
  @@index([sharedBy])
  @@index([sharedTo])
  @@index([shareCode])
  @@index([expiresAt])
  @@index([createdAt])
  @@map("character_shares")
}

model AudioGenerationStyle {
  id                String          @id @default(cuid())
  audioGenerationId String
  styleId           String
  createdAt         DateTime        @default(now())
  audioGeneration   AudioGeneration @relation(fields: [audioGenerationId], references: [id], onDelete: Cascade)
  style             UserStyle       @relation(fields: [styleId], references: [id], onDelete: Cascade)

  @@unique([audioGenerationId, styleId])
  @@index([audioGenerationId])
  @@index([styleId])
  @@index([createdAt])
  @@map("audio_generation_styles")
}

model AudioGenerationCharacter {
  id                String               @id @default(cuid())
  audioGenerationId String
  characterId       String
  createdAt         DateTime             @default(now())
  audioGeneration   AudioGeneration      @relation(fields: [audioGenerationId], references: [id], onDelete: Cascade)
  character         CustomVoiceCharacter @relation(fields: [characterId], references: [id], onDelete: Cascade)

  @@unique([audioGenerationId, characterId])
  @@index([audioGenerationId])
  @@index([characterId])
  @@index([createdAt])
  @@map("audio_generation_characters")
}

model TextGeneration {
  id            String             @id @default(cuid())
  userId        String
  type          TextGenerationType
  prompt        String
  generatedText String
  parameters    Json?
  tokenCount    Int
  cost          Decimal?           @db.Decimal(10, 6)
  languageCode  String?
  createdAt     DateTime           @default(now())
  updatedAt     DateTime           @updatedAt
  user          User               @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([type])
  @@index([createdAt])
}

model TextGenerationUsage {
  id              String   @id @default(cuid())
  userId          String
  date            DateTime @db.Date
  tokenCount      Int      @default(0)
  generationCount Int      @default(0)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, date])
}

model LanguageCharacterAvatar {
  id            String                @id @default(cuid())
  languageId    String
  languageCode  String
  avatarUrl     String
  localizedName String?
  description   String?
  isActive      Boolean               @default(true)
  createdAt     DateTime              @default(now())
  updatedAt     DateTime              @updatedAt
  templateId    String?
  language      Language              @relation(fields: [languageId], references: [id], onDelete: Cascade)
  template      ApiCharacterTemplate? @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@unique([templateId, languageId])
  @@index([templateId])
  @@index([languageId])
  @@index([languageCode])
}

model AvatarGenerationHistory {
  id              String                @id @default(cuid())
  languageCode    String?
  avatarUrl       String
  prompt          String
  metadata        String?
  generatedBy     String
  createdAt       DateTime              @default(now())
  templateId      String?
  generatedByUser User                  @relation(fields: [generatedBy], references: [id], onDelete: Cascade)
  template        ApiCharacterTemplate? @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@index([templateId])
  @@index([generatedBy])
  @@index([createdAt])
}

enum ServiceType {
  STANDARD_VOICE
  PROFESSIONAL_VOICE
  TEXT_GENERATION
}

enum PaymentProvider {
  PAYPAL
  KHQR
  WECHAT
  STRIPE
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

enum ConversationStatus {
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

enum ApiProvider {
  GEMINI
  OPENAI
}

enum ApiStatus {
  ACTIVE
  INACTIVE
  MAINTENANCE
  ERROR
}

enum UserRole {
  USER
  SUPER_ADMIN
}

enum Gender {
  MALE
  FEMALE
  NEUTRAL
}

enum AudioFormat {
  MP3
  WAV
  AAC
}

enum QuotaType {
  STANDARD
  PROFESSIONAL
}

enum CreditUsageType {
  VOICE_GENERATION_FAST
  VOICE_GENERATION_HIGH
  TEXT_GENERATION
  MULTI_SPEAKER
  API_USAGE
  OTHER
}

enum CreditPurchaseStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

enum GiftType {
  WELCOME_BONUS
  REFERRAL_BONUS
  ADMIN_GIFT
  PROMOTION
  USER_GIFT
}

enum StyleType {
  SINGLE_SPEAKER
  MULTI_SPEAKER
}

enum StyleSource {
  OFFICIAL
  USER_CREATED
  COMMUNITY
}

enum TextGenerationType {
  SINGLE
  CONVERSATION
}
