import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 语音试听模块种子数据
 * 为每个语音角色在每种语言下创建试听占位符
 */

// 各语言的试听文本
const DEMO_TEXTS = {
  'km-KH': 'សួស្ដី! ខ្ញុំជាសំឡេងសិប្បនិម្មិត AI របស់ Voctana។ តើអ្នកចង់ឮខ្ញុំនិយាយអ្វីទៀត?',
  'zh-CN': '您好！我是 Voctana 的 AI 语音助手。您想听我说些什么呢？',
  'en-US': 'Hello! I am your AI voice assistant from Voctana. What would you like me to say?',
  'ja-JP': 'こんにちは！私は Voctana の AI 音声アシスタントです。何か話してほしいことはありますか？',
  'ko-KR': '안녕하세요! 저는 Voctana의 AI 음성 어시스턴트입니다. 무엇을 말해드릴까요?',
  'ar-EG': 'مرحباً! أنا مساعد الصوت الذكي من Voctana. ماذا تريد مني أن أقول؟',
  'de-DE': 'Hallo! Ich bin Ihr KI-Sprachassistent von Voctana. Was möchten Sie hören?',
  'es-US': '¡Hola! Soy tu asistente de voz AI de Voctana. ¿Qué te gustaría que dijera?',
  'fr-FR': 'Bonjour ! Je suis votre assistant vocal IA de Voctana. Que voulez-vous m\'entendre dire ?',
  'hi-IN': 'नमस्ते! मैं Voctana का AI वॉयस असिस्टेंट हूँ। आप मुझसे क्या कहलवाना चाहते हैं?',
  'id-ID': 'Halo! Saya asisten suara AI dari Voctana. Apa yang ingin Anda dengar dari saya?',
  'it-IT': 'Ciao! Sono il tuo assistente vocale AI di Voctana. Cosa vorresti sentirmi dire?',
  'pt-BR': 'Olá! Eu sou seu assistente de voz AI da Voctana. O que você gostaria de me ouvir dizer?',
  'ru-RU': 'Привет! Я ваш голосовой AI-помощник от Voctana. Что бы вы хотели услышать?',
  'nl-NL': 'Hallo! Ik ben uw AI-stemassistent van Voctana. Wat wilt u dat ik zeg?',
  'pl-PL': 'Cześć! Jestem twoim asystentem głosowym AI od Voctana. Co chciałbyś usłyszeć?',
  'th-TH': 'สวัสดี! ฉันเป็นผู้ช่วยเสียง AI จาก Voctana คุณอยากให้ฉันพูดอะไร?',
  'tr-TR': 'Merhaba! Ben Voctana\'dan AI ses asistanınızım. Ne söylememi istersiniz?',
  'vi-VN': 'Xin chào! Tôi là trợ lý giọng nói AI của Voctana. Bạn muốn tôi nói gì?',
  'ro-RO': 'Salut! Sunt asistentul vocal AI de la Voctana. Ce ai vrea să spun?',
  'uk-UA': 'Привіт! Я ваш голосовий AI-помічник від Voctana. Що ви хочете почути?',
  'bn-BD': 'হ্যালো! আমি Voctana এর AI ভয়েস অ্যাসিস্ট্যান্ট। আপনি আমাকে কী বলতে শুনতে চান?',
  'en-IN': 'Hello! I am your AI voice assistant from Voctana. What would you like me to say?',
  'mr-IN': 'नमस्कार! मी Voctana चा AI व्हॉईस असिस्टंट आहे। तुम्हाला मी काय म्हणावे असे वाटते?',
  'ta-IN': 'வணக்கம்! நான் Voctana இன் AI குரல் உதவியாளர். நீங்கள் என்னை என்ன சொல்ல வேண்டும் என்று விரும்புகிறீர்கள்?',
  'te-IN': 'హలో! నేను Voctana నుండి మీ AI వాయిస్ అసిస్టెంట్. మీరు నన్ను ఏమి చెప్పాలని అనుకుంటున్నారు?'
};

async function seedVoiceDemos() {
  console.log('🎵 开始创建语音试听种子数据...');

  // 获取所有语言角色和语言
  const characters = await prisma.languageCharacter.findMany({
    include: {
      language: {
        select: { id: true, code: true, name: true }
      }
    }
  });

  console.log(`📊 准备为 ${characters.length} 个语言角色创建试听占位符...`);

  let createdCount = 0;

  for (const character of characters) {
    const language = character.language;
    const demoText = DEMO_TEXTS[language.code as keyof typeof DEMO_TEXTS] || DEMO_TEXTS['en-US'];

    // 只为有模板的语言角色创建试听
    if (character.templateId) {
      await prisma.voiceDemo.upsert({
        where: {
          templateId_languageId: {
            templateId: character.templateId,
            languageId: language.id
          }
        },
        update: {
          demoText: demoText,
          languageCode: language.code
        },
        create: {
          templateId: character.templateId,
          languageId: language.id,
          languageCode: language.code,
          demoText: demoText,
          audioUrl: '', // 初始为空字符串，需要手工生成
          audioKey: `demo/${character.templateId}/${language.code}.mp3`,
          duration: undefined,
          fileSize: undefined,
          quality: 'standard'
        }
      });
    }

    createdCount++;

    if (createdCount % 50 === 0) {
      console.log(`✅ 已创建 ${createdCount} 个试听占位符...`);
    }
  }

  console.log(`🎉 成功创建 ${createdCount} 个语音试听占位符！`);
  console.log('📝 注意：音频文件需要通过管理界面手工生成');
}

async function main() {
  await seedVoiceDemos();
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main()
    .catch((e) => {
      console.error('❌ 语音试听种子数据创建失败:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { seedVoiceDemos };