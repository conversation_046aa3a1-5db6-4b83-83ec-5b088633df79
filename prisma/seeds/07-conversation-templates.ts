import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 对话模板模块种子数据
 * 创建系统预设的对话模板
 */

const CONVERSATION_TEMPLATES = [
  // 中文对话模板
  {
    name: '客服对话模板',
    description: '标准客服接待对话，适合客服培训和演示',
    languageCode: 'zh-CN',
    characters: JSON.stringify([
      {
        id: '1',
        name: '客服小雅',
        voiceId: 'voice-1',
        speed: 1.0,
        pitch: 0,
        volume: 1.0,
        emotion: 'friendly'
      },
      {
        id: '2',
        name: '客户张先生',
        voiceId: 'voice-4',
        speed: 0.9,
        pitch: -2,
        volume: 1.0,
        emotion: 'neutral'
      }
    ]),
    dialogues: JSON.stringify([
      { characterId: '1', text: '您好，欢迎致电客服热线，我是客服小雅，请问有什么可以帮助您的吗？', order: 1 },
      { characterId: '2', text: '你好，我想咨询一下我的订单状态，订单号是12345。', order: 2 },
      { characterId: '1', text: '好的张先生，请您稍等片刻，我帮您查询一下订单状态。', order: 3 },
      { characterId: '1', text: '您的订单已经发货，预计明天下午送达，请您注意查收。', order: 4 },
      { characterId: '2', text: '好的，谢谢你的帮助！', order: 5 },
      { characterId: '1', text: '不客气，如果还有其他问题，随时联系我们。祝您生活愉快！', order: 6 }
    ]),
    isPublic: true,
    usageCount: 0
  },
  {
    name: '教育课堂对话',
    description: '师生对话模板，适合教育内容制作',
    languageCode: 'zh-CN',
    characters: JSON.stringify([
      {
        id: '1',
        name: '李老师',
        voiceId: 'voice-5',
        speed: 0.9,
        pitch: 0,
        volume: 1.0,
        emotion: 'professional'
      },
      {
        id: '2',
        name: '学生小明',
        voiceId: 'voice-2',
        speed: 1.1,
        pitch: 2,
        volume: 1.0,
        emotion: 'curious'
      }
    ]),
    dialogues: JSON.stringify([
      { characterId: '1', text: '同学们，今天我们来学习太阳系的知识。小明，你知道太阳系有几颗行星吗？', order: 1 },
      { characterId: '2', text: '老师，太阳系有八颗行星！', order: 2 },
      { characterId: '1', text: '回答得很好！那你能说出这八颗行星的名字吗？', order: 3 },
      { characterId: '2', text: '水星、金星、地球、火星、木星、土星、天王星、海王星！', order: 4 },
      { characterId: '1', text: '非常棒！看来小明同学课前预习得很认真。', order: 5 }
    ]),
    isPublic: true,
    usageCount: 0
  },
  {
    name: '新闻播报对话',
    description: '新闻主播对话模板，适合新闻内容制作',
    languageCode: 'zh-CN',
    characters: JSON.stringify([
      {
        id: '1',
        name: '主播王芳',
        voiceId: 'voice-13',
        speed: 0.9,
        pitch: 0,
        volume: 1.0,
        emotion: 'professional'
      },
      {
        id: '2',
        name: '记者李强',
        voiceId: 'voice-5',
        speed: 0.95,
        pitch: -1,
        volume: 1.0,
        emotion: 'informative'
      }
    ]),
    dialogues: JSON.stringify([
      { characterId: '1', text: '观众朋友们晚上好，欢迎收看今晚的新闻联播。', order: 1 },
      { characterId: '1', text: '首先关注科技新闻，现在连线前方记者李强。', order: 2 },
      { characterId: '2', text: '王芳你好，我现在在科技展览会现场。', order: 3 },
      { characterId: '2', text: '今天的展览展示了最新的人工智能技术，特别是语音合成领域取得了重大突破。', order: 4 },
      { characterId: '1', text: '谢谢李强的现场报道。下面播报其他新闻。', order: 5 }
    ]),
    isPublic: true,
    usageCount: 0
  },
  {
    name: '产品介绍对话',
    description: '产品销售对话模板，适合营销内容制作',
    languageCode: 'zh-CN',
    characters: JSON.stringify([
      {
        id: '1',
        name: '销售顾问',
        voiceId: 'voice-5',
        speed: 1.0,
        pitch: 1,
        volume: 1.0,
        emotion: 'enthusiastic'
      },
      {
        id: '2',
        name: '潜在客户',
        voiceId: 'voice-4',
        speed: 1.0,
        pitch: 0,
        volume: 1.0,
        emotion: 'interested'
      }
    ]),
    dialogues: JSON.stringify([
      { characterId: '1', text: '您好，欢迎了解我们的AI语音合成服务！', order: 1 },
      { characterId: '2', text: '你们的服务有什么特点吗？', order: 2 },
      { characterId: '1', text: '我们支持30种不同的语音角色和26种语言，质量非常高！', order: 3 },
      { characterId: '2', text: '听起来不错，价格怎么样？', order: 4 },
      { characterId: '1', text: '我们有多种套餐选择，从个人用户到企业用户都有相应的方案。', order: 5 },
      { characterId: '2', text: '可以试用一下吗？', order: 6 },
      { characterId: '1', text: '当然可以！我现在就可以为您演示。', order: 7 }
    ]),
    isPublic: true,
    usageCount: 0
  },
  {
    name: '面试对话模板',
    description: '面试场景对话，适合HR培训和求职指导',
    languageCode: 'zh-CN',
    characters: JSON.stringify([
      {
        id: '1',
        name: 'HR经理',
        voiceId: 'voice-6',
        speed: 0.9,
        pitch: -1,
        volume: 1.0,
        emotion: 'professional'
      },
      {
        id: '2',
        name: '应聘者小李',
        voiceId: 'voice-2',
        speed: 1.0,
        pitch: 1,
        volume: 1.0,
        emotion: 'nervous'
      }
    ]),
    dialogues: JSON.stringify([
      { characterId: '1', text: '小李你好，欢迎来到我们公司面试。请先做个自我介绍吧。', order: 1 },
      { characterId: '2', text: '您好，我叫李明，毕业于计算机专业，有三年的开发经验。', order: 2 },
      { characterId: '1', text: '很好，你为什么想加入我们公司呢？', order: 3 },
      { characterId: '2', text: '我对贵公司的AI技术很感兴趣，希望能在这里发挥我的专长。', order: 4 },
      { characterId: '1', text: '不错，你对我们公司有什么了解吗？', order: 5 },
      { characterId: '2', text: '我知道贵公司是语音技术领域的领先企业，产品质量很高。', order: 6 },
      { characterId: '1', text: '谢谢你的回答，我们会尽快给你反馈。', order: 7 }
    ]),
    isPublic: true,
    usageCount: 0
  },

  // 新增更多中文对话模板
  {
    name: '餐厅点餐对话',
    description: '餐厅服务员与顾客的点餐对话，适合服务业培训',
    languageCode: 'zh-CN',
    characters: JSON.stringify([
      {
        id: '1',
        name: '服务员小王',
        voiceId: 'voice-1',
        speed: 1.0,
        pitch: 1,
        volume: 1.0,
        emotion: 'friendly'
      },
      {
        id: '2',
        name: '顾客李女士',
        voiceId: 'voice-2',
        speed: 1.0,
        pitch: 0,
        volume: 1.0,
        emotion: 'casual'
      }
    ]),
    dialogues: JSON.stringify([
      { characterId: '1', text: '欢迎光临！请问几位用餐？', order: 1 },
      { characterId: '2', text: '两位，谢谢。', order: 2 },
      { characterId: '1', text: '好的，请跟我来。这是菜单，请慢慢看。', order: 3 },
      { characterId: '2', text: '请问你们的招牌菜是什么？', order: 4 },
      { characterId: '1', text: '我们的红烧肉和糖醋里脊都很受欢迎，还有今天的特色汤。', order: 5 },
      { characterId: '2', text: '那就来一份红烧肉，再来个青菜，两碗米饭。', order: 6 },
      { characterId: '1', text: '好的，请稍等，马上为您准备。', order: 7 }
    ]),
    isPublic: true,
    usageCount: 0
  },

  {
    name: '医生问诊对话',
    description: '医生与患者的问诊对话，适合医疗培训',
    languageCode: 'zh-CN',
    characters: JSON.stringify([
      {
        id: '1',
        name: '张医生',
        voiceId: 'voice-5',
        speed: 0.9,
        pitch: 0,
        volume: 1.0,
        emotion: 'professional'
      },
      {
        id: '2',
        name: '患者王先生',
        voiceId: 'voice-4',
        speed: 1.0,
        pitch: 0,
        volume: 1.0,
        emotion: 'concerned'
      }
    ]),
    dialogues: JSON.stringify([
      { characterId: '1', text: '王先生您好，请坐。今天哪里不舒服？', order: 1 },
      { characterId: '2', text: '医生，我最近总是头疼，特别是下午的时候。', order: 2 },
      { characterId: '1', text: '头疼多长时间了？有没有其他症状？', order: 3 },
      { characterId: '2', text: '大概一个星期了，有时候还会感到恶心。', order: 4 },
      { characterId: '1', text: '最近工作压力大吗？睡眠怎么样？', order: 5 },
      { characterId: '2', text: '确实比较忙，经常熬夜加班。', order: 6 },
      { characterId: '1', text: '我先给您检查一下，然后开一些药，注意休息。', order: 7 }
    ]),
    isPublic: true,
    usageCount: 0
  },

  {
    name: '购物咨询对话',
    description: '商场导购与顾客的购物咨询对话',
    languageCode: 'zh-CN',
    characters: JSON.stringify([
      {
        id: '1',
        name: '导购小刘',
        voiceId: 'voice-2',
        speed: 1.0,
        pitch: 1,
        volume: 1.0,
        emotion: 'enthusiastic'
      },
      {
        id: '2',
        name: '顾客陈女士',
        voiceId: 'voice-1',
        speed: 1.0,
        pitch: 0,
        volume: 1.0,
        emotion: 'interested'
      }
    ]),
    dialogues: JSON.stringify([
      { characterId: '1', text: '您好，请问需要什么帮助吗？', order: 1 },
      { characterId: '2', text: '我想买一件适合上班穿的衣服。', order: 2 },
      { characterId: '1', text: '好的，请问您平时穿什么尺码？喜欢什么颜色？', order: 3 },
      { characterId: '2', text: 'M码，我比较喜欢深色系的，比如黑色或深蓝色。', order: 4 },
      { characterId: '1', text: '这件黑色西装很适合您，版型很好，面料也很舒适。', order: 5 },
      { characterId: '2', text: '看起来不错，可以试穿一下吗？', order: 6 },
      { characterId: '1', text: '当然可以，试衣间在那边，我帮您拿过去。', order: 7 }
    ]),
    isPublic: true,
    usageCount: 0
  },

  // 英文对话模板
  {
    name: 'Business Meeting',
    description: 'Professional business meeting dialogue for corporate training',
    languageCode: 'en-US',
    characters: JSON.stringify([
      {
        id: '1',
        name: 'Manager Sarah',
        voiceId: 'voice-1',
        speed: 0.9,
        pitch: 0,
        volume: 1.0,
        emotion: 'professional'
      },
      {
        id: '2',
        name: 'Employee John',
        voiceId: 'voice-4',
        speed: 1.0,
        pitch: -1,
        volume: 1.0,
        emotion: 'confident'
      }
    ]),
    dialogues: JSON.stringify([
      { characterId: '1', text: 'Good morning John. Thank you for joining today\'s meeting.', order: 1 },
      { characterId: '2', text: 'Good morning Sarah. I\'m excited to discuss the new project.', order: 2 },
      { characterId: '1', text: 'Great! Let\'s start with the project timeline. What\'s your assessment?', order: 3 },
      { characterId: '2', text: 'I believe we can complete it in 8 weeks with the current team.', order: 4 },
      { characterId: '1', text: 'That sounds reasonable. What resources will you need?', order: 5 },
      { characterId: '2', text: 'We\'ll need two additional developers and access to the new software.', order: 6 },
      { characterId: '1', text: 'I\'ll arrange that for you. Let\'s schedule a follow-up next week.', order: 7 }
    ]),
    isPublic: true,
    usageCount: 0
  },

  {
    name: 'Hotel Check-in',
    description: 'Hotel reception and guest check-in conversation',
    languageCode: 'en-US',
    characters: JSON.stringify([
      {
        id: '1',
        name: 'Receptionist',
        voiceId: 'voice-2',
        speed: 1.0,
        pitch: 1,
        volume: 1.0,
        emotion: 'friendly'
      },
      {
        id: '2',
        name: 'Guest',
        voiceId: 'voice-5',
        speed: 1.0,
        pitch: 0,
        volume: 1.0,
        emotion: 'polite'
      }
    ]),
    dialogues: JSON.stringify([
      { characterId: '1', text: 'Welcome to Grand Hotel! How may I assist you today?', order: 1 },
      { characterId: '2', text: 'Hello, I have a reservation under the name Johnson.', order: 2 },
      { characterId: '1', text: 'Let me check that for you. Yes, I see your reservation for three nights.', order: 3 },
      { characterId: '2', text: 'Perfect. Is breakfast included in the rate?', order: 4 },
      { characterId: '1', text: 'Yes, breakfast is served from 6 to 10 AM in our restaurant.', order: 5 },
      { characterId: '2', text: 'Excellent. What time is check-out?', order: 6 },
      { characterId: '1', text: 'Check-out is at 11 AM. Here\'s your key card. Enjoy your stay!', order: 7 }
    ]),
    isPublic: true,
    usageCount: 0
  }
];

async function seedConversationTemplates() {
  console.log('💬 开始创建对话模板种子数据...');

  for (const [index, template] of CONVERSATION_TEMPLATES.entries()) {
    await prisma.conversationTemplate.upsert({
      where: { id: `template-${String(index + 1).padStart(2, '0')}` },
      update: template,
      create: {
        id: `template-${String(index + 1).padStart(2, '0')}`,
        userId: null, // 系统模板
        ...template,
      },
    });
    
    console.log(`✅ 创建对话模板: ${template.name}`);
  }

  console.log(`🎉 成功创建 ${CONVERSATION_TEMPLATES.length} 个对话模板！`);
}

async function main() {
  await seedConversationTemplates();
}

// 检查是否直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
    .catch((e) => {
      console.error('❌ 对话模板种子数据创建失败:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { seedConversationTemplates };