import { PrismaClient } from '@prisma/client';

// 导入所有种子模块
import { seedUsers } from './01-users';
import { seedLanguages } from './02-languages';
import { seedVoiceCharacters } from './03-voice-characters';
// import { seedCharacterPackages } from './04-character-packages'; // 已移除，使用简化积分包系统
import { seedCharacterMultilingualNames } from './05-character-multilingual-names';
import { seedVoiceDemos } from './06-voice-demos';
import { seedConversationTemplates } from './07-conversation-templates';
import { seedApiConfigs } from './08-api-configs';
import { seedApiPricingConfig } from './05-api-pricing-config';
import { seedCreditPackagePricing } from './05-credit-package-pricing';
import { seedStyleCategories } from './09-style-categories';
import { seedSystemStyles } from './10-system-styles';
import { seedCharacterAvatars } from './11-character-avatars';

const prisma = new PrismaClient();

/**
 * 生产环境模块化种子数据主执行脚本
 * 按正确的依赖顺序执行所有种子模块
 */

const SEED_MODULES = [
  { name: '用户模块', fn: seedUsers, required: true },
  { name: '语言模块', fn: seedLanguages, required: true },
  { name: 'API配置模块', fn: seedApiConfigs, required: true },
  { name: 'API定价配置模块', fn: seedApiPricingConfig, required: true },
  { name: '积分包定价模块', fn: seedCreditPackagePricing, required: true },
  // { name: '字符包模块', fn: seedCharacterPackages, required: true }, // 已移除，使用简化积分包系统
  { name: '语音角色模块', fn: seedVoiceCharacters, required: true },
  { name: '角色头像模块', fn: seedCharacterAvatars, required: false },
  { name: '风格分类模块', fn: seedStyleCategories, required: true },
  { name: '系统风格模块', fn: seedSystemStyles, required: true },
  { name: '角色名称本地化模块', fn: seedCharacterMultilingualNames, required: false },
  { name: '语音试听模块', fn: seedVoiceDemos, required: true },
  { name: '对话模板模块', fn: seedConversationTemplates, required: false },
];

async function runProductionSeeds() {
  console.log('🚀 开始执行生产环境种子数据初始化...');
  console.log('=' .repeat(60));

  const results = {
    success: 0,
    failed: 0,
    skipped: 0,
    total: SEED_MODULES.length
  };

  for (const [index, module] of SEED_MODULES.entries()) {
    console.log(`\n📦 [${index + 1}/${SEED_MODULES.length}] 正在执行: ${module.name}`);
    console.log('-'.repeat(40));

    try {
      const startTime = Date.now();
      await module.fn();
      const duration = Date.now() - startTime;
      
      console.log(`✅ ${module.name} 执行成功 (${duration}ms)`);
      results.success++;
      
    } catch (error) {
      console.error(`❌ ${module.name} 执行失败:`, error);
      
      if (module.required) {
        console.error(`💥 必需模块 "${module.name}" 失败，停止执行`);
        results.failed++;
        throw error;
      } else {
        console.warn(`⚠️  可选模块 "${module.name}" 失败，继续执行`);
        results.skipped++;
      }
    }
  }

  console.log('\n' + '='.repeat(60));
  console.log('🎉 生产环境种子数据初始化完成！');
  console.log('📊 执行统计:');
  console.log(`   ✅ 成功: ${results.success}/${results.total}`);
  console.log(`   ❌ 失败: ${results.failed}/${results.total}`);
  console.log(`   ⚠️  跳过: ${results.skipped}/${results.total}`);

  if (results.failed === 0) {
    console.log('\n🎯 所有必需模块都已成功初始化，系统可以正常运行！');
  }

  return results;
}

// 导出选择性执行函数
export async function runBasicSeeds() {
  console.log('🔧 执行基础种子数据 (用户 + 语言 + API配置)...');
  
  await seedUsers();
  await seedLanguages(); 
  await seedApiConfigs();
  
  console.log('✅ 基础种子数据执行完成！');
}

export async function runContentSeeds() {
  console.log('📦 执行内容种子数据 (角色 + 套餐 + 模板)...');
  
  await seedCharacterPackages();
  await seedVoiceCharacters();
  await seedVoiceDemos();
  await seedConversationTemplates();
  
  console.log('✅ 内容种子数据执行完成！');
}

export async function runOptionalSeeds() {
  console.log('🌐 执行可选种子数据 (本地化名称)...');
  
  try {
    await seedCharacterMultilingualNames();
    console.log('✅ 可选种子数据执行完成！');
  } catch (error) {
    console.warn('⚠️  可选种子数据执行失败，但不影响系统运行:', error);
  }
}

async function main() {
  await runProductionSeeds();
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main()
    .catch((e) => {
      console.error('💥 种子数据初始化失败:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { runProductionSeeds, main };