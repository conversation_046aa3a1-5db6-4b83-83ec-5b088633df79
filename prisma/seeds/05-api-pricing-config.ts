import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedApiPricingConfig() {
  console.log('🔧 Seeding API pricing configuration...');

  // 检查是否已存在配置
  const existingConfig = await prisma.apiPricingConfig.findFirst({
    where: { isActive: true },
  });

  if (existingConfig) {
    console.log('✅ API pricing configuration already exists');
    return;
  }

  // 创建默认API定价配置（简化版本）
  const defaultConfig = await prisma.apiPricingConfig.create({
    data: {
      // 语音生成定价
      standardInputPrice: 0.50,
      standardOutputPrice: 10.00,
      professionalInputPrice: 1.00,
      professionalOutputPrice: 20.00,
      // AI文本生成定价
      textGenerationInputPrice: 0.50,
      textGenerationOutputPrice: 1.50,
      isActive: true,
      description: 'Default API pricing configuration - simplified version',
    },
  });

  console.log('✅ Created default API pricing configuration:', defaultConfig.id);
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  seedApiPricingConfig()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
