import { PrismaClient, Gender } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 语音角色头像种子数据
 * 为30个语音角色根据性别配置合适的头像
 */

// 女性角色头像集合（使用多样化的女性头像）
const FEMALE_AVATARS = [
  'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1506277886164-e25aa3f4ef7f?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1521146764736-56c929d59c83?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1502823403499-6ccfcf4fb453?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1499996860823-5214fcc65f8f?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?w=150&h=150&fit=crop&crop=face&auto=format',
];

// 男性角色头像集合（使用多样化的男性头像）
const MALE_AVATARS = [
  'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1522556189639-b150ed9c4330?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1568602471122-7832951cc4c5?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1607990281513-2c110a25bd8c?w=150&h=150&fit=crop&crop=face&auto=format',
  'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face&auto=format',
];

// 角色名称到头像的映射（根据性别和个性特征）
const CHARACTER_AVATAR_MAPPING = {
  // 女性角色 (15个)
  'Zephyr': FEMALE_AVATARS[0],      // 明亮清新 - 年轻活力的女性
  'Leda': FEMALE_AVATARS[1],        // 青春甜美 - 甜美的年轻女性
  'Aoede': FEMALE_AVATARS[2],       // 轻松自然 - 自然舒适的女性
  'Callirrhoe': FEMALE_AVATARS[3],  // 轻松随意 - 亲切随和的女性
  'Achird': FEMALE_AVATARS[4],      // 友好亲切 - 温暖友好的女性
  'Despina': FEMALE_AVATARS[5],     // 平滑优雅 - 优雅成熟的女性
  'Erinome': FEMALE_AVATARS[6],     // 清晰准确 - 专业干练的女性
  'Laomedeia': FEMALE_AVATARS[7],   // 欢快愉悦 - 开朗活泼的女性
  'Autonoe': FEMALE_AVATARS[8],     // 明亮清晰 - 商务专业的女性
  'Pulcherrima': FEMALE_AVATARS[9], // 直率坦诚 - 真实自然的女性
  'Sulafat': FEMALE_AVATARS[10],    // 温暖亲切 - 温情的女性
  'Vindemiatrix': FEMALE_AVATARS[11], // 温和柔软 - 温和的女性
  'Schedar': FEMALE_AVATARS[12],    // 均匀稳定 - 稳重的女性
  'Sadachbia': FEMALE_AVATARS[13],  // 活泼生动 - 活泼的女性
  'Enceladus': FEMALE_AVATARS[14],  // 气声质感 - 独特魅力的女性

  // 男性角色 (15个)
  'Puck': MALE_AVATARS[0],          // 欢快活泼 - 年轻活力的男性
  'Charon': MALE_AVATARS[1],        // 信息丰富 - 博学专业的男性
  'Kore': MALE_AVATARS[2],          // 坚定有力 - 坚定自信的男性
  'Fenrir': MALE_AVATARS[3],        // 兴奋激动 - 热情活力的男性
  'Orus': MALE_AVATARS[4],          // 企业专业 - 权威可靠的男性
  'Iapetus': MALE_AVATARS[5],       // 清晰明了 - 简洁准确的男性
  'Umbriel': MALE_AVATARS[6],       // 随和亲切 - 友好温和的男性
  'Algieba': MALE_AVATARS[7],       // 平滑流畅 - 优雅动听的男性
  'Algenib': MALE_AVATARS[8],       // 沙哑质感 - 个性独特的男性
  'Rasalgethi': MALE_AVATARS[9],    // 信息丰富 - 智慧博学的男性
  'Achernar': MALE_AVATARS[10],     // 柔和温暖 - 亲切舒缓的男性
  'Alnilam': MALE_AVATARS[11],      // 坚定有力 - 态度明确的男性
  'Gacrux': MALE_AVATARS[12],       // 成熟稳重 - 经验丰富的男性
  'Zubenelgenubi': MALE_AVATARS[13], // 随意轻松 - 平民化亲切的男性
  'Sadaltager': MALE_AVATARS[14],   // 知识渊博 - 智慧专业的男性
};

async function seedCharacterAvatars() {
  console.log('🎭 开始为语音角色配置头像...');

  try {
    // 获取所有现有的语言角色和对应的模板
    const languageCharacters = await prisma.languageCharacter.findMany({
      include: {
        template: true,
        language: true,
      },
    });

    console.log(`📋 找到 ${languageCharacters.length} 个语言角色需要配置头像`);

    let updatedCount = 0;
    let skippedCount = 0;

    for (const langChar of languageCharacters) {
      // 如果角色已经有头像，跳过
      if (langChar.avatarUrl) {
        console.log(`⏭️  跳过已有头像的角色: ${langChar.name} (${langChar.language.code})`);
        skippedCount++;
        continue;
      }

      // 根据模板的原始名称获取头像URL
      let avatarUrl = CHARACTER_AVATAR_MAPPING[langChar.template.originalName as keyof typeof CHARACTER_AVATAR_MAPPING];

      // 如果没有找到特定映射，根据性别随机选择
      if (!avatarUrl) {
        if (langChar.template.gender === Gender.FEMALE) {
          const randomIndex = Math.floor(Math.random() * FEMALE_AVATARS.length);
          avatarUrl = FEMALE_AVATARS[randomIndex];
        } else if (langChar.template.gender === Gender.MALE) {
          const randomIndex = Math.floor(Math.random() * MALE_AVATARS.length);
          avatarUrl = MALE_AVATARS[randomIndex];
        } else {
          // 中性角色随机选择
          const allAvatars = [...FEMALE_AVATARS, ...MALE_AVATARS];
          const randomIndex = Math.floor(Math.random() * allAvatars.length);
          avatarUrl = allAvatars[randomIndex];
        }
      }

      // 更新语言角色头像
      await prisma.languageCharacter.update({
        where: { id: langChar.id },
        data: { avatarUrl },
      });

      const genderText = langChar.template.gender === Gender.FEMALE ? '女性' :
                        langChar.template.gender === Gender.MALE ? '男性' : '中性';

      console.log(`✅ 更新语言角色头像: ${langChar.name} (${langChar.language.code}, ${genderText}) -> ${avatarUrl.substring(0, 50)}...`);
      updatedCount++;
    }

    console.log(`🎉 头像配置完成！`);
    console.log(`  ✅ 更新: ${updatedCount} 个语言角色`);
    console.log(`  ⏭️  跳过: ${skippedCount} 个语言角色（已有头像）`);

    // 显示性别分布统计
    const genderStats = await prisma.languageCharacter.groupBy({
      by: ['templateId'],
      _count: {
        id: true,
      },
      where: {
        avatarUrl: {
          not: null,
        },
      },
    });

    // 获取模板的性别信息来显示统计
    const templates = await prisma.apiCharacterTemplate.findMany({
      select: { id: true, gender: true }
    });

    const genderCounts = { FEMALE: 0, MALE: 0, NEUTRAL: 0 };

    genderStats.forEach(stat => {
      const template = templates.find(t => t.id === stat.templateId);
      if (template) {
        genderCounts[template.gender] += stat._count.id;
      }
    });

    console.log('\n📊 头像配置统计:');
    console.log(`  女性: ${genderCounts.FEMALE} 个语言角色`);
    console.log(`  男性: ${genderCounts.MALE} 个语言角色`);
    console.log(`  中性: ${genderCounts.NEUTRAL} 个语言角色`);

  } catch (error) {
    console.error('❌ 配置角色头像失败:', error);
    throw error;
  }
}

async function main() {
  await seedCharacterAvatars();
}

// 检查是否直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
    .catch((e) => {
      console.error('❌ 头像配置失败:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { seedCharacterAvatars };
