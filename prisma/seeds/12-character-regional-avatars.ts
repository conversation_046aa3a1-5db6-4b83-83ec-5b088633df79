import { PrismaClient, Gender } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 语音角色地区化头像种子数据
 * 根据不同语言配置对应国家风格的人物头像
 */

// 按地区和性别分类的头像集合
const REGIONAL_AVATARS = {
  // 亚洲地区头像
  asian: {
    female: [
      'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face&auto=format', // 亚洲女性1
      'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=150&h=150&fit=crop&crop=face&auto=format', // 亚洲女性2
      'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=150&h=150&fit=crop&crop=face&auto=format', // 亚洲女性3
      'https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=150&h=150&fit=crop&crop=face&auto=format', // 亚洲女性4
      'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face&auto=format', // 亚洲女性5
      'https://images.unsplash.com/photo-1506277886164-e25aa3f4ef7f?w=150&h=150&fit=crop&crop=face&auto=format', // 亚洲女性6
      'https://images.unsplash.com/photo-1521146764736-56c929d59c83?w=150&h=150&fit=crop&crop=face&auto=format', // 亚洲女性7
      'https://images.unsplash.com/photo-1502823403499-6ccfcf4fb453?w=150&h=150&fit=crop&crop=face&auto=format', // 亚洲女性8
    ],
    male: [
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format', // 亚洲男性1
      'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face&auto=format', // 亚洲男性2
      'https://images.unsplash.com/photo-1522556189639-b150ed9c4330?w=150&h=150&fit=crop&crop=face&auto=format', // 亚洲男性3
      'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face&auto=format', // 亚洲男性4
      'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face&auto=format', // 亚洲男性5
      'https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=150&h=150&fit=crop&crop=face&auto=format', // 亚洲男性6
      'https://images.unsplash.com/photo-1568602471122-7832951cc4c5?w=150&h=150&fit=crop&crop=face&auto=format', // 亚洲男性7
      'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?w=150&h=150&fit=crop&crop=face&auto=format', // 亚洲男性8
    ]
  },
  
  // 欧美地区头像
  western: {
    female: [
      'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face&auto=format', // 欧美女性1
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face&auto=format', // 欧美女性2
      'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=face&auto=format', // 欧美女性3
      'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face&auto=format', // 欧美女性4
      'https://images.unsplash.com/photo-1499996860823-5214fcc65f8f?w=150&h=150&fit=crop&crop=face&auto=format', // 欧美女性5
      'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?w=150&h=150&fit=crop&crop=face&auto=format', // 欧美女性6
    ],
    male: [
      'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face&auto=format', // 欧美男性1
      'https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=150&h=150&fit=crop&crop=face&auto=format', // 欧美男性2
      'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face&auto=format', // 欧美男性3
      'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face&auto=format', // 欧美男性4
      'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=150&h=150&fit=crop&crop=face&auto=format', // 欧美男性5
      'https://images.unsplash.com/photo-1607990281513-2c110a25bd8c?w=150&h=150&fit=crop&crop=face&auto=format', // 欧美男性6
      'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face&auto=format', // 欧美男性7
    ]
  },

  // 中东地区头像
  middleEast: {
    female: [
      'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face&auto=format', // 中东女性1
      'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=150&h=150&fit=crop&crop=face&auto=format', // 中东女性2
    ],
    male: [
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format', // 中东男性1
      'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face&auto=format', // 中东男性2
    ]
  },

  // 南亚地区头像
  southAsian: {
    female: [
      'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=150&h=150&fit=crop&crop=face&auto=format', // 南亚女性1
      'https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=150&h=150&fit=crop&crop=face&auto=format', // 南亚女性2
    ],
    male: [
      'https://images.unsplash.com/photo-1522556189639-b150ed9c4330?w=150&h=150&fit=crop&crop=face&auto=format', // 南亚男性1
      'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face&auto=format', // 南亚男性2
    ]
  }
};

// 语言代码到地区的映射
const LANGUAGE_TO_REGION = {
  // 亚洲语言
  'zh-CN': 'asian',    // 中文（简体）
  'zh-TW': 'asian',    // 中文（繁体）
  'ja-JP': 'asian',    // 日语
  'ko-KR': 'asian',    // 韩语
  'th-TH': 'asian',    // 泰语
  'vi-VN': 'asian',    // 越南语
  'km-KH': 'asian',    // 高棉语
  'id-ID': 'asian',    // 印尼语

  // 欧美语言
  'en-US': 'western',  // 英语（美国）
  'en-IN': 'western',  // 英语（印度）
  'de-DE': 'western',  // 德语
  'es-US': 'western',  // 西班牙语
  'fr-FR': 'western',  // 法语
  'it-IT': 'western',  // 意大利语
  'pt-BR': 'western',  // 葡萄牙语
  'ru-RU': 'western',  // 俄语
  'nl-NL': 'western',  // 荷兰语
  'pl-PL': 'western',  // 波兰语
  'ro-RO': 'western',  // 罗马尼亚语
  'uk-UA': 'western',  // 乌克兰语

  // 中东语言
  'ar-EG': 'middleEast', // 阿拉伯语
  'tr-TR': 'middleEast', // 土耳其语

  // 南亚语言
  'hi-IN': 'southAsian', // 印地语
  'bn-BD': 'southAsian', // 孟加拉语
  'mr-IN': 'southAsian', // 马拉地语
  'ta-IN': 'southAsian', // 泰米尔语
  'te-IN': 'southAsian', // 泰卢固语
};

// 获取指定地区和性别的头像
function getRegionalAvatar(region: string, gender: Gender, index: number): string {
  const genderKey = gender === Gender.FEMALE ? 'female' : 'male';
  const avatars = REGIONAL_AVATARS[region as keyof typeof REGIONAL_AVATARS]?.[genderKey];
  
  if (!avatars || avatars.length === 0) {
    // 如果没有找到对应地区的头像，使用亚洲头像作为默认
    const defaultAvatars = REGIONAL_AVATARS.asian[genderKey];
    return defaultAvatars[index % defaultAvatars.length];
  }
  
  return avatars[index % avatars.length];
}

async function seedCharacterRegionalAvatars() {
  console.log('🌍 开始为语音角色配置地区化头像...');

  try {
    // 获取所有语言角色
    const languageCharacters = await prisma.languageCharacter.findMany({
      include: {
        template: true,
        language: true,
      },
    });

    console.log(`🎭 找到 ${languageCharacters.length} 个语言角色需要配置地区化头像`);

    let updatedCount = 0;
    let skippedCount = 0;

    // 按模板分组，为每个模板的不同语言版本配置地区化头像
    const templateGroups = new Map<string, typeof languageCharacters>();

    languageCharacters.forEach(langChar => {
      const templateId = langChar.templateId;
      if (!templateGroups.has(templateId)) {
        templateGroups.set(templateId, []);
      }
      templateGroups.get(templateId)!.push(langChar);
    });

    for (const [templateId, langChars] of templateGroups) {
      const template = langChars[0].template;
      console.log(`\n🎭 处理模板: ${template.originalName} (${template.gender})`);

      for (const [index, langChar] of langChars.entries()) {
        // 检查是否需要强制更新头像（可以通过参数控制）
        const forceUpdate = process.argv.includes('--force');

        if (langChar.avatarUrl && !forceUpdate) {
          console.log(`⏭️  跳过已有头像的语言角色: ${langChar.name} (${langChar.language.code})`);
          skippedCount++;
          continue;
        }

        // 根据语言确定地区
        const region = LANGUAGE_TO_REGION[langChar.language.code as keyof typeof LANGUAGE_TO_REGION] || 'asian';

        // 根据地区、性别和索引获取头像
        const avatarUrl = getRegionalAvatar(region, template.gender, index);

        // 更新语言角色头像
        await prisma.languageCharacter.update({
          where: { id: langChar.id },
          data: { avatarUrl },
        });

        const genderText = template.gender === Gender.FEMALE ? '女性' :
                          template.gender === Gender.MALE ? '男性' : '中性';

        console.log(`✅ 更新语言角色头像: ${langChar.name} (${langChar.language.code}, ${genderText}, ${region}) -> ${avatarUrl.substring(0, 50)}...`);
        updatedCount++;
      }
    }

    console.log(`\n🎉 地区化头像配置完成！`);
    console.log(`  ✅ 更新: ${updatedCount} 个语言角色头像`);
    console.log(`  ⏭️  跳过: ${skippedCount} 个语言角色（已有头像）`);

    // 显示地区分布统计
    console.log('\n📊 地区分布统计:');
    const regionCounts = new Map<string, number>();

    languageCharacters.forEach(langChar => {
      const region = LANGUAGE_TO_REGION[langChar.language.code as keyof typeof LANGUAGE_TO_REGION] || 'asian';
      regionCounts.set(region, (regionCounts.get(region) || 0) + 1);
    });

    regionCounts.forEach((count, region) => {
      console.log(`  ${region} 地区: ${count} 个语言角色`);
    });

  } catch (error) {
    console.error('❌ 配置地区化头像失败:', error);
    throw error;
  }
}

async function main() {
  await seedCharacterRegionalAvatars();
}

// 检查是否直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
    .catch((e) => {
      console.error('❌ 地区化头像配置失败:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { seedCharacterRegionalAvatars };
