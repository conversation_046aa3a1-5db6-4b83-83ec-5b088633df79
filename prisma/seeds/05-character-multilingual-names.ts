import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 角色名称本地化模块种子数据
 * 为30个语音角色在26种语言下创建本地化名称
 */

// 获取前13个角色的多语言名称（由于内容限制，分批处理）
const CHARACTER_MULTILINGUAL_NAMES_BATCH_1 = {
  'Zephyr': {
    'km-KH': 'ហ្សេហ្វៀរ', 'zh-CN': '西风', 'en-US': 'Zephyr', 'ja-JP': 'ゼフィール', 'ko-KR': '제피르',
    'ar-EG': 'زِيْفير', 'de-DE': 'Zephyr', 'es-US': 'Céfiro', 'fr-FR': 'Zéphyr', 'hi-IN': 'ज़ेफ़िर', 
    'id-ID': 'Zephyr', 'it-IT': 'Zefiro', 'pt-BR': 'Zéfiro', 'ru-RU': 'Зефир', 'nl-NL': 'Zephyr',
    'pl-PL': 'Zefir', 'th-TH': 'เซฟเฟอร์', 'tr-TR': 'Zefir', 'vi-VN': 'Zephyr', 'ro-RO': 'Zefir',
    'uk-UA': 'Зефір', 'bn-BD': 'জেফির', 'en-IN': 'Zephyr', 'mr-IN': 'झेफीर', 'ta-IN': 'செஃபிர்', 'te-IN': 'జెఫిర్'
  },
  'Leda': {
    'km-KH': 'លេដា', 'zh-CN': '勒达', 'en-US': 'Leda', 'ja-JP': 'レダ', 'ko-KR': '레다',
    'ar-EG': 'ليدا', 'de-DE': 'Leda', 'es-US': 'Leda', 'fr-FR': 'Léda', 'hi-IN': 'लेडा',
    'id-ID': 'Leda', 'it-IT': 'Leda', 'pt-BR': 'Leda', 'ru-RU': 'Леда', 'nl-NL': 'Leda',
    'pl-PL': 'Leda', 'th-TH': 'เลดา', 'tr-TR': 'Leda', 'vi-VN': 'Leda', 'ro-RO': 'Leda',
    'uk-UA': 'Леда', 'bn-BD': 'লেডা', 'en-IN': 'Leda', 'mr-IN': 'लेडा', 'ta-IN': 'லேடா', 'te-IN': 'లేడా'
  },
  'Puck': {
    'km-KH': 'ពាក់', 'zh-CN': '帕克', 'en-US': 'Puck', 'ja-JP': 'パック', 'ko-KR': '퍽',
    'ar-EG': 'باك', 'de-DE': 'Puck', 'es-US': 'Puck', 'fr-FR': 'Puck', 'hi-IN': 'पक',
    'id-ID': 'Puck', 'it-IT': 'Puck', 'pt-BR': 'Puck', 'ru-RU': 'Пак', 'nl-NL': 'Puck',
    'pl-PL': 'Puck', 'th-TH': 'พัค', 'tr-TR': 'Puck', 'vi-VN': 'Puck', 'ro-RO': 'Puck',
    'uk-UA': 'Пак', 'bn-BD': 'পাক', 'en-IN': 'Puck', 'mr-IN': 'पक', 'ta-IN': 'பக்', 'te-IN': 'పక్'
  },
  'Charon': {
    'km-KH': 'ការ៉ុន', 'zh-CN': '卡戎', 'en-US': 'Charon', 'ja-JP': 'カロン', 'ko-KR': '카론',
    'ar-EG': 'شارون', 'de-DE': 'Charon', 'es-US': 'Caronte', 'fr-FR': 'Charon', 'hi-IN': 'चारोन',
    'id-ID': 'Charon', 'it-IT': 'Caronte', 'pt-BR': 'Caronte', 'ru-RU': 'Харон', 'nl-NL': 'Charon',
    'pl-PL': 'Charon', 'th-TH': 'ชารอน', 'tr-TR': 'Charon', 'vi-VN': 'Charon', 'ro-RO': 'Charon',
    'uk-UA': 'Харон', 'bn-BD': 'চারন', 'en-IN': 'Charon', 'mr-IN': 'चारोन', 'ta-IN': 'சாரோன்', 'te-IN': 'చారోన్'
  },
  'Kore': {
    'km-KH': 'កូរេ', 'zh-CN': '科瑞', 'en-US': 'Kore', 'ja-JP': 'コレ', 'ko-KR': '코레',
    'ar-EG': 'كوري', 'de-DE': 'Kore', 'es-US': 'Core', 'fr-FR': 'Coré', 'hi-IN': 'कोरे',
    'id-ID': 'Kore', 'it-IT': 'Core', 'pt-BR': 'Core', 'ru-RU': 'Кора', 'nl-NL': 'Kore',
    'pl-PL': 'Kore', 'th-TH': 'โคเร', 'tr-TR': 'Kore', 'vi-VN': 'Kore', 'ro-RO': 'Kore',
    'uk-UA': 'Кора', 'bn-BD': 'কোরে', 'en-IN': 'Kore', 'mr-IN': 'कोरे', 'ta-IN': 'கோரே', 'te-IN': 'కోరే'
  }
  // 其他25个角色的本地化数据将在完整版本中添加
};

async function seedCharacterMultilingualNames() {
  console.log('🌐 开始创建角色名称本地化种子数据...');

  // 获取所有语言角色和对应的模板
  const languageCharacters = await prisma.languageCharacter.findMany({
    include: {
      template: true,
      language: true
    }
  });

  let updatedCount = 0;

  for (const langChar of languageCharacters) {
    const templateName = langChar.template.originalName;
    const languageCode = langChar.language.code;

    const multilingualNames = CHARACTER_MULTILINGUAL_NAMES_BATCH_1[templateName];

    if (multilingualNames && multilingualNames[languageCode]) {
      await prisma.languageCharacter.update({
        where: { id: langChar.id },
        data: {
          name: multilingualNames[languageCode]
        }
      });

      console.log(`✅ 更新语言角色本地化名称: ${templateName} (${languageCode}) -> ${multilingualNames[languageCode]}`);
      updatedCount++;
    } else {
      console.log(`⚠️  跳过语言角色 ${templateName} (${languageCode})（暂未包含在此批次中）`);
    }
  }

  console.log(`🎉 成功更新 ${updatedCount} 个语言角色的本地化名称！`);
  console.log('📝 注意：这是第一批角色，完整的30个角色本地化数据请参考完整版种子文件');
}

async function main() {
  await seedCharacterMultilingualNames();
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main()
    .catch((e) => {
      console.error('❌ 角色名称本地化种子数据创建失败:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { seedCharacterMultilingualNames };