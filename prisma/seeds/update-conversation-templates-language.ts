import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function updateConversationTemplatesLanguage() {
  console.log('🔄 开始更新对话模板的语言字段...');

  try {
    // 获取所有现有的对话模板
    const templates = await prisma.conversationTemplate.findMany();
    
    console.log(`📋 找到 ${templates.length} 个对话模板需要更新`);

    // 根据模板名称判断语言
    const languageMapping: Record<string, string> = {
      'Business Meeting': 'en-US',
      'Hotel Check-in': 'en-US',
      'Restaurant Ordering': 'en-US',
      'Job Interview': 'en-US',
      'Doctor Consultation': 'en-US',
      'Shopping Assistance': 'en-US',
      'News Broadcasting': 'en-US',
      'Product Introduction': 'en-US',
      'Customer Service': 'en-US',
      'Educational Classroom': 'en-US',
    };

    let updatedCount = 0;

    for (const template of templates) {
      // 判断语言
      let languageCode = 'zh-CN'; // 默认中文
      
      // 检查是否是英文模板
      if (languageMapping[template.name]) {
        languageCode = languageMapping[template.name];
      } else if (
        template.name.includes('Business') ||
        template.name.includes('Hotel') ||
        template.name.includes('Meeting') ||
        /^[A-Za-z\s]+$/.test(template.name) // 纯英文名称
      ) {
        languageCode = 'en-US';
      }

      // 检查对话内容判断语言
      try {
        const dialogues = JSON.parse(template.dialogues);
        if (dialogues.length > 0) {
          const firstDialogue = dialogues[0];
          const text = firstDialogue.text || '';
          
          // 简单的语言检测：如果包含中文字符，则为中文
          if (/[\u4e00-\u9fff]/.test(text)) {
            languageCode = 'zh-CN';
          } else if (/^[A-Za-z\s.,!?'"]+$/.test(text)) {
            languageCode = 'en-US';
          }
        }
      } catch (error) {
        console.warn(`⚠️  解析对话内容失败: ${template.name}`);
      }

      // 更新模板
      await prisma.conversationTemplate.update({
        where: { id: template.id },
        data: { languageCode },
      });

      console.log(`✅ 更新模板: ${template.name} -> ${languageCode}`);
      updatedCount++;
    }

    console.log(`🎉 成功更新 ${updatedCount} 个对话模板的语言字段！`);

    // 显示统计信息
    const stats = await prisma.conversationTemplate.groupBy({
      by: ['languageCode'],
      _count: {
        id: true,
      },
    });

    console.log('\n📊 语言分布统计:');
    stats.forEach(stat => {
      const languageName = stat.languageCode === 'zh-CN' ? '中文' : 
                          stat.languageCode === 'en-US' ? '英文' : 
                          stat.languageCode;
      console.log(`  ${languageName} (${stat.languageCode}): ${stat._count.id} 个模板`);
    });

  } catch (error) {
    console.error('❌ 更新对话模板语言字段失败:', error);
    throw error;
  }
}

async function main() {
  await updateConversationTemplatesLanguage();
}

// 检查是否直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
    .catch((e) => {
      console.error('❌ 更新失败:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { updateConversationTemplatesLanguage };
