import { PrismaClient, StyleType } from '@prisma/client';
import { createSeparatedStyleCategories } from './09-separated-style-categories';

const prisma = new PrismaClient();

/**
 * 系统官方风格模块种子数据
 * 创建Voctana预设的官方风格
 */

// 系统预设风格
const SYSTEM_STYLES: any[] = [
  // 情感类风格
  {
    name: '温暖亲切',
    nameEn: 'Warm & Friendly',
    nameKhmer: 'ក្ដី​ស្រឡាញ់ និង​ភ្ញាក់​ផ្អែម',
    description: '温暖亲切的语调，适合客服、教育等场景',
    prompt: 'in a warm and friendly tone, like a caring friend',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['温暖', '亲切', '友好', '客服', '教育'],
    icon: '❤️',
    color: '#FF6B6B',
    categoryId: null, // 将在运行时设置
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.0,
      pitch: 0,
      volume: 0,
    },
  },
  {
    name: '专业权威',
    nameEn: 'Professional & Authoritative',
    nameKhmer: 'អាជីព និង​មាន​សិទ្ធិ​បញ្ញាត្តិ',
    description: '专业权威的语调，适合商务、新闻等正式场合',
    prompt: 'with a professional and authoritative tone, like a news anchor',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['专业', '权威', '正式', '商务', '新闻'],
    icon: '💼',
    color: '#4ECDC4',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 0.9,
      pitch: -1,
      volume: 0,
    },
  },
  {
    name: '活泼欢快',
    nameEn: 'Lively & Cheerful',
    nameKhmer: 'រំភើប និង​រីករាយ',
    description: '活泼欢快的语调，适合儿童内容、娱乐等场景',
    prompt: 'lively and cheerful tone, like an excited child',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['活泼', '欢快', '儿童', '娱乐', '游戏'],
    icon: '🎉',
    color: '#FFE66D',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.1,
      pitch: 2,
      volume: 1,
    },
  },
  {
    name: '冷静沉着',
    nameEn: 'Calm & Composed',
    nameKhmer: 'ស្ងប់​ស្ងាត់ និង​មាន​សត្ថិ',
    description: '冷静沉着的语调，适合冥想、心理咨询等场景',
    prompt: 'calm and composed tone, like a meditation guide',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['冷静', '沉着', '冥想', '心理', '放松'],
    icon: '🧘',
    color: '#6C5CE7',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 0.8,
      pitch: -2,
      volume: -1,
    },
  },
  {
    name: '激情澎湃',
    nameEn: 'Passionate & Energetic',
    description: '激情澎湃的语调，适合演讲、广告等需要感染力的场景',
    prompt: 'passionate and energetic tone, like a motivational speaker',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['激情', '澎湃', '演讲', '广告', '激励'],
    icon: '🔥',
    color: '#FF4757',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.0,
      pitch: 3,
      volume: 2,
    },
  },

  // 语调类风格
  {
    name: '轻柔细语',
    nameEn: 'Soft Whisper',
    nameKhmer: 'សំឡេង​ប៉ុផ្ទះ',
    description: '轻柔细语的语调，适合睡前故事、放松音频等',
    prompt: 'soft whisper tone, like someone telling a bedtime story',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['轻柔', '细语', '睡前', '放松', '故事'],
    icon: '🌙',
    color: '#A29BFE',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 0.7,
      pitch: -3,
      volume: -3,
    },
  },
  {
    name: '清晰明亮',
    nameEn: 'Clear & Bright',
    description: '清晰明亮的语调，适合教学、说明等需要清晰表达的场景',
    prompt: 'clear and bright tone, like a teacher explaining a concept',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['清晰', '明亮', '教学', '说明', '解释'],
    icon: '💡',
    color: '#00B894',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.0,
      pitch: 1,
      volume: 0,
    },
  },
  {
    name: '深沉稳重',
    nameEn: 'Deep & Steady',
    description: '深沉稳重的语调，适合纪录片、历史内容等',
    prompt: 'deep and steady tone, like a documentary narrator',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['深沉', '稳重', '纪录片', '历史', '叙事'],
    icon: '🏛️',
    color: '#2D3436',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 0.8,
      pitch: -4,
      volume: 0,
    },
  },

  // 场景类风格
  {
    name: '新闻播报',
    nameEn: 'News Broadcast',
    nameKhmer: 'ការ​ផ្សព្វផ្សាយ​ព័ត៌មាន',
    description: '标准新闻播报语调',
    prompt: 'news broadcast tone, like a professional news anchor',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['新闻', '播报', '正式', '信息', '权威'],
    icon: '📺',
    color: '#0984E3',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 0.9,
      pitch: 0,
      volume: 0,
    },
  },
  {
    name: '儿童故事',
    nameEn: 'Children\'s Story',
    nameKhmer: 'រឿង​និទាន​កុមារ',
    description: '适合儿童故事的语调',
    prompt: 'children\'s story tone, like a parent reading bedtime stories',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['儿童', '故事', '睡前', '娱乐', '教育'],
    icon: '📚',
    color: '#FD79A8',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.0,
      pitch: 3,
      volume: 1,
    },
  },
  {
    name: '神秘悬疑',
    nameEn: 'Mysterious & Suspenseful',
    nameKhmer: 'អាថ៌កំបាំង និង​រំភើប',
    description: '神秘悬疑的语调，适合悬疑故事、恐怖内容',
    prompt: 'in a mysterious and suspenseful tone, creating intrigue',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['神秘', '悬疑', '恐怖', '故事', '氛围'],
    icon: '🔮',
    color: '#2D3436',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 0.8,
      pitch: -3,
      volume: -2,
    },
  },
  {
    name: '幽默搞笑',
    nameEn: 'Humorous & Funny',
    nameKhmer: 'កំប្លែង និង​គួរ​ឱ្យ​សើច',
    description: '幽默搞笑的语调，适合喜剧、娱乐内容',
    prompt: 'in a humorous and funny tone, with comedic timing',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['幽默', '搞笑', '喜剧', '娱乐', '轻松'],
    icon: '😄',
    color: '#FDCB6E',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.2,
      pitch: 3,
      volume: 1,
    },
  },
  {
    name: '浪漫温柔',
    nameEn: 'Romantic & Gentle',
    nameKhmer: 'រ៉ូមែនទិក និង​ទន់​ភ្លន់',
    description: '浪漫温柔的语调，适合爱情故事、情感内容',
    prompt: 'in a romantic and gentle tone, with tender emotion',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['浪漫', '温柔', '爱情', '情感', '温馨'],
    icon: '💕',
    color: '#FD79A8',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 0.9,
      pitch: 1,
      volume: -1,
    },
  },

  // 角色类风格
  {
    name: '智能助手',
    nameEn: 'AI Assistant',
    nameKhmer: 'ជំនួយការី AI',
    description: '友好专业的AI助手语调',
    prompt: 'in a friendly and professional AI assistant tone',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['AI', '助手', '科技', '友好', '专业'],
    icon: '🤖',
    color: '#6C5CE7',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.0,
      pitch: 0,
      volume: 0,
    },
  },
  {
    name: '虚拟主播',
    nameEn: 'Virtual Streamer',
    nameKhmer: 'អ្នក​ផ្សព្វផ្សាយ​និម្មិត',
    description: '适合直播的虚拟主播语调',
    prompt: 'virtual streamer tone, energetic and engaging',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['直播', '主播', '娱乐', '互动', '活力'],
    icon: '🎮',
    color: '#FF7675',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.1,
      pitch: 2,
      volume: 1,
    },
  },
  {
    name: '老师讲课',
    nameEn: 'Teacher Lecturing',
    nameKhmer: 'គ្រូ​បង្រៀន',
    description: '耐心细致的老师讲课语调',
    prompt: 'in a patient and detailed teacher tone, explaining clearly',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['教育', '老师', '讲课', '耐心', '清晰'],
    icon: '👩‍🏫',
    color: '#00B894',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 0.9,
      pitch: 0,
      volume: 0,
    },
  },
  {
    name: '医生诊断',
    nameEn: 'Doctor Consultation',
    nameKhmer: 'វេជ្ជបណ្ឌិត​ពិនិត្យ',
    description: '专业关怀的医生诊断语调',
    prompt: 'in a professional and caring doctor tone, with medical authority',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['医疗', '医生', '专业', '关怀', '权威'],
    icon: '👨‍⚕️',
    color: '#0984E3',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 0.95,
      pitch: -1,
      volume: 0,
    },
  },
  {
    name: '客服代表',
    nameEn: 'Customer Service',
    nameKhmer: 'សេវាកម្ម​អតិថិជន',
    description: '礼貌耐心的客服代表语调',
    prompt: 'in a polite and patient customer service tone, helpful and understanding',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['客服', '服务', '礼貌', '耐心', '帮助'],
    icon: '🎧',
    color: '#00CEC9',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.0,
      pitch: 1,
      volume: 0,
    },
  },
  {
    name: '销售顾问',
    nameEn: 'Sales Consultant',
    nameKhmer: 'ទីប្រឹក្សា​លក់',
    description: '热情专业的销售顾问语调',
    prompt: 'in an enthusiastic and professional sales tone, persuasive and confident',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['销售', '顾问', '热情', '专业', '说服'],
    icon: '💼',
    color: '#E17055',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.1,
      pitch: 2,
      volume: 1,
    },
  },
  {
    name: '导游解说',
    nameEn: 'Tour Guide',
    nameKhmer: 'មគ្គុទ្ទេសក៍​ទេសចរណ៍',
    description: '生动有趣的导游解说语调',
    prompt: 'in a lively and informative tour guide tone, engaging and descriptive',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['导游', '旅游', '解说', '生动', '有趣'],
    icon: '🗺️',
    color: '#FDCB6E',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.1,
      pitch: 1,
      volume: 1,
    },
  },
  {
    name: '健身教练',
    nameEn: 'Fitness Trainer',
    nameKhmer: 'គ្រូ​បង្វឹក​កាយសម្ព័ន្ធ',
    description: '充满活力的健身教练语调',
    prompt: 'in an energetic and motivating fitness trainer tone, encouraging and dynamic',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['健身', '教练', '活力', '激励', '运动'],
    icon: '💪',
    color: '#E84393',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.2,
      pitch: 3,
      volume: 2,
    },
  },

  // 场景类风格
  {
    name: '睡前故事',
    nameEn: 'Bedtime Story',
    nameKhmer: 'រឿង​មុន​គេង',
    description: '温柔舒缓的睡前故事语调',
    prompt: 'in a gentle and soothing bedtime story tone, calm and dreamy',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['睡前', '故事', '温柔', '舒缓', '儿童'],
    icon: '🌙',
    color: '#6C5CE7',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 0.8,
      pitch: 0,
      volume: -2,
    },
  },
  {
    name: '冥想引导',
    nameEn: 'Meditation Guide',
    nameKhmer: 'មគ្គុទ្ទេសក៍​សមាធិ',
    description: '平静深沉的冥想引导语调',
    prompt: 'in a peaceful and deep meditation guide tone, serene and mindful',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['冥想', '引导', '平静', '深沉', '正念'],
    icon: '🧘',
    color: '#00B894',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 0.7,
      pitch: -2,
      volume: -3,
    },
  },
  {
    name: '游戏解说',
    nameEn: 'Game Commentary',
    nameKhmer: 'ការ​ពណ៌នា​ហ្គេម',
    description: '激动兴奋的游戏解说语调',
    prompt: 'in an excited and dynamic game commentary tone, thrilling and engaging',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['游戏', '解说', '激动', '兴奋', '娱乐'],
    icon: '🎮',
    color: '#A29BFE',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.3,
      pitch: 4,
      volume: 2,
    },
  },
  {
    name: '烹饪教学',
    nameEn: 'Cooking Tutorial',
    nameKhmer: 'ការ​បង្រៀន​ធ្វើ​ម្ហូប',
    description: '亲切详细的烹饪教学语调',
    prompt: 'in a warm and detailed cooking tutorial tone, instructive and encouraging',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['烹饪', '教学', '亲切', '详细', '指导'],
    icon: '👨‍🍳',
    color: '#E17055',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 0.95,
      pitch: 1,
      volume: 0,
    },
  },
  {
    name: '科技评测',
    nameEn: 'Tech Review',
    nameKhmer: 'ការ​វាយ​តម្លៃ​បច្ចេកវិទ្យា',
    description: '专业客观的科技评测语调',
    prompt: 'in a professional and objective tech review tone, analytical and informative',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['科技', '评测', '专业', '客观', '分析'],
    icon: '📱',
    color: '#636E72',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.0,
      pitch: 0,
      volume: 0,
    },
  },

  // 节奏类风格
  {
    name: '快节奏播报',
    nameEn: 'Fast-Paced Broadcast',
    nameKhmer: 'ការ​ផ្សាយ​លឿន',
    description: '快节奏的播报风格，适合新闻快讯、体育解说',
    prompt: 'in a fast-paced and energetic broadcast tone, quick and dynamic',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['快节奏', '播报', '新闻', '体育', '动态'],
    icon: '⚡',
    color: '#FF6B35',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.4,
      pitch: 2,
      volume: 1,
    },
  },
  {
    name: '慢节奏叙述',
    nameEn: 'Slow-Paced Narration',
    nameKhmer: 'ការ​រៀប​រាប់​យឺត',
    description: '慢节奏的叙述风格，适合纪录片、历史内容',
    prompt: 'in a slow-paced and thoughtful narration tone, deliberate and reflective',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['慢节奏', '叙述', '纪录片', '历史', '深思'],
    icon: '🐌',
    color: '#6C5CE7',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 0.7,
      pitch: -2,
      volume: 0,
    },
  },
  {
    name: '节拍感强',
    nameEn: 'Rhythmic & Beat-driven',
    nameKhmer: 'មាន​ចង្វាក់​ខ្លាំង',
    description: '有节拍感的语调，适合音乐介绍、舞蹈教学',
    prompt: 'with a rhythmic and beat-driven tone, musical and flowing',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['节拍', '音乐', '舞蹈', '韵律', '流畅'],
    icon: '🎵',
    color: '#A29BFE',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.1,
      pitch: 1,
      volume: 1,
    },
  },
  {
    name: '停顿有致',
    nameEn: 'Well-Paced with Pauses',
    nameKhmer: 'មាន​ការ​ឈប់​សម្រាក​ល្អ',
    description: '停顿有致的语调，适合演讲、朗诵',
    prompt: 'with well-timed pauses and deliberate pacing, dramatic and impactful',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['停顿', '演讲', '朗诵', '戏剧', '有力'],
    icon: '⏸️',
    color: '#2D3436',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 0.9,
      pitch: 0,
      volume: 0,
    },
  },

  // 行业类风格
  {
    name: '金融分析',
    nameEn: 'Financial Analysis',
    nameKhmer: 'ការ​វិភាគ​ហិរញ្ញវត្ថុ',
    description: '专业的金融分析语调，适合财经新闻、投资建议',
    prompt: 'in a professional financial analysis tone, authoritative and data-driven',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['金融', '分析', '财经', '投资', '专业'],
    icon: '📈',
    color: '#00B894',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.0,
      pitch: -1,
      volume: 0,
    },
  },
  {
    name: '法律咨询',
    nameEn: 'Legal Consultation',
    nameKhmer: 'ការ​ប្រឹក្សា​ច្បាប់',
    description: '严谨的法律咨询语调，适合法律解释、合同说明',
    prompt: 'in a serious and precise legal consultation tone, formal and authoritative',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['法律', '咨询', '严谨', '正式', '权威'],
    icon: '⚖️',
    color: '#2D3436',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 0.9,
      pitch: -2,
      volume: 0,
    },
  },
  {
    name: '房产介绍',
    nameEn: 'Real Estate Presentation',
    nameKhmer: 'ការ​ណែនាំ​អចលនទ្រព្យ',
    description: '热情的房产介绍语调，适合房产销售、楼盘介绍',
    prompt: 'in an enthusiastic real estate presentation tone, persuasive and descriptive',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['房产', '销售', '热情', '说服', '描述'],
    icon: '🏠',
    color: '#E17055',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.1,
      pitch: 2,
      volume: 1,
    },
  },
  {
    name: '汽车评测',
    nameEn: 'Automotive Review',
    nameKhmer: 'ការ​វាយ​តម្លៃ​រថយន្ត',
    description: '专业的汽车评测语调，适合汽车介绍、试驾体验',
    prompt: 'in a knowledgeable automotive review tone, technical and engaging',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['汽车', '评测', '技术', '专业', '吸引'],
    icon: '🚗',
    color: '#636E72',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.0,
      pitch: 0,
      volume: 0,
    },
  },
  {
    name: '美食介绍',
    nameEn: 'Culinary Introduction',
    nameKhmer: 'ការ​ណែនាំ​អាហារ',
    description: '诱人的美食介绍语调，适合餐厅推荐、菜品介绍',
    prompt: 'in a delicious and appetizing culinary tone, mouth-watering and descriptive',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['美食', '介绍', '诱人', '餐厅', '描述'],
    icon: '🍽️',
    color: '#FDCB6E',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 0.95,
      pitch: 1,
      volume: 0,
    },
  },
  {
    name: '时尚潮流',
    nameEn: 'Fashion & Trend',
    nameKhmer: 'ម៉ូដ និង​និន្នាការ',
    description: '时尚的潮流介绍语调，适合服装介绍、时尚资讯',
    prompt: 'in a stylish and trendy fashion tone, chic and sophisticated',
    type: StyleType.SINGLE_SPEAKER,
    tags: ['时尚', '潮流', '服装', '优雅', '精致'],
    icon: '👗',
    color: '#FD79A8',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    parameters: {
      speed: 1.05,
      pitch: 2,
      volume: 0,
    },
  },

  // 多人对话风格
  {
    name: '客服对话',
    nameEn: 'Customer Service Dialogue',
    description: '标准客服对话场景',
    prompt: 'customer service dialogue with professional agent and friendly customer',
    type: StyleType.MULTI_SPEAKER,
    tags: ['客服', '对话', '商业', '服务', '支持'],
    icon: '🎧',
    color: '#00B894',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    speakerConfig: {
      speakers: [
        {
          name: '客服专员',
          prompt: 'professional and helpful customer service agent',
          gender: 'FEMALE',
          speed: 1.0,
          pitch: 0,
          volume: 0,
        },
        {
          name: '客户',
          prompt: 'friendly customer with questions',
          gender: 'MALE',
          speed: 1.0,
          pitch: 0,
          volume: 0,
        },
      ],
    },
  },
  {
    name: '教学对话',
    nameEn: 'Educational Dialogue',
    description: '教师与学生对话场景',
    prompt: 'educational dialogue between teacher and student',
    type: StyleType.MULTI_SPEAKER,
    tags: ['教学', '教育', '对话', '学习', '知识'],
    icon: '👨‍🏫',
    color: '#0984E3',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    speakerConfig: {
      speakers: [
        {
          name: '教师',
          prompt: 'knowledgeable teacher explaining concepts clearly',
          gender: 'FEMALE',
          speed: 0.9,
          pitch: -1,
          volume: 0,
        },
        {
          name: '学生',
          prompt: 'curious student asking questions',
          gender: 'MALE',
          speed: 1.1,
          pitch: 2,
          volume: 0,
        },
      ],
    },
  },
  {
    name: '医患对话',
    nameEn: 'Doctor-Patient Dialogue',
    nameKhmer: 'ការ​សន្ទនា​រវាង​វេជ្ជបណ្ឌិត​និង​អ្នកជំងឺ',
    description: '医生与患者的专业对话场景',
    prompt: 'medical consultation dialogue between professional doctor and concerned patient',
    type: StyleType.MULTI_SPEAKER,
    tags: ['医疗', '对话', '专业', '关怀', '健康'],
    icon: '🏥',
    color: '#0984E3',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    speakerConfig: {
      speakers: [
        {
          name: '医生',
          prompt: 'professional and caring doctor, explaining medical information clearly',
          gender: 'MALE',
          speed: 0.9,
          pitch: -1,
          volume: 0,
        },
        {
          name: '患者',
          prompt: 'concerned patient asking questions about health',
          gender: 'FEMALE',
          speed: 1.0,
          pitch: 1,
          volume: 0,
        },
      ],
    },
  },
  {
    name: '面试对话',
    nameEn: 'Job Interview',
    nameKhmer: 'ការ​សម្ភាសន៍​ការងារ',
    description: '求职面试的正式对话场景',
    prompt: 'professional job interview dialogue between interviewer and candidate',
    type: StyleType.MULTI_SPEAKER,
    tags: ['面试', '工作', '正式', '专业', '求职'],
    icon: '💼',
    color: '#2D3436',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    speakerConfig: {
      speakers: [
        {
          name: '面试官',
          prompt: 'professional interviewer asking thoughtful questions',
          gender: 'FEMALE',
          speed: 1.0,
          pitch: 0,
          volume: 0,
        },
        {
          name: '求职者',
          prompt: 'confident job candidate answering professionally',
          gender: 'MALE',
          speed: 0.95,
          pitch: 0,
          volume: 0,
        },
      ],
    },
  },
  {
    name: '朋友聊天',
    nameEn: 'Friends Chatting',
    nameKhmer: 'ការ​ជជែក​រវាង​មិត្តភក្តិ',
    description: '朋友之间的轻松聊天场景',
    prompt: 'casual and friendly conversation between close friends',
    type: StyleType.MULTI_SPEAKER,
    tags: ['朋友', '聊天', '轻松', '友好', '日常'],
    icon: '👫',
    color: '#FDCB6E',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    speakerConfig: {
      speakers: [
        {
          name: '朋友A',
          prompt: 'cheerful and talkative friend sharing stories',
          gender: 'FEMALE',
          speed: 1.1,
          pitch: 2,
          volume: 1,
        },
        {
          name: '朋友B',
          prompt: 'relaxed and humorous friend responding casually',
          gender: 'MALE',
          speed: 1.0,
          pitch: 0,
          volume: 0,
        },
      ],
    },
  },
  {
    name: '销售对话',
    nameEn: 'Sales Conversation',
    nameKhmer: 'ការ​សន្ទនា​លក់ដូរ',
    description: '销售人员与客户的商务对话',
    prompt: 'professional sales conversation between salesperson and potential customer',
    type: StyleType.MULTI_SPEAKER,
    tags: ['销售', '商务', '专业', '说服', '客户'],
    icon: '🤝',
    color: '#E17055',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    speakerConfig: {
      speakers: [
        {
          name: '销售员',
          prompt: 'enthusiastic and professional salesperson presenting products',
          gender: 'MALE',
          speed: 1.1,
          pitch: 1,
          volume: 1,
        },
        {
          name: '客户',
          prompt: 'interested customer asking questions and considering options',
          gender: 'FEMALE',
          speed: 1.0,
          pitch: 0,
          volume: 0,
        },
      ],
    },
  },
  {
    name: '播客对话',
    nameEn: 'Podcast Conversation',
    nameKhmer: 'ការ​សន្ទនា​ផតខាស់',
    description: '播客主持人与嘉宾的访谈对话',
    prompt: 'engaging podcast conversation between host and guest',
    type: StyleType.MULTI_SPEAKER,
    tags: ['播客', '访谈', '媒体', '专业', '娱乐'],
    icon: '🎙️',
    color: '#A29BFE',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    speakerConfig: {
      speakers: [
        {
          name: '主持人',
          prompt: 'professional podcast host asking engaging questions',
          gender: 'FEMALE',
          speed: 1.0,
          pitch: 1,
          volume: 0,
        },
        {
          name: '嘉宾',
          prompt: 'knowledgeable guest sharing insights and experiences',
          gender: 'MALE',
          speed: 0.95,
          pitch: 0,
          volume: 0,
        },
      ],
    },
  },
  {
    name: '家庭对话',
    nameEn: 'Family Conversation',
    nameKhmer: 'ការ​សន្ទនា​គ្រួសារ',
    description: '家庭成员之间的温馨对话',
    prompt: 'warm and loving family conversation between family members',
    type: StyleType.MULTI_SPEAKER,
    tags: ['家庭', '温馨', '亲情', '日常', '关爱'],
    icon: '👨‍👩‍👧‍👦',
    color: '#FD79A8',
    categoryId: null,
    isPublic: true,
    isOfficial: true,
    speakerConfig: {
      speakers: [
        {
          name: '父母',
          prompt: 'caring parent speaking with love and guidance',
          gender: 'FEMALE',
          speed: 0.9,
          pitch: 0,
          volume: 0,
        },
        {
          name: '孩子',
          prompt: 'excited child sharing daily experiences',
          gender: 'MALE',
          speed: 1.2,
          pitch: 3,
          volume: 1,
        },
      ],
    },
  },
];

async function seedSystemStyles() {
  console.log('🎨 开始创建系统官方风格种子数据...');

  // 创建分离的分类系统
  console.log('📂 创建分离的分类系统...');
  const categories = await createSeparatedStyleCategories();
  console.log('✅ 分类系统创建完成\n');

  // 为风格分配分类ID
  SYSTEM_STYLES.forEach((style) => {
    const name = style.name;
    const tags = style.tags;
    const type = style.type;

    if (type === 'SINGLE_SPEAKER') {
      // 单人风格分类
      if (tags.includes('温暖') || tags.includes('激情') || tags.includes('冷静') || tags.includes('神秘') || tags.includes('幽默') || tags.includes('浪漫') ||
          name.includes('温暖') || name.includes('激情') || name.includes('神秘') || name.includes('幽默') || name.includes('浪漫')) {
        style.categoryId = categories.singleSpeaker.emotion?.id ?? null; // 情感表达
      }
      else if (tags.includes('清晰') || tags.includes('深沉') || tags.includes('轻柔') || tags.includes('专业') || tags.includes('权威') ||
               name.includes('清晰') || name.includes('深沉') || name.includes('轻柔') || name.includes('专业') || name.includes('权威')) {
        style.categoryId = categories.singleSpeaker.tone?.id ?? null; // 语调风格
      }
      else if (tags.includes('AI') || tags.includes('助手') || tags.includes('主播') || tags.includes('老师') || tags.includes('医生') ||
               tags.includes('客服') || tags.includes('销售') || tags.includes('导游') || tags.includes('教练') ||
               name.includes('助手') || name.includes('主播') || name.includes('老师') || name.includes('医生') ||
               name.includes('客服') || name.includes('销售') || name.includes('导游') || name.includes('教练')) {
        style.categoryId = categories.singleSpeaker.role?.id ?? null; // 专业角色
      }
      else if (tags.includes('快节奏') || tags.includes('慢节奏') || tags.includes('节拍') || tags.includes('停顿') ||
               name.includes('快节奏') || name.includes('慢节奏') || name.includes('节拍') || name.includes('停顿')) {
        style.categoryId = categories.singleSpeaker.rhythm?.id ?? null; // 节奏控制
      }
      else if (tags.includes('金融') || tags.includes('法律') || tags.includes('房产') || tags.includes('汽车') || tags.includes('美食') || tags.includes('时尚') ||
               name.includes('金融') || name.includes('法律') || name.includes('房产') || name.includes('汽车') || name.includes('美食') || name.includes('时尚')) {
        style.categoryId = categories.singleSpeaker.industry?.id ?? null; // 行业专用
      }
      else {
        style.categoryId = categories.singleSpeaker.scenario?.id ?? null; // 应用场景
      }
    } else if (type === 'MULTI_SPEAKER') {
      // 多人对话风格分类
      if (tags.includes('客服') || tags.includes('服务') || tags.includes('支持') || tags.includes('咨询') ||
          name.includes('客服') || name.includes('服务') || name.includes('支持')) {
        style.categoryId = categories.multiSpeaker.service?.id ?? null; // 服务咨询
      }
      else if (tags.includes('教学') || tags.includes('教育') || tags.includes('学习') || tags.includes('知识') ||
               name.includes('教学') || name.includes('教育') || name.includes('学习')) {
        style.categoryId = categories.multiSpeaker.education?.id ?? null; // 教育互动
      }
      else if (tags.includes('销售') || tags.includes('商务') || tags.includes('面试') || tags.includes('工作') ||
               name.includes('销售') || name.includes('商务') || name.includes('面试')) {
        style.categoryId = categories.multiSpeaker.business?.id ?? null; // 商务对话
      }
      else if (tags.includes('朋友') || tags.includes('家庭') || tags.includes('聊天') || tags.includes('日常') ||
               name.includes('朋友') || name.includes('家庭') || name.includes('聊天')) {
        style.categoryId = categories.multiSpeaker.social?.id ?? null; // 社交聊天
      }
      else if (tags.includes('播客') || tags.includes('访谈') || tags.includes('媒体') ||
               name.includes('播客') || name.includes('访谈') || name.includes('媒体')) {
        style.categoryId = categories.multiSpeaker.media?.id ?? null; // 媒体访谈
      }
      else if (tags.includes('医疗') || tags.includes('法律') || tags.includes('专业') ||
               name.includes('医患') || name.includes('法律') || name.includes('专业')) {
        style.categoryId = categories.multiSpeaker.professional?.id ?? null; // 专业咨询
      }
      else {
        style.categoryId = categories.multiSpeaker.social?.id ?? null; // 默认为社交聊天
      }
    }
  });

  // 获取超级管理员用户ID
  const superAdmin = await prisma.user.findFirst({
    where: { role: 'SUPER_ADMIN' },
  });

  if (!superAdmin) {
    throw new Error('未找到超级管理员用户，请先运行用户种子数据');
  }

  let createdCount = 0;

  for (const [index, style] of SYSTEM_STYLES.entries()) {
    await prisma.userStyle.upsert({
      where: { id: `system-style-${String(index + 1).padStart(2, '0')}` },
      update: {
        ...style,
        parameters: style.parameters ? JSON.stringify(style.parameters) : undefined,
        speakerConfig: style.speakerConfig ? JSON.stringify(style.speakerConfig) : undefined,
        updatedAt: new Date(),
      },
      create: {
        id: `system-style-${String(index + 1).padStart(2, '0')}`,
        userId: superAdmin.id,
        ...style,
        parameters: style.parameters ? JSON.stringify(style.parameters) : undefined,
        speakerConfig: style.speakerConfig ? JSON.stringify(style.speakerConfig) : undefined,
        source: 'OFFICIAL',
      },
    });

    console.log(`✅ 创建系统风格: ${style.name} (${style.nameEn})`);
    createdCount++;
  }

  console.log(`🎉 成功创建 ${createdCount} 个系统官方风格！`);
}

async function main() {
  await seedSystemStyles();
}

// 检查是否作为主模块运行
const isMainModule = process.argv[1] && process.argv[1].endsWith(import.meta.url.split('/').pop() || '');

if (isMainModule) {
  main()
    .catch((e) => {
      console.error('❌ 系统官方风格种子数据创建失败:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { seedSystemStyles, SYSTEM_STYLES };