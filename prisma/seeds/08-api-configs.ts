import { PrismaClient, ApiProvider, ApiStatus } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * API配置模块种子数据
 * 创建API提供商配置和定价配置
 */

// API提供商配置
const API_PROVIDER_CONFIGS = [
  {
    provider: ApiProvider.GEMINI,
    displayName: 'Google Gemini',
    description: 'Google Gemini API - 高质量多语言语音合成服务',
    status: ApiStatus.ACTIVE,
    priority: 1,
    isEnabled: true,
    config: {
      apiKey: process.env.GEMINI_API_KEY || '',
      baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
      model: 'gemini-pro',
      voiceModel: 'text-to-speech',
      maxTokens: 1000000,
      rateLimit: {
        requestsPerMinute: 60,
        requestsPerDay: 1000
      }
    },
    dailyLimit: 1000,
    monthlyLimit: 30000,
    costLimit: 100.0,
    totalRequests: 0,
    successRequests: 0,
    failedRequests: 0,
    totalCost: 0.0
  },
  {
    provider: ApiProvider.OPENAI,
    displayName: 'OpenAI TTS',
    description: 'OpenAI Text-to-Speech API - 备用语音合成服务',
    status: ApiStatus.INACTIVE,
    priority: 2,
    isEnabled: false,
    config: {
      apiKey: process.env.OPENAI_API_KEY || '',
      baseUrl: 'https://api.openai.com/v1',
      model: 'tts-1',
      voice: 'alloy',
      maxTokens: 4096,
      rateLimit: {
        requestsPerMinute: 50,
        requestsPerDay: 500
      }
    },
    dailyLimit: 500,
    monthlyLimit: 15000,
    costLimit: 50.0,
    totalRequests: 0,
    successRequests: 0,
    failedRequests: 0,
    totalCost: 0.0
  }
];

// API定价配置已移至 05-api-pricing-config.ts

async function seedApiProviderConfigs() {
  console.log('🔧 开始创建API提供商配置...');

  for (const config of API_PROVIDER_CONFIGS) {
    await prisma.apiProviderConfig.upsert({
      where: { provider: config.provider },
      update: {
        ...config,
        config: JSON.stringify(config.config),
        updatedAt: new Date()
      },
      create: {
        ...config,
        config: JSON.stringify(config.config)
      },
    });
    
    console.log(`✅ 创建API配置: ${config.displayName} (${config.provider})`);
  }

  console.log(`🎉 成功创建 ${API_PROVIDER_CONFIGS.length} 个API提供商配置！`);
}

// API定价配置函数已移至 05-api-pricing-config.ts

async function seedApiConfigs() {
  await seedApiProviderConfigs();
  // API定价配置现在在 05-api-pricing-config.ts 中处理
}

async function main() {
  await seedApiConfigs();
}

// 检查是否作为主模块运行
const isMainModule = process.argv[1] && process.argv[1].endsWith(import.meta.url.split('/').pop() || '');

if (isMainModule) {
  main()
    .catch((e) => {
      console.error('❌ API配置种子数据创建失败:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { seedApiConfigs, seedApiProviderConfigs };