import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 风格分类模块种子数据
 * 创建系统预设的风格分类
 */

const STYLE_CATEGORIES = [
  {
    name: '情感',
    nameEn: 'Emotion',
    nameKhmer: 'អារម្មណ៍',
    description: '基于情感表达的语音风格',
    icon: '❤️',
    sortOrder: 1,
    isActive: true,
  },
  {
    name: '语调',
    nameEn: 'Tone',
    nameKhmer: 'សំឡេង',
    description: '不同语调的语音风格',
    icon: '🎤',
    sortOrder: 2,
    isActive: true,
  },
  {
    name: '节奏',
    nameEn: 'Rhythm',
    nameKhmer: 'ចង្វាក់',
    description: '不同节奏的语音风格',
    icon: '🎵',
    sortOrder: 3,
    isActive: true,
  },
  {
    name: '场景',
    nameEn: 'Scenario',
    nameKhmer: 'អាកប្បកិរិយា',
    description: '适用于特定场景的语音风格',
    icon: '🎬',
    sortOrder: 4,
    isActive: true,
  },
  {
    name: '角色',
    nameEn: 'Character',
    nameKhmer: 'តួនាទី',
    description: '模拟特定角色的语音风格',
    icon: '🎭',
    sortOrder: 5,
    isActive: true,
  },
  {
    name: '行业',
    nameEn: 'Industry',
    nameKhmer: 'ឧស្សាហកម្ម',
    description: '适用于特定行业的语音风格',
    icon: '🏢',
    sortOrder: 6,
    isActive: true,
  },
];

async function seedStyleCategories() {
  console.log('🏷️  开始创建风格分类种子数据...');

  for (const [index, category] of STYLE_CATEGORIES.entries()) {
    await prisma.styleCategory.upsert({
      where: { name: category.name },
      update: category,
      create: {
        ...category,
        sortOrder: index + 1,
      },
    });
    
    console.log(`✅ 创建风格分类: ${category.name} (${category.nameEn})`);
  }

  console.log(`🎉 成功创建 ${STYLE_CATEGORIES.length} 个风格分类！`);
}

async function main() {
  await seedStyleCategories();
}

// 检查是否作为主模块运行
const isMainModule = process.argv[1] && process.argv[1].endsWith(import.meta.url.split('/').pop() || '');

if (isMainModule) {
  main()
    .catch((e) => {
      console.error('❌ 风格分类种子数据创建失败:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { seedStyleCategories, STYLE_CATEGORIES };