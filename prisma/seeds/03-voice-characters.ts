import { PrismaClient, ApiProvider, Gender } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 语音角色模块种子数据
 * 创建30个Google Gemini官方语音角色
 */

// Google Gemini API的30个预设语音角色
const GEMINI_VOICE_CHARACTERS = [
  // 女性角色 (15个)
  {
    characterName: 'Zephyr',
    characterNameEn: 'Zephyr',
    originalName: 'Zephyr',
    apiVoiceName: 'Zephyr',
    gender: Gender.FEMALE,
    description: '明亮清新的女性声音，充满活力和正能量',
    style: '明亮',
    personality: JSON.stringify(['明亮', '活力', '清新', '积极']),
    bestFor: JSON.stringify(['广告配音', '教育内容', '儿童节目', '产品介绍'])
  },
  {
    characterName: 'Leda',
    characterNameEn: 'Leda', 
    originalName: 'Leda',
    apiVoiceName: 'Leda',
    gender: Gender.FEMALE,
    description: '年轻甜美的女性声音，青春活力，适合年轻群体',
    style: '青春',
    personality: JSON.stringify(['青春', '甜美', '活力', '亲切']),
    bestFor: JSON.stringify(['青年内容', '时尚广告', '校园节目', '青春剧'])
  },
  {
    characterName: 'Aoede',
    characterNameEn: 'Aoede',
    originalName: 'Aoede',
    apiVoiceName: 'Aoede',
    gender: Gender.FEMALE,
    description: '轻松自然的女性声音，如微风般舒适',
    style: 'Breezy',
    personality: JSON.stringify(['轻松', '自然', '舒适', '平和']),
    bestFor: JSON.stringify(['冥想引导', '放松音频', '生活方式', '瑜伽指导'])
  },
  {
    characterName: 'Callirrhoe',
    characterNameEn: 'Callirrhoe',
    originalName: 'Callirrhoe',
    apiVoiceName: 'Callirrhoe',
    gender: Gender.FEMALE,
    description: '轻松随意的女性声音，平易近人',
    style: '轻松',
    personality: JSON.stringify(['轻松', '随意', '亲切', '平易近人']),
    bestFor: JSON.stringify(['聊天机器人', '社交应用', '日常对话', '非正式场合'])
  },
  {
    characterName: 'Achird',
    characterNameEn: 'Achird',
    originalName: 'Achird',
    apiVoiceName: 'Achird',
    gender: Gender.FEMALE,
    description: '友好亲切的女性声音，温暖可靠',
    style: '友好',
    personality: JSON.stringify(['友好', '亲切', '温暖', '可靠']),
    bestFor: JSON.stringify(['客服录音', '友好品牌', '温馨内容', '社区活动'])
  },
  {
    characterName: 'Despina',
    characterNameEn: 'Despina',
    originalName: 'Despina',
    apiVoiceName: 'Despina',
    gender: Gender.FEMALE,
    description: '平滑优雅的女性声音，流畅动听',
    style: '平滑',
    personality: JSON.stringify(['平滑', '优雅', '流畅', '动听']),
    bestFor: JSON.stringify(['有声书', '优雅品牌', '艺术内容', '高端服务'])
  },
  {
    characterName: 'Erinome',
    characterNameEn: 'Erinome',
    originalName: 'Erinome',
    apiVoiceName: 'Erinome',
    gender: Gender.FEMALE,
    description: '清晰准确的女性声音，条理分明',
    style: '清除',
    personality: JSON.stringify(['清晰', '准确', '条理', '专业']),
    bestFor: JSON.stringify(['教育内容', '指导说明', '技术讲解', '培训材料'])
  },
  {
    characterName: 'Laomedeia',
    characterNameEn: 'Laomedeia',
    originalName: 'Laomedeia',
    apiVoiceName: 'Laomedeia',
    gender: Gender.FEMALE,
    description: '欢快愉悦的女性声音，充满正能量',
    style: '欢快',
    personality: JSON.stringify(['欢快', '愉悦', '积极', '有趣']),
    bestFor: JSON.stringify(['娱乐节目', '游戏配音', '儿童内容', '活动主持'])
  },
  {
    characterName: 'Autonoe',
    characterNameEn: 'Autonoe',
    originalName: 'Autonoe',
    apiVoiceName: 'Autonoe',
    gender: Gender.FEMALE,
    description: '明亮清晰的女性声音，充满活力',
    style: '明亮',
    personality: JSON.stringify(['明亮', '清晰', '活力', '专业']),
    bestFor: JSON.stringify(['商务演示', '企业培训', '正式场合', '新闻播报'])
  },
  {
    characterName: 'Pulcherrima',
    characterNameEn: 'Pulcherrima',
    originalName: 'Pulcherrima',
    apiVoiceName: 'Pulcherrima',
    gender: Gender.FEMALE,
    description: '直率坦诚的女性声音，真实自然',
    style: '直率',
    personality: JSON.stringify(['直率', '坦诚', '真实', '自然']),
    bestFor: JSON.stringify(['真实故事', '诚实品牌', '纪实内容', '访谈节目'])
  },
  {
    characterName: 'Sulafat',
    characterNameEn: 'Sulafat',
    originalName: 'Sulafat',
    apiVoiceName: 'Sulafat',
    gender: Gender.FEMALE,
    description: '温暖亲切的女性声音，充满温情',
    style: '暖色',
    personality: JSON.stringify(['温暖', '亲切', '温情', '舒适']),
    bestFor: JSON.stringify(['温馨广告', '家庭内容', '情感故事', '关怀服务'])
  },
  {
    characterName: 'Vindemiatrix',
    characterNameEn: 'Vindemiatrix',
    originalName: 'Vindemiatrix',
    apiVoiceName: 'Vindemiatrix',
    gender: Gender.FEMALE,
    description: '温和柔软的女性声音，平静舒缓',
    style: '温和',
    personality: JSON.stringify(['温和', '柔软', '平静', '舒缓']),
    bestFor: JSON.stringify(['冥想引导', '心理咨询', '舒缓内容', '治疗音频'])
  },
  {
    characterName: 'Schedar',
    characterNameEn: 'Schedar',
    originalName: 'Schedar',
    apiVoiceName: 'Schedar',
    gender: Gender.FEMALE,
    description: '均匀稳定的女性声音，平衡可靠',
    style: '均匀',
    personality: JSON.stringify(['均匀', '稳定', '平衡', '可靠']),
    bestFor: JSON.stringify(['新闻播报', '正式公告', '稳重品牌', '官方声明'])
  },
  {
    characterName: 'Sadachbia',
    characterNameEn: 'Sadachbia',
    originalName: 'Sadachbia',
    apiVoiceName: 'Sadachbia',
    gender: Gender.FEMALE,
    description: '活泼生动的女性声音，充满活力',
    style: '活泼',
    personality: JSON.stringify(['活泼', '生动', '活力', '有趣']),
    bestFor: JSON.stringify(['儿童节目', '活泼广告', '游戏配音', '娱乐内容'])
  },
  {
    characterName: 'Enceladus',
    characterNameEn: 'Enceladus',
    originalName: 'Enceladus',
    apiVoiceName: 'Enceladus',
    gender: Gender.FEMALE,
    description: '气声质感的女性声音，轻柔魅力',
    style: '气声',
    personality: JSON.stringify(['气声', '轻柔', '魅力', '独特']),
    bestFor: JSON.stringify(['情感表达', '个性品牌', '艺术作品', '特色内容'])
  },

  // 男性角色 (15个)
  {
    characterName: 'Puck',
    characterNameEn: 'Puck',
    originalName: 'Puck',
    apiVoiceName: 'Puck',
    gender: Gender.MALE,
    description: '欢快活泼的男性声音，充满正能量和活力',
    style: '欢快',
    personality: JSON.stringify(['欢快', '活泼', '积极', '热情']),
    bestFor: JSON.stringify(['娱乐节目', '儿童内容', '轻松广告', '游戏配音'])
  },
  {
    characterName: 'Charon',
    characterNameEn: 'Charon',
    originalName: 'Charon',
    apiVoiceName: 'Charon',
    gender: Gender.MALE,
    description: '信息丰富的男性声音，知识渊博，适合教育内容',
    style: '信息丰富',
    personality: JSON.stringify(['博学', '专业', '可靠', '权威']),
    bestFor: JSON.stringify(['教育内容', '新闻播报', '科普节目', '学术讲座'])
  },
  {
    characterName: 'Kore',
    characterNameEn: 'Kore',
    originalName: 'Kore',
    apiVoiceName: 'Kore',
    gender: Gender.MALE,
    description: '坚定有力的男性声音，充满决心和毅力',
    style: '坚定',
    personality: JSON.stringify(['坚定', '有力', '决心', '毅力']),
    bestFor: JSON.stringify(['激励演讲', '领导力内容', '企业培训', '正式宣告'])
  },
  {
    characterName: 'Fenrir',
    characterNameEn: 'Fenrir',
    originalName: 'Fenrir',
    apiVoiceName: 'Fenrir',
    gender: Gender.MALE,
    description: '兴奋激动的男性声音，充满热情和活力',
    style: 'Excitable',
    personality: JSON.stringify(['兴奋', '激动', '热情', '富有感染力']),
    bestFor: JSON.stringify(['体育解说', '激情广告', '活动宣传', '动感内容'])
  },
  {
    characterName: 'Orus',
    characterNameEn: 'Orus',
    originalName: 'Orus',
    apiVoiceName: 'Orus',
    gender: Gender.MALE,
    description: '企业级专业男性声音，权威可靠',
    style: '公司',
    personality: JSON.stringify(['专业', '企业', '权威', '可靠']),
    bestFor: JSON.stringify(['企业介绍', '商务演示', '公司培训', '正式场合'])
  },
  {
    characterName: 'Iapetus',
    characterNameEn: 'Iapetus',
    originalName: 'Iapetus',
    apiVoiceName: 'Iapetus',
    gender: Gender.MALE,
    description: '清晰明了的男性声音，表达简洁准确',
    style: '清晰',
    personality: JSON.stringify(['清晰', '明了', '简洁', '准确']),
    bestFor: JSON.stringify(['指导说明', '技术解释', '操作教程', '规程宣读'])
  },
  {
    characterName: 'Umbriel',
    characterNameEn: 'Umbriel',
    originalName: 'Umbriel',
    apiVoiceName: 'Umbriel',
    gender: Gender.MALE,
    description: '随和亲切的男性声音，平易近人',
    style: '随和',
    personality: JSON.stringify(['随和', '亲切', '平易近人', '友好']),
    bestFor: JSON.stringify(['日常对话', '友好服务', '生活内容', '轻松场合'])
  },
  {
    characterName: 'Algieba',
    characterNameEn: 'Algieba',
    originalName: 'Algieba',
    apiVoiceName: 'Algieba',
    gender: Gender.MALE,
    description: '平滑流畅的男性声音，优雅动听',
    style: '平滑',
    personality: JSON.stringify(['平滑', '流畅', '优雅', '动听']),
    bestFor: JSON.stringify(['有声书', '诗歌朗诵', '艺术内容', '高雅品牌'])
  },
  {
    characterName: 'Algenib',
    characterNameEn: 'Algenib',
    originalName: 'Algenib',
    apiVoiceName: 'Algenib',
    gender: Gender.MALE,
    description: '沙哑质感的男性声音，富有个性',
    style: 'Gravelly',
    personality: JSON.stringify(['沙哑', '质感', '个性', '独特']),
    bestFor: JSON.stringify(['个性品牌', '特色广告', '角色配音', '艺术表达'])
  },
  {
    characterName: 'Rasalgethi',
    characterNameEn: 'Rasalgethi',
    originalName: 'Rasalgethi',
    apiVoiceName: 'Rasalgethi',
    gender: Gender.MALE,
    description: '信息丰富的智慧男性声音，博学专业',
    style: '信息丰富',
    personality: JSON.stringify(['智慧', '博学', '专业', '深度']),
    bestFor: JSON.stringify(['学术讲座', '知识分享', '专业培训', '深度分析'])
  },
  {
    characterName: 'Achernar',
    characterNameEn: 'Achernar',
    originalName: 'Achernar',
    apiVoiceName: 'Achernar',
    gender: Gender.MALE,
    description: '柔和温暖的男性声音，亲切舒缓',
    style: '软',
    personality: JSON.stringify(['柔和', '温暖', '亲切', '舒缓']),
    bestFor: JSON.stringify(['温馨内容', '治疗音频', '放松引导', '温暖品牌'])
  },
  {
    characterName: 'Alnilam',
    characterNameEn: 'Alnilam',
    originalName: 'Alnilam',
    apiVoiceName: 'Alnilam',
    gender: Gender.MALE,
    description: '坚定有力的男性声音，态度明确',
    style: 'Firm',
    personality: JSON.stringify(['坚定', '有力', '明确', '果断']),
    bestFor: JSON.stringify(['决策宣布', '权威声明', '领导讲话', '正式通告'])
  },
  {
    characterName: 'Gacrux',
    characterNameEn: 'Gacrux',
    originalName: 'Gacrux',
    apiVoiceName: 'Gacrux',
    gender: Gender.MALE,
    description: '成熟稳重的成人男性声音，经验丰富',
    style: '成人',
    personality: JSON.stringify(['成熟', '稳重', '经验丰富', '可靠']),
    bestFor: JSON.stringify(['成人内容', '专业咨询', '商务场合', '权威解读'])
  },
  {
    characterName: 'Zubenelgenubi',
    characterNameEn: 'Zubenelgenubi',
    originalName: 'Zubenelgenubi',
    apiVoiceName: 'Zubenelgenubi',
    gender: Gender.MALE,
    description: '随意轻松的男性声音，平民化亲切',
    style: '随意',
    personality: JSON.stringify(['随意', '轻松', '平民化', '亲切']),
    bestFor: JSON.stringify(['日常交流', '轻松内容', '生活分享', '非正式场合'])
  },
  {
    characterName: 'Sadaltager',
    characterNameEn: 'Sadaltager',
    originalName: 'Sadaltager',
    apiVoiceName: 'Sadaltager',
    gender: Gender.MALE,
    description: '知识渊博的男性声音，智慧专业',
    style: '知识渊博',
    personality: JSON.stringify(['知识渊博', '智慧', '专业', '博学']),
    bestFor: JSON.stringify(['学术研究', '专业讲解', '知识传播', '教育培训'])
  }
];

async function seedVoiceCharacters() {
  console.log('🎭 开始创建语音角色种子数据...');

  // 首先创建 API 模板
  console.log('📝 创建 API 角色模板...');

  for (const [index, character] of GEMINI_VOICE_CHARACTERS.entries()) {
    const templateId = `template-${index + 1}`;

    // 创建 API 模板
    await prisma.apiCharacterTemplate.upsert({
      where: { id: templateId },
      update: {
        apiProvider: ApiProvider.GEMINI,
        apiVoiceName: character.apiVoiceName,
        originalName: character.originalName,
        gender: character.gender,
        defaultStyle: character.style,
        defaultDescription: character.description,
        isActive: true,
        sortOrder: index,
      },
      create: {
        id: templateId,
        apiProvider: ApiProvider.GEMINI,
        apiVoiceName: character.apiVoiceName,
        originalName: character.originalName,
        gender: character.gender,
        defaultStyle: character.style,
        defaultDescription: character.description,
        isActive: true,
        sortOrder: index,
      },
    });

    // 为每种语言创建语言角色
    const languages = await prisma.language.findMany({
      where: { isActive: true },
    });

    for (const language of languages) {
      const languageCharacterId = `lang-char-${language.code}-${index + 1}`;

      await prisma.languageCharacter.upsert({
        where: { id: languageCharacterId },
        update: {
          name: character.characterName,
          description: character.description,
          style: character.style,
          personality: character.personality,
          bestFor: character.bestFor,
          avatarUrl: '',
          isActive: true,
          sortOrder: index,
          isCustom: false,
        },
        create: {
          id: languageCharacterId,
          languageId: language.id,
          templateId: templateId,
          name: character.characterName,
          description: character.description,
          style: character.style,
          personality: character.personality,
          bestFor: character.bestFor,
          avatarUrl: '',
          isActive: true,
          sortOrder: index,
          isCustom: false,
        },
      });
    }

    console.log(`✅ 创建角色模板和语言角色: ${character.characterName}`);
  }

  console.log('🎉 语音角色种子数据创建完成！');
}

export { seedVoiceCharacters };