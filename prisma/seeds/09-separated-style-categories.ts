/**
 * 分离的风格分类种子数据
 * 为单人风格和多人对话风格创建不同的分类系统
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// 单人风格分类
const SINGLE_SPEAKER_CATEGORIES = [
  {
    name: '情感表达',
    nameEn: 'Emotional Expression',
    nameKhmer: 'ការ​បង្ហាញ​អារម្មណ៍',
    description: '控制语音的情感色彩和表达方式',
    icon: '💝',
    sortOrder: 1,
  },
  {
    name: '语调风格',
    nameEn: 'Tone Style',
    nameKhmer: 'រចនាប័ទ្ម​សំឡេង',
    description: '调节语音的音调特性和说话方式',
    icon: '🎵',
    sortOrder: 2,
  },
  {
    name: '专业角色',
    nameEn: 'Professional Roles',
    nameKhmer: 'តួនាទី​វិជ្ជាជីវៈ',
    description: '模拟不同职业和专业角色的语音风格',
    icon: '👔',
    sortOrder: 3,
  },
  {
    name: '应用场景',
    nameEn: 'Application Scenarios',
    nameKhmer: 'ស្ថានភាព​ប្រើប្រាស់',
    description: '适用于特定使用场景的语音风格',
    icon: '🎬',
    sortOrder: 4,
  },
  {
    name: '节奏控制',
    nameEn: 'Rhythm Control',
    nameKhmer: 'ការ​គ្រប់គ្រង​ចង្វាក់',
    description: '控制语音的节奏和语速特性',
    icon: '⏱️',
    sortOrder: 5,
  },
  {
    name: '行业专用',
    nameEn: 'Industry Specific',
    nameKhmer: 'ឧស្សាហកម្ម​ជាក់លាក់',
    description: '针对特定行业和领域的专业语音风格',
    icon: '🏢',
    sortOrder: 6,
  },
];

// 多人对话分类
const MULTI_SPEAKER_CATEGORIES = [
  {
    name: '商务对话',
    nameEn: 'Business Dialogue',
    nameKhmer: 'ការ​សន្ទនា​ពាណិជ្ជកម្ម',
    description: '商务场景下的专业对话风格',
    icon: '💼',
    sortOrder: 1,
  },
  {
    name: '教育互动',
    nameEn: 'Educational Interaction',
    nameKhmer: 'អន្តរកម្ម​អប់រំ',
    description: '教学和学习场景的对话风格',
    icon: '📚',
    sortOrder: 2,
  },
  {
    name: '服务咨询',
    nameEn: 'Service Consultation',
    nameKhmer: 'ការ​ប្រឹក្សា​សេវាកម្ម',
    description: '客服、咨询等服务场景的对话',
    icon: '🎧',
    sortOrder: 3,
  },
  {
    name: '社交聊天',
    nameEn: 'Social Chat',
    nameKhmer: 'ការ​ជជែក​សង្គម',
    description: '朋友、家人等社交场景的对话',
    icon: '👥',
    sortOrder: 4,
  },
  {
    name: '媒体访谈',
    nameEn: 'Media Interview',
    nameKhmer: 'ការ​សម្ភាសន៍​ប្រព័ន្ធ​ផ្សព្វផ្សាយ',
    description: '播客、访谈等媒体场景的对话',
    icon: '🎙️',
    sortOrder: 5,
  },
  {
    name: '专业咨询',
    nameEn: 'Professional Consultation',
    nameKhmer: 'ការ​ប្រឹក្សា​វិជ្ជាជីវៈ',
    description: '医疗、法律等专业咨询对话',
    icon: '🏥',
    sortOrder: 6,
  },
];

async function createSeparatedStyleCategories() {
  console.log('🗂️ 开始创建分离的风格分类...\n');

  try {
    // 删除现有的分类（如果需要重新创建）
    console.log('🗑️ 清理现有分类...');
    await prisma.styleCategory.deleteMany({});

    // 创建单人风格分类
    console.log('👤 创建单人风格分类...');
    const singleSpeakerCategories = [];
    for (const category of SINGLE_SPEAKER_CATEGORIES) {
      const created = await prisma.styleCategory.create({
        data: {
          ...category,
          isActive: true,
        },
      });
      singleSpeakerCategories.push(created);
      console.log(`   ✅ ${category.icon} ${category.name}`);
    }

    // 创建多人对话分类
    console.log('\n👥 创建多人对话分类...');
    const multiSpeakerCategories = [];
    for (const category of MULTI_SPEAKER_CATEGORIES) {
      const created = await prisma.styleCategory.create({
        data: {
          ...category,
          isActive: true,
        },
      });
      multiSpeakerCategories.push(created);
      console.log(`   ✅ ${category.icon} ${category.name}`);
    }

    console.log(`\n🎉 分类创建完成！`);
    console.log(`   单人风格分类: ${singleSpeakerCategories.length} 个`);
    console.log(`   多人对话分类: ${multiSpeakerCategories.length} 个`);
    console.log(`   总计分类: ${singleSpeakerCategories.length + multiSpeakerCategories.length} 个`);

    // 返回分类映射，供风格分配使用
    return {
      singleSpeaker: {
        emotion: singleSpeakerCategories.find(c => c.name === '情感表达'),
        tone: singleSpeakerCategories.find(c => c.name === '语调风格'),
        role: singleSpeakerCategories.find(c => c.name === '专业角色'),
        scenario: singleSpeakerCategories.find(c => c.name === '应用场景'),
        rhythm: singleSpeakerCategories.find(c => c.name === '节奏控制'),
        industry: singleSpeakerCategories.find(c => c.name === '行业专用'),
      },
      multiSpeaker: {
        business: multiSpeakerCategories.find(c => c.name === '商务对话'),
        education: multiSpeakerCategories.find(c => c.name === '教育互动'),
        service: multiSpeakerCategories.find(c => c.name === '服务咨询'),
        social: multiSpeakerCategories.find(c => c.name === '社交聊天'),
        media: multiSpeakerCategories.find(c => c.name === '媒体访谈'),
        professional: multiSpeakerCategories.find(c => c.name === '专业咨询'),
      },
    };

  } catch (error) {
    console.error('❌ 创建分类时出错:', error);
    throw error;
  }
}

// 如果直接运行此文件
const isMainModule = process.argv[1]?.endsWith('09-separated-style-categories.ts');

if (isMainModule) {
  createSeparatedStyleCategories()
    .then(() => {
      console.log('\n✅ 分离的风格分类创建完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 创建过程失败:', error);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { createSeparatedStyleCategories };
