import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 语言模块种子数据
 * 创建系统支持的26种语言配置
 */

// 26种支持的语言（24种Gemini官方支持 + 中文 + 高棉语）
const SUPPORTED_LANGUAGES = [
  // 主要语言
  { code: 'km-KH', name: 'Khmer (Cambodia)', nativeName: 'ភាសាខ្មែរ (កម្ពុជា)', region: 'Cambodia', flag: '🇰🇭' },
  { code: 'zh-CN', name: 'Chinese (China)', nativeName: '中文 (中国)', region: 'China', flag: '🇨🇳' },
  
  // Google Gemini 官方支持的24种语言
  { code: 'ar-EG', name: 'Arabic (Egypt)', nativeName: 'العربية (مصر)', region: 'Egypt', flag: '🇪🇬' },
  { code: 'de-DE', name: 'German (Germany)', nativeName: 'Deutsch (Deutschland)', region: 'Germany', flag: '🇩🇪' },
  { code: 'en-US', name: 'English (United States)', nativeName: 'English (United States)', region: 'United States', flag: '🇺🇸' },
  { code: 'es-US', name: 'Spanish (United States)', nativeName: 'español (Estados Unidos)', region: 'United States', flag: '🇺🇸' },
  { code: 'fr-FR', name: 'French (France)', nativeName: 'français (France)', region: 'France', flag: '🇫🇷' },
  { code: 'hi-IN', name: 'Hindi (India)', nativeName: 'हिन्दी (भारत)', region: 'India', flag: '🇮🇳' },
  { code: 'id-ID', name: 'Indonesian (Indonesia)', nativeName: 'Bahasa Indonesia (Indonesia)', region: 'Indonesia', flag: '🇮🇩' },
  { code: 'it-IT', name: 'Italian (Italy)', nativeName: 'italiano (Italia)', region: 'Italy', flag: '🇮🇹' },
  { code: 'ja-JP', name: 'Japanese (Japan)', nativeName: '日本語 (日本)', region: 'Japan', flag: '🇯🇵' },
  { code: 'ko-KR', name: 'Korean (South Korea)', nativeName: '한국어 (대한민국)', region: 'South Korea', flag: '🇰🇷' },
  { code: 'pt-BR', name: 'Portuguese (Brazil)', nativeName: 'português (Brasil)', region: 'Brazil', flag: '🇧🇷' },
  { code: 'ru-RU', name: 'Russian (Russia)', nativeName: 'русский (Россия)', region: 'Russia', flag: '🇷🇺' },
  { code: 'nl-NL', name: 'Dutch (Netherlands)', nativeName: 'Nederlands (Nederland)', region: 'Netherlands', flag: '🇳🇱' },
  { code: 'pl-PL', name: 'Polish (Poland)', nativeName: 'polski (Polska)', region: 'Poland', flag: '🇵🇱' },
  { code: 'th-TH', name: 'Thai (Thailand)', nativeName: 'ไทย (ประเทศไทย)', region: 'Thailand', flag: '🇹🇭' },
  { code: 'tr-TR', name: 'Turkish (Turkey)', nativeName: 'Türkçe (Türkiye)', region: 'Turkey', flag: '🇹🇷' },
  { code: 'vi-VN', name: 'Vietnamese (Vietnam)', nativeName: 'Tiếng Việt (Việt Nam)', region: 'Vietnam', flag: '🇻🇳' },
  { code: 'ro-RO', name: 'Romanian (Romania)', nativeName: 'română (România)', region: 'Romania', flag: '🇷🇴' },
  { code: 'uk-UA', name: 'Ukrainian (Ukraine)', nativeName: 'українська (Україна)', region: 'Ukraine', flag: '🇺🇦' },
  { code: 'bn-BD', name: 'Bengali (Bangladesh)', nativeName: 'বাংলা (বাংলাদেশ)', region: 'Bangladesh', flag: '🇧🇩' },
  { code: 'en-IN', name: 'English (India)', nativeName: 'English (India)', region: 'India', flag: '🇮🇳' },
  { code: 'mr-IN', name: 'Marathi (India)', nativeName: 'मराठी (भारत)', region: 'India', flag: '🇮🇳' },
  { code: 'ta-IN', name: 'Tamil (India)', nativeName: 'தமிழ் (இந்தியா)', region: 'India', flag: '🇮🇳' },
  { code: 'te-IN', name: 'Telugu (India)', nativeName: 'తెలుగు (భారతదేశం)', region: 'India', flag: '🇮🇳' }
];

async function seedLanguages() {
  console.log('🌍 开始创建语言种子数据...');

  for (const [index, language] of SUPPORTED_LANGUAGES.entries()) {
    await prisma.language.upsert({
      where: { code: language.code },
      update: language,
      create: {
        ...language,
        sortOrder: index,
        isActive: true,
      },
    });
    console.log(`✅ 创建语言: ${language.name} (${language.code})`);
  }

  console.log(`🎉 成功创建 ${SUPPORTED_LANGUAGES.length} 种语言！`);
}

async function main() {
  await seedLanguages();
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main()
    .catch((e) => {
      console.error('❌ 语言种子数据创建失败:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { seedLanguages, SUPPORTED_LANGUAGES };