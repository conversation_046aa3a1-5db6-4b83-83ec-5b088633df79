import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedCreditPackagePricing() {
  console.log('💰 Seeding credit package pricing...');

  // 检查是否已存在积分包定价
  const existingPackages = await prisma.creditPackagePricing.findMany({
    where: { isActive: true },
  });

  if (existingPackages.length > 0) {
    console.log('✅ Credit package pricing already exists');
    return;
  }

  // 获取API定价配置来计算成本价
  const apiConfig = await prisma.apiPricingConfig.findFirst({
    where: { isActive: true },
  });

  if (!apiConfig) {
    throw new Error('API pricing configuration not found. Please run API pricing seed first.');
  }

  // 计算成本价的函数
  const calculateCostPrice = (credits: number) => {
    const tokensPerCredit = 1000;
    const totalTokens = credits * tokensPerCredit;
    const avgTokenCost = (apiConfig.standardInputPrice + apiConfig.standardOutputPrice) / 2;
    return (totalTokens / 1000000) * avgTokenCost;
  };

  // 默认积分包配置
  const defaultPackages = [
    {
      packageType: 'basic',
      credits: 1000,
      salePrice: 9.99,
      promoPrice: null, // 无优惠价格
    },
    {
      packageType: 'standard',
      credits: 3000,
      salePrice: 24.99,
      promoPrice: 19.99, // 有优惠价格
    },
    {
      packageType: 'premium',
      credits: 10000,
      salePrice: 79.99,
      promoPrice: 59.99, // 有优惠价格
    },
  ];

  // 创建积分包定价
  for (const pkg of defaultPackages) {
    const costPrice = calculateCostPrice(pkg.credits);
    
    const createdPackage = await prisma.creditPackagePricing.create({
      data: {
        packageType: pkg.packageType,
        credits: pkg.credits,
        costPrice: Math.round(costPrice * 100) / 100, // 保留两位小数
        salePrice: pkg.salePrice,
        promoPrice: pkg.promoPrice,
        isActive: true,
      },
    });

    console.log(`✅ Created ${pkg.packageType} package: ${pkg.credits} credits, cost: $${createdPackage.costPrice}, sale: $${pkg.salePrice}${pkg.promoPrice ? `, promo: $${pkg.promoPrice}` : ''}`);
  }

  console.log('✅ Credit package pricing seeding completed');
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  seedCreditPackagePricing()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
