# 🌱 Voctana 生产环境模块化种子数据

## 📋 概述

本目录包含了 Voctana 项目的模块化种子数据，专门为生产环境部署设计。每个模块都可以独立运行，也可以作为整体批量执行。

## 📁 文件结构

```
prisma/seeds/
├── index.ts                           # 主执行脚本
├── 01-users.ts                       # 用户模块 (超级管理员 + 测试用户)
├── 02-languages.ts                   # 语言模块 (26种支持的语言)
├── 03-voice-characters.ts            # 语音角色模块 (30个Google Gemini角色)
├── 04-character-packages.ts          # 字符包模块 (标准版 + 专业版套餐)
├── 05-character-multilingual-names.ts # 角色名称本地化模块
├── 06-voice-demos.ts                 # 语音试听模块 (780个试听占位符)
├── 07-conversation-templates.ts      # 对话模板模块 (5个系统模板)
├── 08-api-configs.ts                 # API配置模块 (Gemini + OpenAI配置)
├── 09-style-categories.ts            # 风格分类模块 (6个预设分类)
├── 10-system-styles.ts               # 系统风格模块 (14个官方风格)
└── README.md                         # 本说明文件
```

## 🚀 使用方法

### 1. 完整执行 (推荐生产环境)

```bash
# 执行所有模块的种子数据
npx tsx prisma/seeds/index.ts
```

### 2. 选择性执行

```bash
# 仅执行基础模块 (用户 + 语言 + API配置)
npx tsx -e "
import { runBasicSeeds } from './prisma/seeds/index.ts';
runBasicSeeds().finally(() => process.exit(0));
"

# 仅执行内容模块 (角色 + 套餐 + 模板)
npx tsx -e "
import { runContentSeeds } from './prisma/seeds/index.ts';
runContentSeeds().finally(() => process.exit(0));
"

# 仅执行可选模块 (本地化名称)
npx tsx -e "
import { runOptionalSeeds } from './prisma/seeds/index.ts';
runOptionalSeeds().finally(() => process.exit(0));
"
```

### 3. 单独执行某个模块

```bash
# 执行用户模块
npx tsx prisma/seeds/01-users.ts

# 执行语言模块
npx tsx prisma/seeds/02-languages.ts

# 执行语音角色模块
npx tsx prisma/seeds/03-voice-characters.ts

# 执行风格分类模块
npx tsx prisma/seeds/09-style-categories.ts

# 执行系统风格模块
npx tsx prisma/seeds/10-system-styles.ts

# ... 其他模块类似
```

## 📊 模块依赖关系

```mermaid
graph TD
    A[01-users] --> E[05-character-multilingual-names]
    B[02-languages] --> F[06-voice-demos]
    C[03-voice-characters] --> E
    C --> F
    D[04-character-packages]
    G[07-conversation-templates]
    H[08-api-configs]
    I[09-style-categories] --> J[10-system-styles]
    K[01-users] --> J
    
    B --> F
    E --> F
```

**执行顺序说明:**
1. **必需模块** (按顺序): users → languages → api-configs → character-packages → voice-characters → style-categories → system-styles → voice-demos
2. **可选模块** (任意顺序): character-multilingual-names, conversation-templates

## 🎯 各模块功能说明

### 01-users.ts - 用户模块
- ✅ 创建超级管理员 (`<EMAIL>`)
- ✅ 创建测试用户 (`<EMAIL>`) - 仅非生产环境
- 🔑 初始密码: `admin123456` / `test123456`

### 02-languages.ts - 语言模块
- ✅ 创建26种支持的语言
- 🌍 包含高棉语、中文和24种Google Gemini官方语言
- 🏁 包含语言代码、名称、本地名称、地区和国旗

### 03-voice-characters.ts - 语音角色模块
- ✅ 创建30个Google Gemini官方语音角色
- 👥 15个女性角色 + 15个男性角色
- 🎭 包含详细的角色描述、风格、个性和适用场景

### 04-character-packages.ts - 字符包模块
- ✅ 创建8个字符套餐 (4个标准版 + 4个专业版)
- 💰 价格从 $9.99 到 $329.99
- 📊 字符数量从 2万 到 100万

### 05-character-multilingual-names.ts - 角色名称本地化模块
- ✅ 为语音角色创建多语言本地化名称
- 🌐 支持26种语言的角色名称翻译
- 📝 注意：当前版本包含前5个角色，完整版本需要单独部署

### 06-voice-demos.ts - 语音试听模块
- ✅ 创建780个试听占位符 (30角色 × 26语言)
- 🎵 为每种语言组合创建本地化试听文本
- ⚠️  音频文件需要通过管理界面手工生成

### 07-conversation-templates.ts - 对话模板模块
- ✅ 创建5个系统对话模板
- 💬 客服、教育、新闻、销售、面试场景
- 🎬 每个模板包含角色配置和对话内容

### 08-api-configs.ts - API配置模块
- ✅ 创建Google Gemini和OpenAI API配置
- 💰 创建API定价配置 (50%利润率)
- 📈 标准版: $22.5/1M字符, 专业版: $45/1M字符

### 09-style-categories.ts - 风格分类模块
- ✅ 创建6个预设风格分类
- 🏷️ 情感、语调、节奏、场景、角色、行业分类
- 🌍 支持中英高棉三语分类名称

### 10-system-styles.ts - 系统风格模块
- ✅ 创建14个官方预设风格
- 🎨 包含情感、语调、场景、角色等不同类型
- 👥 支持单人和多人对话风格
- 🎯 适用于各种使用场景

## 🔧 环境变量要求

确保以下环境变量已配置:

```env
DATABASE_URL=postgresql://...
GEMINI_API_KEY=your_gemini_api_key
OPENAI_API_KEY=your_openai_api_key  # 可选
NODE_ENV=production  # 生产环境设置
```

## 📈 数据统计

执行完所有模块后，系统将包含:

- 👥 **用户**: 1个超级管理员 (+ 1个测试用户，非生产环境)
- 🌍 **语言**: 26种支持的语言
- 🎭 **语音角色**: 30个Google Gemini角色
- 📦 **字符套餐**: 8个套餐 (标准版 + 专业版)
- 🎵 **试听占位符**: 780个 (需要手工生成音频)
- 💬 **对话模板**: 5个系统模板
- 🏷️ **风格分类**: 6个预设分类
- 🎨 **系统风格**: 14个官方预设风格
- 🔧 **API配置**: 完整的API和定价配置

## ⚠️  重要提醒

1. **生产环境部署**: 确保 `NODE_ENV=production`
2. **API密钥安全**: 妥善保管 API 密钥，不要提交到版本控制
3. **音频生成**: 试听音频需要通过管理界面手工生成
4. **数据备份**: 执行前建议备份现有数据库
5. **权限检查**: 确保数据库连接具有创建和更新权限

## 🔄 更新和维护

- 各模块可以单独更新和重新执行
- 使用 `upsert` 操作，重复执行是安全的
- 新增语言或角色时，只需更新对应模块
- 生产环境建议定期备份种子数据

## 🆘 故障排除

如果遇到问题:

1. 检查数据库连接和权限
2. 确认环境变量配置正确
3. 查看具体模块的错误日志
4. 可以单独执行失败的模块进行调试
5. 必要时清理数据库重新执行

---

🎯 **现在您的 Voctana 项目拥有了完整的模块化种子数据系统，可以灵活适应各种生产环境部署需求！**