import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 字符包模块种子数据
 * 创建标准版和专业版字符包套餐
 */

// 字符套餐配置
const CHARACTER_PACKAGES = [
  // 标准版套餐
  {
    name: '5万字符包 - 标准版',
    type: 'STANDARD',
    price: 9.99,
    characters: 50000,
    popular: false,
    description: '适合个人用户和小型项目，经济实惠的入门选择',
  },
  {
    name: '20万字符包 - 标准版',
    type: 'STANDARD',
    price: 29.99,
    characters: 200000,
    discount: 25,
    popular: true,
    description: '最受欢迎的标准套餐，性价比最高，适合中小企业',
  },
  {
    name: '50万字符包 - 标准版',
    type: 'STANDARD',
    price: 69.99,
    characters: 500000,
    discount: 30,
    popular: false,
    description: '适合中型企业和内容创作者，大容量更优惠',
  },
  {
    name: '100万字符包 - 标准版',
    type: 'STANDARD',
    price: 129.99,
    characters: 1000000,
    discount: 35,
    popular: false,
    description: '企业级标准版套餐，适合大型项目和持续使用',
  },

  // 专业版套餐
  {
    name: '2万字符包 - 专业版',
    type: 'PROFESSIONAL',
    price: 19.99,
    characters: 20000,
    popular: false,
    description: '专业质量语音，适合商业用途和高品质要求',
  },
  {
    name: '10万字符包 - 专业版',
    type: 'PROFESSIONAL',
    price: 79.99,
    characters: 100000,
    discount: 20,
    popular: true,
    description: '专业版最受欢迎套餐，商务首选',
  },
  {
    name: '25万字符包 - 专业版',
    type: 'PROFESSIONAL',
    price: 179.99,
    characters: 250000,
    discount: 25,
    popular: false,
    description: '适合大型企业和专业工作室，顶级语音质量',
  },
  {
    name: '50万字符包 - 专业版',
    type: 'PROFESSIONAL',
    price: 329.99,
    characters: 500000,
    discount: 30,
    popular: false,
    description: '企业级专业版套餐，最高品质语音服务',
  },
];

async function seedCharacterPackages() {
  console.log('📦 开始创建字符包种子数据...');

  for (const [index, pkg] of CHARACTER_PACKAGES.entries()) {
    const packageData = {
      ...pkg,
      sortOrder: index,
      isActive: true,
    };

    await prisma.characterPackage.upsert({
      where: { id: `package-${String(index + 1).padStart(2, '0')}` },
      update: packageData,
      create: {
        id: `package-${String(index + 1).padStart(2, '0')}`,
        ...packageData,
      },
    });

    const discountText = pkg.discount ? ` (${pkg.discount}% 折扣)` : '';
    console.log(`✅ 创建套餐: ${pkg.name}${discountText} - ¥${pkg.price}`);
  }

  console.log(`🎉 成功创建 ${CHARACTER_PACKAGES.length} 个字符包套餐！`);
}

async function main() {
  await seedCharacterPackages();
}

// 检查是否作为主模块运行
const isMainModule = process.argv[1] && process.argv[1].endsWith(import.meta.url.split('/').pop() || '');

if (isMainModule) {
  main()
    .catch((e) => {
      console.error('❌ 字符包种子数据创建失败:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { seedCharacterPackages, CHARACTER_PACKAGES };