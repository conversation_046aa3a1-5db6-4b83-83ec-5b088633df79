import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

/**
 * 用户模块种子数据
 * 创建系统必需的管理员用户
 */
async function seedUsers() {
  console.log('👥 开始创建用户种子数据...');

  // 创建超级管理员用户
  const hashedPassword = await bcrypt.hash('admin123456', 12);
  
  const superAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Super Admin',
      password: hashedPassword,
      role: 'SUPER_ADMIN',
      credits: 100000,
      usedCredits: 0,
      isActive: true,
      emailVerified: new Date(),
    },
  });
  console.log('✅ 超级管理员用户创建成功:', superAdmin.email);

  // 创建测试用户（可选，仅开发环境）
  if (process.env.NODE_ENV !== 'production') {
    const testUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Test User',
        password: await bcrypt.hash('test123456', 12),
        role: 'USER',
        credits: 1000,
        usedCredits: 0,
        isActive: true,
        emailVerified: new Date(),
      },
    });
    console.log('✅ 测试用户创建成功:', testUser.email);
  }

  console.log('🎉 用户种子数据创建完成！');
}

async function main() {
  await seedUsers();
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main()
    .catch((e) => {
      console.error('❌ 用户种子数据创建失败:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { seedUsers };