-- CreateEnum
CREATE TYPE "TextGenerationType" AS ENUM ('SINGLE', 'CONVERSATION');

-- CreateTable
CREATE TABLE "TextGeneration" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" "TextGenerationType" NOT NULL,
    "prompt" TEXT NOT NULL,
    "generatedText" TEXT NOT NULL,
    "parameters" JSONB,
    "tokenCount" INTEGER NOT NULL,
    "cost" DECIMAL(10,6),
    "languageCode" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TextGeneration_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TextGenerationUsage" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "date" DATE NOT NULL,
    "tokenCount" INTEGER NOT NULL DEFAULT 0,
    "generationCount" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TextGenerationUsage_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "TextGeneration_userId_idx" ON "TextGeneration"("userId");

-- CreateIndex
CREATE INDEX "TextGeneration_type_idx" ON "TextGeneration"("type");

-- CreateIndex
CREATE INDEX "TextGeneration_createdAt_idx" ON "TextGeneration"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "TextGenerationUsage_userId_date_key" ON "TextGenerationUsage"("userId", "date");

-- AddForeignKey
ALTER TABLE "TextGeneration" ADD CONSTRAINT "TextGeneration_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TextGenerationUsage" ADD CONSTRAINT "TextGenerationUsage_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add text generation quota columns to User table
ALTER TABLE "User" ADD COLUMN "textGenerationQuota" INTEGER NOT NULL DEFAULT 10000;
ALTER TABLE "User" ADD COLUMN "usedTextGenerationQuota" INTEGER NOT NULL DEFAULT 0;
