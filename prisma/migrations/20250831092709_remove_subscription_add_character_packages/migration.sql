-- CreateEnum
CREATE TYPE "public"."PaymentProvider" AS ENUM ('PAYPAL', 'KHQR', 'WECHAT', 'STRIPE');

-- CreateEnum
CREATE TYPE "public"."PaymentStatus" AS ENUM ('PENDING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "public"."ConversationStatus" AS ENUM ('PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "public"."ApiProvider" AS ENUM ('GEMINI', 'OPENAI');

-- CreateEnum
CREATE TYPE "public"."ApiStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'MAINTENANCE', 'ERROR');

-- CreateEnum
CREATE TYPE "public"."UserRole" AS ENUM ('USER', 'SUPER_ADMIN');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "public"."Gender" AS ENUM ('MALE', 'FEMALE', 'NEUTRAL');

-- CreateEnum
CREATE TYPE "public"."AudioFormat" AS ENUM ('MP3', 'WAV', 'AAC');

-- CreateEnum
CREATE TYPE "public"."QuotaType" AS ENUM ('STANDARD', 'PROFESSIONAL');

-- CreateEnum
CREATE TYPE "public"."PackageType" AS ENUM ('STANDARD', 'PROFESSIONAL');

-- CreateEnum
CREATE TYPE "public"."StyleType" AS ENUM ('SINGLE_SPEAKER', 'MULTI_SPEAKER');

-- CreateEnum
CREATE TYPE "public"."StyleSource" AS ENUM ('OFFICIAL', 'USER_CREATED', 'COMMUNITY');

-- CreateTable
CREATE TABLE "public"."Conversation" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "sceneDescription" TEXT,
    "pauseBetweenLines" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "exportFormat" TEXT NOT NULL DEFAULT 'mp3',
    "status" "public"."ConversationStatus" NOT NULL DEFAULT 'PROCESSING',
    "audioUrl" TEXT,
    "duration" INTEGER,
    "totalCharacters" INTEGER NOT NULL DEFAULT 0,
    "errorMessage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Conversation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ConversationCharacter" (
    "id" TEXT NOT NULL,
    "conversationId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "voiceCharacterId" TEXT NOT NULL,
    "speed" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "pitch" INTEGER NOT NULL DEFAULT 0,
    "volume" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "emotion" TEXT NOT NULL DEFAULT 'neutral',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ConversationCharacter_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ConversationDialogue" (
    "id" TEXT NOT NULL,
    "conversationId" TEXT NOT NULL,
    "characterId" TEXT NOT NULL,
    "text" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "audioUrl" TEXT,
    "duration" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ConversationDialogue_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ConversationTemplate" (
    "id" TEXT NOT NULL,
    "userId" TEXT,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "characters" TEXT NOT NULL,
    "dialogues" TEXT NOT NULL,
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "usageCount" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ConversationTemplate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Account" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,
    "refresh_token_expires_in" INTEGER,

    CONSTRAINT "Account_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Session" (
    "id" TEXT NOT NULL,
    "sessionToken" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Session_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."User" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "email" TEXT,
    "emailVerified" TIMESTAMP(3),
    "image" TEXT,
    "password" TEXT,
    "role" "public"."UserRole" NOT NULL DEFAULT 'USER',
    "standardQuota" INTEGER NOT NULL DEFAULT 500,
    "usedStandardQuota" INTEGER NOT NULL DEFAULT 0,
    "professionalQuota" INTEGER NOT NULL DEFAULT 0,
    "usedProfessionalQuota" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "lastActiveAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."VerificationToken" (
    "identifier" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL
);

-- CreateTable
CREATE TABLE "public"."VoiceCharacter" (
    "id" TEXT NOT NULL,
    "characterName" TEXT NOT NULL,
    "characterNameEn" TEXT NOT NULL,
    "multilingualNames" TEXT,
    "originalName" TEXT NOT NULL,
    "apiProvider" "public"."ApiProvider" NOT NULL,
    "apiVoiceName" TEXT NOT NULL,
    "gender" "public"."Gender" NOT NULL,
    "description" TEXT NOT NULL,
    "style" TEXT,
    "personality" TEXT,
    "bestFor" TEXT,
    "avatarUrl" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "isCustom" BOOLEAN NOT NULL DEFAULT false,
    "createdBy" TEXT,
    "customSettings" TEXT,
    "customPrompt" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "VoiceCharacter_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."AudioGeneration" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "inputText" TEXT NOT NULL,
    "outputAudioUrl" TEXT NOT NULL,
    "characterId" TEXT NOT NULL,
    "apiProvider" "public"."ApiProvider" NOT NULL,
    "actualVoiceName" TEXT NOT NULL,
    "audioFormat" "public"."AudioFormat" NOT NULL DEFAULT 'MP3',
    "speed" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "duration" DOUBLE PRECISION,
    "characterCount" INTEGER NOT NULL,
    "fileSize" INTEGER,
    "cost" DECIMAL(65,30),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AudioGeneration_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ApiProviderConfig" (
    "id" TEXT NOT NULL,
    "provider" "public"."ApiProvider" NOT NULL,
    "displayName" TEXT NOT NULL,
    "description" TEXT,
    "status" "public"."ApiStatus" NOT NULL DEFAULT 'ACTIVE',
    "priority" INTEGER NOT NULL DEFAULT 0,
    "isEnabled" BOOLEAN NOT NULL DEFAULT true,
    "config" JSONB NOT NULL,
    "dailyLimit" INTEGER,
    "monthlyLimit" INTEGER,
    "costLimit" DOUBLE PRECISION,
    "totalRequests" INTEGER NOT NULL DEFAULT 0,
    "successRequests" INTEGER NOT NULL DEFAULT 0,
    "failedRequests" INTEGER NOT NULL DEFAULT 0,
    "totalCost" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "lastUsedAt" TIMESTAMP(3),

    CONSTRAINT "ApiProviderConfig_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ApiUsageLog" (
    "id" TEXT NOT NULL,
    "provider" "public"."ApiProvider" NOT NULL,
    "requestType" TEXT NOT NULL,
    "inputSize" INTEGER NOT NULL,
    "outputSize" INTEGER,
    "success" BOOLEAN NOT NULL,
    "responseTime" INTEGER NOT NULL,
    "errorMessage" TEXT,
    "cost" DOUBLE PRECISION,
    "userId" TEXT,
    "sessionId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ApiUsageLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."UserPreference" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "defaultCharacterId" TEXT,
    "defaultQuality" TEXT,
    "defaultSpeed" DOUBLE PRECISION,
    "defaultPitch" INTEGER,
    "defaultVolumeGainDb" DOUBLE PRECISION,
    "favoriteCharacters" TEXT,
    "styleTemplates" TEXT,
    "totalGenerations" INTEGER NOT NULL DEFAULT 0,
    "totalCharacters" INTEGER NOT NULL DEFAULT 0,
    "enableRecommendations" BOOLEAN NOT NULL DEFAULT true,
    "autoSelectQuality" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UserPreference_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Usage" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "date" DATE NOT NULL,
    "characterCount" INTEGER NOT NULL DEFAULT 0,
    "audioCount" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Usage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."QuotaAdjustment" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "quotaType" "public"."QuotaType" NOT NULL,
    "amount" INTEGER NOT NULL,
    "reason" TEXT NOT NULL,
    "adminId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "QuotaAdjustment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."payments" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "characterPurchaseId" TEXT,
    "amount" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "provider" "public"."PaymentProvider" NOT NULL,
    "status" "public"."PaymentStatus" NOT NULL DEFAULT 'PENDING',
    "paypalOrderId" TEXT,
    "khqrTransactionId" TEXT,
    "wechatOrderId" TEXT,
    "stripePaymentId" TEXT,
    "description" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),

    CONSTRAINT "payments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."character_purchases" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "packageId" TEXT NOT NULL,
    "packageName" TEXT NOT NULL,
    "packageType" "public"."PackageType" NOT NULL,
    "characters" INTEGER NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "orderNumber" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),

    CONSTRAINT "character_purchases_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."character_packages" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "public"."PackageType" NOT NULL,
    "price" DOUBLE PRECISION NOT NULL,
    "characters" INTEGER NOT NULL,
    "discount" INTEGER,
    "popular" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "character_packages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."api_pricing_configs" (
    "id" TEXT NOT NULL,
    "standardInputPrice" DOUBLE PRECISION NOT NULL,
    "standardOutputPrice" DOUBLE PRECISION NOT NULL,
    "professionalInputPrice" DOUBLE PRECISION NOT NULL,
    "professionalOutputPrice" DOUBLE PRECISION NOT NULL,
    "markup" DOUBLE PRECISION NOT NULL,
    "standardFinalPrice" DOUBLE PRECISION NOT NULL,
    "professionalFinalPrice" DOUBLE PRECISION NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "api_pricing_configs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."languages" (
    "id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "nativeName" TEXT NOT NULL,
    "region" TEXT NOT NULL,
    "flag" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "languages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."voice_demos" (
    "id" TEXT NOT NULL,
    "voiceCharacterId" TEXT NOT NULL,
    "languageId" TEXT NOT NULL,
    "languageCode" TEXT NOT NULL,
    "demoText" TEXT NOT NULL,
    "audioUrl" TEXT NOT NULL,
    "audioKey" TEXT NOT NULL,
    "duration" DOUBLE PRECISION,
    "fileSize" INTEGER,
    "quality" TEXT NOT NULL DEFAULT 'standard',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "voice_demos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."voice_demo_play_stats" (
    "id" TEXT NOT NULL,
    "voiceDemoId" TEXT NOT NULL,
    "userId" TEXT,
    "userAgent" TEXT,
    "ipAddress" TEXT,
    "referrer" TEXT,
    "playDuration" DOUBLE PRECISION,
    "isCompleted" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "voice_demo_play_stats_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."voice_demo_aggregate_stats" (
    "id" TEXT NOT NULL,
    "voiceDemoId" TEXT NOT NULL,
    "totalPlays" INTEGER NOT NULL DEFAULT 0,
    "uniquePlays" INTEGER NOT NULL DEFAULT 0,
    "completedPlays" INTEGER NOT NULL DEFAULT 0,
    "averageDuration" DOUBLE PRECISION,
    "lastPlayedAt" TIMESTAMP(3),
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "voice_demo_aggregate_stats_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."style_categories" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "nameEn" TEXT NOT NULL,
    "nameKhmer" TEXT,
    "description" TEXT,
    "icon" TEXT,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "style_categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."user_styles" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "nameEn" TEXT,
    "nameKhmer" TEXT,
    "description" TEXT,
    "prompt" TEXT NOT NULL,
    "type" "public"."StyleType" NOT NULL DEFAULT 'SINGLE_SPEAKER',
    "source" "public"."StyleSource" NOT NULL DEFAULT 'USER_CREATED',
    "categoryId" TEXT,
    "parameters" JSONB,
    "speakerConfig" JSONB,
    "tags" TEXT[],
    "icon" TEXT,
    "color" TEXT,
    "usageCount" INTEGER NOT NULL DEFAULT 0,
    "favoriteCount" INTEGER NOT NULL DEFAULT 0,
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "isOfficial" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_styles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."style_favorites" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "styleId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "style_favorites_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."style_shares" (
    "id" TEXT NOT NULL,
    "styleId" TEXT NOT NULL,
    "sharedBy" TEXT NOT NULL,
    "sharedTo" TEXT,
    "shareCode" TEXT,
    "expiresAt" TIMESTAMP(3),
    "usageCount" INTEGER NOT NULL DEFAULT 0,
    "maxUsage" INTEGER,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "style_shares_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."custom_voice_characters" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "nameEn" TEXT,
    "nameKhmer" TEXT,
    "description" TEXT,
    "baseCharacterId" TEXT NOT NULL,
    "speed" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "pitch" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "volume" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "defaultStyleId" TEXT,
    "avatarUrl" TEXT,
    "personality" TEXT[],
    "bestFor" TEXT[],
    "tags" TEXT[],
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "isOfficial" BOOLEAN NOT NULL DEFAULT false,
    "usageCount" INTEGER NOT NULL DEFAULT 0,
    "favoriteCount" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "custom_voice_characters_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."character_favorites" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "characterId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "character_favorites_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."character_shares" (
    "id" TEXT NOT NULL,
    "characterId" TEXT NOT NULL,
    "sharedBy" TEXT NOT NULL,
    "sharedTo" TEXT,
    "shareCode" TEXT,
    "expiresAt" TIMESTAMP(3),
    "usageCount" INTEGER NOT NULL DEFAULT 0,
    "maxUsage" INTEGER,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "character_shares_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."audio_generation_styles" (
    "id" TEXT NOT NULL,
    "audioGenerationId" TEXT NOT NULL,
    "styleId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "audio_generation_styles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."audio_generation_characters" (
    "id" TEXT NOT NULL,
    "audioGenerationId" TEXT NOT NULL,
    "characterId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "audio_generation_characters_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Conversation_userId_idx" ON "public"."Conversation"("userId");

-- CreateIndex
CREATE INDEX "Conversation_status_idx" ON "public"."Conversation"("status");

-- CreateIndex
CREATE INDEX "Conversation_createdAt_idx" ON "public"."Conversation"("createdAt");

-- CreateIndex
CREATE INDEX "ConversationCharacter_conversationId_idx" ON "public"."ConversationCharacter"("conversationId");

-- CreateIndex
CREATE INDEX "ConversationCharacter_voiceCharacterId_idx" ON "public"."ConversationCharacter"("voiceCharacterId");

-- CreateIndex
CREATE INDEX "ConversationDialogue_conversationId_idx" ON "public"."ConversationDialogue"("conversationId");

-- CreateIndex
CREATE INDEX "ConversationDialogue_characterId_idx" ON "public"."ConversationDialogue"("characterId");

-- CreateIndex
CREATE INDEX "ConversationDialogue_order_idx" ON "public"."ConversationDialogue"("order");

-- CreateIndex
CREATE INDEX "ConversationTemplate_userId_idx" ON "public"."ConversationTemplate"("userId");

-- CreateIndex
CREATE INDEX "ConversationTemplate_isPublic_idx" ON "public"."ConversationTemplate"("isPublic");

-- CreateIndex
CREATE INDEX "ConversationTemplate_createdAt_idx" ON "public"."ConversationTemplate"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "Account_provider_providerAccountId_key" ON "public"."Account"("provider", "providerAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "Session_sessionToken_key" ON "public"."Session"("sessionToken");

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "public"."User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_token_key" ON "public"."VerificationToken"("token");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_identifier_token_key" ON "public"."VerificationToken"("identifier", "token");

-- CreateIndex
CREATE INDEX "VoiceCharacter_apiProvider_idx" ON "public"."VoiceCharacter"("apiProvider");

-- CreateIndex
CREATE INDEX "VoiceCharacter_isActive_idx" ON "public"."VoiceCharacter"("isActive");

-- CreateIndex
CREATE INDEX "VoiceCharacter_isCustom_idx" ON "public"."VoiceCharacter"("isCustom");

-- CreateIndex
CREATE INDEX "VoiceCharacter_createdBy_idx" ON "public"."VoiceCharacter"("createdBy");

-- CreateIndex
CREATE INDEX "AudioGeneration_userId_idx" ON "public"."AudioGeneration"("userId");

-- CreateIndex
CREATE INDEX "AudioGeneration_characterId_idx" ON "public"."AudioGeneration"("characterId");

-- CreateIndex
CREATE INDEX "AudioGeneration_apiProvider_idx" ON "public"."AudioGeneration"("apiProvider");

-- CreateIndex
CREATE INDEX "AudioGeneration_createdAt_idx" ON "public"."AudioGeneration"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "ApiProviderConfig_provider_key" ON "public"."ApiProviderConfig"("provider");

-- CreateIndex
CREATE INDEX "ApiProviderConfig_provider_idx" ON "public"."ApiProviderConfig"("provider");

-- CreateIndex
CREATE INDEX "ApiProviderConfig_status_idx" ON "public"."ApiProviderConfig"("status");

-- CreateIndex
CREATE INDEX "ApiProviderConfig_priority_idx" ON "public"."ApiProviderConfig"("priority");

-- CreateIndex
CREATE INDEX "ApiUsageLog_provider_idx" ON "public"."ApiUsageLog"("provider");

-- CreateIndex
CREATE INDEX "ApiUsageLog_userId_idx" ON "public"."ApiUsageLog"("userId");

-- CreateIndex
CREATE INDEX "ApiUsageLog_createdAt_idx" ON "public"."ApiUsageLog"("createdAt");

-- CreateIndex
CREATE INDEX "ApiUsageLog_success_idx" ON "public"."ApiUsageLog"("success");

-- CreateIndex
CREATE UNIQUE INDEX "UserPreference_userId_key" ON "public"."UserPreference"("userId");

-- CreateIndex
CREATE INDEX "UserPreference_userId_idx" ON "public"."UserPreference"("userId");

-- CreateIndex
CREATE INDEX "Usage_userId_idx" ON "public"."Usage"("userId");

-- CreateIndex
CREATE INDEX "Usage_date_idx" ON "public"."Usage"("date");

-- CreateIndex
CREATE UNIQUE INDEX "Usage_userId_date_key" ON "public"."Usage"("userId", "date");

-- CreateIndex
CREATE UNIQUE INDEX "character_purchases_orderNumber_key" ON "public"."character_purchases"("orderNumber");

-- CreateIndex
CREATE UNIQUE INDEX "languages_code_key" ON "public"."languages"("code");

-- CreateIndex
CREATE INDEX "languages_isActive_idx" ON "public"."languages"("isActive");

-- CreateIndex
CREATE INDEX "languages_sortOrder_idx" ON "public"."languages"("sortOrder");

-- CreateIndex
CREATE INDEX "voice_demos_languageCode_idx" ON "public"."voice_demos"("languageCode");

-- CreateIndex
CREATE INDEX "voice_demos_voiceCharacterId_idx" ON "public"."voice_demos"("voiceCharacterId");

-- CreateIndex
CREATE UNIQUE INDEX "voice_demos_voiceCharacterId_languageId_key" ON "public"."voice_demos"("voiceCharacterId", "languageId");

-- CreateIndex
CREATE INDEX "voice_demo_play_stats_voiceDemoId_idx" ON "public"."voice_demo_play_stats"("voiceDemoId");

-- CreateIndex
CREATE INDEX "voice_demo_play_stats_userId_idx" ON "public"."voice_demo_play_stats"("userId");

-- CreateIndex
CREATE INDEX "voice_demo_play_stats_createdAt_idx" ON "public"."voice_demo_play_stats"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "voice_demo_aggregate_stats_voiceDemoId_key" ON "public"."voice_demo_aggregate_stats"("voiceDemoId");

-- CreateIndex
CREATE INDEX "voice_demo_aggregate_stats_totalPlays_idx" ON "public"."voice_demo_aggregate_stats"("totalPlays");

-- CreateIndex
CREATE INDEX "voice_demo_aggregate_stats_lastPlayedAt_idx" ON "public"."voice_demo_aggregate_stats"("lastPlayedAt");

-- CreateIndex
CREATE UNIQUE INDEX "style_categories_name_key" ON "public"."style_categories"("name");

-- CreateIndex
CREATE INDEX "style_categories_sortOrder_idx" ON "public"."style_categories"("sortOrder");

-- CreateIndex
CREATE INDEX "style_categories_isActive_idx" ON "public"."style_categories"("isActive");

-- CreateIndex
CREATE INDEX "user_styles_userId_idx" ON "public"."user_styles"("userId");

-- CreateIndex
CREATE INDEX "user_styles_categoryId_idx" ON "public"."user_styles"("categoryId");

-- CreateIndex
CREATE INDEX "user_styles_type_idx" ON "public"."user_styles"("type");

-- CreateIndex
CREATE INDEX "user_styles_source_idx" ON "public"."user_styles"("source");

-- CreateIndex
CREATE INDEX "user_styles_isPublic_idx" ON "public"."user_styles"("isPublic");

-- CreateIndex
CREATE INDEX "user_styles_isOfficial_idx" ON "public"."user_styles"("isOfficial");

-- CreateIndex
CREATE INDEX "user_styles_usageCount_idx" ON "public"."user_styles"("usageCount");

-- CreateIndex
CREATE INDEX "user_styles_favoriteCount_idx" ON "public"."user_styles"("favoriteCount");

-- CreateIndex
CREATE INDEX "user_styles_createdAt_idx" ON "public"."user_styles"("createdAt");

-- CreateIndex
CREATE INDEX "style_favorites_userId_idx" ON "public"."style_favorites"("userId");

-- CreateIndex
CREATE INDEX "style_favorites_styleId_idx" ON "public"."style_favorites"("styleId");

-- CreateIndex
CREATE INDEX "style_favorites_createdAt_idx" ON "public"."style_favorites"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "style_favorites_userId_styleId_key" ON "public"."style_favorites"("userId", "styleId");

-- CreateIndex
CREATE UNIQUE INDEX "style_shares_shareCode_key" ON "public"."style_shares"("shareCode");

-- CreateIndex
CREATE INDEX "style_shares_styleId_idx" ON "public"."style_shares"("styleId");

-- CreateIndex
CREATE INDEX "style_shares_sharedBy_idx" ON "public"."style_shares"("sharedBy");

-- CreateIndex
CREATE INDEX "style_shares_sharedTo_idx" ON "public"."style_shares"("sharedTo");

-- CreateIndex
CREATE INDEX "style_shares_shareCode_idx" ON "public"."style_shares"("shareCode");

-- CreateIndex
CREATE INDEX "style_shares_expiresAt_idx" ON "public"."style_shares"("expiresAt");

-- CreateIndex
CREATE INDEX "style_shares_createdAt_idx" ON "public"."style_shares"("createdAt");

-- CreateIndex
CREATE INDEX "custom_voice_characters_userId_idx" ON "public"."custom_voice_characters"("userId");

-- CreateIndex
CREATE INDEX "custom_voice_characters_baseCharacterId_idx" ON "public"."custom_voice_characters"("baseCharacterId");

-- CreateIndex
CREATE INDEX "custom_voice_characters_defaultStyleId_idx" ON "public"."custom_voice_characters"("defaultStyleId");

-- CreateIndex
CREATE INDEX "custom_voice_characters_isPublic_idx" ON "public"."custom_voice_characters"("isPublic");

-- CreateIndex
CREATE INDEX "custom_voice_characters_isOfficial_idx" ON "public"."custom_voice_characters"("isOfficial");

-- CreateIndex
CREATE INDEX "custom_voice_characters_usageCount_idx" ON "public"."custom_voice_characters"("usageCount");

-- CreateIndex
CREATE INDEX "custom_voice_characters_favoriteCount_idx" ON "public"."custom_voice_characters"("favoriteCount");

-- CreateIndex
CREATE INDEX "custom_voice_characters_createdAt_idx" ON "public"."custom_voice_characters"("createdAt");

-- CreateIndex
CREATE INDEX "character_favorites_userId_idx" ON "public"."character_favorites"("userId");

-- CreateIndex
CREATE INDEX "character_favorites_characterId_idx" ON "public"."character_favorites"("characterId");

-- CreateIndex
CREATE INDEX "character_favorites_createdAt_idx" ON "public"."character_favorites"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "character_favorites_userId_characterId_key" ON "public"."character_favorites"("userId", "characterId");

-- CreateIndex
CREATE UNIQUE INDEX "character_shares_shareCode_key" ON "public"."character_shares"("shareCode");

-- CreateIndex
CREATE INDEX "character_shares_characterId_idx" ON "public"."character_shares"("characterId");

-- CreateIndex
CREATE INDEX "character_shares_sharedBy_idx" ON "public"."character_shares"("sharedBy");

-- CreateIndex
CREATE INDEX "character_shares_sharedTo_idx" ON "public"."character_shares"("sharedTo");

-- CreateIndex
CREATE INDEX "character_shares_shareCode_idx" ON "public"."character_shares"("shareCode");

-- CreateIndex
CREATE INDEX "character_shares_expiresAt_idx" ON "public"."character_shares"("expiresAt");

-- CreateIndex
CREATE INDEX "character_shares_createdAt_idx" ON "public"."character_shares"("createdAt");

-- CreateIndex
CREATE INDEX "audio_generation_styles_audioGenerationId_idx" ON "public"."audio_generation_styles"("audioGenerationId");

-- CreateIndex
CREATE INDEX "audio_generation_styles_styleId_idx" ON "public"."audio_generation_styles"("styleId");

-- CreateIndex
CREATE INDEX "audio_generation_styles_createdAt_idx" ON "public"."audio_generation_styles"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "audio_generation_styles_audioGenerationId_styleId_key" ON "public"."audio_generation_styles"("audioGenerationId", "styleId");

-- CreateIndex
CREATE INDEX "audio_generation_characters_audioGenerationId_idx" ON "public"."audio_generation_characters"("audioGenerationId");

-- CreateIndex
CREATE INDEX "audio_generation_characters_characterId_idx" ON "public"."audio_generation_characters"("characterId");

-- CreateIndex
CREATE INDEX "audio_generation_characters_createdAt_idx" ON "public"."audio_generation_characters"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "audio_generation_characters_audioGenerationId_characterId_key" ON "public"."audio_generation_characters"("audioGenerationId", "characterId");

-- AddForeignKey
ALTER TABLE "public"."Conversation" ADD CONSTRAINT "Conversation_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ConversationCharacter" ADD CONSTRAINT "ConversationCharacter_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES "public"."Conversation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ConversationCharacter" ADD CONSTRAINT "ConversationCharacter_voiceCharacterId_fkey" FOREIGN KEY ("voiceCharacterId") REFERENCES "public"."VoiceCharacter"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ConversationDialogue" ADD CONSTRAINT "ConversationDialogue_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES "public"."Conversation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ConversationDialogue" ADD CONSTRAINT "ConversationDialogue_characterId_fkey" FOREIGN KEY ("characterId") REFERENCES "public"."ConversationCharacter"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ConversationTemplate" ADD CONSTRAINT "ConversationTemplate_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Account" ADD CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Session" ADD CONSTRAINT "Session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."VoiceCharacter" ADD CONSTRAINT "VoiceCharacter_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."AudioGeneration" ADD CONSTRAINT "AudioGeneration_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."AudioGeneration" ADD CONSTRAINT "AudioGeneration_characterId_fkey" FOREIGN KEY ("characterId") REFERENCES "public"."VoiceCharacter"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ApiUsageLog" ADD CONSTRAINT "ApiUsageLog_provider_fkey" FOREIGN KEY ("provider") REFERENCES "public"."ApiProviderConfig"("provider") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ApiUsageLog" ADD CONSTRAINT "ApiUsageLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserPreference" ADD CONSTRAINT "UserPreference_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Usage" ADD CONSTRAINT "Usage_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."QuotaAdjustment" ADD CONSTRAINT "QuotaAdjustment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."payments" ADD CONSTRAINT "payments_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."payments" ADD CONSTRAINT "payments_characterPurchaseId_fkey" FOREIGN KEY ("characterPurchaseId") REFERENCES "public"."character_purchases"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."character_purchases" ADD CONSTRAINT "character_purchases_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."character_purchases" ADD CONSTRAINT "character_purchases_packageId_fkey" FOREIGN KEY ("packageId") REFERENCES "public"."character_packages"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."voice_demos" ADD CONSTRAINT "voice_demos_voiceCharacterId_fkey" FOREIGN KEY ("voiceCharacterId") REFERENCES "public"."VoiceCharacter"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."voice_demos" ADD CONSTRAINT "voice_demos_languageId_fkey" FOREIGN KEY ("languageId") REFERENCES "public"."languages"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."voice_demo_play_stats" ADD CONSTRAINT "voice_demo_play_stats_voiceDemoId_fkey" FOREIGN KEY ("voiceDemoId") REFERENCES "public"."voice_demos"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."voice_demo_play_stats" ADD CONSTRAINT "voice_demo_play_stats_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."voice_demo_aggregate_stats" ADD CONSTRAINT "voice_demo_aggregate_stats_voiceDemoId_fkey" FOREIGN KEY ("voiceDemoId") REFERENCES "public"."voice_demos"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_styles" ADD CONSTRAINT "user_styles_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_styles" ADD CONSTRAINT "user_styles_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "public"."style_categories"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."style_favorites" ADD CONSTRAINT "style_favorites_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."style_favorites" ADD CONSTRAINT "style_favorites_styleId_fkey" FOREIGN KEY ("styleId") REFERENCES "public"."user_styles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."style_shares" ADD CONSTRAINT "style_shares_styleId_fkey" FOREIGN KEY ("styleId") REFERENCES "public"."user_styles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."style_shares" ADD CONSTRAINT "style_shares_sharedBy_fkey" FOREIGN KEY ("sharedBy") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."style_shares" ADD CONSTRAINT "style_shares_sharedTo_fkey" FOREIGN KEY ("sharedTo") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."custom_voice_characters" ADD CONSTRAINT "custom_voice_characters_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."custom_voice_characters" ADD CONSTRAINT "custom_voice_characters_baseCharacterId_fkey" FOREIGN KEY ("baseCharacterId") REFERENCES "public"."VoiceCharacter"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."custom_voice_characters" ADD CONSTRAINT "custom_voice_characters_defaultStyleId_fkey" FOREIGN KEY ("defaultStyleId") REFERENCES "public"."user_styles"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."character_favorites" ADD CONSTRAINT "character_favorites_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."character_favorites" ADD CONSTRAINT "character_favorites_characterId_fkey" FOREIGN KEY ("characterId") REFERENCES "public"."custom_voice_characters"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."character_shares" ADD CONSTRAINT "character_shares_characterId_fkey" FOREIGN KEY ("characterId") REFERENCES "public"."custom_voice_characters"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."character_shares" ADD CONSTRAINT "character_shares_sharedBy_fkey" FOREIGN KEY ("sharedBy") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."character_shares" ADD CONSTRAINT "character_shares_sharedTo_fkey" FOREIGN KEY ("sharedTo") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."audio_generation_styles" ADD CONSTRAINT "audio_generation_styles_audioGenerationId_fkey" FOREIGN KEY ("audioGenerationId") REFERENCES "public"."AudioGeneration"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."audio_generation_styles" ADD CONSTRAINT "audio_generation_styles_styleId_fkey" FOREIGN KEY ("styleId") REFERENCES "public"."user_styles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."audio_generation_characters" ADD CONSTRAINT "audio_generation_characters_audioGenerationId_fkey" FOREIGN KEY ("audioGenerationId") REFERENCES "public"."AudioGeneration"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."audio_generation_characters" ADD CONSTRAINT "audio_generation_characters_characterId_fkey" FOREIGN KEY ("characterId") REFERENCES "public"."custom_voice_characters"("id") ON DELETE CASCADE ON UPDATE CASCADE;
