-- 添加积分消耗比字段到 api_pricing_configs 表
ALTER TABLE "api_pricing_configs" 
ADD COLUMN "textGenerationRatio" INTEGER DEFAULT 1000,
ADD COLUMN "standardVoiceRatio" INTEGER DEFAULT 100,
ADD COLUMN "professionalVoiceRatio" INTEGER DEFAULT 50;

-- 更新现有记录的消耗比配置
UPDATE "api_pricing_configs" 
SET 
  "textGenerationRatio" = 1000,
  "standardVoiceRatio" = 100,
  "professionalVoiceRatio" = 50
WHERE "textGenerationRatio" IS NULL;

-- 设置字段为非空
ALTER TABLE "api_pricing_configs" 
ALTER COLUMN "textGenerationRatio" SET NOT NULL,
ALTER COLUMN "standardVoiceRatio" SET NOT NULL,
ALTER COLUMN "professionalVoiceRatio" SET NOT NULL;
