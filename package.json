{"name": "voctana", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:seed": "tsx prisma/seed.ts", "db:seed:production": "tsx prisma/seeds/index.ts", "db:seed:basic": "tsx -e \"import { runBasicSeeds } from './prisma/seeds/index.ts'; runBasicSeeds().finally(() => process.exit(0));\"", "db:seed:content": "tsx -e \"import { runContentSeeds } from './prisma/seeds/index.ts'; runContentSeeds().finally(() => process.exit(0));\"", "db:seed:optional": "tsx -e \"import { runOptionalSeeds } from './prisma/seeds/index.ts'; runOptionalSeeds().finally(() => process.exit(0));\"", "db:seed:styles": "tsx -e \"import { seedStyleCategories, seedSystemStyles } from './prisma/seeds/index.ts'; (async () => { await seedStyleCategories(); await seedSystemStyles(); })().finally(() => process.exit(0));\"", "db:status": "tsx scripts/db-status.ts", "db:studio": "prisma studio", "test:apis": "tsx scripts/test-apis.ts", "test:openai": "tsx scripts/test-openai-only.ts", "test:gemini": "tsx scripts/test-gemini-tts.ts", "dev": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "postinstall": "prisma generate", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/prisma-adapter": "^2.7.2", "@aws-sdk/client-s3": "^3.876.0", "@aws-sdk/s3-request-presigner": "^3.876.0", "@google/genai": "^1.16.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.2.1", "@paypal/checkout-server-sdk": "^1.0.3", "@paypal/payouts-sdk": "^1.1.1", "@prisma/client": "^6.5.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.69.0", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "@types/bcrypt": "^6.0.0", "@types/bcryptjs": "^2.4.6", "@types/ioredis": "^4.28.10", "@types/nodemailer": "^7.0.1", "aws-sdk": "^2.1692.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^17.2.1", "html2canvas": "^1.4.1", "ioredis": "^5.7.0", "jspdf": "^3.0.2", "lucide-react": "^0.542.0", "mime": "^4.0.7", "next": "^15.2.3", "next-auth": "^5.0.0-beta.29", "next-themes": "^0.4.6", "nodemailer": "^6.10.1", "openai": "^5.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.62.0", "react-to-print": "^3.1.1", "recharts": "^3.1.2", "server-only": "^0.0.1", "sharp": "^0.34.3", "sonner": "^2.0.7", "stripe": "^18.4.0", "superjson": "^2.2.1", "tailwind-merge": "^3.3.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.0.15", "@types/node": "^20.14.10", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.5.0", "tailwindcss": "^4.0.15", "tsx": "^4.20.5", "tw-animate-css": "^1.3.7", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "npm@11.4.2"}