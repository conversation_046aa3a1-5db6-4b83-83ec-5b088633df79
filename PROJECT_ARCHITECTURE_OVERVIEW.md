# 🏗️ Voctana 项目架构全览

## 📋 项目概述

**Voctana** 是一个基于 AI 的多语言语音生成平台，专注于高质量的文本转语音(TTS)服务，特别优化了柬埔寨语(Khmer)支持。

### 🔧 技术栈
- **前端**: Next.js 15 + React 19 + TypeScript
- **后端**: Next.js API Routes + tRPC
- **数据库**: PostgreSQL + Prisma ORM
- **认证**: NextAuth.js v5
- **UI**: Tailwind CSS + Shadcn/ui + Radix UI
- **状态管理**: TanStack Query + React Context
- **AI服务**: OpenAI TTS + Google Gemini
- **存储**: Cloudflare R2 + AWS S3
- **支付**: PayPal + Stripe
- **部署**: 支持 Docker + Vercel

## 🏗️ 项目结构

### 📁 根目录结构
```
├── src/                    # 源代码目录
├── prisma/                 # 数据库相关
├── public/                 # 静态资源
├── scripts/                # 工具脚本
├── docs/                   # 项目文档
├── package.json            # 项目配置
├── next.config.js          # Next.js配置
├── tailwind.config.ts      # Tailwind配置
└── tsconfig.json           # TypeScript配置
```

### 🎯 核心功能模块

#### 1. **用户认证与权限管理**
- **路径**: `src/app/auth/`, `src/server/auth/`
- **功能**: 登录、注册、密码重置、角色权限
- **角色**: USER, SUPER_ADMIN

#### 2. **语音生成系统**
- **单人语音**: `src/app/generate/`
- **多人对话**: `src/app/multi-speaker/`
- **字幕配音**: `src/app/demo/components/SubtitleDubbingMode.tsx`
- **API**: `src/app/api/voice/`, `src/lib/tts/`

#### 3. **角色与语言管理**
- **语音角色**: `src/app/characters/`, `src/components/voice-character-selector.tsx`
- **语言支持**: `src/app/languages/`, `src/lib/multilingual-utils.ts`
- **管理后台**: `src/app/admin/language-characters/`

#### 4. **积分与支付系统**
- **积分管理**: `src/app/credits/`, `src/lib/credits/`
- **支付集成**: `src/app/payment/`, `src/lib/paypal.ts`
- **订单管理**: `src/app/orders/`

#### 5. **内容分享与社区**
- **内容分享**: `src/app/share/`, `src/components/character-share.tsx`
- **社区功能**: `src/app/community/`
- **模板系统**: `src/app/templates/`

## 🗄️ 数据库架构

### 核心数据模型

#### 用户相关
- **User**: 用户基础信息、积分、角色
- **Account/Session**: NextAuth.js 认证
- **UserPreference**: 用户偏好设置

#### 语音生成相关
- **AudioGeneration**: 单人语音生成记录
- **Conversation**: 多人对话生成
- **ConversationCharacter**: 对话角色配置
- **ConversationDialogue**: 对话内容

#### 角色与语言
- **ApiCharacterTemplate**: API角色模板
- **LanguageCharacter**: 语言特定角色
- **Language**: 支持的语言列表
- **VoiceDemo**: 语音试听样本

#### 积分与支付
- **CreditUsage**: 积分使用记录
- **CreditPurchase**: 积分购买记录
- **Payment**: 支付记录
- **CreditGift**: 积分赠送

#### 内容管理
- **StyleCategory**: 语音风格分类
- **UserStyle**: 用户自定义风格
- **ConversationTemplate**: 对话模板

## 🔌 API 架构

### tRPC 路由结构
```
src/server/api/routers/
├── auth.ts                 # 认证相关
├── voice.ts                # 语音生成
├── character.ts            # 角色管理
├── credits.ts              # 积分系统
├── conversation.ts         # 对话生成
├── admin.ts                # 管理功能
├── paypal.ts               # 支付集成
└── community.ts            # 社区功能
```

### REST API 端点
```
src/app/api/
├── auth/[...nextauth]/     # NextAuth.js
├── voice/                  # 语音生成API
├── audio/                  # 音频处理
├── ai/                     # AI服务集成
├── webhooks/               # 第三方回调
└── upload/                 # 文件上传
```

## 🎨 前端架构

### 页面结构
```
src/app/
├── (auth)/                 # 认证页面
├── dashboard/              # 用户仪表板
├── demo/                   # 演示页面
├── generate/               # 单人语音生成
├── multi-speaker/          # 多人对话
├── characters/             # 角色管理
├── history/                # 生成历史
├── credits/                # 积分管理
├── admin/                  # 管理后台
└── settings/               # 用户设置
```

### 组件架构
```
src/components/
├── ui/                     # 基础UI组件
├── auth/                   # 认证组件
├── admin/                  # 管理组件
├── landing/                # 落地页组件
├── voice-*.tsx             # 语音相关组件
├── character-*.tsx         # 角色相关组件
├── credit-*.tsx            # 积分相关组件
└── app-layout.tsx          # 应用布局
```

### 状态管理
- **全局状态**: React Context (Language, AudioPlayer, Theme)
- **服务端状态**: TanStack Query + tRPC
- **表单状态**: React Hook Form + Zod

## 🔧 核心服务

### 1. **TTS 服务**
- **OpenAI TTS**: 高质量语音合成
- **Google Gemini**: 多语言支持
- **音频处理**: 格式转换、质量优化

### 2. **存储服务**
- **Cloudflare R2**: 主要音频存储
- **AWS S3**: 备用存储方案
- **本地缓存**: 临时文件处理

### 3. **支付服务**
- **PayPal**: 主要支付方式
- **Stripe**: 备用支付方案
- **积分系统**: 消费追踪、余额管理

### 4. **AI 服务**
- **头像生成**: AI生成角色头像
- **文本生成**: 智能对话内容生成
- **语音优化**: 自然度提升

## 🛡️ 安全与权限

### 认证机制
- **NextAuth.js**: 统一认证框架
- **JWT Token**: 会话管理
- **密码加密**: bcrypt 哈希

### 权限控制
- **角色权限**: USER, SUPER_ADMIN
- **API保护**: tRPC 中间件
- **路由守卫**: 页面级权限检查

### 数据安全
- **环境变量**: 敏感信息隔离
- **CORS配置**: 跨域请求控制
- **输入验证**: Zod schema 验证

## 📊 监控与分析

### 使用统计
- **语音生成**: 次数、时长、质量
- **用户行为**: 页面访问、功能使用
- **系统性能**: API响应时间、错误率

### 日志系统
- **API调用**: 请求响应日志
- **错误追踪**: 异常信息记录
- **用户操作**: 关键行为审计

## 🚀 部署架构

### 开发环境
- **本地开发**: Next.js dev server
- **数据库**: 本地 PostgreSQL
- **存储**: 本地文件系统

### 生产环境
- **应用部署**: Vercel / Docker
- **数据库**: 云端 PostgreSQL
- **CDN**: Cloudflare
- **监控**: 应用性能监控

## 📈 扩展性设计

### 微服务架构
- **API模块化**: 独立的tRPC路由
- **服务解耦**: 松耦合的功能模块
- **水平扩展**: 支持负载均衡

### 插件系统
- **TTS提供商**: 可插拔的语音服务
- **支付网关**: 多种支付方式支持
- **存储后端**: 灵活的存储选择

## 🎯 核心功能模块详解

### 1. **Demo演示系统** (`src/app/demo/`)
**完整的语音生成演示平台，包含：**
- **DemoHeader**: 顶部导航，Logo+品牌，主题切换，语言切换，用户登录
- **SinglePersonMode**: 单人语音生成，支持文本输入、语音风格、角色选择
- **MultiPersonMode**: 多人对话生成，对话解析、角色分配、批量生成
- **SubtitleDubbingMode**: 字幕配音功能，视频上传、SRT字幕、配音生成
- **UserStatusBar**: 用户状态栏，积分显示、历史记录、充值管理
- **ContentShareOptions**: 内容分享，公开/私有设置，下载功能
- **QuickNavigation**: 快速导航，功能入口，登录引导

### 2. **语音生成引擎** (`src/lib/tts/`)
**多提供商TTS服务集成：**
- **TTSManager**: 统一的TTS管理器，支持多API提供商
- **GeminiTTSService**: Google Gemini TTS集成
- **OpenAI TTS**: OpenAI语音合成服务
- **音频处理**: WAV格式处理、音频合并、质量优化
- **参数控制**: 语速、音调、音量、情感控制

### 3. **积分经济系统** (`src/lib/credits/`)
**完整的积分管理和计费系统：**
- **CreditCalculationService**: 积分计算服务，基于token消耗
- **消耗比系统**: 不同服务类型的积分消耗比例
- **使用追踪**: 详细的积分使用记录和统计
- **充值系统**: PayPal/Stripe支付集成
- **赠送系统**: 积分赠送、推荐奖励、欢迎奖励

### 4. **角色管理系统** (`src/app/admin/language-characters/`)
**多语言角色配置和管理：**
- **ApiCharacterTemplate**: API角色模板，支持多提供商
- **LanguageCharacter**: 语言特定角色配置
- **性别管理**: 可编辑的性别属性，支持继承和覆盖
- **头像系统**: AI生成头像，本地上传，多语言适配
- **试听功能**: 语音预览，质量评估

### 5. **音频存储系统** (`src/lib/r2-storage.ts`)
**多云存储解决方案：**
- **Cloudflare R2**: 主要存储后端，成本优化
- **AWS S3**: 备用存储方案，高可用性
- **音频缓存**: 本地缓存机制，提升访问速度
- **CDN加速**: 全球内容分发，降低延迟
- **下载代理**: 解决CORS问题，安全下载

### 6. **用户认证系统** (`src/server/auth/`)
**安全的用户认证和授权：**
- **NextAuth.js v5**: 现代认证框架
- **凭据认证**: 邮箱密码登录，bcrypt加密
- **角色权限**: USER/SUPER_ADMIN角色控制
- **会话管理**: JWT token，安全会话
- **路由保护**: 页面级和API级权限检查

### 7. **管理后台系统** (`src/app/admin/`)
**全面的系统管理功能：**
- **用户管理**: 用户列表、权限管理、积分调整
- **角色管理**: 语音角色配置、模板管理
- **语言管理**: 多语言支持配置
- **订单管理**: 支付记录、退款处理
- **系统监控**: API状态、使用统计、性能监控
- **内容管理**: 模板管理、风格配置

### 8. **国际化系统** (`src/lib/i18n.ts`)
**多语言支持框架：**
- **24种语言**: 包括柬埔寨语、中文、英文等
- **动态切换**: 实时语言切换，无需刷新
- **本地化内容**: 界面文本、错误信息、帮助文档
- **RTL支持**: 阿拉伯语等从右到左语言
- **语言检测**: 自动检测用户首选语言

## 🔄 数据流架构

### 语音生成流程
```
用户输入 → 参数验证 → 积分检查 → TTS API调用 → 音频处理 → 存储上传 → 记录保存 → 结果返回
```

### 积分消费流程
```
服务请求 → 成本计算 → 余额检查 → 积分扣除 → 使用记录 → 统计更新
```

### 用户认证流程
```
登录请求 → 凭据验证 → 会话创建 → 权限检查 → 资源访问
```

## 🚀 性能优化策略

### 前端优化
- **代码分割**: 按路由和功能模块分割
- **懒加载**: 组件和资源按需加载
- **缓存策略**: 静态资源和API响应缓存
- **图片优化**: Next.js Image组件，WebP格式

### 后端优化
- **数据库索引**: 关键查询字段索引优化
- **连接池**: 数据库连接复用
- **API缓存**: Redis缓存热点数据
- **批量操作**: 减少数据库查询次数

### 存储优化
- **音频压缩**: 智能压缩算法，平衡质量和大小
- **CDN分发**: 全球节点加速访问
- **预签名URL**: 安全的直接上传下载
- **生命周期管理**: 自动清理过期文件

这个架构设计确保了系统的可维护性、可扩展性和高性能，为用户提供稳定可靠的AI语音生成服务。
