
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { auth } from '~/server/auth';

// 公开路由（不需要认证）
const publicRoutes = [
  '/auth/signin',
  '/auth/signup',
  '/auth/forgot-password',
  '/auth/reset-password',
  '/api/auth', // NextAuth.js API路由
  '/api/webhooks', // Webhook路由（如Stripe）
  '/api/audio', // 音频相关API路由（包含自己的认证逻辑）
  '/demo', // 演示页面保持公开
  '/_next', // Next.js静态资源
  '/favicon.ico',
  '/robots.txt',
  '/sitemap.xml',
];

// 用户专用路由（需要登录但不需要管理员权限）
const userRoutes = [
  '/generate', // 单人语音生成
  '/multi-speaker', // 多人对话生成
];

// 管理员专用路由（需要SUPER_ADMIN权限）
const adminRoutes = [
  '/admin', // 所有管理员功能都在 /admin/ 下
];

// 需要认证的路由（包括用户和管理员路由）
const protectedRoutes = [...userRoutes, ...adminRoutes, '/api/trpc'];

export default async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  console.log('🔒 Middleware running for:', pathname);

  // 跳过静态资源和公开路由
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    console.log('✅ Public route, allowing access:', pathname);
    return NextResponse.next();
  }

  // 获取用户session
  const session = await auth();
  console.log('🔍 Session check:', {
    pathname,
    hasSession: !!session?.user,
    userEmail: session?.user?.email
  });

  // 检查是否为受保护的路由
  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route));
  const isAdminRoute = adminRoutes.some(route => pathname.startsWith(route));
  const isUserRoute = userRoutes.some(route => pathname.startsWith(route));

  if (isProtectedRoute) {
    // 未登录用户重定向到登录页
    if (!session?.user) {
      console.log('🚫 Protected route accessed without auth, redirecting:', pathname);
      const signInUrl = new URL('/auth/signin', request.url);
      signInUrl.searchParams.set('callbackUrl', pathname);
      return NextResponse.redirect(signInUrl);
    }
    console.log('✅ Protected route accessed with valid session:', pathname);

    // 检查管理员路由权限 - 在middleware中只做基本检查，详细权限在页面组件中验证
    if (isAdminRoute) {
      // 管理员路由需要在页面组件中进行详细的角色检查
      // 这里只确保用户已登录
      if (!session.user.email) {
        return NextResponse.redirect(new URL('/auth/signin', request.url));
      }
    }

    // 用户路由只需要登录即可
    if (isUserRoute) {
      if (!session.user.email) {
        return NextResponse.redirect(new URL('/auth/signin', request.url));
      }
    }
  }

  // 根路径重定向逻辑
  if (pathname === '/') {
    // 所有用户（无论是否登录）都显示 landing 页面
    // 已登录用户可以在 landing 页面使用完整的语音生成功能
    // 未登录用户可以浏览功能并在需要时登录
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - api/auth (NextAuth.js)
     * - _next/static (静态文件)
     * - _next/image (图片优化)
     * - favicon.ico (网站图标)
     * - 其他静态资源
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)',
  ],
};
